<template>
  <div>
    <div class="row">
      <div class="col s6 m6 l6">
        <div class="e4s-form-header">Competition Event Builder</div>
      </div>

      <div class="col s6 m6 l6">
        <div class="right">
          <div v-if="isLoading" class="e4s-force-inline-block">
            <loading-spinner></loading-spinner>
          </div>
          <button
            v-if="!showSubmit"
            class="btn waves-effect waves green"
            v-on:click.stop="confirmSubmit"
          >
            <span v-text="$t('buttons.save')"></span>
          </button>
          <button
            v-if="showSubmit"
            class="btn xxx-btn-small btn-flat red-text e4s-bold"
            v-on:click.stop="onCancel"
          >
            <span v-text="$t('buttons.cancel')"></span>
          </button>
          <button
            v-if="showSubmit"
            class="btn waves-effect waves green"
            :disabled="isLoading"
            v-on:click.stop="onSubmit"
          >
            <span v-text="$t('buttons.confirm')"></span>
          </button>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m6 l6">
        <span v-text="getCompDate" :id="PREFIX + 'date'"></span>
        <label class="active" :for="PREFIX + 'date'">Date</label>
      </div>

      <div class="input-field col s12 m6 l6">
        <span v-text="getCompTime" :id="PREFIX + 'time'"></span>
        <label class="active" :for="PREFIX + 'time'">Time</label>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m6 l6">
        <span v-text="compEvent.event.name" :id="PREFIX + 'name'"></span>
        <label class="active" :for="PREFIX + 'name'">Name</label>
      </div>

      <div class="input-field col s12 m6 l6">
        <span v-text="compEvent.event.gender" :id="PREFIX + 'gender'"></span>
        <label class="active" :for="PREFIX + 'name'">Gender</label>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m6 l6">
        <span v-text="compEvent.event.tf" :id="PREFIX + 'tf'"></span>
        <label class="active" :for="PREFIX + 'name'">Event Type</label>
      </div>

      <div class="input-field col s12 m6 l6">
        <span v-text="getUomLabel" :id="PREFIX + 'uom'"></span>
        <label class="active" :for="PREFIX + 'name'">Uom</label>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m6 l6">
        <span
          v-text="compEvent.ageGroup.name"
          :id="PREFIX + 'age-group'"
        ></span>
        <label class="active" :for="PREFIX + 'age-group'">Age Group</label>
      </div>

      <div class="input-field col s12 m6 l6">
        <span v-text="compEvent.price.name" :id="PREFIX + 'price'"></span>
        <label class="active" :for="PREFIX + 'price'">Price</label>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s6 m6 l6">
        <select
          :id="PREFIX + 'is-open'"
          :name="PREFIX + 'is-open'"
          class="generic-select"
          v-model="compEvent.isOpen"
        >
          <option value="" disabled selected hidden>Select</option>
          <option :value="1">Yes</option>
          <option :value="0">No</option>
        </select>
        <label class="active" :for="PREFIX + 'is-open'">Is Event Open</label>
      </div>

      <div class="input-field col s6 m6 l6">
        <input
          :id="PREFIX + 'maxAthletes'"
          :name="PREFIX + 'maxAthletes'"
          type="number"
          v-model="compEvent.maxAthletes"
          placeholder="Max athletes that can enter this event"
        />
        <label class="active" :for="PREFIX + 'maxAthletes'">Max Athletes</label>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s6 m6 l6">
        <input
          :id="PREFIX + 'split'"
          :name="PREFIX + 'split'"
          type="number"
          v-model="compEvent.split"
          placeholder="Max athletes that can enter this event"
        />
        <label class="active" :for="PREFIX + 'split'">PB Split</label>
      </div>

      <div class="input-field col s6 m6 l6">
        <input
          :id="PREFIX + 'event-no'"
          :name="PREFIX + 'event-no'"
          type="number"
          v-model.number="compEvent.eventNo"
          placeholder=""
        />
        <label class="active" :for="PREFIX + 'event-no'">Event Number</label>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s10 m10 l10">
        <input
          :id="PREFIX + 'help-text'"
          :name="PREFIX + 'help-text'"
          type="text"
          v-model="compEvent.options.helpText"
          placeholder="Enter any text that may further explain event"
        />
        <label class="active" :for="PREFIX + 'help-text'">Help text</label>
      </div>

      <!--Want it to always show but need to handle legacy already saved data.-->
      <div
        class="input-field col s12 m12 l2"
        v-if="!compEvent.options.rowOptions.autoExpandHelpText"
      >
        <p>
          <label>
            <input
              class="e4s-checkbox"
              type="checkbox"
              v-model="compEvent.options.rowOptions.autoExpandHelpText"
            />
            <span v-html="'Auto Show'"></span>
          </label>
        </p>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s6 m6 l6">
        <input
          :id="PREFIX + 'xi-text'"
          :name="PREFIX + 'xi-text'"
          type="text"
          v-model="compEvent.options.xiText"
          placeholder="Enter any text"
        />
        <label class="active" :for="PREFIX + 'xi-text'"
          >Extra event text schedule(xiText)</label
        >
      </div>

      <div class="input-field col s6 m6 l6">
        <input
          :id="PREFIX + 'xr-text'"
          :name="PREFIX + 'xr-text'"
          type="text"
          v-model="compEvent.options.xrText"
          placeholder="Enter any text"
        />
        <label class="active" :for="PREFIX + 'xr-text'"
          >Extra event text report(xrText)</label
        >
      </div>
    </div>

    <div class="row">
      <div class="input-field col s6 m6 l6">
        <input
          :id="PREFIX + 'xb-text'"
          :name="PREFIX + 'xb-text'"
          type="text"
          v-model="compEvent.options.xbText"
          placeholder="Enter any text"
        />
        <label class="active" :for="PREFIX + 'xb-text'"
          >Extra event text basket(xbText)</label
        >
      </div>

      <div class="input-field col s6 m6 l6">
        <input
          :id="PREFIX + 'xe-text'"
          :name="PREFIX + 'xe-text'"
          type="text"
          v-model="compEvent.options.xeText"
          placeholder="Enter any text"
        />
        <label class="active" :for="PREFIX + 'xe-text'"
          >Extra event text basket(xeText)</label
        >
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m12 l12">Row options</div>
    </div>

    <div class="row">
      <div class="input-field col s6 m6 l6">
        <p>
          <label>
            <input
              class="e4s-checkbox"
              type="checkbox"
              v-model="compEvent.options.excludeFromCntRule"
            />
            <span v-html="'Exclude from row count'"></span>
          </label>
        </p>
      </div>

      <div class="input-field col s6 m6 l6">
        <p>
          <label>
            <input
              class="e4s-checkbox"
              type="checkbox"
              v-model="compEvent.options.rowOptions.showPB"
            />
            <span v-html="'Show PB'"></span>
          </label>
        </p>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s6 m6 l6">
        <p>
          <label>
            <input
              class="e4s-checkbox"
              type="checkbox"
              v-model="compEvent.options.rowOptions.showPrice"
            />
            <span v-html="'Show Price'"></span>
          </label>
        </p>
      </div>

      <div class="input-field col s6 m6 l6">
        <p>
          <label>
            <input
              class="e4s-checkbox"
              type="checkbox"
              v-model="compEvent.options.rowOptions.showEntryCount"
            />
            <span v-html="'Show Entry Count'"></span>
          </label>
        </p>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m12 l12">Team options</div>
    </div>

    <div class="row">
      <div class="input-field col s12 m12 l12">
        <p>
          <label>
            <input
              class="e4s-checkbox"
              type="checkbox"
              v-model="compEvent.event.options.isTeamEvent"
            />
            <span v-text="'Is Team Event'"></span>
          </label>
        </p>
      </div>
    </div>

    <event-team-options
      v-if="compEvent.event.options.isTeamEvent"
      :event-team-prop="compEvent.options.eventTeam"
      v-on:onInputChange="eventTeamOptionsChanged"
    >
    </event-team-options>

    <div class="row">
      <div class="input-field col s12 m12 l12">
        Comp Unique (if user selects this event, disable the following)
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m12 l12">
        <comp-event-selector
          :gender="compEvent.event.gender"
          :comp-events-prop="getCompEventsSchedule"
          v-on:onSelected="uniqueCompEventSelected"
        >
        </comp-event-selector>
      </div>
    </div>

    <div class="row">
      <div v-for="option in compEvent.options.unique" :key="option.id">
        <div class="input-field col s1 m1 l1">
          <span v-text="option.id"></span>
        </div>
        <div class="input-field col s1 m1 l1">
          <span v-text="option.tf"></span>
        </div>
        <div class="input-field col s1 m1 l1">
          <span v-text="option.gender"></span>
        </div>
        <div class="input-field col s7 m7 l7">
          <span
            v-text="
              option.name
                ? option.name
                : option.event
                ? option.event.name
                : 'xxx'
            "
          ></span>
        </div>
        <div class="input-field col s2 m2 l2">
          <button
            class="btn waves-effect waves red right"
            v-on:click.stop="removeUnique(option.id)"
          >
            <span v-text="$t('buttons.remove')"></span>
          </button>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m12 l12">Multi Event Options</div>
    </div>

    <event-grid-select :gender="compEvent.event.gender"></event-grid-select>

    <div class="row">
      <div class="input-field col s12 m12 l12">
        Upscaling (what other age groups can enter)
      </div>
    </div>

    <div class="row">
      <div class="col s12 m6 l6">
        <label class="active" :for="PREFIX + 'age-groups-comp'"
          >Age Group Selector</label
        >
        <builder-age-group-select
          :id="PREFIX + 'age-groups-comp'"
          :age-groups="getAgeGroupsForUpScale"
          v-on:onSelected="onAgeGroupUpScaleSelected"
        >
        </builder-age-group-select>
      </div>

      <div class="col s12 m6 l6">
        <div
          class="row"
          v-for="ageGroup in compEvent.options.ageGroups"
          :key="ageGroup.id"
        >
          <div class="col s12 m6 l6">
            <span v-text="ageGroup.name"></span>
          </div>
          <div class="col s12 m6 l6">
            <button
              class="btn waves-effect waves red"
              v-on:click.stop="onRemoveUpscale(ageGroup.id)"
            >
              <span v-text="$t('buttons.remove')"></span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="row" v-if="validationResults.length > 0">
      <div class="input-field col s12 m12 l2">
        <span class="errors">Please enter:</span>
        <user-validation-messages
          :validation-messages="validationResults"
        ></user-validation-messages>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div class="right">
          <div v-if="isLoading" class="e4s-force-inline-block">
            <loading-spinner></loading-spinner>
          </div>
          <button
            v-if="!showSubmit"
            class="btn waves-effect waves green"
            v-on:click.stop="confirmSubmit"
          >
            <span v-text="$t('buttons.save')"></span>
          </button>
          <button
            v-if="showSubmit"
            class="btn xxx-btn-small btn-flat red-text e4s-bold"
            v-on:click.stop="onCancel"
          >
            <span v-text="$t('buttons.cancel')"></span>
          </button>
          <button
            v-if="showSubmit"
            class="btn waves-effect waves green"
            :disabled="isLoading"
            v-on:click.stop="onSubmit"
          >
            <span v-text="$t('buttons.confirm')"></span>
          </button>
        </div>
      </div>
    </div>

    <!--        event.options{{compEvent.event.options}}<br><br>-->
    <!--        compEvent.options{{compEvent.options}}<br><br>-->
    <!--        compEvent: {{compEvent}}-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ICompEvent } from "./compevent-models";
import { CompEventService } from "./compevent-service";
import { IValidationResult } from "../common/common-models";
import { EventService } from "../event/event-service";
import EventTeamOptions from "../event/event-team-options.vue";
import {
  IEventTeam,
  IEventTeamCe,
  IUnique,
} from "../athletecompsched/athletecompsched-models";
import EventSelector from "../event/event-selector.vue";
import { IEventE4S, IUniqueEventDisplay } from "../event/event-models";
import { CommonService } from "../common/common-service";
import EventGridSelect from "../event/event-grid-select.vue";
import EventGridSelectRow from "../event/event-grid-select-row.vue";
import { mapState } from "vuex";
import { BUILDER_STORE_CONST } from "../builder/builder-store-constants";
import { IBuilderStoreState } from "../builder/builder-store";
import { IAgeGroup, IAgeGroupBase } from "../agegroup/agegroup-models";
import BuilderAgeGroupSelect from "../agegroup/agegroup-builder/builder-age-group-select.vue";
import { IPrice } from "../price/price-models";
import LoadingSpinner from "../common/ui/loading-spinner.vue";
import UserValidationMessages from "../user-message/user-validation-messages.vue";
import CompEventSelector from "./comp-event-selector.vue";

@Component({
  name: "comp-event-form",
  components: {
    "event-team-options": EventTeamOptions,
    "event-selector": EventSelector,
    "event-grid-select": EventGridSelect,
    "event-grid-select-row": EventGridSelectRow,
    "builder-age-group-select": BuilderAgeGroupSelect,
    "loading-spinner": LoadingSpinner,
    "user-validation-messages": UserValidationMessages,
    "comp-event-selector": CompEventSelector,
  },
  computed: {
    ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
      ageGroupsForComp: (state: IBuilderStoreState) => state.ageGroupsForComp,
      compEventsSchedule: (state: IBuilderStoreState) =>
        state.compEventsSchedule,
    }),
  },
})
export default class CompEventForm extends Vue {
  @Prop({ default: false }) public isLoading: boolean;
  @Prop({
    default: () => {
      return {
        id: 0,
      };
    },
  })
  public compEventProp: ICompEvent;

  @Prop({
    default: () => {
      return {
        id: 0,
      };
    },
  })
  public eventE4s: IEventE4S;
  @Prop({
    default: () => {
      return {
        id: 0,
      };
    },
  })
  public ageGroup: IAgeGroup;
  @Prop({
    default: () => {
      return {
        id: 0,
      };
    },
  })
  public price: IPrice;
  @Prop({ default: "" }) public startDateTime: string;
  @Prop({ default: 1 }) public eventNo: number;

  public ageGroupsForComp: IAgeGroup[];
  public compEventsSchedule: ICompEvent[];

  public compEventService: CompEventService = new CompEventService();
  public commonService: CommonService = new CommonService();
  public eventService: EventService = new EventService();
  public PREFIX = Math.random().toString(36).substring(2);
  public compEvent: ICompEvent = this.compEventService.factoryGetCompEvent();
  public validationResults: IValidationResult[] = [];
  public showSubmit: boolean = false;

  public created() {
    this.reset();
  }

  public reset() {
    this.validationResults = [];
    this.showSubmit = false;
    this.compEvent = this.compEventService.factoryGetCompEvent();
  }

  @Watch("compEventProp")
  public onCompEventPropChanged(newValue: ICompEvent) {
    this.reset();
    this.compEvent = R.clone(newValue);
  }

  @Watch("eventE4s")
  public onGenderChanged(newValue: IEventE4S) {
    const compEvent = this.compEventService.factoryGetCompEvent();
    compEvent.event = R.clone(newValue);
    this.compEvent = compEvent;
  }

  @Watch("ageGroup")
  public onAgeGroupChanged(newValue: IAgeGroup) {
    this.compEvent.ageGroup = R.clone(newValue);
  }

  @Watch("price")
  public onPriceChanged(newValue: IPrice) {
    this.compEvent.price = R.clone(newValue) as IPrice;
  }

  @Watch("eventNo")
  public oneventNoChanged(newValue: number) {
    this.compEvent.eventNo = newValue;
    this.compEvent.maxGroup = newValue.toString();
  }

  public get getUomLabel() {
    return this.eventService.getUomLabel(this.compEvent.event.uom);
  }

  public get getCompDate() {
    return this.commonService.transformIsoForInputField(
      this.compEvent.startDateTime,
      true,
      false
    );
  }

  public get getCompTime() {
    return this.commonService.transformIsoForInputField(
      this.compEvent.startDateTime,
      false,
      true
    );
  }

  public eventTeamOptionsChanged(eventTeam: IEventTeam) {
    this.compEvent.options.eventTeam = R.clone(
      eventTeam
    ) as any as IEventTeamCe;
  }

  public get getCompEventsSchedule() {
    const compEventsSchedule = this.compEventsSchedule.filter(
      (compEvent: ICompEvent) => {
        return compEvent.event.gender === this.compEvent.event.gender;
      }
    );

    compEventsSchedule.unshift(this.compEventService.factoryGetCompEvent());
    return compEventsSchedule;
  }

  public uniqueCompEventSelected(compEvent: ICompEvent) {
    const eventE4sDisplay: IUniqueEventDisplay = R.clone(
      compEvent
    ) as any as IUniqueEventDisplay;
    eventE4sDisplay.ce = compEvent.id;
    if (compEvent && compEvent.id > 0) {
      this.compEvent.options.unique.push(R.clone(eventE4sDisplay));
    }
  }

  public removeUnique(id: number) {
    id = Number(id);
    this.compEvent.options.unique = this.compEvent.options.unique.filter(
      (evt: IUnique) => {
        return evt.ce !== id;
      }
    );
  }

  public get getAgeGroupsForUpScale() {
    return this.ageGroupsForComp.filter((ageGroup: IAgeGroup) => {
      return ageGroup.id !== this.compEvent.ageGroup.id;
    });
  }

  public onAgeGroupUpScaleSelected(ageGroup: IAgeGroup) {
    const ageGroups: IAgeGroupBase[] = R.clone(
      this.compEvent.options.ageGroups
    );
    ageGroups.push(R.clone(ageGroup));
    this.compEvent.options.ageGroups = ageGroups;
  }

  public onRemoveUpscale(id: number) {
    let ageGroups: IAgeGroupBase[] = R.clone(this.compEvent.options.ageGroups);
    ageGroups = ageGroups.filter((ag) => {
      return ag.id !== id;
    });
    this.compEvent.options.ageGroups = ageGroups;
  }

  public onCancel() {
    this.showSubmit = false;
    this.$emit("onCancel");
  }

  public confirmSubmit() {
    this.showSubmit = true;
  }

  public onSubmit() {
    // this.validationResults = this.compEventService.validate(this.compEvent);
    if (this.validationResults.length > 0) {
      return;
    }
    const compEvent = R.clone(this.compEvent);
    if (!compEvent.options.isTeamEvent) {
      compEvent.options.eventTeam = {} as IEventTeamCe;
    }
    this.$emit("onSubmit", compEvent);
  }
}
</script>
