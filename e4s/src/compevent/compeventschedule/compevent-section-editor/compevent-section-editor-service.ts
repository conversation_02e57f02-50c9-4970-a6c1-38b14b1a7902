import { IEventGroupSummary } from "../../compevent-models";
import {
  CompeventSectionEditorSortProp,
  ICompeventSectionEditorTrackField,
} from "./compevent-section-editor-models";
import * as CommonServiceUtils from "../../../common/common-service-utils";

export function sortDataByProp(
  sortType: CompeventSectionEditorSortProp,
  eventGroupSummaries: IEventGroupSummary[]
): IEventGroupSummary[] {
  const sortKeyMap: Record<
    CompeventSectionEditorSortProp,
    keyof IEventGroupSummary
  > = {
    EVENT_NO: "eventNo",
    TYPE_NO: "typeNo",
    NAME: "name",
    TIME: "eventDateTime",
    BIB_SORT: "bibSortNo",
  };

  const dataToSort = eventGroupSummaries;
  if (sortType === "TIME") {
    //  Put the 00:00 stacked at bottom
    const noTime = eventGroupSummaries.reduce<{
      time: IEventGroupSummary[];
      noTime: IEventGroupSummary[];
    }>(
      (accum, evt) => {
        if (doesEventNeedTimeSetting(evt)) {
          accum.noTime.push(evt);
        } else {
          accum.time.push(evt);
        }
        return accum;
      },
      {
        time: [],
        noTime: [],
      }
    );

    return [
      ...CommonServiceUtils.sortArray(sortKeyMap.TIME, noTime.time),
      ...noTime.noTime,
    ] as IEventGroupSummary[];
  }
  if (sortType === "TYPE_NO") {
    return sortByTrackFieldProp(eventGroupSummaries, "typeNo");
  }
  return CommonServiceUtils.sortArray(sortKeyMap[sortType], dataToSort);
}

export function sortByTrackFieldTimeArray(
  eventGroupSummaries: IEventGroupSummary[]
): IEventGroupSummary[] {
  const trackField = sortByTrackFieldTimeMap(eventGroupSummaries);
  return [...trackField.track, ...trackField.field];
}

export function splitByEventType(
  eventGroupSummaries: IEventGroupSummary[]
): Record<"track" | "field", IEventGroupSummary[]> {
  return eventGroupSummaries.reduce<
    Record<"track" | "field", IEventGroupSummary[]>
  >(
    (accum, evt) => {
      if (evt.type === "T") {
        accum.track.push(evt);
      } else {
        accum.field.push(evt);
      }
      return accum;
    },
    {
      track: [],
      field: [],
    }
  );
}

export function sortByTrackFieldTimeMap(
  eventGroupSummaries: IEventGroupSummary[]
): { track: IEventGroupSummary[]; field: IEventGroupSummary[] } {
  const eventGroupSummariesInternal =
    CommonServiceUtils.simpleClone(eventGroupSummaries);

  const trackField =
    eventGroupSummariesInternal.reduce<ICompeventSectionEditorTrackField>(
      (accum, evt) => {
        if (doesEventNeedTimeSetting(evt)) {
          if (evt.type === "T") {
            accum.trackNoTime.push(evt);
          } else {
            accum.fieldNoTime.push(evt);
          }
        } else {
          if (evt.type === "T") {
            accum.track.push(evt);
          } else {
            accum.field.push(evt);
          }
        }

        return accum;
      },
      {
        track: [],
        trackNoTime: [],
        field: [],
        fieldNoTime: [],
      }
    );

  const trackEventGroupSummaries = CommonServiceUtils.sortArray(
    "eventDateTime",
    trackField.track
  )
    .concat(trackField.trackNoTime)
    .map((eventGroupSummary, index) => {
      return eventGroupSummary;
    });

  const fieldEventGroupSummaries = CommonServiceUtils.sortArray(
    "eventDateTime",
    trackField.field
  )
    .concat(trackField.fieldNoTime)
    .map((eventGroupSummary, index) => {
      return eventGroupSummary;
    });

  return {
    track: trackEventGroupSummaries,
    field: fieldEventGroupSummaries,
  };
}

export function sortByTrackFieldProp(
  eventGroupSummaries: IEventGroupSummary[],
  prop: keyof IEventGroupSummary
): IEventGroupSummary[] {
  const eventGroupSummariesInternal =
    CommonServiceUtils.simpleClone(eventGroupSummaries);

  const trackField = splitByEventType(eventGroupSummariesInternal);

  const trackEventGroupSummaries = CommonServiceUtils.sortArray(
    prop,
    trackField.track
  ).map((eventGroupSummary, index) => {
    return eventGroupSummary;
  });

  const fieldEventGroupSummaries = CommonServiceUtils.sortArray(
    prop,
    trackField.field
  ).map((eventGroupSummary, index) => {
    return eventGroupSummary;
  });

  return [...trackEventGroupSummaries, ...fieldEventGroupSummaries];
}

export function calculateEventNumbers(
  eventGroupSummaries: IEventGroupSummary[]
): IEventGroupSummary[] {
  const trackField = sortByTrackFieldTimeMap(eventGroupSummaries);
  let counter = 0;

  applyCounter(trackField.track);
  applyCounter(trackField.field);

  function applyCounter(evtGroupSummaries: IEventGroupSummary[]) {
    evtGroupSummaries.map((eventGroupSummary, index) => {
      counter = counter + 1;
      eventGroupSummary.eventNo = counter;
      eventGroupSummary.typeNo = index + 1;
      return eventGroupSummary;
    });
  }

  return [...trackField.track, ...trackField.field];
}

export function getEventsNeedingTimeSetting(
  eventGroupSummaries: IEventGroupSummary[]
): IEventGroupSummary[] {
  return eventGroupSummaries.filter((eventGroupSummary) => {
    return doesEventNeedTimeSetting(eventGroupSummary);
  });
}

export function doesEventNeedTimeSetting(
  eventGroupSummary: IEventGroupSummary
): boolean {
  if (eventGroupSummary.eventDateTime === "") {
    return true;
  }
  const timePortion = eventGroupSummary.eventDateTime.split("T")[1].slice(0, 5);
  return timePortion === "00:00";
}

export function clearEventTimes(
  eventGroupSummaries: IEventGroupSummary[]
): IEventGroupSummary[] {
  return eventGroupSummaries.map((eventGroupSummary) => {
    if (eventGroupSummary.eventDateTime.length > 0) {
      const datePortion = eventGroupSummary.eventDateTime.split("T")[0];
      eventGroupSummary.eventDateTime = datePortion + "T00:00:00";
    }
    return eventGroupSummary;
  });
}
