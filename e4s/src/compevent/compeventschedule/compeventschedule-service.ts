import * as R from "ramda";
import { ICompEventSchedGrid } from "./compeventschedule-models";
import {
  ICompEvent,
  IEventGroupLimits,
  IEventGroupSummary,
} from "../compevent-models";
import {
  IBulkEventMove,
  ISortBy,
  sortScheduleByProp,
} from "../bulkevent/bulkevent-models";
import { BulkEventMoveService } from "../bulkevent/bulkeventmove-service";
import {
  EventType,
  IEventSeed,
  ISecurity,
} from "../../athletecompsched/athletecompsched-models";
import { format, parse } from "date-fns";
import {
  IObjectKeyType,
  IsoDate,
  IsoDateTime,
} from "../../common/common-models";
import { CommonService } from "../../common/common-service";
import { IValidationProp } from "../../validation/validation-models";
import { ValidationService } from "../../validation/validation-service";
import { IAgeGroup } from "../../agegroup/agegroup-models";
import { IEntryCount } from "../../builder/buildercompevent/builder-comp-event-models";
import {
  convertArrayToObjectArray,
  simpleClone,
} from "../../common/common-service-utils";
import { messageDispatchHelper } from "../../user-message/user-message-store";

const bulkEventMoveService: BulkEventMoveService = new BulkEventMoveService();
const validationService: ValidationService = new ValidationService();
const commonService: CommonService = new CommonService();

export class CompEventScheduleService {
  public mapCompEventsToSchedule(
    compEvents: ICompEvent[]
  ): ICompEventSchedGrid[] {
    compEvents = simpleClone(compEvents);
    const compEventsSched = compEvents.map((compEvent: ICompEvent) => {
      return this.mapCompEventToSchedule(compEvent);
    });
    return compEventsSched;
  }

  public mapCompEventToSchedule(compEvent: ICompEvent): ICompEventSchedGrid {
    // const maxGroup = String("00000" + compEvent.maxGroup.toString()).slice(-3);
    // maxGroup: (compEvent.maxGroup && compEvent.maxGroup.toString().length === 1 ? "0" : "") + compEvent.maxGroup,

    return {
      id: compEvent.id,
      startDateTime: compEvent.startDateTime,
      sortDateTime: compEvent.sortDateTime,
      gender: compEvent.event.gender,
      eventName: compEvent.event.name,
      eventNo: compEvent.eventNo,
      maxGroup: this.createEventNoFromMaxGroup(compEvent),
      eventGroup: compEvent.eventGroup,
      isEditEnabled: true,
      isSelected: false,
      showMore: false,
      compEvent,
    };
  }

  public createEventNoFromMaxGroup(compEvent: ICompEvent): string {
    if (!compEvent) {
      return "";
    }
    if (!compEvent.maxGroup) {
      return "";
    }
    let maxGroup = compEvent.maxGroup;
    if (typeof (maxGroup as unknown) !== "string") {
      maxGroup = (maxGroup as unknown as number).toString();
    }
    return String("00000" + maxGroup.toString()).slice(-3);
  }

  public getGroupByKeyFunction(
    sortBy: ISortBy
  ): (compEventSchedGrid: ICompEventSchedGrid) => string {
    if (sortBy.value === "startDateTime") {
      return (compEventSchedGrid: ICompEventSchedGrid) => {
        return (
          compEventSchedGrid.startDateTime + "-" + compEventSchedGrid.eventGroup
        );
      };
    }

    return (compEventSchedGrid: ICompEventSchedGrid) => {
      const sortByValueProp = sortBy.value;

      let sortByValue: string = compEventSchedGrid[sortByValueProp].toString();
      if (sortByValueProp === "eventNo") {
        const lengthToPad = 4 - sortByValue.length;
        sortByValue = "0000".slice(0, lengthToPad) + sortByValue;
      }
      return sortByValue + "-" + compEventSchedGrid.eventGroup;
    };
  }

  public createCompEventsSchedGridObject(
    groupByKey: (compEventSchedGrid: ICompEventSchedGrid) => string,
    compEvents: ICompEventSchedGrid[],
    debugString: string
  ): Record<string, ICompEventSchedGrid[]> {
    return convertArrayToObjectArray(groupByKey, compEvents);
  }

  public sortSchedule(
    prop: sortScheduleByProp,
    compEvents: ICompEventSchedGrid[]
  ): ICompEventSchedGrid[] {
    compEvents = [...compEvents];
    compEvents.sort(
      (a: ICompEventSchedGrid, b: ICompEventSchedGrid): number => {
        const genderA = a.gender ? a.gender : "";
        const genderB = b.gender ? b.gender : "";

        const propSort = function () {
          if (prop === "eventNo") {
            return Number(a[prop]) - Number(b[prop]);
          } else {
            return a[prop].toString().localeCompare(b[prop].toString());
          }
        };

        return (
          propSort() ||
          a.startDateTime.localeCompare(b.startDateTime) ||
          genderA.localeCompare(genderB) ||
          a.compEvent.ageGroup.minAge - b.compEvent.ageGroup.minAge
        );

        // return (a[prop]).toString().localeCompare(b[prop].toString()) ||
        //     a.startDateTime.localeCompare(b.startDateTime) ||
        //     genderA.localeCompare(genderB) ||
        //     (a.compEvent.ageGroup.minAge - b.compEvent.ageGroup.minAge);
      }
    );
    return compEvents;
  }

  // public sortScheduleObject(prop: string, compEvents: ICompEventSchedGrid[]): IObjectKeyTypeArray<ICompEventSchedGrid> {
  //     compEvents = this.sortSchedule(prop, compEvents);
  //     return commonService.convertArrayToObjectArray(prop, compEvents);
  // }
  //
  // public filterSchedule(prop: string, startsWith: string, compEventsSched: ICompEventSchedGrid[]): ICompEventSchedGrid[] {
  //     compEventsSched = R.clone(compEventsSched);
  //     startsWith = startsWith.toLowerCase();
  //     compEventsSched = compEventsSched.event-teams-filter( (compEventSched: ICompEventSchedGrid) => {
  //         // @ts-ignore
  //         const propVal: string = compEventSched[ prop ].toString() as any as string;
  //         const propValCheck: string = propVal.slice(0, startsWith.length).toLowerCase();
  //         const isMatch = propValCheck === startsWith;
  //         return isMatch;
  //     });
  //     return compEventsSched;
  // }
  //
  // public filterScheduleAge(startsWith: string, compEventsSched: ICompEventSchedGrid[]): ICompEventSchedGrid[] {
  //     return compEventsSched.event-teams-filter( (compEventSched: ICompEventSchedGrid) => {
  //         return compEventSched.compEvent.ageGroup.keyName.toLowerCase().indexOf(startsWith.toLowerCase()) > -1;
  //     });
  // }

  public deSelectAll(
    compEventsSched: ICompEventSchedGrid[]
  ): ICompEventSchedGrid[] {
    return compEventsSched.map((evt) => {
      evt.isSelected = false;
      evt.isEditEnabled = true;
      return evt;
    });
  }

  public setSelected(
    compEventSched: ICompEventSchedGrid,
    setAsSelected: boolean,
    compEventsSched: ICompEventSchedGrid[],
    applyEditableRules: boolean
  ): ICompEventSchedGrid[] {
    /*
    compEventsSched = compEventsSched
      .map((evt) => {
        //  reset all as editable...
        evt.isEditEnabled = true;
        return evt;
      })
      .map((evt) => {
        if (evt.compEvent.id === compEventSched.compEvent.id) {
          //  set as selected...
          evt.isSelected = setAsSelected;
        }
        return evt;
      });

    if (applyEditableRules) {
      //  now work out what is "editable"
      const eventsSelected = compEventsSched.filter((evt) => {
        return evt.isSelected;
      });
      if (eventsSelected.length === 0) {
        compEventsSched = compEventsSched.map((evt) => {
          //  reset all as editable...
          evt.isEditEnabled = true;
          return evt;
        });
      } else {
        compEventsSched = this.getEditEnabledSchedule(
          compEventSched,
          compEventsSched
        );
      }
    }
    return compEventsSched;
    */

    return this.setSelectedOnlySameEventGroup(
      compEventSched,
      setAsSelected,
      compEventsSched
    );
  }

  public setSelectedOnlySameEventGroup(
    compEventSched: ICompEventSchedGrid,
    setAsSelected: boolean,
    compEventsSched: ICompEventSchedGrid[]
  ): ICompEventSchedGrid[] {
    const otherEventsSelected = compEventsSched.filter((evt) => {
      return evt.isSelected;
    });

    const allEventGroupsSelected =
      this.getAllEventGroupNamesSelected(otherEventsSelected);

    //  If selecting events, then check if they are all in the same event group...
    if (
      setAsSelected &&
      allEventGroupsSelected.length > 0 &&
      allEventGroupsSelected.indexOf(compEventSched.eventGroup) === -1
    ) {
      messageDispatchHelper(
        "Warning, you have selected events in the different event groups: " +
          allEventGroupsSelected
            .concat([compEventSched.eventGroup])
            .join(", ") +
          ". " +
          "If this is not what you intended, please deselect the events and try again."
      );
    }

    const compEvents = simpleClone(compEventsSched).map((evt) => {
      if (evt.compEvent.id === compEventSched.compEvent.id) {
        //  set currently "selected" as selected...
        evt.isSelected = setAsSelected;
      }
      return evt;
    });

    // if (applyEditableRules) {
    //   //  now work out what is "editable"
    //   const eventsSelected = compEventsSched.filter((evt) => {
    //     return evt.isSelected;
    //   });
    //   if (eventsSelected.length === 0) {
    //     compEventsSched = compEventsSched.map((evt) => {
    //       //  reset all as editable...
    //       evt.isEditEnabled = true;
    //       return evt;
    //     });
    //   } else {
    //     compEventsSched = this.getEditEnabledSchedule(
    //       compEventSched,
    //       compEventsSched
    //     );
    //   }
    // }
    return compEvents;
  }

  public setAllSelected(
    compEventsSched: ICompEventSchedGrid[],
    ids: number[],
    setSelected: boolean
  ) {
    const idsObj = ids.reduce((accum, id: number) => {
      accum[id] = id;
      return accum;
    }, {} as IObjectKeyType<number>);

    compEventsSched = [...compEventsSched];
    compEventsSched.map((evt) => {
      if (idsObj[evt.compEvent.id]) {
        evt.isEditEnabled = true;
        evt.isSelected = setSelected;
      }
      // evt.isEditEnabled = idsObj[evt.compEvent.id] ? true : false;
      return evt;
    });
    return compEventsSched;
  }

  public getAllEventGroupNames(
    compEventsSched: ICompEventSchedGrid[]
  ): string[] {
    return Object.keys(
      compEventsSched.reduce<Record<string, any>>((accum, evt) => {
        accum[evt.eventGroup] = "";
        return accum;
      }, {})
    );
  }

  public getAllEventGroupNamesSelected(
    compEventsSched: ICompEventSchedGrid[]
  ): string[] {
    return Object.keys(
      compEventsSched.reduce<Record<string, any>>((accum, evt) => {
        if (evt.isSelected) {
          accum[evt.eventGroup] = "";
        }
        return accum;
      }, {})
    );
  }

  public getAllByIds(
    compEventsSched: ICompEventSchedGrid[],
    ids: number[]
  ): ICompEventSchedGrid[] {
    const idsObj = ids.reduce((accum, id: number) => {
      accum[id] = id;
      return accum;
    }, {} as IObjectKeyType<number>);

    console.log("idsObj=======", idsObj);
    // const compEventsSchedInt = [...compEventsSched];
    const compEventsSchedInt: ICompEventSchedGrid[] = compEventsSched.filter(
      (evt) => {
        const isInObj = idsObj[evt.compEvent.id];
        if (isInObj) {
          console.log("isInObj id: " + evt.compEvent.id, isInObj);
          return true;
        }
        return false;
        // console.log("isInObj id: " + evt.compEvent.id, isInObj);
        // const res = idsObj[evt.compEvent.id] !== undefined ? true : false;
        // console.log("res id: " + evt.compEvent.id, isInObj);
        // return res;
      }
    );
    return compEventsSchedInt;
  }

  public getEditEnabledSchedule(
    compEventSched: ICompEventSchedGrid,
    compEventsSched: ICompEventSchedGrid[]
  ): ICompEventSchedGrid[] {
    // compEventsSched = R.clone(compEventsSched);
    compEventsSched = [...compEventsSched];
    compEventsSched.map((evt) => {
      evt.isEditEnabled = this.isEventSimilar(compEventSched, evt);
      return evt;
    });
    return compEventsSched;
  }

  public isEventSimilar(
    compEventSched: ICompEventSchedGrid,
    compEventSchedOther: ICompEventSchedGrid
  ): boolean {
    return (
      compEventSched.compEvent.event.name ===
      compEventSchedOther.compEvent.event.name
    );
  }

  public areAllEventsSimilar(compEventsSched: ICompEventSchedGrid[]): boolean {
    if (compEventsSched.length === 0) {
      return true;
    }
    const key: string = compEventsSched[0].eventGroup;
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < compEventsSched.length; i++) {
      // console.log(i + ": " + key + " = " + compEventsSched[i].eventGroup);

      if (compEventsSched[i].eventGroup !== key) {
        return false;
      }
    }
    return true;
  }

  public getSelected(
    compEventsSched: ICompEventSchedGrid[]
  ): ICompEventSchedGrid[] {
    // compEventsSched = R.clone(compEventsSched);
    compEventsSched = [...compEventsSched];
    return compEventsSched.filter((evt) => {
      return evt.isSelected;
    });
  }

  public createBulkEventMove(
    compEventsSched: ICompEventSchedGrid[],
    action: string,
    eventNo: number
  ): IBulkEventMove {
    const bulkEventMove = bulkEventMoveService.factory();
    const ceids: number[] = compEventsSched.map((evt) => {
      return evt.compEvent.id;
    });
    bulkEventMove.ceids = ceids;
    bulkEventMove.action = action;
    //  TODO  remove Number()
    bulkEventMove.eventNo = Number(eventNo);
    return bulkEventMove;
  }

  public areOptionsTheSame(compEventsSched: ICompEventSchedGrid[]): {
    isSame: boolean;
    compSched: ICompEventSchedGrid;
    diffs: any;
  } {
    const initState = {
      isSame: true,
      compSched: {} as ICompEventSchedGrid,
      diffs: {},
    };
    const result = compEventsSched.reduce((accum, compSched) => {
      if (!accum.compSched.id) {
        accum.compSched = R.clone(compSched);
      }
      const isSame = R.equals(
        accum.compSched.compEvent.options,
        compSched.compEvent.options
      );
      if (!isSame) {
        if (accum.compSched.compEvent) {
          const diffs = new CommonService().differenceBetweenTwoObjects(
            accum.compSched.compEvent.options,
            compSched.compEvent.options
          );
          accum.diffs = diffs;
          console.log("diff", diffs);
        }
        accum.isSame = false;
      }
      return accum;
    }, initState);
    return result;
  }

  public getAddToEventNumbers(
    compEventsSelected: ICompEvent[],
    compEvents: ICompEvent[]
  ): string[] {
    if (compEventsSelected.length === 0) {
      return [];
    }
    const compEventSelected = compEventsSelected[0];
    const compEventSelectedEventNo =
      this.createEventNoFromMaxGroup(compEventSelected);
    return compEvents
      .reduce((accum: string[], compEvent: ICompEvent) => {
        if (this.isMoveToMatch(compEventSelected, compEvent)) {
          const eventNo = this.createEventNoFromMaxGroup(compEvent);
          if (
            compEventSelectedEventNo !== eventNo &&
            accum.indexOf(eventNo) === -1
          ) {
            accum.push(eventNo);
          }
        }
        return accum;
      }, [])
      .sort((a: string, b: string) => {
        return a.toString().localeCompare(b);
      });
  }

  public isMoveToMatch(
    compEvent: ICompEvent,
    compEventToMatch: ICompEvent
  ): boolean {
    // Doesn't matter about Gender..
    return compEvent.event.name === compEventToMatch.event.name;
  }

  public getInsertEventNumbers(
    compEventsSelected: ICompEvent[],
    compEvents: ICompEvent[]
  ): string[] {
    const compEventSelected = compEventsSelected[0];
    const compEventSelectedEventNo =
      this.createEventNoFromMaxGroup(compEventSelected);
    return compEvents
      .reduce((accum: string[], compEvent: ICompEvent) => {
        const eventNo = this.createEventNoFromMaxGroup(compEvent);
        if (
          compEventSelectedEventNo !== eventNo &&
          accum.indexOf(eventNo) === -1
        ) {
          accum.push(eventNo);
        }
        return accum;
      }, [])
      .sort((a: string, b: string) => {
        return a.toString().localeCompare(b);
      });
  }

  public hasSecurity(compEvent: ICompEvent): boolean {
    const security: ISecurity | null =
      compEvent.options &&
      compEvent.options.security &&
      compEvent.options.security
        ? compEvent.options.security
        : null;
    if (security) {
      return security.clubs && security.clubs.length > 0
        ? true
        : false || (security.counties && security.counties.length > 0)
        ? true
        : false || (security.regions && security.regions.length > 0)
        ? true
        : false;
    }
    return false;
  }

  public getSectionNameForTime(
    compEventSchedGrid: ICompEventSchedGrid,
    prop: string
  ): string {
    // @ts-ignore
    const datePropValue = compEventSchedGrid[prop];
    const dateFns = parse(datePropValue);
    const startTime = format(dateFns, "HH:mm");
    if (startTime === "00:00") {
      return "TBC";
    }
    return format(dateFns, "ddd Do MMM HH:mm");
  }

  public hasRestrictions(compEvent: ICompEvent): boolean {
    if (compEvent.maxAthletes && compEvent.maxAthletes > 0) {
      return true;
    }
    if (compEvent.options.maxInHeat && compEvent.options.maxInHeat > 0) {
      return true;
    }
    if (
      compEvent.options.heatInfo &&
      compEvent.options.heatInfo.useLanes &&
      compEvent.options.heatInfo.useLanes !== "A"
    ) {
      return true;
    }
    if (compEvent.split && compEvent.split !== 0) {
      return true;
    }
    if (compEvent.options.mandatoryPB) {
      return true;
    }
    return false;
  }

  public validateEventGroupSummary(
    eventGroupSummary: IEventGroupSummary
  ): Record<string, IValidationProp> {
    let validationState: Record<string, IValidationProp> = {};

    if (eventGroupSummary.name.replace(/\s/g, "").length === 0) {
      validationState = validationService.addMessage(
        "name",
        "Required.",
        validationState
      );
    }

    if (eventGroupSummary.eventNo.toString().replace(/\s/g, "").length === 0) {
      validationState = validationService.addMessage(
        "eventNo",
        "Required.",
        validationState
      );
    } else {
      if (eventGroupSummary.eventNo <= 0) {
        validationState = validationService.addMessage(
          "eventNo",
          "1 or above",
          validationState
        );
      }
    }

    if (eventGroupSummary.typeNo.toString().replace(/\s/g, "").length === 0) {
      validationState = validationService.addMessage(
        "typeNo",
        "Required.",
        validationState
      );
    } else {
      if (eventGroupSummary.typeNo <= 0) {
        validationState = validationService.addMessage(
          "typeNo",
          "1 or above",
          validationState
        );
      }
    }

    // const bibSortNo = (!eventGroupSummary.bibSortNo ? "" : eventGroupSummary.bibSortNo);
    const bibSortNo =
      eventGroupSummary.bibSortNo === undefined
        ? ""
        : eventGroupSummary.bibSortNo;
    if (bibSortNo.toString().replace(/\s/g, "").length > 0) {
      if (bibSortNo < 1) {
        validationState = validationService.addMessage(
          "bibSortNo",
          "1 or above",
          validationState
        );
      }
    }

    return validationState;
  }

  public validateEventGroupLimits(
    eventGroupLimits: IEventGroupLimits
  ): Record<string, IValidationProp> {
    let validationState: Record<string, IValidationProp> = {};
    if (
      !validationService.isNumberEqualOrAbove(eventGroupLimits.maxAthletes, -1)
    ) {
      validationState = validationService.addMessage(
        "maxAthletes",
        "0 or above.",
        validationState
      );
    }
    if (
      !validationService.isNumberEqualOrAbove(eventGroupLimits.maxInHeat, 0)
    ) {
      validationState = validationService.addMessage(
        "maxInHeat",
        "0 or above.",
        validationState
      );
    }
    return validationState;
  }

  public validateEventGroupSeed(
    eventSeed: IEventSeed
  ): Record<string, IValidationProp> {
    let validationState: Record<string, IValidationProp> = {};
    if (!validationService.isNumberEqualOrAbove(eventSeed.firstLane, 1)) {
      validationState = validationService.addMessage(
        "firstLane",
        "0 or above.",
        validationState
      );
    }
    if (!validationService.isNumberEqualOrAbove(eventSeed.laneCount, 1)) {
      validationState = validationService.addMessage(
        "laneCount",
        "0 or above.",
        validationState
      );
    }
    return validationState;
  }

  public eventGroupChangeWarning(
    eventGroupSummary: IEventGroupSummary,
    allCompEvents: ICompEvent[]
  ): Record<string, IValidationProp> {
    let validationState: Record<string, IValidationProp> = {};

    const mapEventGroupsAndTypeGroups =
      this.mapEventGroupsAndTypeGroups(allCompEvents);
    const eventNosMap =
      mapEventGroupsAndTypeGroups.eventNo[eventGroupSummary.eventNo.toString()];
    let eventNos: string[] = [];
    if (eventNosMap) {
      eventNos = Object.keys(eventNosMap).filter((key) => {
        return key !== eventGroupSummary.name;
      });
    }
    const typeNoKeysMap =
      mapEventGroupsAndTypeGroups.typeNoKey[
        eventGroupSummary.type + eventGroupSummary.typeNo.toString()
      ];
    let typeNoKeys: string[] = [];
    if (typeNoKeysMap) {
      typeNoKeys = Object.keys(typeNoKeysMap).filter((key) => {
        return key !== eventGroupSummary.name;
      });
    }

    if (eventNos.length > 0) {
      validationState = validationService.addMessage(
        "eventNoDups",
        eventNos.join(", "),
        validationState
      );
    }

    if (typeNoKeys.length > 0) {
      validationState = validationService.addMessage(
        "typeNoKeyDups",
        typeNoKeys.join(", "),
        validationState
      );
    }
    return validationState;
  }

  public mapEventGroupsAndTypeGroups(allCompEvents: ICompEvent[]): {
    eventNo: Record<string, Record<string, null>>;
    typeNoKey: Record<string, Record<string, null>>;
  } {
    return allCompEvents.reduce<{
      eventNo: Record<string, Record<string, null>>;
      typeNoKey: Record<string, Record<string, null>>;
    }>(
      (accum, compEvent) => {
        const eventNo = compEvent.eventGroupSummary.eventNo.toString();
        if (!accum.eventNo[eventNo]) {
          accum.eventNo[eventNo] = {};
        }
        if (!accum.eventNo[eventNo][compEvent.eventGroupSummary.name]) {
          accum.eventNo[eventNo][compEvent.eventGroupSummary.name] = null;
        }

        const typeNoKey =
          compEvent.eventGroupSummary.type +
          compEvent.eventGroupSummary.typeNo.toString();
        if (!accum.typeNoKey[typeNoKey]) {
          accum.typeNoKey[typeNoKey] = {};
        }
        if (!accum.typeNoKey[typeNoKey][compEvent.eventGroupSummary.name]) {
          accum.typeNoKey[typeNoKey][compEvent.eventGroupSummary.name] = null;
        }

        return accum;
      },
      {
        eventNo: {},
        typeNoKey: {},
      }
    );
  }

  public isEventTrackish(compEventSched: ICompEventSchedGrid): boolean {
    return compEventSched.compEvent.eventGroupSummary.type === "T";
  }

  public isEventFieldish(compEventSched: ICompEventSchedGrid): boolean {
    return compEventSched.compEvent.eventGroupSummary.type === "F";
  }

  public isEventTeamOnly(compEventSched: ICompEventSchedGrid): boolean {
    return compEventSched.compEvent.options.isTeamEvent;
  }

  public hasAnyTeamEvents(compEvents: ICompEvent[]): boolean {
    return compEvents.reduce<boolean>((accum, compEvent) => {
      if (compEvent.options.isTeamEvent) {
        accum = true;
      }
      return accum;
    }, false);
  }

  public getAllEventTypes(compEvents: ICompEvent[]): EventType[] {
    return commonService
      .pluckUnique((compEvent) => {
        return compEvent.eventGroupSummary.type;
      }, compEvents)
      .filter((eventType) => {
        return eventType.length > 0;
      });
  }

  public getAllAgeGroups(compEvents: ICompEvent[]): string[] {
    return commonService.pluckUnique((compEvent) => {
      return compEvent.ageGroup.keyName;
    }, compEvents);
  }

  public getAllAgeGroupObjects(compEvents: ICompEvent[]): IAgeGroup[] {
    return commonService.pluckUnique((compEvent) => {
      return compEvent.ageGroup;
    }, compEvents);
  }

  public doesEventContainThisAgeGroupSearchTerm(
    compEventSched: ICompEventSchedGrid,
    searchTerm: string
  ): any {
    // if (searchTerm.length === 0) {
    //     return true;
    // }
    //
    // const hasKeyName = (ageGroup: IAgeGroup) => {
    //     return ageGroup.keyName
    //       .toLowerCase()
    //       .indexOf(searchTerm.toLowerCase()) > -1;
    // }
    // const isKeyName = hasKeyName(compEventSched.compEvent.ageGroup);
    //
    // const hasName = (ageGroup: IAgeGroup) => {
    //     return ageGroup.name
    //       .toLowerCase()
    //       .indexOf(searchTerm.toLowerCase()) > -1;
    // }
    // const isName = hasName(compEventSched.compEvent.ageGroup);
    //
    // const hasAgeGroupId = (ageGroup: IAgeGroup) => {
    //     return ageGroup.id.toString()
    //       .toLowerCase()
    //       .indexOf(searchTerm.toLowerCase()) > -1;
    // }
    // const isAgeGroupId = hasAgeGroupId(compEventSched.compEvent.ageGroup);

    const ageGroupMatch = this.doesAgeGroupMatchSearchTerm(
      compEventSched.compEvent.ageGroup,
      searchTerm
    );
    const isInUpscaling = this.doesUpscalingMatchAgeGroupSearchTerm(
      compEventSched,
      searchTerm
    );

    return ageGroupMatch || isInUpscaling;
  }

  public doesAgeGroupMatchSearchTerm(
    ageGroup: IAgeGroup,
    searchTerm: string
  ): boolean {
    if (searchTerm.length === 0) {
      return true;
    }

    const hasKeyName = (agGroup: IAgeGroup) => {
      return (
        agGroup.keyName.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1
      );
    };
    const isKeyName = hasKeyName(ageGroup);

    const hasName = (agGroup: IAgeGroup) => {
      return agGroup.name.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1;
    };
    const isName = hasName(ageGroup);

    const hasAgeGroupId = (agGroup: IAgeGroup) => {
      return (
        ageGroup.id.toString().toLowerCase().indexOf(searchTerm.toLowerCase()) >
        -1
      );
    };
    const isAgeGroupId = hasAgeGroupId(ageGroup);
    return isName || isKeyName || isAgeGroupId;
  }

  public doesUpscalingMatchAgeGroupSearchTerm(
    compEventSched: ICompEventSchedGrid,
    searchTerm: string
  ): boolean {
    const isInUpscaling =
      compEventSched.compEvent.options.ageGroups.reduce<boolean>(
        (accum, ageGroupUpscaling) => {
          if (
            this.doesAgeGroupMatchSearchTerm(
              ageGroupUpscaling as IAgeGroup,
              searchTerm
            )
          ) {
            accum = true;
          }

          return accum;
        },
        false
      );
    return isInUpscaling;
  }

  public getAllDateTimesForComp(compEvents: ICompEvent[]): IsoDateTime[] {
    return Object.keys(
      compEvents.reduce<Record<IsoDateTime, unknown>>((accum, compEvent) => {
        accum[compEvent.startDateTime] = "";
        return accum;
      }, {})
    );
  }

  public getAllDatesForComp(compEvents: ICompEvent[]): IsoDate[] {
    return Object.keys(
      this.getAllDateTimesForComp(compEvents).reduce<
        Record<IsoDateTime, unknown>
      >((accum, isoDateTime) => {
        accum[isoDateTime.split("T")[0]] = "";
        return accum;
      }, {})
    );
  }

  public getTotalsForCompEvents(
    compEventSchedGrids: ICompEventSchedGrid[]
  ): IEntryCount {
    return compEventSchedGrids.reduce(
      (accum, compEventSchedGrid) => {
        const compEvent = compEventSchedGrid.compEvent;
        if (compEvent.entryCount) {
          accum.total += compEvent.entryCount.total
            ? compEvent.entryCount.total
            : 0;
          accum.waiting += compEvent.entryCount.waiting
            ? compEvent.entryCount.waiting
            : 0;
        }
        return accum;
      },
      {
        total: 0,
        waiting: 0,
      }
    );
  }

  public getTotalAthleteEntries(compEvents: ICompEvent[]): IEntryCount {
    return compEvents.reduce(
      (accum, compEvent) => {
        if (compEvent.entryCount) {
          accum.total += compEvent.entryCount.total
            ? compEvent.entryCount.total
            : 0;
          accum.waiting += compEvent.entryCount.waiting
            ? compEvent.entryCount.waiting
            : 0;
        }
        return accum;
      },
      {
        total: 0,
        waiting: 0,
      }
    );
  }

  public getEntryCountDisplayText(
    entryCount: IEntryCount,
    max: number
  ): string {
    if (max > 0) {
      /*
      if (entryCount.total > max) {
        if (entryCount.waiting === 0) {
          //  This isn't quite right, the total os over the max and no waiting list...
          //  ...so something has gone wrong.
          return "! " + entryCount.total.toString() + " / " + max.toString();
        }
        return max.toString() + " +" + (entryCount.total - max).toString();
      }
      */
      return entryCount.total.toString() + " / " + max.toString();
    }
    return entryCount.total > 0 ? entryCount.total.toString() : "";
  }

  public isChildEvent(compEventSchedGrid: ICompEventSchedGrid): boolean {
    return (
      compEventSchedGrid.compEvent.options.entriesFrom &&
      compEventSchedGrid.compEvent.options.entriesFrom.id > 0
    );
  }

  public getParentEventName(compEventSchedGrid: ICompEventSchedGrid): string {
    return compEventSchedGrid.compEvent.options.entriesFrom
      ? compEventSchedGrid.compEvent.options.entriesFrom.name
      : "";
  }

  public getParentEvents(
    compEventSchedGrid: ICompEventSchedGrid,
    allEvents: ICompEventSchedGrid[]
  ): ICompEventSchedGrid[] {
    const entriesFromId = compEventSchedGrid.compEvent.options.entriesFrom.id;
    return allEvents.filter((compEventSchedGridOther) => {
      return (
        compEventSchedGridOther.compEvent.eventGroupSummary.id === entriesFromId
      );
    });
  }
}
