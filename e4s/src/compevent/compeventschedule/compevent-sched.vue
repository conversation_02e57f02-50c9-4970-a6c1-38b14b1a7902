<template>
  <div v-if="getIsCompLoaded" class="e4s-flex-column e4s-gap--large">
    <LoadingSpinnerV2 v-if="builderControllerFactory.state.isLoading" />
    <div
      class="e4s-flex-row e4s-justify-flex-space-between"
      v-if="getCanShowHeaderButtons && !showAdvancedFilters"
    >
      <!--      <div class="e4s-flex-row e4s-gap&#45;&#45;standard">-->
      <FormGenericInputTemplateV2 form-label="" style="margin: 0">
        <InputWithButton
          class="e4s-full-width-x"
          slot="field"
          v-show="areAnyEvents"
        >
          <ButtonGenericV2
            class="e4s-button--auto"
            with-input="left"
            button-type="primary"
            :text="
              'Filters' +
              (getFilterCount > 0 ? ' (' + getFilterCount + ')' : '')
            "
            @click="showAdvancedFilters = true"
            slot="before"
          >
            <template slot="button-content">
              <div
                class="
                  e4s-flex-row
                  e4s-gap--tiny
                  e4s-justify-flex-row-vert-center
                "
              >
                <span
                  style="color: var(--e4s-button--primary__text-color)"
                  v-text="
                    'Filters' +
                    (getFilterCount > 0 ? ' (' + getFilterCount + ')' : '')
                  "
                ></span>
                <!--                      <TriangleDown />-->
                <PlayMinor :arrow-direction="'down'" fill="white" />
              </div>
            </template>
          </ButtonGenericV2>

          <FieldTextV2
            slot="field"
            class="e4s-square--left e4s-square--right e4s-flex-grow"
            :value="filterEventName"
            place-holder="Filter event"
            @keyUpEnter="quickSearchFilter"
            @keyUp="quickSearchFilter"
          />

          <button-generic-v2
            class="e4s-button--auto"
            with-input="right"
            @click="processFilters"
            slot="after"
          />
        </InputWithButton>
      </FormGenericInputTemplateV2>
    </div>

    <!--Filters-->
    <div
      class="e4s-flex-column e4s-card e4s-card--generic"
      style="border-width: 2px"
      v-if="showAdvancedFilters"
    >
      <div class="e4s-flex-column e4s-gap--standard">
        <div class="e4s-flex-row">
          <div class="e4s-header--400">Filters</div>
        </div>

        <hr class="dat-e4s-hr-only dat-e4s-hr--slightly-lighter" />

        <FormGenericFieldGridV2>
          <template slot="content">
            <FormGenericInputTemplateV2 form-label="Event">
              <input
                slot="field"
                class="e4s-input"
                v-model="filterEventName"
                v-on:keyup="processFilters"
                :placeholder="'Quick Filter'"
              />
            </FormGenericInputTemplateV2>

            <FormGenericInputTemplateV2 form-label="Type">
              <div
                slot="field"
                class="e4s-flex-column e4s-gap--standard e4s-flex-wrap"
              >
                <FieldRadioV2
                  option-value="ALL"
                  v-model="filterEventType"
                  label="ALL"
                  @onChanged="processFilters"
                />

                <FieldRadioV2
                  v-for="eventType in getAllEventTypes"
                  :key="eventType"
                  :option-value="eventType"
                  v-model="filterEventType"
                  :label="
                    EventTypeDictionary[eventType] + ' (' + eventType + ')'
                  "
                  @onChanged="processFilters"
                />

                <FieldRadioV2
                  v-if="getHasTeamEvents"
                  option-value="TEAM_ONLY"
                  v-model="filterEventType"
                  label="Team"
                  @onChanged="processFilters"
                />
              </div>
            </FormGenericInputTemplateV2>

            <FormGenericInputTemplateV2 form-label="Gender">
              <div
                slot="field"
                class="e4s-flex-column e4s-gap--standard e4s-flex-wrap"
              >
                <FieldRadioV2
                  option-value="ALL"
                  v-model="filterGender"
                  label="ALL"
                  @onChanged="processFilters"
                />

                <FieldRadioV2
                  :option-value="gender.FEMALE"
                  v-model="filterGender"
                  label="Female"
                  @onChanged="processFilters"
                />

                <FieldRadioV2
                  :option-value="gender.MALE"
                  v-model="filterGender"
                  label="Male"
                  @onChanged="processFilters"
                />
              </div>
            </FormGenericInputTemplateV2>

            <FormGenericInputTemplateV2 form-label="Age Group">
              <a
                slot="after-label"
                href="#"
                v-on:click.prevent="toggleAgeGroupFilter"
              >
                Show
                <span
                  v-text="
                    showAgeGroupFilter === 'FREE_TEXT'
                      ? 'Drop down'
                      : 'Free text'
                  "
                ></span>
              </a>

              <div slot="field" class="e4s-flex-row e4s-gap--standard">
                <input
                  v-if="showAgeGroupFilter === 'FREE_TEXT'"
                  class="e4s-input"
                  v-model="filterAge"
                  v-on:keyup="processFilters"
                  :placeholder="'Filter age group'"
                />

                <select
                  v-if="showAgeGroupFilter === 'SELECT'"
                  class="browser-default"
                  v-model="filterAgeGroupObject"
                  v-on:change="processFilters"
                >
                  <option
                    v-for="ageGroup in filterAgeGroupObjects"
                    :key="ageGroup.id"
                    :value="ageGroup"
                    v-text="ageGroup.keyName"
                  ></option>
                </select>
              </div>
            </FormGenericInputTemplateV2>

            <FormGenericInputTemplateV2
              form-label="Date"
              v-if="getIsMultiDateComp"
            >
              <div slot="field" class="e4s-flex-row e4s-gap--standard">
                <select
                  class="browser-default"
                  v-model="filterDate"
                  v-on:change="processFilters"
                >
                  <option value="ALL">ALL</option>
                  <option
                    v-for="date in getScheduleDates"
                    :key="date"
                    :value="date"
                    v-text="date"
                  ></option>
                </select>
              </div>
            </FormGenericInputTemplateV2>
          </template>
        </FormGenericFieldGridV2>
      </div>
      <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
        <ButtonGenericV2
          button-type="tertiary"
          class="e4s-button--auto"
          text="Close Filters"
          @click="closeAdvancedFilters"
        />

        <ButtonGenericV2
          :disabled="!getIsFilterOn"
          button-type="secondary"
          class="e4s-button--100"
          :text="'Reset (' + getFilterCount + ')'"
          @click="resetFilters"
        />
      </div>
    </div>
    <!--    </CollapseSection>-->
    <!--/Filters-->

    <div class="e4s-flex-row">
      <ButtonGenericV2
        v-if="areAnyEvents"
        button-type="secondary"
        :text="(showAgeGroupCoverage ? 'Hide' : 'Show') + ' Age Group Coverage'"
        class="e4s-button--auto"
        @click="showAgeGroupCoverage = !showAgeGroupCoverage"
      />

      <div class="e4s-flex-column e4s-gap--standard e4s-flex-row--end">
        <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
          <ButtonGenericV2
            text="Add Event"
            @click="addEvent"
            v-if="getCanShowAddButton"
            :disabled="isFieldDisabled"
          />

          <ButtonGenericV2
            v-if="areAnyEvents"
            button-type="secondary"
            class="e4s-button--auto"
            :text="
              (showAllGroupEditors ? 'Hide' : 'Show') + ' Group Editor Grid'
            "
            @click="toggleGroupEditor"
          />
        </div>
        <div v-if="isFieldDisabled">
          <div class="e4s-info-text--error">
            Competition Cloned from
            {{ builderCompetition.options.cloneInfo.fromId.toString() }}. Enable
            edit on 'General' tab
          </div>
        </div>
      </div>
    </div>

    <!--    v-if="!showAllGroupEditors"-->
    <div class="row">
      <div class="col s12 m12 l12">
        <div
          class="e4s-flex-column e4s-card e4s-card--generic"
          v-if="showAgeGroupCoverage"
        >
          <FormGenericSectionHeader
            value="Displays age group coverage for events displayed below."
          />
          <CompeventAgeGroupCoverage
            slot="section-content"
            :age-group-comp-coverage-models="ageGroupCompCoverageModels"
            :ao-code="configApp.defaultao.code"
            :comp-date="builderCompetitionInternal.date"
            style="overflow-x: auto"
            v-on:ageGroupClicked="ageGroupCoverageClicked"
          />
        </div>
      </div>
    </div>

    <div
      v-if="
        builderCompetition && builderCompetition.id > 0 && !showAllGroupEditors
      "
    >
      <div class="row">
        <div class="col s12 m12 l12">
          <div class="e4s-flex-row">
            <span
              class="e4s-header--400"
              v-text="'Total Athlete Entries: ' + getTotalAthleteEntries.total"
            ></span>

            <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
              <button-generic-v2
                v-if="
                  builderCompetition.options.dates.length > 1 &&
                  !showTimeOptions
                "
                text="Set Time Options"
                @click="showTimeOptions = true"
                class="e4s-button--auto"
              />
            </div>
          </div>
        </div>
      </div>

      <ModalV2
        :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
        :always-show-header-blank="true"
        v-if="showTimeOptions"
      >
        <ScheduleTimeOptions
          slot="body"
          style="margin: 8px"
          :comp-id="builderCompetition.id"
          :dates="getScheduleDates"
          :comp-events="compEvents"
          @cancel="showTimeOptions = false"
          @save="onScheduleTimeOptionsSaved"
        />
      </ModalV2>

      <div class="row" v-for="(value, name) in compEventsSchedGridObject">
        <div class="col s12 m12 l12">
          <CompEventSchedSection
            :builder-competition="builderCompetition"
            :section-name="name"
            :sort-by="sortBy"
            :comp-events-prop="value"
            :all-comp-events="compEvents"
            :is-loading="getIsThisLoading"
            :event-numbers="addMoveEventNumbers"
            :current-section-action="currentSectionAction"
            :can-show-action-button="getCanShowActionButton"
            :can-show-check-boxes="getDisplayCheckBox"
            :event-group-being-edited="getEventGroupBeingEdited"
            :is-multi-date-comp="getIsMultiDateComp"
            v-on:onShowMore="onShowMore"
            v-on:showMoreAllSection="showMoreAllSection"
            v-on:onSelect="onSelect"
            v-on:onEditMe="onEditMe"
            v-on:onSelectAction="onSelectAction"
            v-on:runAction="runAction"
          />
        </div>
      </div>
    </div>

    <div v-if="showAllGroupEditors">
      <div class="e4s-section-padding-separator"></div>
      <CompeventSectionEditorV2
        :comp-events="compEvents"
        :is-filter-on="getIsFilterOn"
        :comp-events-sched-grid-object="compEventsSchedGridObject"
        v-on:reload="reload"
        v-on:isDirty="groupEditorIsDirty"
      />
    </div>

    <e4s-modal
      v-if="showBulkEditConf"
      header-message="Bulk Event Edit"
      body-message="The options on these events are different, continue?"
      button-primary-text="Continue"
      :isLoading="false"
      v-on:closeSecondary="showBulkEditConf = false"
      v-on:closePrimary="bulkEditConfConfirm"
    />

    <e4s-modal
      v-if="showGroupEditorDirtyConf"
      header-message="Group Editor"
      body-message="Any unsaved changes in the Group Editor will be lost.  Continue?"
      button-primary-text="Continue"
      :isLoading="false"
      v-on:closeSecondary="showGroupEditorDirtyConf = false"
      v-on:closePrimary="groupEditorDirtyConfConfirm"
    />
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { ICompEvent, IRenameGroup } from "../compevent-models";
import { Prop, Watch } from "vue-property-decorator";
import { CommonService } from "../../common/common-service";
import { CompEventService } from "../compevent-service";
import { GENDER, IsoDate } from "../../common/common-models";
import {
  ICompEventSchedGrid,
  IGroupEditorOutput,
  ISectionAction,
} from "./compeventschedule-models";
import { CompEventScheduleService } from "./compeventschedule-service";
import {
  BULK_EVENT_MOVE_ACTION,
  BULK_ACTIONS,
  SORT_BY,
  ISortBy,
  ScheduleSortByType,
} from "../bulkevent/bulkevent-models";
import { BulkEventMoveData } from "../bulkevent/bulkeventmove-data";
import { BUILDER_STORE_CONST } from "../../builder/builder-store-constants";
import {
  IBuilderCompEvent,
  IAgeCeidLink,
  IEntryCount,
} from "../../builder/buildercompevent/builder-comp-event-models";
import {
  IAgeGroup,
  IAgeGroupCompCoverageModel,
} from "../../agegroup/agegroup-models";
import E4sModal from "../../common/ui/e4s-modal.vue";
import CompEventSchedSection from "./compevent-section.vue";
import GenderSelect from "../../common/ui/gender-select.vue";
import EventGroupMove from "../../builder/form/event-group/event-group-move/event-group-move.vue";
import { CompEventData } from "../compevent-data";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import { mapGetters, mapState } from "vuex";
import { IBuilderStoreState } from "../../builder/builder-store";
import { IBuilderCompetition } from "../../builder/builder-models";
import CollapseSection from "../../common/ui/collapse/collapse-section.vue";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { IConfigApp, IUserInfo } from "../../config/config-app-models";
import { ConfigService } from "../../config/config-service";
import CompeventAgeGroupCoverage from "./compevent-age-group-coverage/compevent-age-group-coverage.vue";
import FieldHelp from "../../common/ui/field/field-help/field-help.vue";
import {
  EventTypeNameGeneric,
  EventTypeDictionary,
  EventType,
} from "../../athleteCompSched/athletecompsched-models";
import { format, parse } from "date-fns";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import FieldTextV2 from "../../common/ui/layoutV2/fields/field-text-v2.vue";
import FormGenericInputTextV2 from "../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import CompeventSectionEditorV2 from "./compevent-section-editor/v2/CompeventSectionEditorV2.vue";
import { hasTeamEvents } from "../../entry/v2/schools/clubCompInfoService";
import FormGenericFieldGridV2 from "../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import {
  isNullOrUndefined,
  simpleClone,
} from "../../common/common-service-utils";
import InputCheckboxV2 from "../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import { useBuilderControllerFactory } from "../../builder/builder-controller-factory";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";
import { BuilderService } from "../../builder/builder-service";
import { debounce } from "../../common/debounce";
import FieldRadioV2 from "../../common/ui/layoutV2/fields/field-radio-v2.vue";
import InputWithButton from "../../common/ui/layoutV2/fields/InputWithButton.vue";
import PlayMinor from "../../common/ui/svg/PlayMinor.vue";
import FormGenericSectionHeader from "../../common/ui/layoutV2/form/form-generic-section-header.vue";
import ScheduleTimeOptions from "../../builder/form/time-options/ui/ScheduleTimeOptions.vue";
import ModalV2 from "../../common/ui/layoutV2/modal/modal-v2.vue";
import { VUE_MQ_SIZES } from "../../index";

// const BulkUpdateForm = () => {
//  return import(
/* webpackChunkName: "bulk-update-form" */ ("../bulkupdate/bulk-update-form.vue");
//  );
// };

const builderService: BuilderService = new BuilderService();

@Component({
  name: "comp-event-sched",
  methods: { hasTeamEvents },
  components: {
    ModalV2,
    ScheduleTimeOptions,
    FormGenericSectionHeader,
    PlayMinor,
    InputWithButton,
    FieldRadioV2,
    LoadingSpinnerV2,
    InputCheckboxV2,
    FormGenericFieldGridV2,
    CompeventSectionEditorV2,
    FormGenericInputTemplateV2,
    FormGenericInputTextV2,
    FieldTextV2,
    ButtonGenericV2,
    FieldHelp,
    CompeventAgeGroupCoverage,
    //  BulkUpdateForm,
    CollapseSection,
    EventGroupMove,
    "e4s-modal": E4sModal,
    CompEventSchedSection,
    GenderSelect,
  },
  computed: {
    ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
      builderCompetition: (state: IBuilderStoreState) =>
        state.builderCompetition,
      compEventLoading: (state: IBuilderStoreState) => state.compEventLoading,
      ageGroupCompCoverageModels: (state: IBuilderStoreState) =>
        state.ageGroupCompCoverageModels,
      ageGroupsDefault: (state: IBuilderStoreState) => state.ageGroupsDefault,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      userInfo: (state: IConfigStoreState) => state.configApp.userInfo,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
      isCompetitionAdminEditMode: (state: IConfigStoreState) =>
        state.competition.adminEditMode,
    }),
    VUE_MQ_SIZES() {
      return VUE_MQ_SIZES;
    },
  },
})
export default class CompEventSched extends Vue {
  public readonly builderCompetition: IBuilderCompetition;
  public readonly userInfo: IUserInfo;
  public readonly isAdmin: boolean;
  public readonly compEventLoading: boolean;
  public readonly ageGroupCompCoverageModels: IAgeGroupCompCoverageModel[];
  public readonly configApp: IConfigApp;
  public readonly isCompetitionAdminEditMode: boolean;
  public readonly ageGroupsDefault: IAgeGroup[];

  @Prop({ default: false }) public isLoading: boolean;
  @Prop({ default: () => [] }) public compEventsProp: ICompEvent[];
  @Prop({ default: 0 }) public compId: number;

  public PREFIX = Math.random().toString(36).substring(2);

  public gender = GENDER;
  public commonService: CommonService = new CommonService();
  public compEventService: CompEventService = new CompEventService();
  public compEventScheduleService: CompEventScheduleService =
    new CompEventScheduleService();
  public compEvents: ICompEvent[] = [];
  public compEventEdit: ICompEvent =
    this.compEventService.factoryGetCompEvent();
  public compEventsSchedGrid: ICompEventSchedGrid[] = [];
  // public compEventsSchedGridObject: IObjectKeyTypeArray<ICompEventSchedGrid> =
  //   {} as IObjectKeyTypeArray<ICompEventSchedGrid>;
  public compEventsSchedGridObject: Record<string, ICompEventSchedGrid[]> = {};
  public bulkEventMoveData: BulkEventMoveData = new BulkEventMoveData();

  public showBulkEditConf: boolean = false;
  public bulkEditConfReqd: boolean = false;
  public bulkEditConfCompSchedGrid: ICompEventSchedGrid =
    {} as ICompEventSchedGrid;
  public bulkEditConfResult: { isSame: boolean; diffs: any } = {
    isSame: true,
    diffs: {},
  };

  public sortBy: ISortBy = {} as ISortBy;
  public sortByOptions: Pick<
    Record<ScheduleSortByType, ISortBy>,
    "EVENT_NUMBER" | "EVENT_GROUP" | "START_TIME"
  > = {
    EVENT_NUMBER: SORT_BY.EVENT_NUMBER,
    EVENT_GROUP: SORT_BY.EVENT_GROUP,
    START_TIME: SORT_BY.START_TIME,
  };

  public bulkActions = BULK_ACTIONS;
  public bulkAction: string = "";

  public bulkEventmoveAction = BULK_EVENT_MOVE_ACTION;
  public eventNo: number = 0;
  public eventNoAction: BULK_EVENT_MOVE_ACTION = this.bulkEventmoveAction.BLANK;
  public currentSectionAction: ISectionAction = {
    sectionName: "",
  };

  public addMoveEventNumbers: string[] = [];

  public showFilters = false;
  public showAdvancedFilters = false;

  public showAgeGroupCoverage: boolean = false;
  public filterEventName: string = "";
  public showAgeGroupFilter: "SELECT" | "FREE_TEXT" = "SELECT";

  public filterAge: string = "";
  public filterAgeGroup: string | "ALL" = "ALL";
  public filterAgeGroupObject: IAgeGroup = {
    id: 0,
    name: "ALL",
    keyName: "ALL",
  } as IAgeGroup;
  public filterAgeGroupObjects: IAgeGroup[] = [];
  public filterGender: string | "ALL" = "ALL";
  public filterTrackField: "ALL" | EventTypeNameGeneric = "ALL";
  public filterEventType: "ALL" | EventType | "TEAM_ONLY" = "ALL";
  public filterDate: IsoDate | "ALL" = "ALL";

  public showMoveGroupForm: boolean = false;
  public isShowEventMoveLoading: boolean = false;
  public readonly allowSelectAny: boolean = false;

  public showBulkUpdate: boolean = false;
  public showAllGroupEditors: boolean = false;
  public isGroupEditorDirty: boolean = false;
  public showGroupEditorDirtyConf: boolean = false;
  public EventTypeDictionary = EventTypeDictionary;

  public showTimeOptions: boolean = false;
  public isReloading: boolean = false;

  public debounceFilter: any;

  public builderCompetitionInternal: IBuilderCompetition =
    builderService.factoryGetBuilder({});
  public builderControllerFactory = useBuilderControllerFactory();

  public get getIsCompLoaded() {
    return (
      this.builderCompetition &&
      this.builderCompetition.id &&
      this.builderCompetition.id > 0
    );
  }

  public mounted() {
    this.sortBy = this.sortByOptions.EVENT_NUMBER;

    //  N.B.  not being called now, just setting up.
    this.debounceFilter = debounce(() => {
      this.startProcessFilters();
    }, 200);

    this.init();
  }

  public init() {
    const compEventsSchedGrid =
      this.compEventScheduleService.mapCompEventsToSchedule(this.compEvents);

    //  Make sure none are selected
    compEventsSchedGrid.forEach((compEventSchedGrid) => {
      compEventSchedGrid.isSelected = false;
    });

    this.compEventsSchedGrid = compEventsSchedGrid;
    // const gridObject = this.compEventsSchedGridObject;

    this.setAllAgeGroupObjects();
    this.startProcessFilters();
    this.checkIfAgeGroupsNeedLoading;
  }

  // , { immediate: true }
  @Watch("compEventsProp", { immediate: true })
  public onCompEventsPropChanged(newValue: ICompEvent[]) {
    //  THe data has been re-loaded to store.
    // console.log("CompEventsProp changed typeof: " + typeof newValue, newValue);
    if (isNullOrUndefined(newValue)) {
      return;
    }
    this.compEvents = R.clone(newValue);
    this.init();
  }

  @Watch("builderCompetition")
  public onBuilderCompetitionChanged(newValue: IBuilderCompetition) {
    this.builderCompetitionInternal = simpleClone(newValue);
  }

  public startSort() {
    const sortByScheduleProp = this.sortBy.value;

    if (typeof this.sortBy.value === "undefined") {
      return;
    }

    // let groupByKey = (compEventSchedGrid: ICompEventSchedGrid) => {
    //   let sortByValueProp = this.sortBy.value;
    //   let sortByValue: string = compEventSchedGrid[sortByValueProp].toString();
    //   if (sortByValueProp === "eventNo") {
    //     const lengthToPad = 4 - sortByValue.length;
    //     sortByValue = "0000".slice(0, lengthToPad) + sortByValue;
    //   }
    //   return sortByValue + "-" + compEventSchedGrid.eventGroup;
    // };
    //
    // if (this.sortBy.value === "startDateTime") {
    //   groupByKey = (compEventSchedGrid: ICompEventSchedGrid) => {
    //     return (
    //       compEventSchedGrid.startDateTime + "-" + compEventSchedGrid.eventGroup
    //     );
    //   };
    // }

    const compEventsSchedGrid = this.compEventScheduleService.sortSchedule(
      sortByScheduleProp,
      simpleClone(this.compEventsSchedGrid)
    );
    this.compEventsSchedGrid = compEventsSchedGrid;

    const groupByKey = this.compEventScheduleService.getGroupByKeyFunction(
      this.sortBy
    );

    this.compEventsSchedGridObject =
      this.compEventScheduleService.createCompEventsSchedGridObject(
        groupByKey,
        this.compEventsSchedGrid,
        "A"
      );

    // this.compEventsSchedGridObject =
    //   this.commonService.convertArrayToObjectArray(
    //     groupByKey,
    //     this.compEventsSchedGrid
    //   );
  }

  public getStartDateTime(compEvent: ICompEvent) {
    return this.commonService.getE4sStandardDateTimeOutPut(
      compEvent.startDateTime
    );
  }

  public quickSearchFilter(filterValue: string) {
    this.filterEventName = filterValue;
    this.processFilters();
  }

  public processFilters() {
    this.debounceFilter();
  }

  public toggleAgeGroupFilter() {
    const showAgeGroupFilter =
      this.showAgeGroupFilter === "SELECT" ? "FREE_TEXT" : "SELECT";

    this.showAgeGroupFilter = showAgeGroupFilter;
  }

  public startProcessFilters() {
    const filterGender = this.filterGender;
    const filterAge = this.filterAge;
    const filterAgeGroup = this.filterAgeGroup;
    const filterEventName = this.filterEventName;
    const filterTrackField = this.filterTrackField;
    const filterEventType = this.filterEventType;
    const filterDate = this.filterDate;

    const compEventsSchedGrid =
      this.compEventScheduleService.mapCompEventsToSchedule(this.compEvents);

    const genderPredicate = (compEventSched: ICompEventSchedGrid): boolean => {
      if (filterGender.length === 0 || filterGender === "ALL") {
        return true;
      }
      return (
        compEventSched.compEvent.event.gender
          .toLowerCase()
          .indexOf(filterGender.toLowerCase()) > -1
      );
    };

    const agePredicate = (compEventSched: ICompEventSchedGrid): boolean => {
      if (this.showAgeGroupFilter === "SELECT") {
        //  only apply the SELECT filter
        return true;
      }
      return this.compEventScheduleService.doesEventContainThisAgeGroupSearchTerm(
        compEventSched,
        filterAge
      );
    };

    const ageGroupPredicate = (
      compEventSched: ICompEventSchedGrid
    ): boolean => {
      console.log("ageGroupPredicate() A", compEventSched);
      if (this.showAgeGroupFilter === "FREE_TEXT") {
        console.log("ageGroupPredicate() B, only apply the FREE_TEXT filter");
        //  only apply the FREE_TEXT filter
        return true;
      }
      // if (filterAgeGroup === "ALL" || filterAgeGroup.length === 0) {
      //   console.log("ageGroupPredicate() C, ALL or length 0");
      //   return true;
      // }

      if (this.filterAgeGroupObject.id === 0) {
        console.log("ageGroupPredicate() C, ALL or length 0");
        return true;
      }

      console.log(
        "ageGroupPredicate() D" +
          filterAgeGroup +
          "===" +
          compEventSched.compEvent.ageGroup.keyName +
          (filterAgeGroup === compEventSched.compEvent.ageGroup.keyName)
      );

      return (
        this.filterAgeGroupObject.id === compEventSched.compEvent.ageGroup.id
      );

      // return filterAgeGroup === compEventSched.compEvent.ageGroup.keyName;
    };

    const datePredicate = (compEventSched: ICompEventSchedGrid): boolean => {
      if (filterDate === "ALL" || filterDate.length === 0) {
        return true;
      }
      return (
        filterDate === compEventSched.compEvent.startDateTime.split("T")[0]
      );
    };

    const trackFieldPredicate = (
      compEventSched: ICompEventSchedGrid
    ): boolean => {
      if (filterTrackField === "ALL") {
        return true;
      }
      // console.log(compEventSched.id + ": " + filterTrackField);
      if (filterTrackField === "Field") {
        return this.compEventScheduleService.isEventFieldish(compEventSched);
      }
      //  Some sort of track-ish event.
      if (filterTrackField === "Track") {
        return this.compEventScheduleService.isEventTrackish(compEventSched);
      }
      return true;
    };

    const eventTypePredicate = (
      compEventSched: ICompEventSchedGrid
    ): boolean => {
      if (filterEventType.length === 0 || filterEventType === "ALL") {
        return true;
      }

      if (filterEventType === "TEAM_ONLY") {
        return this.compEventScheduleService.isEventTeamOnly(compEventSched);
      }

      return (
        filterEventType === compEventSched.compEvent.eventGroupSummary.type
      );
    };

    const eventNamePredicate = (
      compEventSched: ICompEventSchedGrid
    ): boolean => {
      if (filterEventName.length === 0) {
        return true;
      }
      return (
        compEventSched.eventName
          .toLowerCase()
          .indexOf(filterEventName.toLowerCase()) > -1
      );
    };

    // console.log("CompEventsProp startProcessFilters() D");

    this.compEventsSchedGrid = compEventsSchedGrid.filter(
      (compEventSched: ICompEventSchedGrid) => {
        return (
          agePredicate(compEventSched) &&
          eventNamePredicate(compEventSched) &&
          genderPredicate(compEventSched) &&
          trackFieldPredicate(compEventSched) &&
          datePredicate(compEventSched) &&
          eventTypePredicate(compEventSched) &&
          ageGroupPredicate(compEventSched)
        );
      }
    );

    this.startSort();
  }

  public get getFilterCount(): number {
    const isEventNameOn = this.filterEventName.length > 0 ? 1 : 0;
    const isAgeOn = this.filterAge.length > 0 ? 1 : 0;
    const isAgeGroupOn = this.filterAgeGroup !== "ALL" ? 1 : 0;
    const isGenderOn = this.filterGender !== "ALL" ? 1 : 0;
    const isTrackFieldOn = this.filterTrackField !== "ALL" ? 1 : 0;
    const isEventTypeOn = this.filterEventType !== "ALL" ? 1 : 0;
    const isDateOn = this.filterDate !== "ALL" ? 1 : 0;

    return (
      isEventNameOn +
      isAgeOn +
      isAgeGroupOn +
      isGenderOn +
      isTrackFieldOn +
      isEventTypeOn +
      isDateOn
    );
  }

  public get getIsFilterOn(): boolean {
    const isEventNameOn = this.filterEventName.length > 0;
    const isAgeOn = this.filterAge.length > 0;
    const isAgeGroupOn = this.filterAgeGroup !== "ALL";
    const isGenderOn = this.filterGender !== "ALL";
    const isTrackFieldOn = this.filterTrackField !== "ALL";
    const isEventTypeOn = this.filterEventType !== "ALL";
    const isDateOn = this.filterDate !== "ALL";

    return (
      isEventNameOn ||
      isAgeOn ||
      isAgeGroupOn ||
      isGenderOn ||
      isTrackFieldOn ||
      isEventTypeOn ||
      isDateOn
    );
  }

  public closeAdvancedFilters() {
    this.showAdvancedFilters = false;
  }

  public resetFilters() {
    this.filterEventName = "";
    this.filterAge = "";
    this.filterAgeGroup = "ALL";
    this.filterGender = "ALL";
    this.filterTrackField = "ALL";
    this.filterEventType = "ALL";
    this.filterDate = "ALL";

    this.processFilters();
  }

  public getFilterDateDisplay(date: IsoDate) {
    return format(parse(date), "Do MMM YYYY");
  }

  public get getAllEventTypes() {
    return this.compEventScheduleService.getAllEventTypes(this.compEvents);
  }

  public get getAllAgeGroups() {
    return this.compEventScheduleService.getAllAgeGroups(this.compEvents);
  }

  public get getAllAgeGroupObjects(): IAgeGroup[] {
    return [
      { id: 0, name: "ALL", keyName: "ALL" } as IAgeGroup,
      ...this.compEventScheduleService.getAllAgeGroupObjects(this.compEvents),
    ];
  }

  public setAllAgeGroupObjects() {
    this.filterAgeGroupObjects = [
      { id: 0, name: "ALL", keyName: "ALL" } as IAgeGroup,
      ...this.compEventScheduleService.getAllAgeGroupObjects(this.compEvents),
    ];
  }

  public bulkEventnoMove() {
    const selected = this.compEventScheduleService.getSelected(
      this.compEventsSchedGrid
    );
    const bulkEventMove = this.compEventScheduleService.createBulkEventMove(
      selected,
      this.eventNoAction,
      this.eventNo
    );
    this.bulkEventMoveData.create(bulkEventMove).then(() => {
      // this.$store.dispatch(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME + "/" +
      //     BUILDER_STORE_CONST.BUILDER_ACTIONS_COMPEVENTS_LOAD);
      this.reload();
    });
  }

  public getCompEventSchedGridById(
    id: number,
    compEventsSchedGrid: ICompEventSchedGrid[]
  ) {
    return this.commonService.getObjectByIdFromArray(
      id,
      compEventsSchedGrid
    ) as ICompEventSchedGrid;
  }

  public onEditMe(compEvent: ICompEvent) {
    const compEventSchedGridCurrent: ICompEventSchedGrid =
      this.getCompEventSchedGridById(compEvent.id, this.compEventsSchedGrid);
    if (compEventSchedGridCurrent) {
      this.setData(
        this.compEventScheduleService.deSelectAll(this.compEventsSchedGrid)
      );
      this.setCompEventSchedGridSelected(
        compEventSchedGridCurrent,
        this.compEventsSchedGrid
      );
      this.editEvent(compEventSchedGridCurrent);
    }
  }

  public setCompEventSchedGridSelected(
    compEventSchedGrid: ICompEventSchedGrid,
    compEventsSchedGrid: ICompEventSchedGrid[]
  ) {
    const setAsSelected: boolean = !compEventSchedGrid.isSelected;
    const applyEditableRules = true;
    this.setData(
      this.compEventScheduleService.setSelected(
        compEventSchedGrid,
        setAsSelected,
        compEventsSchedGrid,
        applyEditableRules
      )
    );
    this.compEventEdit = R.clone(compEventSchedGrid.compEvent);
  }

  public toggleAllowSelectAny() {
    // this.allowSelectAny = !this.allowSelectAny;
    this.setData(
      this.compEventScheduleService.deSelectAll(this.compEventsSchedGrid)
    );
  }

  public get getSelected() {
    return this.compEventScheduleService.getSelected(this.compEventsSchedGrid);
  }

  public onSelect(id: number) {
    const compEventSchedGridCurrent: ICompEventSchedGrid =
      this.commonService.getObjectByIdFromArray(
        id,
        this.compEventsSchedGrid
      ) as ICompEventSchedGrid;
    if (compEventSchedGridCurrent) {
      const setAsSelected: boolean = !compEventSchedGridCurrent.isSelected;

      // const applyEditableRules = this.allowSelectAny ? false : true;
      const applyEditableRules = true;
      const compEventsSchedGrid = this.compEventScheduleService.setSelected(
        compEventSchedGridCurrent,
        setAsSelected,
        this.compEventsSchedGrid,
        applyEditableRules
      );

      this.setData(compEventsSchedGrid);
      this.compEventEdit = R.clone(compEventSchedGridCurrent.compEvent);
    }
  }

  public onFilterGenderSelected(gender: string) {
    this.filterGender = gender;
    this.processFilters();
  }

  public onShowMore(payload: { id: number; showMore: boolean }) {
    const compEventsSchedGrid = this.compEventsSchedGrid.map(
      (compEventSchedGrid) => {
        if (compEventSchedGrid.id === payload.id) {
          compEventSchedGrid.showMore = payload.showMore;
        }
        return compEventSchedGrid;
      }
    );
    this.setData(compEventsSchedGrid);
  }

  public showMoreAllSection(payload: {
    sectionName: string;
    showMore: boolean;
  }) {
    const sectionEvents = this.compEventsSchedGridObject[
      payload.sectionName
    ] as ICompEventSchedGrid[];
    const sectionEventsObj = this.commonService.convertArrayToObject(
      "id",
      sectionEvents
    );

    const compEventsSchedGrid = this.compEventsSchedGrid.map(
      (compEventSchedGrid) => {
        if (sectionEventsObj[compEventSchedGrid.id + ""]) {
          compEventSchedGrid.showMore = payload.showMore;
        }
        return compEventSchedGrid;
      }
    );
    this.setData(compEventsSchedGrid);
  }

  public setData(compEventsSchedGrid: ICompEventSchedGrid[]) {
    this.compEventsSchedGrid = [...compEventsSchedGrid];

    const groupByKey = this.compEventScheduleService.getGroupByKeyFunction(
      this.sortBy
    );

    // this.compEventsSchedGridObject =
    //   this.compEventScheduleService.createCompEventsSchedGridObject();

    this.compEventsSchedGridObject =
      this.commonService.convertArrayToObjectArray(
        groupByKey,
        this.compEventsSchedGrid
      );

    // this.compEventsSchedGridObject =
    //   this.commonService.convertArrayToObjectArray(
    //     this.sortBy.value,
    //     this.compEventsSchedGrid
    //   );
  }

  public selectAll(ids: number[], setAsSelected: boolean) {
    if (setAsSelected) {
      // this.allowSelectAny = setAsSelected;
      //  qwerty
      // const compEventsSchedGrid = this.compEventScheduleService.getAllByIds(this.compEventsSchedGrid, ids);
      // if (!this.compEventScheduleService.areAllEventsSimilar(compEventsSchedGrid)) {
      //     messageDispatchHelper("Not all events are similar.", USER_MESSAGE_LEVEL.ERROR.toString());
      //     this.allowSelectAny = false;
      //
      //     this.setShowBulkUpdate(true);
      // }
    }

    const compEventsSchedGrid = this.compEventScheduleService.setAllSelected(
      this.compEventsSchedGrid,
      ids,
      setAsSelected
    );
    this.setData(compEventsSchedGrid);
  }

  public bulkEditConfConfirm() {
    this.showBulkEditConf = false;
    this.bulkEditConfReqd = true;
    const compSchedGrid: ICompEventSchedGrid = R.clone(
      this.bulkEditConfCompSchedGrid
    );
    this.bulkEditConfCompSchedGrid = {} as ICompEventSchedGrid;
    this.editEvent(compSchedGrid);
  }

  public editEventBulk() {
    const selected = this.compEventScheduleService.getSelected(
      this.compEventsSchedGrid
    );
    if (selected.length === 0) {
      return;
    }
    // selected = selected.filter((compEventSchedGrid) => {
    //   const doesNameMatch =
    //     compEventSchedGrid.compEvent.eventGroupSummary.name === this.bulkActio;
    //
    //   console.log(
    //     "doesNameMatch: " +
    //       compEventSchedGrid.compEvent.eventGroupSummary.name +
    //       " === " +
    //       sectionAction.sectionName +
    //       " = " +
    //       doesNameMatch
    //   );
    //   return doesNameMatch;
    // });

    const compSchedGrid: ICompEventSchedGrid = selected[0];
    this.editEvent(compSchedGrid);
  }

  public editEvent(compSchedGrid: ICompEventSchedGrid) {
    if (compSchedGrid) {
      const selected = this.compEventScheduleService.getSelected(
        this.compEventsSchedGrid
      );
      const compEventsFromSchedule = selected.map((sel) => sel.compEvent);
      const areOptionsTheSame =
        this.compEventScheduleService.areOptionsTheSame(selected);
      if (!areOptionsTheSame.isSame) {
        if (!this.bulkEditConfReqd) {
          this.bulkEditConfCompSchedGrid = R.clone(compSchedGrid);
          this.showBulkEditConf = true;
          this.bulkEditConfResult = R.clone(areOptionsTheSame);
          return;
        }
        this.bulkEditConfReqd = false; //  reset the flag
      }

      let builderCompEvent: IBuilderCompEvent;
      // const compEventData: CompEventData = new CompEventData();
      // compEventData.read(compSchedGrid.compEvent.id)
      //    .then((response: IServerResponse<ICompEvent>) => {
      Promise.resolve().then(() => {
        builderCompEvent = R.clone(
          compSchedGrid.compEvent
        ) as IBuilderCompEvent;
        const selectedAgeGroups: IAgeGroup[] = selected.map(
          (compSchedGr: ICompEventSchedGrid) => {
            return compSchedGr.compEvent.ageGroup;
          }
        );
        builderCompEvent.ageGroups = selectedAgeGroups;

        const ids: number[] = selected.map(
          (compSchedGr: ICompEventSchedGrid) => {
            return compSchedGr.compEvent.id;
          }
        );
        builderCompEvent.ceids = ids;

        const ageCiedLinks: IAgeCeidLink[] = selected.map(
          (compSchedGr: ICompEventSchedGrid) => {
            return {
              ceid: compSchedGr.id,
              agid: compSchedGr.compEvent.ageGroup.id,
              crud: "U",
            } as IAgeCeidLink;
          }
        );
        builderCompEvent.ageCeidLink = ageCiedLinks;
        builderCompEvent.compEventsFromSchedule = compEventsFromSchedule;
        this.$emit("onBulkEdit", R.clone(builderCompEvent));
      });
    }
  }

  public deleteEvents() {
    const compEvents = this.compEventScheduleService.getSelected(
      this.compEventsSchedGrid
    );
    this.$emit("onDeleteMultiple", R.clone(compEvents));
  }

  public reload() {
    // console.log("reload...a");
    this.isReloading = true;
    this.$store
      .dispatch(
        BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
          "/" +
          BUILDER_STORE_CONST.BUILDER_ACTIONS_COMPEVENTS_LOAD
      )
      .finally(() => {
        // console.log("reload...z");
        this.isReloading = false;
      });
  }

  public deleteSection(name: string) {
    const sectionEvents = this.compEventsSchedGridObject[
      name
    ] as ICompEventSchedGrid[];
    this.$emit("onDeleteMultiple", sectionEvents);
  }

  public initCurrentSectionAction() {
    this.currentSectionAction = {
      sectionName: "",
    } as ISectionAction;
  }

  public onSelectAction(sectionAction: ISectionAction) {
    this.currentSectionAction = R.clone(sectionAction);
    const compEventsSelected: ICompEvent[] = this.compEventScheduleService
      .getSelected(this.compEventsSchedGrid)
      .map((compEventSched) => {
        return compEventSched.compEvent;
      });
    if (compEventsSelected.length === 0) {
      this.initCurrentSectionAction();
    }
    this.addMoveEventNumbers = [];
    if (sectionAction.action) {
      if (sectionAction.action.value === this.bulkActions.ADD.value) {
        const addToNumbers: string[] =
          this.compEventScheduleService.getAddToEventNumbers(
            compEventsSelected,
            this.compEvents
          );
        this.addMoveEventNumbers = addToNumbers;
      }
      if (sectionAction.action.value === this.bulkActions.INSERT.value) {
        const addToNumbers: string[] =
          this.compEventScheduleService.getInsertEventNumbers(
            compEventsSelected,
            this.compEvents
          );
        this.addMoveEventNumbers = addToNumbers;
      }
    }
  }

  public runAction(sectionAction: ISectionAction) {
    this.currentSectionAction = R.clone(sectionAction);
    let compEventsSelected: ICompEventSchedGrid[] =
      this.compEventScheduleService.getSelected(this.compEventsSchedGrid);

    if (
      [
        this.bulkActions.EDIT_SECTION.value,
        this.bulkActions.EDIT_PARENT_SECTION.value,
      ].indexOf(sectionAction.action!.value) > -1
    ) {
      //  compEventsSelected = compEventsSelected.filter((compEventSchedGrid) => {
      compEventsSelected = compEventsSelected.filter((compEventSchedGrid) => {
        const doesNameMatch =
          compEventSchedGrid.compEvent.eventGroupSummary.name ===
          sectionAction.sectionName;

        // console.log(
        //   "doesNameMatch: " +
        //     compEventSchedGrid.compEvent.eventGroupSummary.name +
        //     " === " +
        //     sectionAction.sectionName +
        //     " = " +
        //     doesNameMatch
        // );
        return doesNameMatch;
      });
    }

    if (compEventsSelected.length === 0) {
      this.initCurrentSectionAction();
    }
    if (!sectionAction.action) {
      return;
    }

    if (sectionAction.action.value === this.bulkActions.EDIT.value) {
    }

    if (sectionAction.action.value === this.bulkActions.EDIT.value) {
      this.editEventBulk();
    }
    if (sectionAction.action.value === this.bulkActions.EDIT_SECTION.value) {
      this.selectAll(
        sectionAction.eventNos ? sectionAction.eventNos : [],
        true
      );
      this.editEventBulk();
    }

    if (
      sectionAction.action!.value === this.bulkActions.EDIT_PARENT_SECTION.value
    ) {
      const selected = this.compEventScheduleService.getSelected(
        this.compEventsSchedGrid
      );
      //  get first child...
      const compEventsSelectedFirst = selected[0];
      if (this.compEventScheduleService.isChildEvent(compEventsSelectedFirst)) {
        // const parentName = this.compEventScheduleService.getParentEventName(
        //   compEventsSelectedFirst
        // );
        const parentEventsEventNos: number[] = this.compEventScheduleService
          .getParentEvents(compEventsSelectedFirst, this.compEventsSchedGrid)
          .map((compEventSchedGrid) => {
            return compEventSchedGrid.id;
          });
        //  ...since it's the child events.
        this.setData(
          this.compEventScheduleService.deSelectAll(this.compEventsSchedGrid)
        );
        //  ...now select all the parent events.
        this.selectAll(parentEventsEventNos, true);
        this.editEventBulk();
      }
    }

    if (sectionAction.action.value === this.bulkActions.DELETE_SECTION.value) {
      this.deleteSection(sectionAction.sectionName);
    }
    if (sectionAction.action.value === this.bulkActions.DELETE.value) {
      this.deleteEvents();
    }
    if (sectionAction.action.value === this.bulkActions.SELECT_ALL.value) {
      this.selectAll(
        sectionAction.eventNos ? sectionAction.eventNos : [],
        true
      );
    }
    if (sectionAction.action.value === this.bulkActions.DE_SELECT_ALL.value) {
      this.selectAll(
        sectionAction.eventNos ? sectionAction.eventNos : [],
        false
      );
    }
    if (
      [this.bulkActions.ADD.value, this.bulkActions.INSERT.value].indexOf(
        sectionAction.action.value
      ) > -1
    ) {
      const selected = this.compEventScheduleService.getSelected(
        this.compEventsSchedGrid
      );
      const bulkEventMove = this.compEventScheduleService.createBulkEventMove(
        selected,
        sectionAction.action.value,
        Number(sectionAction.eventNo)
      );
      this.bulkEventMoveData.create(bulkEventMove).then(() => {
        // this.$store.dispatch(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME + "/" +
        //     BUILDER_STORE_CONST.BUILDER_ACTIONS_COMPEVENTS_LOAD);
        this.reload();
      });
    }
  }

  public addEvent() {
    this.$emit("addEvent");
  }

  public setShowMoveGroup(showIt: boolean) {
    this.showMoveGroupForm = showIt;
    // this.allowSelectAny = showIt;
    this.sortBy = SORT_BY.EVENT_GROUP;
    this.setData(
      this.compEventScheduleService.deSelectAll(this.compEventsSchedGrid)
    );
  }

  public onEventMoveCancel() {
    this.setShowMoveGroup(false);
  }

  public setShowBulkUpdate(showIt: boolean) {
    this.showBulkUpdate = showIt;
    // this.allowSelectAny = showIt;
  }

  public onBulkUpdateSuccess() {
    messageDispatchHelper(
      "Events updated.",
      USER_MESSAGE_LEVEL.INFO.toString()
    );
    this.setShowBulkUpdate(false);
    this.reload();
  }

  public onBulkUpdateCancel() {
    this.showBulkUpdate = false;
    // this.allowSelectAny = false;
    this.setData(
      this.compEventScheduleService.deSelectAll(this.compEventsSchedGrid)
    );
  }

  public onEventMoveSubmit(renameGroup: IRenameGroup) {
    const compEventData: CompEventData = new CompEventData();
    this.isShowEventMoveLoading = true;
    compEventData
      .renameGroup(renameGroup)
      .then((response) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
        } else {
          messageDispatchHelper(
            "Events moved to: " + renameGroup.group.name,
            USER_MESSAGE_LEVEL.INFO.toString()
          );
          this.reload();
          // this.allowSelectAny = false;
          this.showMoveGroupForm = false;
          this.$store.dispatch(
            BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
              "/" +
              BUILDER_STORE_CONST.BUILDER_ACTIONS_COMPEVENTS_LOAD
          );
        }
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return;
      })
      .finally(() => {
        this.isShowEventMoveLoading = false;
      });
  }

  public get getCompEventsSelected(): ICompEventSchedGrid[] {
    return this.compEventsSchedGrid.filter((compEventSchedGrid) => {
      return compEventSchedGrid.isSelected;
    });
  }

  public get getCanShowActionButton() {
    return !this.allowSelectAny && this.getHasBuilderPermissionForComp;
  }

  public get getEventGroupBeingEdited() {
    const eventGroupNamesBeingEdited =
      this.compEventScheduleService.getAllEventGroupNamesSelected(
        this.compEventsSchedGrid
      );
    return eventGroupNamesBeingEdited.length === 1
      ? eventGroupNamesBeingEdited[0]
      : "";
  }

  public get getDisplayCheckBox() {
    // return (
    //   this.getHasBuilderPermissionForComp && this.sortBy.value === "eventGroup"
    // );
    return this.getHasBuilderPermissionForComp;
  }

  public get getHasBuilderPermissionForComp(): boolean {
    return new ConfigService().hasBuilderPermissionForComp(
      this.userInfo,
      this.builderCompetition.compOrg.id,
      this.builderCompetition.id
    );
  }

  public get getCanShowHeaderButtons(): boolean {
    return !this.showMoveGroupForm && !this.showBulkUpdate;
  }

  public get getCanShowAddButton(): boolean {
    return true;
  }

  public get getShowMoveGroupButton() {
    return !this.showMoveGroupForm;
  }

  public get getCanShowAllowSelectAnyButton(): boolean {
    return !this.showMoveGroupForm;
  }

  public get getScheduleDates(): string[] {
    const dates = this.compEvents.reduce<Record<string, unknown>>(
      (accum, compEvent) => {
        accum[compEvent.startDateTime.split("T")[0]] = "";
        return accum;
      },
      {}
    );
    return Object.keys(dates);
  }

  public get getIsMultiDateComp(): boolean {
    // const dates = this.compEvents.reduce<Record<string, unknown>>(
    //   (accum, compEvent) => {
    //     accum[compEvent.startDateTime.split("T")[0]] = "";
    //     return accum;
    //   },
    //   {}
    // );
    // return Object.keys(dates).length > 1;
    return this.getScheduleDates.length > 1;
  }

  public ageGroupCoverageClicked(
    ageGroupCompCoverageModel: IAgeGroupCompCoverageModel
  ) {
    console.log("ageGroupCoverageClicked: ", ageGroupCompCoverageModel);
    // this.filterAge = ageGroupCompCoverageModel.name;
    this.showAgeGroupFilter = "SELECT";
    this.filterAgeGroup = ageGroupCompCoverageModel.name;

    // loop through the age groups and find the one that matches the name
    // set the filterAgeGroupObject to that object.
    this.filterAgeGroupObjects.forEach((ageGroupObject) => {
      if (ageGroupObject.id === ageGroupCompCoverageModel.id) {
        this.filterAgeGroupObject = simpleClone(ageGroupObject);
      }
    });

    // this.filterAgeGroupObject = simpleClone(ageGroupCompCoverageModel);
    this.processFilters();
  }

  public get getAllDatesForComp(): IsoDate[] {
    return this.compEventScheduleService.getAllDatesForComp(this.compEvents);
  }

  public toggleGroupEditor() {
    if (this.showAllGroupEditors && this.isGroupEditorDirty) {
      this.showGroupEditorDirtyConf = true;
      return;
    }
    this.showAllGroupEditors = !this.showAllGroupEditors;
  }

  public groupEditorDirtyConfConfirm() {
    this.isGroupEditorDirty = false;
    this.showGroupEditorDirtyConf = false;
    this.showAllGroupEditors = false;
  }

  public groupEditorIsDirty(groupEditorOutput: IGroupEditorOutput) {
    const compEventSchedGrids: ICompEventSchedGrid[] =
      this.compEventScheduleService.mapCompEventsToSchedule(this.compEvents);

    const eventGroupSummariesOriginal =
      this.compEventService.convertCompEventsIntoEventGroupSummaries(
        compEventSchedGrids
      );

    const isDataTheSame = R.equals(
      eventGroupSummariesOriginal,
      groupEditorOutput.eventGroupSummaries
    );
    // console.log("groupEditorIsDirty isDataTheSame: " + isDataTheSame);

    // const diffs = this.commonService.differenceBetweenTwoObjects(
    //   eventGroupSummariesOriginal,
    //   groupEditorOutput.eventGroupSummaries
    // );
    // console.log("groupEditorIsDirty diffs: ", diffs);
    this.isGroupEditorDirty = !isDataTheSame;
  }

  public get getHasTeamEvents() {
    return this.compEventScheduleService.hasAnyTeamEvents(this.compEvents);
  }

  public get getTotalAthleteEntries(): IEntryCount {
    return this.compEventScheduleService.getTotalAthleteEntries(
      this.compEvents
    );
  }

  public get getIsThisLoading() {
    return this.isLoading || this.isReloading;
  }

  public onPbMandatoryChanged(pbValue: boolean) {
    this.builderCompetitionInternal.options.pbMandatory = pbValue;
    this.builderControllerFactory.submit(this.builderCompetitionInternal);
  }

  public get isFieldDisabled() {
    return (
      builderService.isNationalClonedComp(this.builderCompetition) &&
      !this.isCompetitionAdminEditMode
    );
  }

  public get areAnyEvents() {
    return this.compEvents.length > 0;
  }

  public checkIfAgeGroupsNeedLoading() {
    if (this.ageGroupsDefault.length === 0) {
      this.$store.dispatch(
        BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
          "/" +
          BUILDER_STORE_CONST.BUILDER_ACTIONS_GET_AGE_GROUPS_DEFAULT,
        {
          aocode: this.configApp.defaultao.code,
          compId: this.compId,
        }
      );
    }
  }

  public onScheduleTimeOptionsSaved() {
    // hide the ScheduleTimeOptions modal
    this.showTimeOptions = false;

    this.reload();
  }
}
</script>

<style scoped>
.filter-header {
  font-weight: 600;
  color: darkred;
}
</style>
