import {
  getAllCompEventsAvailableFromAndTo,
  formatCompEventFromToRecord,
  ICompEventFromToRecord,
} from "../compeventschedule-service-v2";
import { ICompEvent } from "../../../compevent/compevent-models";

describe("CompEventScheduleServiceV2", () => {
  describe("getAllCompEventsAvailableFromAndTo", () => {
    test("should return empty object for empty input", () => {
      const result = getAllCompEventsAvailableFromAndTo([]);
      expect(result).toEqual({});
    });

    test("should group events by date and deduplicate availability times", () => {
      const compEvents: ICompEvent[] = [
        {
          startDateTime: "2025-05-01T09:00:00",
          options: {
            availableFrom: "2025-04-01T00:00:00",
            availableTo: "2025-05-01T17:00:00",
          },
        } as ICompEvent,
        {
          startDateTime: "2025-05-01T14:00:00",
          options: {
            availableFrom: "2025-04-01T00:00:00", // Same as previous event
            availableTo: "2025-05-01T17:00:00",
          },
        } as ICompEvent,
        // add another for date above but change the availableTo
        {
          startDateTime: "2025-05-01T14:00:00",
          options: {
            availableFrom: "2025-04-01T00:00:00", // Same as previous event
            availableTo: "2025-05-01T18:00:00",
          },
        } as ICompEvent,
        {
          startDateTime: "2025-05-02T10:00:00",
          options: {
            availableFrom: "2025-05-02T08:00:00",
            availableTo: "2025-05-02T18:00:00",
          },
        } as ICompEvent,
      ];

      const result = getAllCompEventsAvailableFromAndTo(compEvents);

      expect(Object.keys(result)).toHaveLength(2); // Should have two dates
      expect(result["2025-05-01"]).toBeDefined();
      expect(result["2025-05-01"]).toHaveLength(2); // Should have two unique from-to combinations
      expect(result["2025-05-02"]).toBeDefined();
      expect(result["2025-05-02"]).toHaveLength(1);
    });

    test("should handle events without availability options", () => {
      const compEvents: ICompEvent[] = [
        {
          startDateTime: "2025-05-01T09:00:00",
          options: {},
        } as ICompEvent,
        {
          startDateTime: "2025-05-01T14:00:00",
          options: {
            availableFrom: "2025-04-01T00:00:00",
            availableTo: "2025-05-01T17:00:00",
          },
        } as ICompEvent,
      ];

      const result = getAllCompEventsAvailableFromAndTo(compEvents);

      expect(Object.keys(result)).toHaveLength(1);
      expect(result["2025-05-01"]).toHaveLength(1);
    });

    test("should handle events with only availableFrom", () => {
      const compEvents: ICompEvent[] = [
        {
          startDateTime: "2025-05-01T09:00:00",
          options: {
            availableFrom: "2025-04-01T00:00:00",
          },
        } as ICompEvent,
      ];

      const result = getAllCompEventsAvailableFromAndTo(compEvents);

      expect(Object.keys(result)).toHaveLength(1);
      expect(result["2025-05-01"]).toHaveLength(1);
      expect(result["2025-05-01"][0]).toEqual({
        availableFrom: "2025-04-01T00:00:00",
        availableTo: undefined,
      });
    });

    test("should handle events with only availableTo", () => {
      const compEvents: ICompEvent[] = [
        {
          startDateTime: "2025-05-01T09:00:00",
          options: {
            availableTo: "2025-05-01T17:00:00",
          },
        } as ICompEvent,
      ];

      const result = getAllCompEventsAvailableFromAndTo(compEvents);

      expect(Object.keys(result)).toHaveLength(1);
      expect(result["2025-05-01"]).toHaveLength(1);
      expect(result["2025-05-01"][0]).toEqual({
        availableFrom: undefined,
        availableTo: "2025-05-01T17:00:00",
      });
    });

    test("should handle null options", () => {
      const compEvents: ICompEvent[] = [
        {
          startDateTime: "2025-05-01T09:00:00",
          options: null,
        } as any as ICompEvent,
      ];

      const result = getAllCompEventsAvailableFromAndTo(compEvents);

      expect(Object.keys(result)).toHaveLength(1);
      expect(result["2025-05-01"]).toHaveLength(0);
    });
  });
});

describe("formatCompEventFromToRecord", () => {
  test("should format single from and to", () => {
    const input: ICompEventFromToRecord = {
      "2025-05-01": [
        {
          availableFrom: "2025-04-01T00:00:00",
          availableTo: "2025-05-01T17:00:00",
        },
      ],
    };

    const result = formatCompEventFromToRecord(input);
    expect(result["2025-05-01"]).toBe(
      "From: Tue 1st Apr TBC, To: Thu 1st May 05:00pm"
    );
  });

  test("should handle multiple from and to times", () => {
    const input: ICompEventFromToRecord = {
      "2025-05-01": [
        {
          availableFrom: "2025-04-01T00:00:00",
          availableTo: "2025-05-01T17:00:00",
        },
        {
          availableFrom: "2025-04-02T09:00:00",
          availableTo: "2025-05-01T18:00:00",
        },
      ],
    };

    const result = formatCompEventFromToRecord(input);
    expect(result["2025-05-01"]).toBe(
      "From: Tue 1st Apr TBC, Wed 2nd Apr 09:00am, To: Thu 1st May 05:00pm, Thu 1st May 06:00pm"
    );
  });

  test("should handle missing from times", () => {
    const input: ICompEventFromToRecord = {
      "2025-05-01": [
        {
          availableFrom: undefined,
          availableTo: "2025-05-01T17:00:00",
        },
      ],
    } as any as ICompEventFromToRecord;

    const result = formatCompEventFromToRecord(input);
    expect(result["2025-05-01"]).toBe("From: Not Set, To: Thu 1st May 05:00pm");
  });

  test("should handle missing to times", () => {
    const input: ICompEventFromToRecord = {
      "2025-05-01": [
        {
          availableFrom: "2025-04-01T09:00:00",
          availableTo: undefined,
        },
      ],
    } as any as ICompEventFromToRecord;

    const result = formatCompEventFromToRecord(input);
    expect(result["2025-05-01"]).toBe("From: Tue 1st Apr 09:00am, To: Not Set");
  });

  test("should handle both missing times", () => {
    const input: ICompEventFromToRecord = {
      "2025-05-01": [
        {
          availableFrom: undefined,
          availableTo: undefined,
        },
      ],
    } as any as ICompEventFromToRecord;

    const result = formatCompEventFromToRecord(input);
    expect(result["2025-05-01"]).toBe("From: Not Set, To: Not Set");
  });

  test("should handle empty array", () => {
    const input: ICompEventFromToRecord = {
      "2025-05-01": [],
    };

    const result = formatCompEventFromToRecord(input);
    expect(result["2025-05-01"]).toBe("");
  });

  test("should handle empty record", () => {
    const input: ICompEventFromToRecord = {};
    const result = formatCompEventFromToRecord(input);
    expect(Object.keys(result)).toHaveLength(0);
  });
});
