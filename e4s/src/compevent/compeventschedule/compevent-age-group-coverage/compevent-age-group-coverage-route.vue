<template>
  <div>
    <CompeventAgeGroupCoverage
        :age-group-comp-coverage-models="ageGroupCompCoverageModels"
    />
  </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { CompetitionData } from "../../../competition/competition-data";
import { IAgeGroupCompCoverageModel } from "../../../agegroup/agegroup-models";
// import { CompeventAgeGroupCoverageService } from "./compevent-age-group-coverage-service";
import CompeventAgeGroupCoverage from "./compevent-age-group-coverage.vue";

@Component({
  name: "compevent-age-group-coverage-route",
  components: { CompeventAgeGroupCoverage },
})
export default class CompeventAgeGroupCoverageRoute extends Vue {
  public id: number = 0;
  public isLoading = false;

  public ageGroupCompCoverageModels: IAgeGroupCompCoverageModel[] = [];

  public created() {
    const id: number = isNaN(Number(this.$route.params.id))
      ? 0
      : parseInt(this.$route.params.id, 0);
    this.id = id;

    this.isLoading = true;
    new CompetitionData()
      .getCompetitionAgeGroupCoverage(id)
      .then((resp) => {
        if (resp.errNo === 0) {
            // const ageGroupCompCoverageModels = new CompeventAgeGroupCoverageService().convertModels(resp.data);
            this.ageGroupCompCoverageModels = resp.data;
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }
}
</script>
