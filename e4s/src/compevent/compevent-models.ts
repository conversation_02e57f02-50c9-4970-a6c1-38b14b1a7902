import { IAgeGroup } from "../agegroup/agegroup-models";
import {
  EventType,
  ICeoptions,
  IEventSeed,
} from "../athletecompsched/athletecompsched-models";
import { IBase, IsoDateTime } from "../common/common-models";
import { IEventE4S } from "../event/event-models";
import { IPrice } from "../price/price-models";
import { IEntryCount } from "../builder/buildercompevent/builder-comp-event-models";

export interface IMultiEventInfo extends IBase {
  compId: number; //
  multiId: number; //  This is the "id", of the parent event, e.g. Pent. The ID generated on back end which links all these together.  E.g. a Given
  //  "Event", e.g. Pent'n, will have a unique ID for that event.  We might
  //  deprecate when fully working, as it will be the same for all in array.
  eventId: number; //  The ID of the event, e.g. for Pent, 100m ID, 200m ID, etc.
  startDateTime: string; //
}

export interface IEventGroupLimits {
  maxAthletes: -1 | number; //  -1 = no entries, e.g. Schedule Only / Final.
  maxInHeat: 0 | number; //  0 = no limit.
}

export interface IEventGroupSummary {
  readonly id: number; //  Event Group ID. "maxGroup"
  isOpen: number;
  eventNo: number; //  Global event number "think PhotoFinish" number.
  name: string; //  Description, will appear in PhotoFinish, etc.  "100m U15 Girls"
  notes: string | null;
  type: EventType;
  typeNo: number; //  In conjunction with "type", allows "T1", "T2", "F1"...etc.
  bibSortNo?: number | ""; //  E.g. 100m might be running as T4, but we want the bib numbers to be low.
  //  Optionally set this to prioritise a low number.  -1 = leave alone.
  eventDateTime: IsoDateTime;

  options: {
    seed: IEventSeed;
  };

  limits: IEventGroupLimits;
}

export interface ICompEvent extends IBase {
  compId: number;
  event: IEventE4S; //  TODO ideally this
  startDateTime: string;
  isOpen: number; //  Always 1.
  ageGroup: IAgeGroup; //
  ageGroups?: IAgeGroup[];
  price: IPrice;
  maxAthletes: number; //  0 = no max, else max number that can enter event, closes when limit reached.
  maxGroup: string; //  TODO refactor to number. See eventGroup, will supersede this property at some stage.  For "grouping" events for athlete entry counts.
  eventGroup: string; //  "groups" events.
  eventNo: number; //  The "number" for eventGroup
  multiEventInfo: IMultiEventInfo[]; //  @Deprecated   @See options.multiEvents
  split: number; //  +ve or -ve number.
  sortDateTime: string; //  order of entry, so stacks like flyer.  Leave till end.
  eoptions: {
    class: string; //  Disabled classifications
  };
  options: ICeoptions;
  eventGroupSummary: IEventGroupSummary;
  entryCount: IEntryCount;
}

export interface IRenameGroup {
  ceids: number[];
  compId: number;
  group: {
    name: string;
    eventNo: number;
    dateTime: string;
  };
}
