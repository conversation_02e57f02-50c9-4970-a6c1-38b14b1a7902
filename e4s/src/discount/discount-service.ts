import * as R from "ramda";
import {IDiscount} from "./discount-models";
import {AgeGroupService} from "../agegroup/agegroup-service";
import {IBase, IObjectKeyType, IValidationResult} from "../common/common-models";
import {IValidationProp} from "../validation/validation-models";
import {ValidationService} from "../validation/validation-service";

const validationService: ValidationService = new ValidationService();

const ageGroupService: AgeGroupService = new AgeGroupService();

export class DiscountService {
    public factory(): IDiscount {
        return {
            id: 0,
            name: "",
            compId: 0,
            ageGroup: ageGroupService.factoryGetAgeGroup(),
            saleDisc: 0,
            regDisc: 0,
            type: "",
            count: 0,
            options: {
                registered: false,
                unregistered: false,
                clubs: [] as IBase[],
                athletes: [] as IBase[],
                users: [] as IBase[],
                events: [] as IBase[],
                ignoreEvents: [] as IBase[],
                includeSecondClaim: false
            }
        } as IDiscount;
    }

    public validate(discount: IDiscount): IObjectKeyType<IValidationProp> {
        let validationState: IObjectKeyType<IValidationProp> = {};

        if (discount.count === 0) {
            validationState = validationService.addMessage("count", "Count to be greater than 0.", validationState);
        }
        return validationState;
    }

    public getDiscountsForAgeGroup(ageGroupId: number, discounts: IDiscount[]): IDiscount[] {
        discounts = R.clone(discounts);
        return discounts.filter( (disc) => {
            return disc.ageGroup.id === ageGroupId;
        } );
    }

    public canBeSaved(discount: IDiscount, discounts: IDiscount[]): IValidationResult[] {
        const validationResults: IValidationResult[] = [];
        // const discountsWithSameId: IDiscount[] = this.getDiscountsForAgeGroup(discount.ageGroup.id, discounts);
        // if (
        //     discountsWithSameId.length > 0 &&
        //     discountsWithSameId[0].id !== discount.id
        // ) {
        //     validationResults.push({
        //         message: "A discount for [" + discount.ageGroup.id + "] " + discount.ageGroup.name + " already exists"
        //     } as IValidationResult);
        // }
        return validationResults;
    }
}
