import {IBase} from "../common/common-models";
import {IAgeGroup} from "../agegroup/agegroup-models";
import {IRuleBase} from "../builder/form/rules/rule-models";

export const DISCOUNT_TYPE = {
      VALUE: {
          LABEL: "Value",
          VALUE: "V"
      },
    PERCENTAGE: {
        LABEL: "Percentage",
        VALUE: "P"
    }
};

export interface IDiscount extends IBase, IRuleBase {
    name: string;
    compId: number;
    ageGroup: IAgeGroup;
    saleDisc: number;
    regDisc: number;
    type: string;
    count: number;
    options: {
        registered: boolean;
        unregistered: boolean;
        users: IBase[];
        clubs: IBase[];
        athletes: IBase[];
        events: IBase[];
        ignoreEvents: IBase[];
        includeSecondClaim: boolean;
    };
}
