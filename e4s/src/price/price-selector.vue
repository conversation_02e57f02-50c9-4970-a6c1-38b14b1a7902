<template>
  <div>
    <FormGenericFieldGridV2 class="price-selector--grid">
      <template slot="content">
        <span>ID</span>
        <span>Name</span>
        <span>Sale Price/Ends</span>
        <span>Price</span>

        <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end-x"></div>
      </template>
    </FormGenericFieldGridV2>

    <!--    :class="index % 2 === 0 ? '' : 'e4s-card-std__row-odd'"-->
    <div class="e4s-flex-column">
      <div
        class="
          e4s-flex-row
          e4s-gap--standard
          e4s-justify-flex-row-vert-center
          e4s-repeatable-grid--top
        "
        :class="isSelected(price) ? 'price-selector--price-selected' : ''"
        v-for="(price, index) in prices"
        :key="price.id"
      >
        <FormGenericFieldGridV2 class="price-selector--grid">
          <template slot="content">
            <span v-text="price.id"></span>
            <span v-text="getDescription(price)"></span>

            <div class="e4s-flex-column">
              <span v-text="getSalePrice(price)"></span>
              <span v-text="getSaleEndDisplay(price)"></span>
            </div>

            <span v-text="getLatePrice(price)"></span>

            <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
              <ButtonGenericV2
                v-if="priceSelectorUiDisplay.showButtons.delete"
                class="price-selector--button"
                button-type="destructive"
                style="width: 35px"
                text="x"
                v-on:click="onDeleted(price)"
              />
              <ButtonGenericV2
                v-if="priceSelectorUiDisplay.showButtons.edit"
                class="price-selector--button"
                button-type="secondary"
                text="Edit"
                v-on:click="onEdited(price)"
              />
              <ButtonGenericV2
                v-if="priceSelectorUiDisplay.showButtons.select"
                class="price-selector--button"
                style="width: 75px"
                button-type="secondary"
                text="Select"
                v-on:click="onSelected(price)"
              />
              <!--              <FieldRadioV2-->
              <!--                v-model="priceSelected"-->
              <!--                :option-value="price"-->
              <!--                v-if="priceSelectorUiDisplay.showButtons.select"-->
              <!--              />-->
            </div>
          </template>
        </FormGenericFieldGridV2>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { IPrice, IPriceSelectorUiDisplay } from "./price-models";
import { PriceService } from "./price-service";
import { CommonService } from "../common/common-service";
import { parse, format } from "date-fns";
import FieldHelp from "../common/ui/field/field-help/field-help.vue";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { getE4sStandardHumanDateOutPut } from "../common/common-service-utils";
import FormGenericFieldGridV2 from "../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FieldRadioV2 from "../common/ui/layoutV2/fields/field-radio-v2.vue";

const priceService: PriceService = new PriceService();

@Component({
  name: "price-selector",
  components: {
    FieldRadioV2,
    FormGenericFieldGridV2,
    ButtonGenericV2,
    FieldHelp,
  },
})
export default class PriceSelector extends Vue {
  @Prop({ default: () => [] })
  public readonly prices: IPrice[];

  @Prop({
    default: () => {
      return priceService.factoryGetPrice();
    },
  })
  public readonly price: IPrice;

  @Prop({
    default: () => {
      const priceSelectorUiDisplay: IPriceSelectorUiDisplay = {
        showButtons: {
          delete: false,
          edit: false,
          select: false,
        },
      };
      return priceSelectorUiDisplay;
    },
  })
  public readonly priceSelectorUiDisplay: IPriceSelectorUiDisplay;

  public commonService: CommonService = new CommonService();
  public priceSelected: IPrice = priceService.factoryGetPrice();

  public created() {
    if (this.prices && this.prices.length === 1) {
      this.priceSelected = R.clone(this.prices[0]);
    }
    if (this.price.id > 0) {
      this.priceSelected = R.clone(this.price);
    }
  }

  @Watch("price")
  public onPriceChanged(newValue: IPrice, oldValue: IPrice) {
    if (newValue && oldValue && newValue.id !== oldValue.id) {
      this.priceSelected = R.clone(newValue);
    }
  }

  public getDescription(price: IPrice): string {
    return price.description;
  }

  public getSalePrice(price: IPrice): string {
    if (price.salePrice === price.price) {
      return "";
    }
    return (
      (price.options.feeIncluded
        ? price.salePrice
        : price.salePrice + price.saleFee) +
      " (" +
      (price.options.feeIncluded ? "inc" : "exl") +
      ")"
    );
  }

  public getLatePrice(price: IPrice): string {
    return (
      (price.options.feeIncluded ? price.price : price.price + price.fee) +
      " (" +
      (price.options.feeIncluded ? "inc" : "exl") +
      ")"
    );
  }

  public getSaleEndDisplay(price: IPrice): string {
    if (price.salePrice === price.price) {
      return "";
    }
    return price.saleEndDate.length > 0
      ? getE4sStandardHumanDateOutPut(price.saleEndDate)
      : "";
  }

  // price.price" v-if="price.salePrice !== price.price"

  public get getLateFeeStartDate(): string {
    return this.prices.reduce((accum, price) => {
      if (price.saleEndDate.length > 0) {
        accum = format(parse(price.saleEndDate), "Do MMM YYYY");
      }
      return accum;
    }, "");
  }

  public onCancel() {
    this.$emit("onCancel");
  }

  public onDeleted(price: IPrice) {
    this.$emit("onDeleted", price);
  }

  public onEdited(price: IPrice) {
    this.$emit("onEdited", R.clone(price));
  }

  public onSelected(price: IPrice) {
    this.priceSelected = R.clone(price);
    this.$emit("onSelected", R.clone(price));
  }

  public isSelected(price: IPrice) {
    return this.priceSelected.id === price.id;
  }
}
</script>

<style scoped>
.price-selector--price-selected {
  font-weight: 700;
  background-color: var(--green-50);
}

.price-selector--grid {
  display: grid;
  grid-template-columns: 50px repeat(auto-fit, minmax(140px, 1fr));
  grid-gap: var(--e4s-gap--standard);
  margin: var(--e4s-gap--standard) 0;
}

.price-selector--button {
  width: 50px;
}
</style>
