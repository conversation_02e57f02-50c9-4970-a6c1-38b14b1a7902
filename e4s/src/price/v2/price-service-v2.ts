import { IPrice } from "../price-models";
import { IConfigOptionsPrice } from "../../config/config-app-models";
import {
  IPriceDisplayCosts,
  PriceErrors,
  PriceValidationProps,
} from "./price-models-v2";

export function factoryGetPrice(): IPrice {
  return {
    id: 0,
    name: "",
    compId: 0,
    description: "",
    price: 0,
    fee: 0,
    salePrice: 0,
    saleFee: 0,
    saleEndDate: "",
    options: {
      displayFee: false,
      feeIncluded: true,
      freeEntry: false,
    },
  };
}

export function calculateDisplayCosts(
  price: number,
  configOptionsPrice: IConfigOptionsPrice,
  feeIncluded: boolean,
  isNational: boolean
): IPriceDisplayCosts {
  let toOrg: number = 0;
  let toE4S: number = 0;
  let costToAthlete: number = 0;

  const addCost = isNational
    ? configOptionsPrice.nationaladdCost
    : configOptionsPrice.addCost;
  // const minCost = isNational ? configOptions.nationalminCost : configOptions.minCost

  // toE4S = price > minCost ? (price * (configOptions.percCharged/100)) + addCost : configOptions.minFee;
  toE4S = price * (configOptionsPrice.percCharged / 100) + addCost;
  if (toE4S < configOptionsPrice.minFee) {
    toE4S = configOptionsPrice.minFee;
  }
  toE4S = Number(toE4S.toFixed(2));

  if (feeIncluded) {
    costToAthlete = price;
    toOrg = Number((price - toE4S).toFixed(2));
  } else {
    toOrg = price;
    costToAthlete = Number((toOrg + toE4S).toFixed(2));
  }
  return {
    toOrg: toOrg.toString(),
    toE4S: toE4S.toString(),
    costToAthlete: costToAthlete.toString(),
  };
}

export function validate(price: IPrice): PriceErrors {
  const validationResultsArray: Record<PriceValidationProps, string[]> = {
    description: [],
    price: [],
    salePrice: [],
    saleEndDate: [],
  };

  if (price.description.length === 0) {
    validationResultsArray.description.push("Required");
  }

  if (price.price < 0) {
    validationResultsArray.price.push("Must be greater than zero");
  }

  if (price.fee > price.price) {
    validationResultsArray.price.push("Fee greater than price");
  }

  if (price.salePrice > price.price) {
    validationResultsArray.salePrice.push("Sale price greater than price");
  }

  if (price.salePrice < price.price && price.saleEndDate.length === 0) {
    validationResultsArray.saleEndDate.push("Sale price requires a valid date");
  }

  const keys: PriceValidationProps[] = Object.keys(
    validationResultsArray
  ) as PriceValidationProps[];
  const validationResults: PriceErrors = keys.reduce<PriceErrors>(
    (accum, key) => {
      const errorsForProp: string[] = validationResultsArray[key];
      // if (errorsForProp && errorsForProp.length > 0) {
      //   accum[key] = errorsForProp.join(", ");
      // }
      accum[key] = errorsForProp.join(", ");
      return accum;
    },
    {
      description: "",
      price: "",
      saleEndDate: "",
      salePrice: "",
    } as PriceErrors
  );

  return validationResults;
}

export function hasValidationErrors(priceErrors: PriceErrors): boolean {
  const isValid = Object.values(priceErrors).reduce<boolean>((accum, value) => {
    if (value.length > 0) {
      accum = false;
    }
    return accum;
  }, true);
  return isValid;
}

export function hasEarlyBird(price: IPrice): boolean {
  return price.salePrice > 0 && price.price !== price.salePrice;
}
