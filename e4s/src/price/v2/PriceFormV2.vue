<template>
  <div class="e4s-flex-column">
    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputTextV2
          form-label="Description"
          v-model="controller.state.price.description"
          :error-message="controller.priceController.state.errors.description"
        />

        <div class="e4s-flex-column e4s-flex-start">
          <InputCheckboxV2
            value-label="Free Entry"
            :value="controller.state.price.options.freeEntry"
            v-on:input="controller.priceController.onFreeEntryChanged"
          />

          <InputCheckboxV2
            value-label="E4S Fee Included in Price"
            :value="controller.state.price.options.feeIncluded"
            v-on:input="controller.priceController.feeIncludedChanged"
          />
        </div>
      </template>
    </FormGenericFieldGridV2>

    <FormGenericFieldGridV2
      v-if="controller.state.price.saleEndDate.length === 0"
    >
      <template slot="content">
        <FormGenericInputTemplateV2 form-label="Price">
          <template slot="field">
            <FieldNumberV2
              :value="controller.state.price.price"
              v-on:input="controller.priceController.priceNormalChanged"
              :error-message="controller.priceController.state.errors.price"
              :is-disabled="controller.state.price.options.freeEntry"
            />
            <p
              v-if="controller.priceController.state.errors.price.length > 0"
              class="e4s-body--200 e4s-body--error"
              v-text="controller.priceController.state.errors.price"
            ></p>
          </template>
        </FormGenericInputTemplateV2>

        <FormGenericInputTextV2
          form-label="Org Receives"
          v-model="controller.state.displayCosts.NORMAL.toOrg"
          :is-disabled="true"
        />

        <FormGenericInputTextV2
          form-label="E4S Receives"
          v-model="controller.state.displayCosts.NORMAL.toE4S"
          :is-disabled="true"
        />

        <FormGenericInputTemplateV2 form-label="Athlete Pays">
          <template slot="field">
            <FieldTextV2
              v-model="controller.state.displayCosts.NORMAL.costToAthlete"
              class="price-form-v2--cost-to-athlete"
              :is-disabled="true"
            />
          </template>
        </FormGenericInputTemplateV2>
      </template>
    </FormGenericFieldGridV2>

    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputTemplateV2
          form-label="If required, apply a price increase from this date.  This will apply to ALL prices for this competition."
        >
          <template slot="field">
            <DateEntryMat
              :iso-date="controller.state.price.saleEndDate"
              v-on:onSelected="controller.priceController.saleEndDateChanged"
            />
          </template>
        </FormGenericInputTemplateV2>
      </template>
    </FormGenericFieldGridV2>

    <div v-if="controller.state.price.saleEndDate.length > 0">
      <hr class="dat-e4s-hr-small" />

      <div class="e4s-header--400">
        Price before <span v-text="controller.state.saleEndDateDisplay"></span>
      </div>

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputTemplateV2 form-label="Price">
            <template slot="field">
              <FieldNumberV2
                :value="controller.state.price.salePrice"
                :is-disabled="controller.state.price.options.freeEntry"
                :error-message="
                  controller.priceController.state.errors.salePrice
                "
                v-on:input="controller.priceController.priceEarlyBirdChanged"
              />
              <p
                v-if="
                  controller.priceController.state.errors.salePrice.length > 0
                "
                class="e4s-body--200 e4s-body--error"
                v-text="controller.priceController.state.errors.salePrice"
              ></p>
            </template>
          </FormGenericInputTemplateV2>

          <FormGenericInputTextV2
            form-label="Org Receives"
            v-model="controller.state.displayCosts.EARLY_BIRD.toOrg"
            :is-disabled="true"
          />

          <FormGenericInputTextV2
            form-label="E4S Receives"
            v-model="controller.state.displayCosts.EARLY_BIRD.toE4S"
            :is-disabled="true"
          />

          <FormGenericInputTemplateV2 form-label="Athlete Pays">
            <template slot="field">
              <FieldTextV2
                v-model="controller.state.displayCosts.EARLY_BIRD.costToAthlete"
                class="price-form-v2--cost-to-athlete"
                :is-disabled="true"
              />
            </template>
          </FormGenericInputTemplateV2>
        </template>
      </FormGenericFieldGridV2>

      <hr class="dat-e4s-hr-small" />

      <div class="e4s-header--400">
        Price from <span v-text="controller.state.saleEndDateDisplay"></span> on
      </div>

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputTemplateV2 form-label="Price">
            <template slot="field">
              <FieldNumberV2
                :value="controller.state.price.price"
                v-on:input="controller.priceController.priceNormalChanged"
                :error-message="controller.priceController.state.errors.price"
                :is-disabled="controller.state.price.options.freeEntry"
              />
              <p
                v-if="controller.priceController.state.errors.price.length > 0"
                class="e4s-body--200 e4s-body--error"
                v-text="controller.priceController.state.errors.price"
              ></p>
            </template>
          </FormGenericInputTemplateV2>

          <FormGenericInputTextV2
            form-label="Org Receives"
            v-model="controller.state.displayCosts.NORMAL.toOrg"
            :is-disabled="true"
          />

          <FormGenericInputTextV2
            form-label="E4S Receives"
            v-model="controller.state.displayCosts.NORMAL.toE4S"
            :is-disabled="true"
          />

          <FormGenericInputTemplateV2 form-label="Athlete Pays">
            <template slot="field">
              <FieldTextV2
                v-model="controller.state.displayCosts.NORMAL.costToAthlete"
                class="price-form-v2--cost-to-athlete"
                :is-disabled="true"
              />
            </template>
          </FormGenericInputTemplateV2>
        </template>
      </FormGenericFieldGridV2>

      <hr class="dat-e4s-hr-small" />
    </div>

    <div
      class="e4s-flex-row e4s-justify-flex-end e4s-gap--standard"
      style="margin-top: var(--e4s-gap--standard)"
    >
      <ButtonGenericV2
        button-type="tertiary"
        text="Cancel"
        v-on:click="onCancel"
      />
      <ButtonGenericV2 text="Save" v-on:click="onSubmit" />
    </div>

    <LoadingSpinnerV2 v-if="controller.state.isLoading" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { IPrice } from "../price-models";
import { usePriceController } from "./price-controller";
import * as PriceServiceV2 from "./price-service-v2";
import FormGenericInputTextV2 from "../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FormGenericFieldGridV2 from "../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import InputCheckboxV2 from "../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import FieldNumberV2 from "../../common/ui/layoutV2/fields/field-number-v2.vue";
import { IPriceControllerInput } from "./price-models-v2";
import { IConfigOptionsPrice } from "../../config/config-app-models";
import FieldTextV2 from "../../common/ui/layoutV2/fields/field-text-v2.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";
import DateEntryMat from "../../common/ui/datetime/date-entry-mat.vue";

export default defineComponent({
  name: "PriceFormV2",
  components: {
    DateEntryMat,
    LoadingSpinnerV2,
    ButtonGenericV2,
    FieldTextV2,
    FieldNumberV2,
    InputCheckboxV2,
    FormGenericInputTemplateV2,
    FormGenericFieldGridV2,
    FormGenericInputTextV2,
  },
  props: {
    price: {
      type: Object as PropType<IPrice>,
      default: () => {
        return PriceServiceV2.factoryGetPrice();
      },
    },
    configOptionsPrice: {
      type: Object as PropType<IConfigOptionsPrice>,
      required: true,
    },
    isNational: {
      type: Boolean,
      default: false,
    },
  },
  setup(
    props: {
      price: IPrice;
      configOptionsPrice: IConfigOptionsPrice;
      isNational: boolean;
    },
    context: SetupContext
  ) {
    const priceControllerInput: IPriceControllerInput = {
      configOptionsPrice: props.configOptionsPrice,
      isNational: props.isNational,
    };

    const controller = usePriceController(priceControllerInput);

    watch(
      () => props.price,
      (newValue: IPrice) => {
        controller.priceController.init(newValue);
      },
      {
        immediate: true,
      }
    );

    function onCancel() {
      context.emit("onCancel");
    }

    function onSubmit() {
      controller.priceController.submit().then((resp) => {
        if (resp && resp.errNo === 0) {
          context.emit("onSubmit");
        }
      });
    }

    return {
      controller,
      onCancel,
      onSubmit,
    };
  },
});
</script>

<style>
.price-form-v2--cost-to-athlete {
  background-color: var(--yellow-50);
}
</style>
