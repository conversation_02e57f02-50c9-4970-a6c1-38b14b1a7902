import {ResourceData} from "../common/resource/resource-service";
import {IEventDef} from "./eventdef-models";
import https from "../common/https";
import {IServerResponseList} from "../common/common-models";

export class EventDefData extends ResourceData<IEventDef> {

    constructor() {
        super("/v5/eventdefs");
    }

    public getEvents(key: string, pageNumber?: number, pageSize?: number): Promise<IServerResponseList<IEventDef>> {
        return https.get( this.getEndPoint() + "?startswith=" + key + "&page=" + pageNumber + "&pagesize=" + pageSize) as any as Promise<IServerResponseList<IEventDef>>;
    }
}
