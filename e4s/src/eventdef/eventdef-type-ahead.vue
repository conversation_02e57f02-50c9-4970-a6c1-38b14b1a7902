<template>
    <auto-complete-mat
            :field-label="''"
            :custom="getCustomForAutoComplete"
            :data = "eventDefs"
            iconClassName=""
            :placeholder="getPlaceHolder"
            :is-loading="isLoading"
            :user-input-preload="''"
            v-on:searchTermChanged="searchTermChanged"
            v-on:autoSelectionMade="onSelected">
    </auto-complete-mat>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import AutoCompleteMat from "../common/ui/autocomplete/auto-complete-mat.vue";
    import {IEventDef} from "./eventdef-models";
    import {debounce} from "../common/debounce";
    import {IServerResponseList} from "../common/common-models";
    import {messageDispatchHelper} from "../user-message/user-message-store";
    import {USER_MESSAGE_LEVEL} from "../user-message/user-message-models";
    import * as R from "ramda";
    import { EventDefData } from "./eventdef-data";
    import {IAutoCompleteValue, ICustom} from "../common/ui/autocomplete/auto-complete-mat-models";

    const eventDefData: EventDefData = new EventDefData();

    @Component({
        name: "eventdef-type-ahead",
        components: {
            AutoCompleteMat
        }
    })
    export default class EventDefTypeAhead extends Vue {
        public isLoading: boolean = false;
        public searchTextEntered: string = "";
        public debounceSearch: any;
        public eventDefs: IEventDef[] = [];

        public mounted() {
            this.debounceSearch =  debounce(( key: string, pageNumber: number, pageSize: number ) => {

                this.isLoading = true;
                eventDefData.getEvents(key)
                    .then( (response: IServerResponseList<IEventDef>) => {
                        if (response.errNo > 0) {
                            messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                            return;
                        }
                        this.eventDefs = R.clone(response.data);
                        return;
                    })
                    .catch((error: any) => {
                        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return {};
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });

            }, 100);
        }

        public searchTermChanged(searchKey: string) {
            if (searchKey.length === 0) {
                return;
            }

            this.searchTextEntered = searchKey;
            this.isLoading = true;
            const pageNumber = 1;
            const pageSize = 10;
            const orderByProperty = "name";

            this.debounceSearch(this.searchTextEntered, pageNumber, pageSize, orderByProperty);
        }

        public getLabelForAutoComplete(eventDef: IEventDef): string {
            return "[" + eventDef.id + "] " + eventDef.name;
        }

        public get getCustomForAutoComplete(): ICustom {
            return {
                dropDownLabelFunc: this.getLabelForAutoComplete
            } as ICustom;
        }

        public get getPlaceHolder(): string {
            return "Enter search term...";
        }

        public onSelected(autoCompleteValue: IAutoCompleteValue) {
            this.$emit("onSelected", R.clone(autoCompleteValue.value));
        }
    }

</script>
