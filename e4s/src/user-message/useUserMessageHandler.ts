export function useUserMessageHandler() {
  function send(message: string, level?: "INFO" | "WARN") {
    // Vue.toasted.show(message, defaultErrorToastOptions);
    (window as any).M.toast({
      html: message, // "<div>Drag left or right to close.</div>"
      classes: level === "WARN" ? "toast-warning" : "toast-info",
      displayLength: 7000,
      inDuration: 500,
      outDuration: 500
    });
  }

  return {
    send
  };
}
