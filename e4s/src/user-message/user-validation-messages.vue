<template>
    <div>
         <div v-for="message in validationMessages"
               :key="message.name"
               class="errors"
               v-html="message.message">
        </div>
    </div>
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop} from "vue-property-decorator";
    import {IValidationResult} from "../common/common-models";

    @Component({
        name: "user-validation-messages"
    })
    export default class UserValidationMessages extends Vue {
        @Prop({default: () => []}) public validationMessages: IValidationResult[];
    }
</script>
