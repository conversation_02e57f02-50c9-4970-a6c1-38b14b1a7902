<template>
  <div>
      Messages: {{messages}}
  </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";
import {mapState} from "vuex";

import {IUserMessageStoreState, USER_MESSAGE_STORE_CONST} from "./user-message-store";


@Component({
    name: "user-messages-table",
      computed: {
     ...mapState(USER_MESSAGE_STORE_CONST.USER_MESSAGE_CONST_MODULE_NAME, {
      messages: (state: IUserMessageStoreState) => state.userMessages
    })
  }
})
export default class UserMessagesTable extends Vue {

}
</script>