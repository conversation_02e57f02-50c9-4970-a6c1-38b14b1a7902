<template>

    <auto-complete-mat
            field-label="Location"
            label-prop = "name"
            :data = "locations"
            iconClassName=""
            :is-loading="isLoading"
            v-on:searchTermChanged="locationsSearchTermChanged"
            v-on:autoSelectionMade="onSelected">
    </auto-complete-mat>

</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {IBuilderStoreState} from "../builder/builder-store";
    import {BUILDER_STORE_CONST} from "../builder/builder-store-constants";
    import {mapState} from "vuex";
    import {BuilderService} from "../builder/builder-service";
    import {IAutoCompleteValue} from "../common/ui/autocomplete/auto-complete-mat-models";
    import AutoCompleteMat from "../common/ui/autocomplete/auto-complete-mat.vue";

    @Component({
        name: "location-select",
        components: {
            "auto-complete-mat": AutoCompleteMat
        },
        computed: {
            ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
                locations: (state: IBuilderStoreState) => state.locations,
                locationsLoading: (state: IBuilderStoreState) => state.locationsLoading
            })
        }
    })
    export default class LocationSelect extends Vue {

        public isLoading: boolean  = false;
        public builderService: BuilderService = new BuilderService();

        public locationsSearchTermChanged(searchKey: string) {
            if (searchKey.length === 0) {
                return;
            }
            const listParams = this.builderService.getListParamsDefault(searchKey);
            this.$store.dispatch(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME + "/" +
                BUILDER_STORE_CONST.BUILDER_ACTIONS_GET_LOCATIONS, {listParams});
        }

        public onSelected(autoComplete: IAutoCompleteValue) {
            this.$emit("onSelected", R.clone(autoComplete.value));
        }

    }
</script>
