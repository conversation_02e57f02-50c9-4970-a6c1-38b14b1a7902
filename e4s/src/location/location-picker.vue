<template>
  <div>
    <div class="row">
      <div class="col s12 m6 l6">
        <div
          v-show="showSection === sections.READ"
          id="location"
          v-text="currentLocation.name + ' (' + currentLocation.id + ')'"
        ></div>

        <simple-object-drop-down
          :simple-objects="locations"
          :current-value="currentLocation"
          please-select="true"
          v-show="showSection === sections.PREVIOUS"
          v-on:onSelected="onLocationSelected"
        >
        </simple-object-drop-down>

        <location-select
          v-show="showSection === sections.SEARCH"
          v-on:onSelected="onLocationSelected"
        >
        </location-select>
      </div>

      <div class="col s12 m6 l6">
        <div class="right">
          <ButtonGenericV2
            v-show="showSection !== sections.READ"
            text="Close"
            @click="setSection(sections.READ)"
          />
          <!--          <button-->
          <!--            class="btn waves-effect waves grey"-->
          <!--            v-show="showSection !== sections.READ"-->
          <!--            v-on:click.stop="setSection(sections.READ)"-->
          <!--          >-->
          <!--            <span>Close</span>-->
          <!--          </button>-->
          <!--                    <button class="btn xxx-btn-small btn-flat green-text e4s-bold"-->
          <!--                            v-show="showSection !== sections.PREVIOUS"-->
          <!--                            v-on:click.stop="setSection(sections.PREVIOUS)">-->
          <!--                        <span>Previous</span>-->
          <!--                    </button>-->
          <ButtonGenericV2
            text="Search"
            v-show="showSection !== sections.SEARCH"
            @click="setSection(sections.SEARCH)"
          />
          <!--          <button-->
          <!--            class="btn waves-effect waves green"-->
          <!--            v-show="showSection !== sections.SEARCH"-->
          <!--            v-on:click.stop="setSection(sections.SEARCH)"-->
          <!--          >-->
          <!--            <span>Search</span>-->
          <!--          </button>-->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ILocation } from "./location-models";
import LocationSelect from "./location-select.vue";
import SimpleObjectDropDown from "../common/ui/simple-object-drop-down.vue";
import { LocationService } from "./location-service";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";

const locationService: LocationService = new LocationService();

@Component({
  name: "location-type-ahead",
  components: {
    ButtonGenericV2,
    "location-select": LocationSelect,
    "simple-object-drop-down": SimpleObjectDropDown,
  },
})
export default class LocationPicker extends Vue {
  @Prop({
    default: () => {
      return locationService.factoryGetLocation();
    },
  })
  public readonly currentLocationProp: ILocation;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly locationsProp: ILocation[];

  public currentLocation: ILocation = locationService.factoryGetLocation();
  public locations: ILocation[] = [];
  public isLoading: boolean = false;

  public sections = {
    READ: "READ",
    PREVIOUS: "PREVIOUS",
    SEARCH: "SEARCH",
  };

  public showSection: string = this.sections.SEARCH;

  public created() {
    this.currentLocation = R.clone(this.currentLocationProp);
  }

  public setSection(section: string) {
    this.showSection = section;
    this.$emit("close");
  }

  @Watch("currentLocationProp")
  public onCurrentLocationPropChanged(newValue: ILocation) {
    this.currentLocation = R.clone(newValue);
  }

  @Watch("locationsProp")
  public onLocationsPropChanged(newValue: ILocation[]) {
    this.locations = R.clone(newValue);
  }

  public onLocationSelected(location: ILocation) {
    this.$emit("onLocationSelected", R.clone(location));
    this.$emit("close");
  }
}
</script>
