<template>
  <div>
    <div
      v-if="location.id > 0"
      class="e4s-warning--section"
      style="margin-bottom: 1em"
    >
      <div class="row">
        <div class="col s12 m12 l12">
          <p style="margin-bottom: 1em">
            Any modifications to
            <strong><span v-text="location.name"></span></strong>
            can be done on this form.
          </p>
          <p style="margin-bottom: 1em">
            <ButtonGenericV2 text="Search" v-on:click="doSearch" />
            to <strong>change</strong> location for
            <strong><span v-text="builderCompetition.name"></span></strong>.
          </p>
          <p>
            <ButtonGenericV2 text="Create" v-on:click="reset" />
            if the location is not currently held in Entry4Sports.
          </p>
        </div>
      </div>
    </div>

    <!--{{location}}-->
    <fieldset class="e4s-fieldset" :disabled="!isEditable">
      <div class="row">
        <div class="col s12 m12 l12">
          <!--new form-->
          <div class="e4s-flex-column">
            <FormGenericFieldGridV2>
              <template slot="content">
                <FormGenericInputTextV2
                  :is-required-field="isEditable && location.name.length === 0"
                  form-label="Location Name"
                  v-model="location.name"
                  @onIsDirty="onInputChanged"
                >
                  <template
                    slot="after-label"
                    v-if="configController.isAdmin.value"
                  >
                    <span v-text="'(' + location.id + ')'"></span>
                  </template>
                </FormGenericInputTextV2>

                <FormGenericInputTextV2
                  form-label="Website"
                  v-model="location.website"
                />
              </template>
            </FormGenericFieldGridV2>

            <FormGenericFieldGridV2>
              <template slot="content">
                <FormGenericInputTextV2
                  form-label="Address 1"
                  v-model="location.address1"
                />

                <FormGenericInputTextV2
                  form-label="Address 2"
                  v-model="location.address2"
                />
              </template>
            </FormGenericFieldGridV2>

            <FormGenericFieldGridV2>
              <template slot="content">
                <FormGenericInputTextV2
                  form-label="Town"
                  v-model="location.town"
                />

                <FormGenericInputTextV2
                  form-label="County"
                  v-model="location.county"
                />
              </template>
            </FormGenericFieldGridV2>

            <FormGenericFieldGridV2>
              <template slot="content">
                <FormGenericInputTextV2
                  form-label="Post Code"
                  v-model="location.postcode"
                />

                <FormGenericInputTextV2
                  form-label="Map Location"
                  v-model="location.map"
                >
                  <template slot="after-label">
                    <FieldHelp
                      :get-from-server="true"
                      help-key="location--map-location"
                    />
                  </template>
                </FormGenericInputTextV2>
              </template>
            </FormGenericFieldGridV2>

            <FormGenericFieldGridV2>
              <template slot="content">
                <FormGenericInputTextV2
                  form-label="Contact"
                  v-model="location.contact"
                />

                <div></div>
              </template>
            </FormGenericFieldGridV2>

            <FormGenericFieldGridV2>
              <template slot="content">
                <FormGenericInputTemplateV2 form-label="Directions">
                  <template slot="field">
                    <FieldTextAreaV2 v-model="location.directions" />
                  </template>
                </FormGenericInputTemplateV2>
              </template>
            </FormGenericFieldGridV2>

            <div class="e4s-vertical-spacer--large"></div>

            <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
              <ButtonGenericV2
                text="Cancel"
                button-type="tertiary"
                v-show="isEditable"
                @click="onCancel"
              />

              <ButtonGenericV2
                text="Save"
                button-type="primary"
                :disabled="isLoading"
                @click="onSubmit"
              />
            </div>
          </div>
          <!--/new form-->
        </div>
      </div>

      <!--      <div class="row">-->
      <!--        <div class="input-field col s12 m6 l6">-->
      <!--          <input-->
      <!--            placeholder=""-->
      <!--            id="name"-->
      <!--            name="Name"-->
      <!--            v-validate="'required'"-->
      <!--            type="text"-->
      <!--            v-model="location.name"-->
      <!--            v-on:change="onInputChanged"-->
      <!--            class="validate"-->
      <!--          />-->

      <!--          <label class="active" for="name"-->
      <!--            >* Name <span v-text="location.id"></span-->
      <!--          ></label>-->
      <!--          <span v-show="errors.has('Name')">{{ errors.first("Name") }}</span>-->
      <!--        </div>-->

      <!--        <div class="input-field col s12 m6 l6">-->
      <!--          <input-->
      <!--            placeholder=""-->
      <!--            id="website"-->
      <!--            name="Website"-->
      <!--            type="text"-->
      <!--            v-model="location.website"-->
      <!--            v-on:change="onInputChanged"-->
      <!--            class="validate"-->
      <!--          />-->

      <!--          <label class="active" for="website">Website</label>-->
      <!--        </div>-->
      <!--      </div>-->

      <!--      <div class="row">-->
      <!--        <div class="input-field col s12 m6 l6">-->
      <!--          <input-->
      <!--            placeholder=""-->
      <!--            id="address1"-->
      <!--            name="address1"-->
      <!--            type="text"-->
      <!--            v-model="location.address1"-->
      <!--            v-on:change="onInputChanged"-->
      <!--          />-->
      <!--          <label class="active" for="address1">Address 1</label>-->
      <!--        </div>-->

      <!--        <div class="input-field col s12 m6 l6">-->
      <!--          <input-->
      <!--            placeholder=""-->
      <!--            id="address2"-->
      <!--            name="Address 2"-->
      <!--            type="text"-->
      <!--            v-model="location.address2"-->
      <!--            v-on:change="onInputChanged"-->
      <!--            class="validate"-->
      <!--          />-->

      <!--          <label class="active" for="address2">Address 2</label>-->
      <!--        </div>-->
      <!--      </div>-->

      <!--      <div class="row">-->
      <!--        <div class="input-field col s12 m6 l6">-->
      <!--          <input-->
      <!--            placeholder=""-->
      <!--            id="town"-->
      <!--            name="Town"-->
      <!--            type="text"-->
      <!--            v-model="location.town"-->
      <!--            v-on:change="onInputChanged"-->
      <!--            class="validate"-->
      <!--          />-->

      <!--          <label class="active" for="town">Town</label>-->
      <!--        </div>-->

      <!--        <div class="input-field col s12 m6 l6">-->
      <!--          <input-->
      <!--            placeholder=""-->
      <!--            id="county"-->
      <!--            name="County"-->
      <!--            type="text"-->
      <!--            v-model="location.county"-->
      <!--            v-on:change="onInputChanged"-->
      <!--            class="validate"-->
      <!--          />-->

      <!--          <label class="active" for="county">County</label>-->
      <!--        </div>-->
      <!--      </div>-->

      <!--      <div class="row">-->
      <!--        <div class="input-field col s12 m6 l6">-->
      <!--          <input-->
      <!--            placeholder=""-->
      <!--            id="potscode"-->
      <!--            name="Postcode"-->
      <!--            type="text"-->
      <!--            v-model="location.postcode"-->
      <!--            v-on:change="onInputChanged"-->
      <!--            class="validate"-->
      <!--          />-->

      <!--          <label class="active" for="town">Post Code</label>-->
      <!--        </div>-->
      <!--      </div>-->

      <!--      <div class="row">-->
      <!--        <div class="input-field col s12 m6 l6">-->
      <!--          <input-->
      <!--            placeholder=""-->
      <!--            id="map"-->
      <!--            name="Map"-->
      <!--            type="text"-->
      <!--            v-model="location.map"-->
      <!--            v-on:change="onInputChanged"-->
      <!--            class="validate"-->
      <!--          />-->

      <!--          <label class="active" for="map">Map Location</label>-->
      <!--        </div>-->

      <!--        <div class="input-field col s12 m6 l6">-->
      <!--          <input-->
      <!--            placeholder=""-->
      <!--            id="contact"-->
      <!--            name="Contact"-->
      <!--            type="text"-->
      <!--            v-model="location.contact"-->
      <!--            v-on:change="onInputChanged"-->
      <!--            class="validate"-->
      <!--          />-->

      <!--          <label class="active" for="contact">Contact</label>-->
      <!--        </div>-->
      <!--      </div>-->

      <!--      <div class="row">-->
      <!--        <div class="input-field col s12 m12 l12">-->
      <!--          <textarea-->
      <!--            placeholder=""-->
      <!--            id="directions"-->
      <!--            name="Directions"-->
      <!--            type="text"-->
      <!--            v-model="location.directions"-->
      <!--            v-on:change="onInputChanged"-->
      <!--            class="validate materialize-textarea"-->
      <!--          ></textarea>-->

      <!--          <label class="active" for="directions">Directions</label>-->
      <!--        </div>-->
      <!--      </div>-->

      <!--      <div class="row">-->
      <!--        <div class="input-field col s12 m12 l12">-->
      <!--          <div class="right">-->
      <!--            <ButtonGenericV2-->
      <!--              text="Cancel"-->
      <!--              button-type="tertiary"-->
      <!--              v-show="isEditable"-->
      <!--              @click="onCancel"-->
      <!--            />-->

      <!--            <ButtonGenericV2-->
      <!--              text="Save"-->
      <!--              button-type="primary"-->
      <!--              :disabled="!isDirty || isLoading"-->
      <!--              @click="onSubmit"-->
      <!--            />-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->
    </fieldset>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ILocation } from "./location-models";
import { LocationService } from "./location-service";
import { LocationData } from "./location-data";
import { IServerGenericResponse } from "../common/common-models";
import { messageDispatchHelper } from "../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../user-message/user-message-models";
import { IBuilderCompetition } from "../builder/builder-models";
import { BuilderService } from "../builder/builder-service";
import { mapState } from "vuex";
import { CONFIG_STORE_CONST, IConfigStoreState } from "../config/config-store";
import { IConfigApp } from "../config/config-app-models";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";
import FormGenericInputTextV2 from "../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FormGenericFieldGridV2 from "../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import { useConfigController } from "../config/useConfigStore";
import FieldHelp from "../common/ui/field/field-help/field-help.vue";
import FormGenericInputTemplateV2 from "../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FieldTextAreaV2 from "../common/ui/layoutV2/fields/field-text-area-v2.vue";

const builderService: BuilderService = new BuilderService();

@Component({
  name: "location-form",
  components: {
    FieldTextAreaV2,
    FormGenericInputTemplateV2,
    FieldHelp,
    FormGenericFieldGridV2,
    FormGenericInputTextV2,
    ButtonGenericV2,
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class LocationForm extends Vue {
  public readonly configApp!: IConfigApp;

  @Prop({
    default: () => {
      return {
        id: 0,
      } as ILocation;
    },
  })
  public locationProp: ILocation;

  @Prop({
    default: () => {
      return builderService.factoryGetBuilder({});
    },
  })
  public readonly builderCompetition: IBuilderCompetition;

  @Prop({ default: true }) public isEditable: boolean;

  public locationService: LocationService = new LocationService();
  public location: ILocation = this.locationService.factoryGetLocation();

  public isDirty: boolean = false;
  public isLoading: boolean = false;

  public configController = useConfigController();

  @Watch("locationProp")
  public onLocationPropChanged(newValue: ILocation) {
    this.location = R.clone(newValue);
  }

  public onInputChanged() {
    this.isDirty = true;
    this.$emit("inputChanged", R.clone(this.location));
  }

  public reset() {
    this.location = this.locationService.factoryGetLocation();
  }

  public onSubmit() {
    this.$validator.validateAll().then((result) => {
      if (!result) {
        return;
      }
      const locationData: LocationData = new LocationData();
      this.isLoading = true;
      const prom =
        this.location.id === 0
          ? locationData.create(this.location)
          : locationData.update(this.location);
      return prom
        .then((response: IServerGenericResponse) => {
          if (response.errNo > 0) {
            messageDispatchHelper(
              response.error,
              USER_MESSAGE_LEVEL.ERROR.toString()
            );
            return;
          }
          messageDispatchHelper(
            "Organisation " + this.location.name + " saved.",
            USER_MESSAGE_LEVEL.INFO.toString()
          );

          this.$emit("onSubmit", R.clone(response.data));
          return response.data;
        })
        .catch((error) => {
          messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
          return;
        })
        .finally(() => {
          this.isLoading = false;
        });
    });
  }

  public onCancel() {
    this.$emit("onCancel");
  }

  public doSearch() {
    this.$emit("search");
  }
}
</script>
