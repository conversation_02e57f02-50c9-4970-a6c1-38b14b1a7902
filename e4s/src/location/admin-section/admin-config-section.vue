<template>

    <div class="admin-config-section">
        <div class="row">
            <div class="col s12 m12 l12">
                Admin Section
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">

                <span v-if="!configApp.options.health">No health object!!!!</span>

                <ConfigHealth
                    :health="configApp.options.health"
                    v-if="configApp.options.health"
                ></ConfigHealth>
            </div>
        </div>
    </div>

</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import ConfigHealth from "../../config/health/config-health.vue";
import {mapState} from "vuex";
import {CONFIG_STORE_CONST, IConfigStoreState} from "../../config/config-store";
import {IConfigApp} from "../../config/config-app-models";

@Component({
    name: "admin-section",
    components: {
        ConfigHealth
    },
    computed: {
        ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
            configApp: (state: IConfigStoreState) => state.configApp
        })
    }
})
export default class AdminConfigSection extends Vue {
    public readonly configApp: IConfigApp;
}
</script>

<style>
.admin-config-section {
    background-color: pink;
    padding: 1rem;
}
</style>
