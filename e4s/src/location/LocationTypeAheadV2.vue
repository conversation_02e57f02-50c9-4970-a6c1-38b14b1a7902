<template>
  <CommonTypeAhead
    slot="default"
    :default-object="valueInternal"
    :data-function="search"
    :show-cancel-button="false"
    :use-spinner="false"
    :is-modal="isModal"
    :get-object-description="getOptionDisplayValue"
    v-on:selected="onChanged"
    v-on:reset="reset"
  >
    <div slot-scope="{ result }">
      <div v-text="getOptionDisplayValue(result)"></div>
    </div>
  </CommonTypeAhead>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";

import { ILocation } from "./location-models";
import { LocationData } from "./location-data";
import { LocationService } from "./location-service";
import { useConfigController } from "../config/useConfigStore";
import { IListParams } from "../common/resource/resource-service";
import CommonTypeAhead from "../common/ui/type-ahead/common-type-ahead.vue";

// const userService = new UserService();
const locationService = new LocationService();

export default defineComponent({
  name: "LocationTypeAheadV2",
  components: { CommonTypeAhead },
  inheritAttrs: false,
  props: {
    value: {
      type: Object as PropType<ILocation>,
      default: () => {
        return locationService.factoryGetLocation();
      },
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    isModal: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  setup(
    props: {
      value: ILocation;
      isDisabled: boolean;
      isModal: boolean;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(props.value);
    const configController = useConfigController();
    const locationData: LocationData = new LocationData();

    // const state = reactive({
    //   results: [] as IUserSummary[],
    // });

    watch(
      () => props.value,
      (newValue: ILocation) => {
        if (newValue !== valueInternal.value) {
          console.log("user-drop-down-v2 value changed", {
            new: newValue,
            internal: valueInternal.value,
          });
          valueInternal.value = newValue;
        }
      },
      {
        immediate: true,
      }
    );

    function search(searchTerm: string) {
      // return clubDataCrud.searchClubs(0, searchTerm, false);

      const listParams: IListParams = {
        startswith: searchTerm,
        pagenumber: 1,
        pagesize: 20,
        sortkey: "",
      };

      return locationData.list(listParams);
    }

    function onChanged(selectedValue: ILocation) {
      valueInternal.value = selectedValue;
      context.emit("input", valueInternal.value);
    }

    function getOptionDisplayValue(option: ILocation) {
      if (option.id === 0) {
        return "";
      }
      return (
        option.name +
        " : " +
        option.address1 +
        (configController.isAdmin.value ? " (" + option.id + ")" : "")
      );
    }

    function reset() {
      const locationDefault: ILocation = locationService.factoryGetLocation();

      if (props.isModal) {
        //  If modal, the expectation is it just changes data in modal, not underlying
        //  component...
        valueInternal.value = locationDefault;
        return;
      }

      //  But if club type ahead is on form, reset the model.
      context.emit("input", locationDefault);
    }

    return {
      valueInternal,
      reset,
      onChanged,
      search,
      getOptionDisplayValue,
      configController,
    };
  },
});
</script>

<style></style>
