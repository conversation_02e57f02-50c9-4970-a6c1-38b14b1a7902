<template>
  <E4sModal
    css-class="e4s-modal-container--full-size"
    :is-full-size-form="true"
    v-show="showContactOrganiser"
  >
    <div slot="header"></div>

    <StandardForm title="Ask Organiser" slot="body">
      <AskOrganiserForm
        style="padding: 1rem"
        slot="form-content"
        :comp-id="getCompId"
        :competition="selectedCompetition"
        v-on:onClose="onClose"
      >
      </AskOrganiserForm>
    </StandardForm>

    <div slot="buttons"></div>
  </E4sModal>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import E4sModal from "../../common/ui/e4s-modal.vue";
import StandardForm from "../../common/ui/standard-form/standard-form.vue";
import AskOrganiserForm from "./ask-organiser-form.vue";
import { ICompetitionInfo } from "../competition-models";
import { CompetitionService } from "../competiton-service";

@Component({
  name: "ask-organiser-modal",
  components: { AskOrganiserForm, StandardForm, E4sModal },
})
export default class AskOrganiserModal extends Vue {
  @Prop({ default: false }) public readonly showContactOrganiser: boolean;
  @Prop({
    default: () => {
      return new CompetitionService().factoryCompetitionInfo();
    },
  })
  public readonly selectedCompetition: ICompetitionInfo;

  public get getCompId(): number {
    //  @ts-ignore
    return this.selectedCompetition.compId
      ? this.selectedCompetition.compId
      : this.selectedCompetition.id;
  }

  // @Watch("selectedCompetition")
  // public onSelectedCompetitionChanged(newValue: ICompetitionInfo) {
  //     this.
  // }

  public onClose() {
    this.$emit("onClose");
  }
}
</script>
