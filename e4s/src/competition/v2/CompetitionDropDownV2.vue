<template>
  <select
    v-if="true"
    v-model="selectedValue"
    class="browser-default e4s-input-field e4s-input-field--primary"
    :disabled="competitionDataController.httpController.state.isLoading"
    v-on:change="onChange"
  >
    <option v-for="comp in getData" :value="comp">
      <span v-text="getCompetitionTitle(comp)"></span>
    </option>
  </select>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import {
  ICompetitionDataControllerResult,
  useCompetitionDataController,
} from "../useCompetitionDataController";
import { IBaseConcrete } from "../../common/common-models";
import {
  getE4sStandardHumanDateOutPut,
  simpleClone,
} from "../../common/common-service-utils";
import { ICompFilterParams } from "./competition-data-v2";

export default defineComponent({
  name: "CompetitionDropDownV2",
  components: {},
  props: {
    value: {
      type: Object as PropType<IBaseConcrete>,
      default: {},
    },
    compFilterParams: {
      type: Object as PropType<Partial<ICompFilterParams>>,
      default: () => {
        return {};
      },
    },
  },
  setup(
    props: {
      value: IBaseConcrete;
      compFilterParams: Partial<ICompFilterParams>;
    },
    context: SetupContext
  ) {
    const competitionDataController = useCompetitionDataController();
    const selectedValue = ref<IBaseConcrete>({
      id: props.value.id,
      name: props.value.name,
    });

    const isReady = ref(false);

    //  If props.compFilterParams has any keys, then use them to filter the competitions.
    const comppFilterKeys = Object.keys(props.compFilterParams);
    if (comppFilterKeys.length > 0) {
      competitionDataController.setSearchParams(props.compFilterParams);
    }

    //  TODO add props.ICompFilterParams, watch, then reload.
    competitionDataController.getMyComps().then((resp) => {
      console.log("CompetitionDropDownV2: getMyComps() done", resp);
      setDefault();
      isReady.value = true;
    });

    watch(
      () => props.value,
      (newValue: IBaseConcrete) => {
        selectedValue.value = simpleClone(newValue);
        setDefault();
      },
      {
        immediate: true,
      }
    );

    watch(
      () => competitionDataController.httpController.state,
      (newValue: any) => {
        const data = newValue.data as IBaseConcrete[];
        if (!data || data.length === 0) {
          return;
        }
        setDefault();
      }
    );

    function setDefault() {
      const comps: IBaseConcrete[] = competitionDataController.httpController
        .state.data as any as IBaseConcrete[];
      if (comps && comps.length > 0) {
        let i: number;
        for (i = 0; i < comps.length; i++) {
          if (comps[i].id === selectedValue.value.id) {
            selectedValue.value = simpleClone(comps[i]);
          }
        }
      }
    }

    function getCompetitionTitle(compOption: ICompetitionDataControllerResult) {
      if (!compOption) {
        return "";
      }
      const comp = compOption as IBaseConcrete;
      // const isOrgInCompName =
      //   comp.name
      //     .toLowerCase()
      //     .indexOf(comp.organiser.name.toLowerCase()) > -1;
      // (isOrgInCompName ? "" : comp.organiser.name + " - ") +
      return comp.id === 0
        ? comp.name
        : comp.id +
            ": " +
            comp.name +
            " - " +
            getE4sStandardHumanDateOutPut(compOption.dates[0]) +
            ", s: " +
            selectedValue.value.id;
    }

    function onChange() {
      context.emit("input", selectedValue.value);
      context.emit("onChange", selectedValue.value);
    }

    const getData = computed<ICompetitionDataControllerResult[]>(() => {
      return competitionDataController.httpController.state
        .data as any as ICompetitionDataControllerResult[];
    });

    return {
      competitionDataController,
      getCompetitionTitle,
      selectedValue,
      onChange,
      getData,
      isReady,
    };
  },
});
</script>
