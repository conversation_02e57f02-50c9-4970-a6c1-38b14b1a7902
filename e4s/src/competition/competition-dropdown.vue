<template>
  <select v-model="selectedCompetition">
    <option v-for="option in competitions" :key="option.id" :value="option">
      {{ option.name }}
    </option>
  </select>
</template>

<script lang="ts">
  import * as R from "ramda";
  import Vue from "vue";
  import Component from "vue-class-component";
  import {Prop, Watch} from "vue-property-decorator";

  import LoadingSpinner from "../common/ui/loading-spinner.vue";
  import {ICompetitionDropDown, ICompetitionInfo} from "./competition-models";

  @Component({
    name: "competition-dropdown",
    components: {
      "loading-spinner": LoadingSpinner
    }
  })
  export default class CompetitionDropDown extends Vue {
    @Prop({
      default: () => {
        return [];
      }
    }) public competitions: ICompetitionDropDown[];
    @Prop({default: false}) public isLoading: boolean;
    @Prop({default: 0}) public autoSelectId: number;
    @Prop() public id: number;
    public competitionLookup: ICompetitionDropDown[] = [];
    public selectedCompetition: ICompetitionDropDown = {} as ICompetitionDropDown;

    @Watch("competitions")
    public onCompetitionsChanged(newValue: ICompetitionDropDown[], oldValue: ICompetitionDropDown[]) {
      this.competitionLookup = R.clone(newValue);
      this.setCompToThis();
    }

    @Watch("autoSelectId")
    public onAutoSelectIdChanged(newValue: number, oldValue: number) {
      if (newValue !== oldValue) {
        this.setCompToThis();
      }
    }

    @Watch("id")
    public onIdChanged(newValue: string, oldValue: string) {
      this.setCompToThis();
    }

    public setCompToThis() {
      if (R.isNil(this.autoSelectId)) {
        return;
      }
      if (this.competitionLookup.length === 2) {
        this.selectedCompetition = this.competitionLookup[1];
      } else {
        if (this.autoSelectId > 0) {
          const index: number = this.competitionLookup.reduce((accum: number, comp: ICompetitionDropDown, ind: number) => {
            if (comp.id === this.autoSelectId) {
              accum = ind;
            }
            return accum;
          }, 0);
          this.selectedCompetition = this.competitionLookup[index];
        } else {
          this.selectedCompetition = this.competitionLookup[0];
        }
      }
      // if (this.autoSelectId > 0) {
      //   const index: number = this.competitionLookup.reduce((accum: number, comp: ICompetitionDropDown, ind: number) => {
      //     if (comp.id === this.autoSelectId) {
      //       accum = ind;
      //     }
      //     return accum;
      //   }, 0);
      //   this.selectedCompetition = this.competitionLookup[index];
      // } else {
      //   this.selectedCompetition = this.competitionLookup[0];
      // }
    }


    @Watch("selectedCompetition")
    public onCompetitionChanged(newValue: ICompetitionInfo, oldValue: ICompetitionInfo) {
      this.$emit("onSelected", {...newValue});
    }

    public get displayDropDown() {
      return this.competitionLookup.length > 0;
    }

  }
</script>
