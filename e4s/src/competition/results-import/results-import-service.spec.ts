import { simpleClone, unique } from "../../common/common-service-utils";
import { mockITimeTronicsCompetitionServerResponse } from "./mock/results-import-server-mock-server";
import * as ResultsImportService from "./results-import-service";
import {
  ITimeTronicsCompetition,
  ITimeTronicsEvent,
} from "./results-import-models-timetronics";
import { mockTimeTronicsEventList } from "./mock/results-import-server-mock-server-list-5";
import {
  ITimeTronicsEventList,
  ITimeTronicsHeatListParticipation,
} from "./results-import-models-timetronics-list";
import { ownersMock } from "./mock/results-import-owners-mock";
import { IOwner, IParticipantOwner } from "./results-import-data";
import {
  IEntityEntry,
  IFlattenedParticipation,
  IResultsImportEventGroupSummary,
  TimeTronicsEventGroupByKey,
} from "./results-import-models";
import { timeTronicsEventListMock } from "./mock/results-import-eventlist-mock";
import { relayTimeTronicsEventListMock } from "./mock/results-import-relay";
import {
  entriesPayloadEventGroupSummary,
  entriesPayloadMapFlattenedParticipations,
  entriesPayloadTimeTronicsEvent,
} from "./mock/results-import-create-entries-payload";
import { getAllEntriesMock } from "./mock/results-import-get-all-entries-mock";
import {
  bestGuessTimeTronicsEventMock1500,
  bestGuessTimeTronicsEventMock400Women,
  bestGuessTimeTronicsEventMockHighJump,
  bestGuessTimeTronicsEventMockShotPut,
  targetResultsImportEventGroupSummariesMock,
} from "./mock/results-import-best-guess-evenet-group-mock";
import { compEventsMock354 } from "./mock/results-import-target-comp-events-mock";
import { IBestMatchOwnerResult } from "./results-import-service";
import { resultsImportEventGroupSummary1500mMenWomenMock } from "./mock/results-import-event-groups-mock";
import { resultsImportGetUrnsFromHeatTimeTronicsHeatListMock } from "./mock/results-import-getUrnsFromHeat-timeTronicsHeatList-mock";
import { timeTronicsEventWalk3000mWomenMock } from "./mock/results-import-event-groups-walk-3000m-mock";
import { resultsImportEventGroupSummariesForComp355Mock } from "./mock/results-import-event-groups-for-comp355-mock";
import { tt60mHurdlesWomen } from "./mock/results-import-tt-event-60m-hurdles";
import { mockTimeTronicsEventGroupResult } from "./mock/dublin/dublinFilterTimeTronicsEventGroupResult";
import {
  mockTargetEventSummariesU11Girls600m,
  timeTronicsEvent600mU11Girls,
} from "./mock/leinster/mockBestGuessU11Girls600m";
import { girlsU12600mTTResults } from "./mock/leinster/600mUnder12GirlsTT";
import { ttEventResultsGirlsU16_80m_hurdles } from "./mock/munster/ttEventResultsGirlsU16_80m_hurdles";
import { ttEventResultsBoysU14_800m } from "./mock/munster/ttEventResultsBoysU14_800m";

describe("results-import-service", () => {
  test("getCompetitionFromResponse", () => {
    const data = simpleClone(mockITimeTronicsCompetitionServerResponse);

    const comp: ITimeTronicsCompetition =
      ResultsImportService.extractCompetitionFromResponse(data)!;

    expect(comp.name).toBe("123.ie National Senior Indoor Championships");
  });

  test("groupEventsBy SEQNO", () => {
    const data = simpleClone(mockITimeTronicsCompetitionServerResponse);

    const comp: ITimeTronicsCompetition =
      ResultsImportService.extractCompetitionFromResponse(data)!;

    const res = ResultsImportService.groupEventsBy(comp.events, "SEQNO");

    const seq1: ITimeTronicsEvent[] = res["1"];
    expect(seq1.length).toBe(1);
    expect(seq1[0].abbreviation).toBe("1500m");
    expect(seq1[0].categories[0].name).toBe("Senior Women");

    const seq2: ITimeTronicsEvent = res["2"][0];
    expect(seq2.abbreviation).toBe("1500m");
    expect(seq2.categories[0].name).toBe("Senior Men");
  });

  test("groupEventsBy SESSION", () => {
    const data = simpleClone(mockITimeTronicsCompetitionServerResponse);

    const comp: ITimeTronicsCompetition =
      ResultsImportService.extractCompetitionFromResponse(data)!;

    const res = ResultsImportService.groupEventsBy(comp.events, "SESSION");

    // const keys = Object.keys(res);
    // expect(keys).toBe("1500m");

    const seq1: ITimeTronicsEvent = res["Senior Women 1500m"][0];
    expect(seq1.name).toBe("Senior Women 1500m");
    // expect(seq1.categories[0].name).toBe("Senior Women");
  });

  test("removeEventsNoFinalEvents", () => {
    const data = simpleClone(mockITimeTronicsCompetitionServerResponse);

    const comp: ITimeTronicsCompetition =
      ResultsImportService.extractCompetitionFromResponse(data)!;

    const compOnlyFinals = ResultsImportService.removeEventsNoFinalEvents(comp);
    const res = ResultsImportService.groupEventsBy(
      compOnlyFinals.events,
      "SESSION"
    );

    const seq1: ITimeTronicsEvent = res["Senior Women 1500m"][0];
    expect(seq1.name).toBe("Senior Women 1500m");

    // expect(Object.keys(res)).toBe("Senior Women 1500m");

    expect(ResultsImportService.isFieldEvent(seq1)).toBe(false);
  });

  test("test track", () => {
    const data = simpleClone(mockITimeTronicsCompetitionServerResponse);

    const comp: ITimeTronicsCompetition =
      ResultsImportService.extractCompetitionFromResponse(data)!;

    const compOnlyFinals = ResultsImportService.removeEventsNoFinalEvents(comp);
    const res = ResultsImportService.groupEventsBy(
      compOnlyFinals.events,
      "SESSION"
    );

    const seq1: ITimeTronicsEvent = res["Senior Women 1500m"][0];
    expect(seq1.name).toBe("Senior Women 1500m");

    // expect(Object.keys(res)).toBe("Senior Women 1500m");

    expect(ResultsImportService.isFieldEvent(seq1)).toBe(false);
  });

  test("ITimeTronicsEventList", () => {
    const data: ITimeTronicsEventList[] = simpleClone(mockTimeTronicsEventList);
    expect(data.length).toBe(1);

    const timeTronicsEventList: ITimeTronicsEventList = data[0];
    expect(timeTronicsEventList.rounds.length).toBe(2);
    expect(timeTronicsEventList.rounds[0].name).toBe("Heat");
    expect(timeTronicsEventList.rounds[1].name).toBe("Final");

    const res =
      ResultsImportService.timeTronicsEventListFinalOnly(timeTronicsEventList);
    expect(res.rounds.length).toBe(1);
    expect(timeTronicsEventList.rounds[0].name).toBe("Heat");
  });

  test("mapToFlattenedParticipation Track", () => {
    const data: ITimeTronicsEventList[] = simpleClone(mockTimeTronicsEventList);
    expect(data.length).toBe(1);

    const timeTronicsEventList: ITimeTronicsEventList = data[0];

    expect(timeTronicsEventList.eventtype.code).toBe("200m Sprint Indoor");

    expect(timeTronicsEventList.rounds.length).toBe(2);
    expect(timeTronicsEventList.rounds[0].name).toBe("Heat");
    expect(timeTronicsEventList.rounds[1].name).toBe("Final");

    const roundFinal = timeTronicsEventList.rounds[1];
    expect(roundFinal.heats.length).toBe(1);

    const participations = roundFinal.heats[0].participations;
    expect(participations.length).toBe(6);

    const participation = participations[0];
    expect(participation.category.name).toBe("Senior Men");
    expect(participation.id).toBe(2288);
    expect(participation.participants[0].competitor.displayname).toBe(
      "Mark SMYTH"
    );

    //  I fisrt place, don't need to sort
    expect(participation.currentorder).toBe(1);

    //  What happens with field events?
    expect(participation.results.length).toBe(1);
    expect(participation.results[0].seqno).toBe(1);
    expect(participation.results[0].value).toBe("20.77");

    const flattenedParticipation =
      ResultsImportService.mapToFlattenedParticipation(participation);

    expect(flattenedParticipation.URN).toBe("188334");
    expect(flattenedParticipation.bib).toBe("755");
    expect(flattenedParticipation.birthdate).toBe("1998-07-02");
    expect(flattenedParticipation.firstName).toBe("Mark");
    expect(flattenedParticipation.lastName).toBe("SMYTH");
    expect(flattenedParticipation.resultPosition).toBe(1);
    expect(flattenedParticipation.result).toBe("20.77");

    expect(flattenedParticipation.club).toBe("Raheny Shamrock A.C.");
    expect(flattenedParticipation.competitionteam).toBe("");
    expect(flattenedParticipation.team).toBe("Raheny Shamrock A.C.");

    const arry = [];
    const len = 5;
    for (let i = 0; i < len; i++) {
      arry[i] = {
        id: i + 1,
        name: (i + 1).toString(),
      };
    }

    expect(arry[0].id).toBe(1);
    expect(arry[4].id).toBe(5);

    //  What happens with field events?
    expect(participation.results.length).toBe(1);
    // expect(arry[4].id).toBe("Test for Track");
    //
    // expect(arry[4].id).toBe("Add USer Entity");
  });

  test("ITimeTronicsEventList", () => {
    let urns = unique(
      ResultsImportService.getUrnsFromHeat(
        resultsImportGetUrnsFromHeatTimeTronicsHeatListMock,
        99
      )
    );
    expect(urns.length).toBe(9);

    urns = unique(
      ResultsImportService.getUrnsFromHeat(
        resultsImportGetUrnsFromHeatTimeTronicsHeatListMock,
        3
      )
    );
    expect(urns.length).toBe(3);
  });

  test("Owners ", () => {
    const data1: IOwner = {
      id: 1,
      name: "<EMAIL>",
      types: [
        {
          type: "Owner",
          entityId: 0,
          entityName: "",
        },
      ],
    };

    const res = ResultsImportService.getOwnerTypes(data1);
    expect(res[0]).toBe("Owner");

    const data2: IOwner = {
      id: 521,
      name: "<EMAIL>",
      types: ["Owner", "Region"],
    };

    const res2 = ResultsImportService.getOwnerTypes(data2);
    expect(res2[1]).toBe("Region");
  });

  test("Owners ", () => {
    const data = simpleClone(ownersMock);

    const participantOwner: IParticipantOwner = data["3860903"];

    expect(participantOwner.urn).toBe(3860903);
    expect(Object.keys(participantOwner.owners).length).toBe(4);

    let owner: IOwner | null = ResultsImportService.getBestOwnerMatch(
      participantOwner,
      0,
      undefined,
      undefined,
      undefined
    );
    expect(owner).toBe(null);

    // owner = ResultsImportService.getBestOwnerMatch(
    //   participantOwner,
    //   5222,
    //   undefined,
    //   undefined,
    //     undefined
    // );
    // expect(owner?.id).toBe(521);
    // expect(owner?.name).toBe("<EMAIL>");

    let bestMatchOwnerResult: IBestMatchOwnerResult;
    bestMatchOwnerResult = ResultsImportService.getBestOwnerMatch2(
      participantOwner,
      undefined,
      resultsImportEventGroupSummary1500mMenWomenMock,
      undefined,
      undefined
    );
    expect(bestMatchOwnerResult.owner).toBe(null);
    expect(bestMatchOwnerResult.message).toBe(
      "Has 4 owners and no rules applied."
    );
    // expect(owner?.name).toBe("<EMAIL>");

    owner = ResultsImportService.getBestOwnerMatch(
      participantOwner,
      11111,
      undefined,
      undefined,
      undefined
    );
    expect(owner).toBe(null);

    /*
    let resultsImportEventGroupSummary: IResultsImportEventGroupSummary = {
      type: "T",
      typeNo: 4,
      id: 5315,
      eventNo: 4,
      bibSortNo: "",
      name: "1500m",
      security: { clubs: [], counties: [], regions: [] },
    } as any as IResultsImportEventGroupSummary;

    owner = ResultsImportService.getBestOwnerMatch(
      participantOwner,
      5222,
      resultsImportEventGroupSummary,
      undefined,
      undefined
    );
    expect(owner?.id).toBe(521);
    expect(owner?.name).toBe("<EMAIL>");

    //  Club security will superceed event group id match
    resultsImportEventGroupSummary = {
      type: "T",
      typeNo: 4,
      id: 5315,
      eventNo: 4,
      bibSortNo: "",
      name: "1500m",
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
    } as any as IResultsImportEventGroupSummary;

    owner = ResultsImportService.getBestOwnerMatch(
      participantOwner,
      5222,
      resultsImportEventGroupSummary,
      undefined,
      undefined
    );
    expect(owner?.id).toBe(12605);
    expect(owner?.name).toBe("<EMAIL>");
    */
  });

  /*
  test("getBestOwnerMatch only one owner", () => {
    const data = simpleClone(ownersMock);

    const participantOwner: IParticipantOwner = data["3943657"];
    expect(participantOwner.urn).toBe(3943657);
    const owner = ResultsImportService.getBestOwnerMatch(
      participantOwner,
      5222,
      undefined,
      undefined,
      undefined
    );
    expect(owner?.id).toBe(15528);
    expect(owner?.name).toBe("<EMAIL>");
  });
  */

  /*
  test("getBestOwnerMatch CarlaSweeney1500m", () => {
    const owner = ResultsImportService.getBestOwnerMatch(
        ownerCarlaSweeneyMock,
        5320,
        resultsImportEventGroupSummaryCarlaSweeney1500m,
        undefined,
        undefined
    );
    expect(owner?.id).toBe(514);
    // expect(owner?.name).toBe("<EMAIL>");

    // {"id":514,"name":"<EMAIL>","types":["Club"]}
  });
  */

  test("Owners getBestOwnerMatchWithSecurity", () => {
    const data = simpleClone(ownersMock);

    let participantOwner: IParticipantOwner = data["3860903"];

    expect(participantOwner.urn).toBe(3860903);
    expect(Object.keys(participantOwner.owners).length).toBe(4);

    const resultsImportEventGroupSummary: IResultsImportEventGroupSummary = {
      type: "T",
      typeNo: 4,
      id: 5315,
      eventNo: 4,
      bibSortNo: "",
      name: "1500m",
      security: { clubs: [{ id: 0, name: "All" }], counties: [], regions: [] },
    } as any as IResultsImportEventGroupSummary;

    let res = ResultsImportService.getBestOwnerMatchWithSecurity(
      participantOwner,
      5222,
      resultsImportEventGroupSummary
    );
    expect(res?.id).toBe(12605);
    expect(res?.name).toBe("<EMAIL>");

    // this athlete has no club owner
    participantOwner = data["3933518"];
    expect(participantOwner.urn).toBe(3933518);
    expect(Object.keys(participantOwner.owners).length).toBe(2);

    res = ResultsImportService.getBestOwnerMatchWithSecurity(
      participantOwner,
      5222,
      resultsImportEventGroupSummary
    );
    expect(res).toBe(undefined);
  });

  test("getUrnsFromTimeTronicsEventList", () => {
    const data = simpleClone(timeTronicsEventListMock);

    const urns: number[] = ResultsImportService.getUrnsFromTimeTronicsEventList(
      data,
      3
    );

    expect(urns.length).toBe(6);
  });

  test("timeTronicsEventListMock", () => {
    const data = simpleClone(relayTimeTronicsEventListMock);

    const participation: ITimeTronicsHeatListParticipation =
      data.rounds[0].heats[0].participations[0];

    expect(participation.teamname).toBe("Dundrum South Duiblin A.C. ");
    expect(ResultsImportService.isTeamParticipation(participation)).toBe(true);
  });

  test("getNameFromFlattenedParticipation", () => {
    const data = simpleClone(relayTimeTronicsEventListMock);

    const participation: ITimeTronicsHeatListParticipation =
      data.rounds[0].heats[0].participations[0];
    expect(participation.teamname).toBe("Dundrum South Duiblin A.C. ");

    const flattenedParticipation: IFlattenedParticipation =
      ResultsImportService.mapToFlattenedParticipation(participation);
    expect(
      ResultsImportService.getNameFromFlattenedParticipation(
        flattenedParticipation
      )
    ).toBe("Dundrum South Duiblin A.C. ");
  });

  test("getTeamAthleteNames", () => {
    const data = simpleClone(relayTimeTronicsEventListMock);

    const participation: ITimeTronicsHeatListParticipation =
      data.rounds[0].heats[0].participations[0];
    expect(participation.teamname).toBe("Dundrum South Duiblin A.C. ");

    const res = ResultsImportService.getTeamAthleteNames(participation);

    expect(res.length).toBe(5);
  });

  test("getClubOrTeamTitleFromFlattenedParticipation", () => {
    const data = simpleClone(relayTimeTronicsEventListMock);

    const participation: ITimeTronicsHeatListParticipation =
      data.rounds[0].heats[0].participations[0];
    expect(participation.teamname).toBe("Dundrum South Duiblin A.C. ");

    const flattenedParticipation: IFlattenedParticipation =
      ResultsImportService.mapToFlattenedParticipation(participation);

    const res =
      ResultsImportService.getClubOrTeamAthletesFromFlattenedParticipation(
        flattenedParticipation
      );

    expect(res.slice(0, 23)).toBe("Molly HOURIHAN - 176657");
  });

  test("getEntriesUniqueSequence", () => {
    const data: ITimeTronicsEvent = {
      abbreviation: "1500m",
      categories: [{ abbreviation: "SEN W", id: 64, name: "Senior Women" }],
      eventtype: {
        abbreviation: "",
        athletesquantity: 1,
        code: "1500m",
        combinedeventsquantity: 0,
        distance: 1500,
        fieldtype: " ",
        id: 264,
        implement: 0,
        name: "",
        shortcode: "1500m",
        windmode: "N",
        windtime: "",
      },
      id: 1,
      info: "NR: 4.06.42 - Ciara Mageean (2020)  CR: 4.13.96 - Fionnuala Britton, Sli Cualann (2013)  NL: 4:18.85 - Georgia Hartigan ",
      name: "Senior Women 1500m",
      rounds: [
        {
          abbreviation: "",
          datescheduled: "2023-02-18",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 82,
              info: "0",
              numberofparticipations: 9,
              official: "16:01:13",
              scheduled: "15:40:00",
              seqno: 1,
              status: 5,
            },
            {
              attempts: 0,
              height: 0,
              id: 83,
              info: "0",
              numberofparticipations: 9,
              official: "16:01:13",
              scheduled: "15:45:00",
              seqno: 2,
              status: 5,
            },
          ],
          id: 1,
          info: "First 5 + 2 Fastest",
          name: "Heats",
          numberofparticipationsround: 18,
          official: "16:01:14",
          scheduled: "15:40:00",
          seqno: 1,
          session: "Distance Day 1",
          status: 5,
        },
        {
          abbreviation: "",
          datescheduled: "2023-02-19",
          heats: [
            {
              attempts: 0,
              height: 0,
              id: 87,
              info: "0",
              numberofparticipations: 13,
              official: "13:14:51",
              scheduled: "13:10:00",
              seqno: 1,
              status: 5,
            },
          ],
          id: 2,
          info: "",
          name: "Final",
          numberofparticipationsround: 13,
          official: "13:14:51",
          scheduled: "13:10:00",
          seqno: 2,
          session: "Distance Day 2",
          status: 5,
        },
      ],
      seqno: 1,
      status: 5,
    };

    const res = ResultsImportService.getE4sResultImportSeqEventKey(data);

    expect(res.slice(0, 23)).toBe("1:264");
  });

  test("createSubmitEntry", () => {
    const entityEntry: IEntityEntry = {
      id: 23,
      level: 3,
    };

    const res = ResultsImportService.createSubmitEntryPayload(
      entityEntry,
      entriesPayloadTimeTronicsEvent,
      entriesPayloadEventGroupSummary,
      entriesPayloadMapFlattenedParticipations
    );
    expect(res.entries.length).toBe(1);
  });

  test("mapAllEntriesByResultKey", () => {
    const data = getAllEntriesMock;

    const res = ResultsImportService.mapAllEntriesByResultKey(data);
    expect(res["1:264"].length).toBe(2);
  });

  test("filterEventGroupSummaries", () => {
    const data = targetResultsImportEventGroupSummariesMock;
    // const timeTronicEvent = bestGuessTimeTronicsEventMock;

    let res = ResultsImportService.filterEventGroupSummaries(data, "Track");
    let eventNames = res.map((evt) => evt.name);
    expect(eventNames.length).toBe(5);

    res = ResultsImportService.filterEventGroupSummaries(data, "Field");
    eventNames = res.map((evt) => evt.name);
    expect(eventNames.length).toBe(2);
  });

  test("eventGenderMatch", () => {
    const res = ResultsImportService.eventGenderMatch(
      targetResultsImportEventGroupSummariesMock[3],
      bestGuessTimeTronicsEventMockShotPut
    );

    expect(res.genderMatchResult).toBe("Exact Match");
  });

  test("eventNameMatch Shot Put", () => {
    let res;
    const evtE4s = resultsImportEventGroupSummariesForComp355Mock[17];
    expect(evtE4s.name).toBe("Shotput Men");
    // expect(
    //   ResultsImportService.eventNameMatch(
    //     targetResultsImportEventGroupSummariesMock[3],
    //     bestGuessTimeTronicsEventMockShotPut
    //   )
    // ).toBe("Exact Match");

    res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
      resultsImportEventGroupSummariesForComp355Mock,
      bestGuessTimeTronicsEventMockShotPut
    );
    expect(res.eventGroupMatches.length).toBe(1);
  });

  test("eventNameMatch Senior Women 3000m", () => {
    let res;
    const evtE4s = resultsImportEventGroupSummariesForComp355Mock[3];
    expect(evtE4s.name).toBe("3000m Track Women");
    //  notice the space.
    expect(timeTronicsEventWalk3000mWomenMock.name).toBe(
      "Senior Women 3000m Walk "
    );
    expect(timeTronicsEventWalk3000mWomenMock.eventtype.code).toBe(
      "3000m Walk Indoor"
    );

    expect(
      ResultsImportService.eventNameMatch(
        resultsImportEventGroupSummariesForComp355Mock[4],
        timeTronicsEventWalk3000mWomenMock
      )
    ).toBe("Partial Match TT");

    res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
      resultsImportEventGroupSummariesForComp355Mock,
      timeTronicsEventWalk3000mWomenMock
    );
    // const eventNames = res.map((evt: any) => {return evt.name});
    // expect(res.eventGroupMatches.length).toBe(2222222);
    expect(res.eventGroupMatchNames.length).toBe(2);
    expect(res.eventGroupMatchNames[0]).toBe("3000m Track Women");
    expect(res.eventGroupMatchNames[1]).toBe("3000m Walk");
  });

  test("eventNameMatch 60m Hurdles", () => {
    let evtE4s;
    evtE4s = resultsImportEventGroupSummariesForComp355Mock[9];
    expect(evtE4s.name).toBe("60m Hurdles");
    //  notice the space.
    expect(tt60mHurdlesWomen.name).toBe("Senior Women 60mH (0.838m )");
    expect(tt60mHurdlesWomen.eventtype.code).toBe(
      "60m Hurdles Indoor (0.838m 100m S)"
    );

    expect(ResultsImportService.eventNameMatch(evtE4s, tt60mHurdlesWomen)).toBe(
      "Partial Match TT"
    );

    let res;
    res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
      resultsImportEventGroupSummariesForComp355Mock,
      tt60mHurdlesWomen
    );
    expect(res.eventGroupMatchNames.length).toBe(2);
    expect(res.eventGroupMatchNames[0]).toBe("60m");
    expect(res.eventGroupMatchNames[1]).toBe("60m Hurdles");
  });

  test("getEventGroupSummaries", () => {
    const res = ResultsImportService.getEventGroupSummaries(compEventsMock354);
    expect(res.length).toBe(7);

    const res1500m = res[0];
    expect(res1500m.name).toBe("1500m");
    expect(res1500m.genders.length).toBe(2);
  });

  test("bestGuessResultsImportEventGroupSummary", () => {
    const data = targetResultsImportEventGroupSummariesMock;
    let res;
    // let eventNames;

    //  1500m exists in target
    expect(
      ResultsImportService.isFieldEvent(bestGuessTimeTronicsEventMock1500)
    ).toBe(false);

    res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
      data,
      bestGuessTimeTronicsEventMock1500
    );
    // const eventNames = res.map((evt: any) => {return evt.name});
    expect(res.eventGroupMatches.length).toBe(1);
    expect(res.eventGroupMatches[0].name).toBe("1500m");

    //  High Jump does not exist in target
    expect(
      ResultsImportService.isFieldEvent(bestGuessTimeTronicsEventMockHighJump)
    ).toBe(true);

    res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
      data,
      bestGuessTimeTronicsEventMockHighJump
    );

    expect(res.eventGroupMatches.length).toBe(0);

    //  Shot Put
    expect(
      ResultsImportService.isFieldEvent(bestGuessTimeTronicsEventMockShotPut)
    ).toBe(true);

    res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
      data,
      bestGuessTimeTronicsEventMockShotPut
    );

    expect(res.eventGroupMatches.length).toBe(1);
    expect(res.eventGroupMatches[0].name).toBe("Shotput");

    //  Try 400m where name chnages to 400m men
    expect(
      ResultsImportService.isFieldEvent(bestGuessTimeTronicsEventMock400Women)
    ).toBe(false);

    const evetSummary400mWomen = targetResultsImportEventGroupSummariesMock[1];
    expect(evetSummary400mWomen.eventName).toBe("400m");
    expect(evetSummary400mWomen.genders[0]).toBe("F");

    expect(
      ResultsImportService.eventNameMatch(
        evetSummary400mWomen,
        bestGuessTimeTronicsEventMock400Women
      )
    ).toBe("Partial Match TT");

    expect(
      ResultsImportService.eventGenderMatch(
        evetSummary400mWomen,
        bestGuessTimeTronicsEventMock400Women
      ).genderMatchResult
    ).toBe("Exact Match");

    res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
      data,
      bestGuessTimeTronicsEventMock400Women
    );

    expect(res.eventGroupMatches.length).toBe(1);
  });

  test("filterTimeTronicsEventGroupResult", () => {
    const data: TimeTronicsEventGroupByKey = simpleClone(
      mockTimeTronicsEventGroupResult
    );

    const eventNames = Object.keys(data);
    expect(eventNames.length).toBe(8);
    // expect(res).toBe(7);

    const res = ResultsImportService.filterTimeTronicsEventGroupResult(
      data,
      "Pole"
    );
    expect(Object.keys(res).length).toBe(1);
  });

  test("bestGuessResultsImportEventGroupSummary Leinster", () => {
    const timeTronicsEvent = simpleClone(timeTronicsEvent600mU11Girls);
    const targetResultsImportEventGroupSummaries = simpleClone(
      mockTargetEventSummariesU11Girls600m
    );

    expect(timeTronicsEvent.name).toBe("U11 Girls 600m");

    const res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
      targetResultsImportEventGroupSummaries,
      timeTronicsEvent
    );
    expect(res.eventTypeNameGeneric).toBe("Track");

    const resGender = ResultsImportService.eventGenderMatch(
      targetResultsImportEventGroupSummaries[0],
      timeTronicsEvent
    );
    expect(resGender.genderMatchResult).toBe("Exact Match");

    expect(res.eventGroupMatches.length).toBe(1);
    expect(res.eventGroupMatches[0].eventName).toBe("600m");
    expect(res.eventGroupMatches[0].name).toBe("600m U11");
    expect(res.eventGroupMatches[0].ageGroup.shortName).toBe("Under 11");
  });

  test("simple record test", () => {
    const simpleRecord = {
      123: "one",
      "456": "two",
    };

    expect(simpleRecord[123]).toBe("one");
    expect(simpleRecord["123"]).toBe("one");
  });

  test("extractParticipationsFromTimeTronicsEventList", () => {
    const data = simpleClone(girlsU12600mTTResults);

    const res =
      ResultsImportService.extractParticipationsFromTimeTronicsEventList(data);

    expect(res.length).toBe(37);

    const firstPlace = res[0];
    expect(firstPlace.currentorder_round).toBe(1);
    expect(firstPlace.participants[0].competitor.displayname).toBe(
      "Stephanie CLARKE"
    );
  });

  test("filterRounds", () => {
    const data = simpleClone(ttEventResultsGirlsU16_80m_hurdles[0]);

    const res = ResultsImportService.filterRounds(data);

    expect(res.heats.length).toBe(1);
    expect(res.finals.length).toBe(1);

    expect(res.heats[0].name).toBe("Heats");
    expect(res.finals[0].name).toBe(" F i n a l ");

    //  HEATS, HEATS, HEATS
    //  By not passing in RoundType, we get heats.
    const partsHeats =
      ResultsImportService.extractParticipationsFromTimeTronicsEventList(data);

    expect(partsHeats.length).toBe(9);
    expect(partsHeats[0].participants[0].competitor.displayname).toBe(
      "MILLER, Cora"
    );
    expect(partsHeats[0].currentorder_round).toBe(1);
    expect(partsHeats[0].results[0].result_value).toBe(12.47);

    expect(partsHeats[1].participants[0].competitor.displayname).toBe(
      "CORBETT, Lily"
    );
    expect(partsHeats[1].currentorder_round).toBe(2);
    expect(partsHeats[1].results[0].result_value).toBe(13.009);

    expect(partsHeats[2].participants[0].competitor.displayname).toBe(
      "AHERN, Giselle"
    );
    expect(partsHeats[2].currentorder_round).toBe(3);
    expect(partsHeats[2].results[0].result_value).toBe(13.035);

    //  FINALS, FINALS, FINALS
    const partsFinals =
      ResultsImportService.extractParticipationsFromTimeTronicsEventList(
        data,
        "FINAL"
      );
    expect(partsFinals.length).toBe(8);
    expect(partsFinals[0].participants[0].competitor.displayname).toBe(
      "MILLER, Cora"
    );
    expect(partsFinals[0].currentorder_round).toBe(1);
    expect(partsFinals[0].results[0].result_value).toBe(12.594);

    expect(partsFinals[1].participants[0].competitor.displayname).toBe(
      "AHERN, Giselle"
    );
    expect(partsFinals[1].currentorder_round).toBe(2);
    expect(partsFinals[1].results[0].result_value).toBe(12.601);

    expect(partsFinals[2].participants[0].competitor.displayname).toBe(
      "CORBETT, Lily"
    );
    expect(partsFinals[2].currentorder_round).toBe(3);
    expect(partsFinals[2].results[0].result_value).toBe(12.766);

    //  ALL, ALL, ALL
    /*
    const partsAll =
      ResultsImportService.extractParticipationsFromTimeTronicsEventList(
        data,
        "ALL"
      );
    expect(partsAll.length).toBe(8);
    expect(partsAll[0].participants[0].competitor.displayname).toBe(
      "MILLER, Cora"
    );
    expect(partsAll[0].currentorder_round).toBe(1);
    expect(partsAll[0].results[0].result_value).toBe(12.594);
     */
  });

  test("filterRounds multiple final rounds", () => {
    const data = simpleClone(ttEventResultsBoysU14_800m[0]);

    const res = ResultsImportService.filterRounds(data);

    expect(res.heats.length).toBe(0);
    expect(res.finals.length).toBe(1);
    expect(res.finals[0].heats.length).toBe(2);

    // expect(res.heats[0].name).toBe("Heats");
    // expect(res.finals[0].name).toBe(" F i n a l ");
  });
});
