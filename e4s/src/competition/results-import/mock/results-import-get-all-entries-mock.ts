import { IR4sAthleteEntry } from "../../scoreboard/rs4/rs4-scoreboard-models";
import {
  EntryIdString,
  EventGroupIdString,
} from "../../../common/common-models";

export const getAllEntriesMock: Record<
  EventGroupIdString,
  Record<EntryIdString, IR4sAthleteEntry>
> = {
  "5320": {
    "71568": {
      athleteId: 25067,
      firstname: "<PERSON>",
      surname: "<PERSON><PERSON>",
      gender: "F",
      urn: 192378,
      clubname: "Aberystwyth A.C.",
      club: {
        id: 264,
        name: "Aberystwyth A.C.",
      },
      entryId: 71568,
      entryOptions: {
        allowAreas: false,
        otd: false,
        forceBasket: false,
        resultsKey: "1:264",
        trackPB: true,
        checkIn: { from: null, to: null },
        autoEntries: {
          targetEntry: { id: 0, paid: 0, orderId: 0 },
          targetEventGroup: { id: 0, name: "" },
        },
      },
      paid: 1,
      egId: 5320,
      ageGroup: {
        currentAge: 30,
        competitionAge: 30,
        name: "Senior",
        shortName: "Senior",
      },
      user: {
        id: 2000,
        email: "el<PERSON>_hart<PERSON>@msn.com",
        login: "<PERSON> <PERSON>nett",
        name: "<PERSON> <PERSON>nett",
      },
      perf: {
        perf: 0,
        perfText: "0.00",
      },
      editAccess: true,
    },
    "71567": {
      athleteId: 184988,
      firstname: "Carla",
      surname: "Sweeney",
      gender: "F",
      urn: 117332,
      clubname: "Rathfarnham W.S.A.F. A.C.",
      club: {
        id: 234,
        name: "Rathfarnham W.S.A.F. A.C.",
      },
      entryId: 71567,
      entryOptions: {
        allowAreas: false,
        otd: false,
        forceBasket: false,
        resultsKey: "1:264",
        trackPB: true,
        checkIn: { from: null, to: null },
        autoEntries: {
          targetEntry: { id: 0, paid: 0, orderId: 0 },
          targetEventGroup: { id: 0, name: "" },
        },
      },
      paid: 0,
      egId: 5320,
      ageGroup: {
        currentAge: 25,
        competitionAge: 25,
        name: "Senior",
        shortName: "Senior",
      },
      user: {
        id: 551,
        email: "<EMAIL>",
        login: "<EMAIL>",
        name: "Andrew Lynam",
      },
      perf: {
        perf: 0,
        perfText: "0.00",
      },
      editAccess: true,
    },
  },
};
