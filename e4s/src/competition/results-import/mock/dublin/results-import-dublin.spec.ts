import * as ResultsImportService from "../../results-import-service";
import { owners742711 } from "./owners-742-711";
import { IResultsImportEventGroupSummary } from "../../results-import-models";
import { simpleClone } from "../../../../common/common-service-utils";
import { dublin2024Feed1015Competition } from "./dublin2024Feed1015";
import { ITimeTronicsCompetition } from "../../results-import-models-timetronics";

describe("dublin", () => {
  test("user match", () => {
    const partOwner = owners742711["308603"];
    expect(partOwner.urn).toBe(308603);
    expect(Object.keys(partOwner.owners).length > 0).toBe(true);
    const bestMatch = ResultsImportService.getBestOwnerMatch(
      partOwner,
      11108,
      undefined,
      undefined,
      { id: 11108 } as IResultsImportEventGroupSummary
    );

    expect(bestMatch?.name).toBe("<EMAIL>");
  });

  test("groupEventsBy", () => {
    const data: ITimeTronicsCompetition = simpleClone(
      dublin2024Feed1015Competition
    );

    const result = ResultsImportService.groupEventsBy(data.events, "SEQNO");

    expect(Object.keys(result).length).toBe(30);
  });
});
