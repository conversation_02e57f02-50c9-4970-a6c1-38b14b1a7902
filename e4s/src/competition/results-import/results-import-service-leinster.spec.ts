import { ttShotputBoysU17 } from "./mock/leinster/tt-shotput-boys-u17";
import { ITimeTronicsEvent } from "./results-import-models-timetronics";
import { src720CompEvents } from "./mock/leinster/es4-src-720-comp-events-mock";
import { IResultsImportEventGroupSummary } from "./results-import-models";
import { simpleClone } from "../../common/common-service-utils";
import { feederAllEntriesForComp1094 } from "./mock/leinster/leinster-all-e4s-entries";
import {
  IAthleteEntryFeeder,
  IFeederAllEntriesByEntryId,
  IFeederAllEntriesForCompByEventGroupIdByEntryId,
  ITeamEntryFeeder,
} from "../scoreboard/scoreboard-data";
import * as ResultsImportService from "./results-import-service";

describe("leinster", () => {
  test("eventNameMatch High Jump", () => {
    //
    // const evtE4s = resultsImportEventGroupSummariesForComp355Mock[17];
    // expect(evtE4s.name).toBe("Shotput Men");
    // expect(
    //   ResultsImportService.eventNameMatch(
    //     targetResultsImportEventGroupSummariesMock[3],
    //     bestGuessTimeTronicsEventMockShotPut
    //   )
    // ).toBe("Exact Match");

    const shotPutTimeTronicsEvent: ITimeTronicsEvent = ttShotputBoysU17;
    expect(shotPutTimeTronicsEvent.name).toBe("U17 Boys Shot Put (5kg)");

    const sourceeventGroupSummaries =
      ResultsImportService.getEventGroupSummaries(src720CompEvents);

    const sourceeventGroupSummariesArray: IResultsImportEventGroupSummary[] =
      Object.values(sourceeventGroupSummaries);
    expect(sourceeventGroupSummariesArray[0].id).toBe(11516);

    // const eventNames: string[] = sourceeventGroupSummariesArray.map( (evet) => {
    //     return evet.eventName;
    // })
    // expect(eventNames).toBe(1);

    const shotPutArray: IResultsImportEventGroupSummary[] =
      sourceeventGroupSummariesArray.filter((evet) => {
        return evet.eventName === "Shotput";
      });

    expect(shotPutArray.length).toBe(1);

    const e4sShotPut = shotPutArray[0];

    expect(ResultsImportService.isFieldEvent(shotPutTimeTronicsEvent)).toBe(
      true
    );

    let res;
    res = ResultsImportService.eventNameMatch2(
      e4sShotPut,
      shotPutTimeTronicsEvent
    );
    expect(res).toBe("Partial Match TT");
    res = ResultsImportService.eventNameMatch(
      e4sShotPut,
      shotPutTimeTronicsEvent
    );
    expect(res).toBe("Partial Match TT");

    res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
      sourceeventGroupSummaries,
      shotPutTimeTronicsEvent
    );

    expect(res.eventGroupMatches.length).toBe(1);
    expect(res.eventGroupMatches[0].id).toBe(11521);
    expect(res.eventGroupMatches[0].name).toBe("Shot Putt");
    //
    // res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
    //     resultsImportEventGroupSummariesForComp355Mock,
    //     bestGuessTimeTronicsEventMockShotPut
    // );
    // expect(res.eventGroupMatches.length).toBe(1);
  });

  test("eventNameMatch High Jump", () => {
    const data: IFeederAllEntriesForCompByEventGroupIdByEntryId = simpleClone(
      feederAllEntriesForComp1094
    );

    const feederAllEntriesByEntryId: IFeederAllEntriesByEntryId = data[22800];
    expect(Object.keys(feederAllEntriesByEntryId).length).toBe(3);

    const teams: ITeamEntryFeeder[] = Object.values(
      feederAllEntriesByEntryId
    ) as any as ITeamEntryFeeder[];

    const team: ITeamEntryFeeder = teams[0];

    expect(team.teamName).toBe("Ratoath A.C. 4x100 Relay Female Under 9 {A}");

    const athletes = team.athletes;
    expect(athletes.length).toBe(4);

    const keyForMap = ResultsImportService.getKeyForEntriesMap(team);
    expect(keyForMap).toBe("363503~368522~368654~434260");

    const indivEvent = data[22813];
    const athleteEntries = Object.values(indivEvent);
    expect(athleteEntries.length).toBe(8);

    const athleteEntryOne = athleteEntries[0];
    expect(
      ResultsImportService.getKeyForEntriesMap(
        athleteEntryOne as IAthleteEntryFeeder
      )
    ).toBe("377824");

    const eventGroupByUrns =
      ResultsImportService.convertAllEntriesForCompToEventGroupUrns(data);

    const feederAllEntriesByEntryId2: IFeederAllEntriesByEntryId =
      eventGroupByUrns[22800];

    // expect(feederAllEntriesByEntryId2).toBe(8);
    expect(Object.keys(feederAllEntriesByEntryId2).length).toBe(3);

    const teams2: ITeamEntryFeeder[] = Object.values(
      feederAllEntriesByEntryId2
    ) as any as ITeamEntryFeeder[];
    expect(teams2.length).toBe(3);

    const team2: ITeamEntryFeeder = feederAllEntriesByEntryId2[
      keyForMap as any as number
    ] as any as ITeamEntryFeeder;
    expect(team2.teamName).toBe("Ratoath A.C. 4x100 Relay Female Under 9 {A}");
  });
});
