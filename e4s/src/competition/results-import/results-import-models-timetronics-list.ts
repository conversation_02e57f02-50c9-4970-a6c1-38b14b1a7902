import {
  ITimeTronicsEventBase,
  ITimeTronicsHeatBase,
  ITimeTronicsRoundBase,
} from "./results-import-models-timetronics";
import { IsoDate } from "../../common/common-models";

export interface ITimeTronicsEventList extends ITimeTronicsEventBase {
  rounds: ITimeTronicsRoundList[];
}

export interface ITimeTronicsRoundList extends ITimeTronicsRoundBase {
  combinedTotal: boolean;
  heats: ITimeTronicsHeatList[];
  participants_noheats: unknown[];
}

export interface ITimeTronicsHeatList extends ITimeTronicsHeatBase {
  numberofheights: number;
  participations: ITimeTronicsHeatListParticipation[];
}

export interface ITimeTronicsHeatListParticipation {
  category: {
    abbreviation: string;
    id: number;
    name: string;
  };
  currentorder: number; //  What is this?   if zero...we filter out?!?!?   In final results, it is zero!?!?
  currentorder_combinedevent: number;
  currentorder_round: number; //  this is overall position over all rounds.  Use this to find position
  //  over all rounds.
  id: number;
  info: string;
  initialorder: number;
  outofcompetition_combinedevent: number;
  participants: ITimeTronicsHeatListParticipant[];
  penalties: [];
  personalbest:
    | {
        alltimeflag: number;
        date: IsoDate;
        id: number;
        result_value: number; //  20.89
        seasonflag: number;
        value: string; //  "20.89";
        wind: number; //  0.0;
      }
    | {};
  points: number; //  0.0;
  qualified: "" | "q" | "Q";
  relay: boolean;
  results: ITimeTronicsHeatListResult[];
  teampoints: 0.0;
  teamname?: string; //  This indicates team
}

export interface ITimeTronicsHeatListParticipant {
  competitor: {
    athlete: ITimeTronicsHeatListAthlete;
    bib: string;
    competitionteam: {
      id?: number;
      name?: string;
    };
    displayname: string;
    id: number;
    license: string; //  URN
    team: ITimeTronicsHeatListTeam;
  };
  id: number;
  seqno: number;
}

export type TimeTronicsGender = "M" | "W" | "F";

export interface ITimeTronicsHeatListAthlete {
  birthdate: IsoDate;
  firstname: string;
  gender: TimeTronicsGender;
  id: number;
  lastname: string;
  middlename: string;
  nationality: string;
}

export interface ITimeTronicsHeatListTeam {
  abbreviation: string; //  "Raheny Shamrock A.C.";
  id: number;
  name: string; //  "Raheny Shamrock A.C.";
}

export interface ITimeTronicsHeatListTeam {
  abbreviation: string;
  id: number;
  name: string;
}

export interface ITimeTronicsHeatListResult {
  bestresult: boolean;
  id: number;
  info: string; //  "PHF0-3-35";
  metavalue: string;
  result_value: number; //  23.583;
  seqno: number;
  type: "PHF" | "MNL" | "TTB";
  value: string; //   "23.59";
  wind: number; //  0.0;
}
