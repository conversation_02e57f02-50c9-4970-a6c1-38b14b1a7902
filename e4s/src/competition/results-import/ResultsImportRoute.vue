<template>
  <ResultsImport
    :target-comp-id="compId"
    :source-comp-id="sourceCompId"
    id="qwsxcederfv"
    style="padding: var(--e4s-gap--standard)"
  />
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import ResultsImport from "./ResultsImport.vue";

@Component({
  name: "results-import-route",
  components: {
    ResultsImport,
  },
})
export default class EmailMessageGridRoute extends Vue {
  public compId: number = 0;
  public sourceCompId = 0;

  public created() {
    this.compId = isNaN(Number(this.$route.params.id))
      ? 0
      : parseInt(this.$route.params.id, 0);

    if (this.$route.query.sourceid) {
      this.sourceCompId = parseInt(this.$route.query.sourceid as any as string);
    }
  }
}
</script>
