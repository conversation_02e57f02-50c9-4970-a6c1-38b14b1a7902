<template>
  <!--  <div class="row" :class="getCheckInRowClass">-->
  <!--    <div class="col s4 m4 l4">-->
  <!--      <label>-->
  <!--        <input-->
  <!--          class="e4s-checkbox"-->
  <!--          type="checkbox"-->
  <!--          v-model="athleteEntryInternal.checkedIn"-->
  <!--          :disabled="isEventDisabled"-->
  <!--          v-on:change="entrySelected"-->
  <!--        />-->
  <!--        <span class="checkin&#45;&#45;events-label">-->
  <!--          <span-->
  <!--            class="checkin-user-entry&#45;&#45;start-time"-->
  <!--            v-text="getStartTime"-->
  <!--          ></span>-->
  <!--          <span :class="getCheckInClass">-->
  <!--            (<span-->
  <!--              class="checkin-user-entry&#45;&#45;start-time"-->
  <!--              v-text="getCheckInTimeRange"-->
  <!--            ></span-->
  <!--            >)-->
  <!--          </span>-->
  <!--        </span>-->
  <!--      </label>-->
  <!--    </div>-->
  <!--    <div class="col s4 m4 l4">-->
  <!--      <span-->
  <!--        class="checkin-user-entry&#45;&#45;event"-->
  <!--        v-text="athleteEntryInternal.name"-->
  <!--      ></span>-->
  <!--    </div>-->
  <!--    <div class="col s4 m4 l4">-->
  <!--      (<span-->
  <!--        class="checkin-user-entry&#45;&#45;age-group"-->
  <!--        v-text="athleteEntryInternal.ageGroup"-->
  <!--      ></span-->
  <!--      >)-->
  <!--    </div>-->
  <!--  </div>-->

  <div
    class="checkin-user-entry--row e4s-flex-center e4s-mobile-only-padding"
    :class="getCheckInRowClass"
  >
    <InputCheckboxV2
      :value="athleteEntryInternal.checkedIn"
      :disabled="isEventDisabled"
      v-on:input="checkInSelected"
    />
    <div class="e4s-flex-row e4s-gap--standard">
      <span class="checkin-user-entry--start-time" v-text="getStartTime"></span>
      <span :class="getCheckInClass">
        (<span
          class="checkin-user-entry--start-time"
          v-text="getCheckInTimeRange"
        ></span
        >)
      </span>
    </div>

    <span
      class="checkin-user-entry--event"
      v-text="athleteEntryInternal.name"
    ></span>

    <span
      class="checkin-user-entry--age-group"
      v-text="athleteEntryInternal.ageGroup"
    ></span>

    <ButtonGenericV2
      button-type="secondary"
      text="Options"
      @click="showOptions"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, computed, onMounted } from "@vue/composition-api";
import { CheckinService } from "../checkin-service";
import { IAthleteEntry, ICheckInAthleteEventStatus } from "../checkin-models";
import { format, parse } from "date-fns";
import InputCheckboxV2 from "../../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import ButtonGotoOptionsV2 from "../../../common/ui/layoutV2/buttons/button-goto-options-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { simpleClone } from "../../../common/common-service-utils";

const checkinService: CheckinService = new CheckinService();

export default defineComponent({
  name: "checkin-user-entry",
  components: { ButtonGenericV2, ButtonGotoOptionsV2, InputCheckboxV2 },
  props: {
    athleteEntry: {
      type: Object as () => IAthleteEntry,
      default: () => checkinService.factoryAthleteEntry()
    },
    isEventDisabled: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const athleteEntryInternal = ref<IAthleteEntry>(checkinService.factoryAthleteEntry());
    const checkInAthleteEventStatus = ref<ICheckInAthleteEventStatus | null>(null);

    const setCheckInAthleteEvent = () => {
      checkInAthleteEventStatus.value = checkinService.getCheckInAthleteEvent(
        props.athleteEntry,
        new Date()
      );
    };

    onMounted(() => {
      athleteEntryInternal.value = simpleClone(props.athleteEntry);
      setCheckInAthleteEvent();
    });

    watch(
      () => props.athleteEntry,
      (newValue: IAthleteEntry) => {
        athleteEntryInternal.value = simpleClone(newValue);
        setCheckInAthleteEvent();
      }
    );

    const getEntryName = computed(() => {
      const startTime = format(
        parse(athleteEntryInternal.value.dateTime),
        "hh:mma"
      );
      return (
        "<span class=''>" +
        startTime +
        "</span>" +
        "<span>" +
        athleteEntryInternal.value.ageGroup +
        "</span>" +
        " - <span>" +
        athleteEntryInternal.value.name +
        "</span>"
      );
    });

    const getStartTime = computed(() => {
      return format(parse(athleteEntryInternal.value.dateTime), "Do MMM hh:mma");
    });

    const getCheckInTimeRange = computed(() => {
      if (!checkInAthleteEventStatus.value) {
        return "";
      }
      return (
        format(
          parse(checkInAthleteEventStatus.value.checkInTimes.from),
          "hh:mma"
        ) +
        " - " +
        format(parse(checkInAthleteEventStatus.value.checkInTimes.to), "hh:mma")
      );
    });

    const getCheckInClass = computed(():
      | "checkin-user-entry--check-in-range-ok"
      | "checkin-user-entry--check-in-range-not-ok"
      | "" => {
      if (props.athleteEntry.checkedIn || !checkInAthleteEventStatus.value) {
        return "checkin-user-entry--check-in-range-ok";
      }
      return checkInAthleteEventStatus.value.isOK
        ? "checkin-user-entry--check-in-range-ok"
        : "checkin-user-entry--check-in-range-not-ok";
    });

    const getCheckInRowClass = computed(():
      | "checkin-user-entry--check-in-row-ok"
      | "checkin-user-entry--check-in-row-not-ok"
      | "" => {
      if (!checkInAthleteEventStatus.value) {
        return "";
      }
      if (props.athleteEntry.checkedIn) {
        return "checkin-user-entry--check-in-row-ok";
      }
      return checkInAthleteEventStatus.value.isOK
        ? "checkin-user-entry--check-in-row-ok"
        : "checkin-user-entry--check-in-row-not-ok";
    });

    const entrySelected = () => {
      emit("onChange", simpleClone(athleteEntryInternal.value));
    };

    const checkInSelected = (isChecked: boolean) => {
      athleteEntryInternal.value.checkedIn = isChecked;
      entrySelected();
    };

    const showOptions = () => {
      emit("showOptions", simpleClone(athleteEntryInternal.value));
    };

    return {
      athleteEntryInternal,
      checkInAthleteEventStatus,
      getEntryName,
      getStartTime,
      getCheckInTimeRange,
      getCheckInClass,
      getCheckInRowClass,
      entrySelected,
      checkInSelected,
      showOptions,
      setCheckInAthleteEvent
    };
  }
})
</script>

<style>
.checkin-user-entry--row {
  display: grid;
  gap: var(--e4s-gap--standard);
  grid-template-columns: 70px 2fr 1fr 1fr 1fr;
  padding-left: var(--e4s-gap--standard);
  padding-right: var(--e4s-gap--standard);
  align-items: center;
}

.checkin-user-entry--age-group {
}

.checkin-user-entry--event {
}

.checkin-user-entry--check-in-range-ok {
  color: grey;
}

.checkin-user-entry--check-in-range-not-ok {
  color: black;
}

.checkin-user-entry--check-in-row-ok {
  background-color: #daf7da;
}

.checkin-user-entry--check-in-row-not-ok {
  background-color: pink;
}
</style>
