<template>
  <div>

    <div class="row">

      <div class="input-field col s12 m12 l12">
        <p>
          <label>
            <input id="data-collected"
                   class="e4s-checkbox"
                   type="checkbox"
                   v-model="data.collected" />
            <span class="e4s-bold">
               Reset all collected bibs to not collected.
            </span>
          </label>
        </p>
      </div>

      <div class="input-field col s12 m6 l6">
        <p>
          <label>
            <input id="data-checkedIn"
                   class="e4s-checkbox"
                   type="checkbox"
                   v-on:change="data.all = false"
                   v-model="data.checkedIn" />
            <span class="e4s-bold">
               Reset checked in athletes.
            </span>
          </label>
        </p>
      </div>

      <div class="input-field col s12 m6 l6" v-if="data.checkedIn">
        <p>
          <label>
            <input id="data-all"
                   class="e4s-checkbox"
                   type="checkbox"
                   v-model="data.all" />
            <span class="e4s-bold">
              <span v-if="data.all">ALL checked in athletes.</span>
              <span v-if="!data.all">Athletes from NOW onwards.</span>
            </span>
          </label>
        </p>
      </div>

    </div>


    <div class="row">
      <div class="col s12 m12 l12">
        <div class="right">
          <LoadingSpinner v-if="clearDownIsLoading"></LoadingSpinner>
          <button class="btn waves-effect waves red"
                  :disabled="clearDownIsLoading"
                  v-on:click="cancel">
            Cancel
          </button>
          <button class="btn waves-effect waves green"
                  v-if="!showClearDownConf"
                  :disabled="clearDownIsLoading"
                  v-on:click="showClearDownConf = true">
            Process Clear Down
          </button>
          <button class="btn waves-effect waves green"
                  v-if="showClearDownConf"
                  :disabled="clearDownIsLoading"
                  v-on:click="submit">
            Are you sure?
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import Component from "vue-class-component";
  import Vue from "vue";
  import {mapState} from "vuex"
  import {CHECKIN_STORE_CONST, IClearDownPayload} from "../checkin-store"
  import {ICheckinCompSummary, ICheckInStoreState} from "../checkin-models"

  @Component({
    name: "checkin-cleardown",
    computed: {
      ...mapState(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME, {
        checkinSummary: (state: ICheckInStoreState) => state.checkinSummary,
        clearDownIsLoading: (state: ICheckInStoreState) => state.clearDownIsLoading
      })
    }
  })
  export default class CheckinClearDown extends Vue {
    public readonly checkinSummary: ICheckinCompSummary;
    public readonly clearDownIsLoading: boolean;

    public showClearDownConf: boolean = false;

    public data: IClearDownPayload = {
      collected: false,
      checkedIn: false,
      all: false
    }

    public created() {

    }

    public cancel() {
      this.$emit("onCancel");
    }

    public submit() {
      this.$store.dispatch(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" + CHECKIN_STORE_CONST.CHECKIN_ACTIONS_CLEAR_DOWN, {
        compId: this.checkinSummary.id,
        data: this.data
      })
          .then( () => {
            this.$emit("onSubmit");
          })
    }

  }

</script>
