import {CheckinService} from "./checkin-service"
import {ICheckinAthlete, ICheckinStats} from "./checkin-models"
import {parse} from "date-fns"

const checkinService: CheckinService = new CheckinService();

describe("CheckinService", () => {

    test("getFirstCheckInEvent", () => {

        let athleteCheckin: ICheckinAthlete = checkinService.factoryCheckinAthlete();

        athleteCheckin.entries.push(
            {
                id: 44,
                name: "",
                checkedIn: false,
                dateTime: "2020-06-28T10:00:00+01:00",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 1,
                name: "",
                checkedIn: false,
                dateTime: "2020-06-28T09:00:00+01:00",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 66,
                name: "",
                checkedIn: false,
                dateTime: "2020-06-28T11:00:00+01:00",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 67,
                name: "",
                checkedIn: false,
                dateTime: "",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            }
        )
        let res = checkinService.getFirstCheckInEvent(athleteCheckin);
        expect(res?.dateTime).toBe("2020-06-28T09:00:00+01:00");

        res = checkinService.getLastCheckInEvent(athleteCheckin);
        expect(res?.dateTime).toBe("2020-06-28T11:00:00+01:00");


        athleteCheckin = checkinService.factoryCheckinAthlete();
        athleteCheckin.entries.push(
            {
                id: 44,
                name: "",
                checkedIn: false,
                dateTime: "",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 1,
                name: "",
                checkedIn: false,
                dateTime: "",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 66,
                name: "",
                checkedIn: false,
                dateTime: "",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 67,
                name: "",
                checkedIn: false,
                dateTime: "",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            }
        )
        res = checkinService.getCheckInEvent(athleteCheckin, true);
        expect(res.id).toBe(0);

        res = checkinService.getLastCheckInEvent(athleteCheckin);
        expect(res.id).toBe(0);
    });

    test("getFirstCheckInEvent adjusted to and from", () => {

        let athleteCheckin: ICheckinAthlete = checkinService.factoryCheckinAthlete();

        athleteCheckin.entries.push(
            {
                id: 44,
                name: "",
                checkedIn: false,
                dateTime: "2020-06-28T10:00:00+01:00",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 1,
                name: "",
                checkedIn: false,
                dateTime: "2020-06-28T09:00:00+01:00",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 66,
                name: "",
                checkedIn: false,
                dateTime: "2020-06-28T11:00:00+01:00",
                ageGroup: "",
                checkInFrom: 180,
                checkInTo: 30
            },
            {
                id: 67,
                name: "",
                checkedIn: false,
                dateTime: "",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            }
        )
        let res = checkinService.getFirstCheckInEvent(athleteCheckin);
        expect(res?.dateTime).toBe("2020-06-28T11:00:00+01:00");

        res = checkinService.getLastCheckInEvent(athleteCheckin);
        expect(res?.dateTime).toBe("2020-06-28T10:00:00+01:00");


        athleteCheckin = checkinService.factoryCheckinAthlete();
        athleteCheckin.entries.push(
            {
                id: 44,
                name: "",
                checkedIn: false,
                dateTime: "",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 1,
                name: "",
                checkedIn: false,
                dateTime: "",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 66,
                name: "",
                checkedIn: false,
                dateTime: "",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 67,
                name: "",
                checkedIn: false,
                dateTime: "",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            }
        )
        res = checkinService.getCheckInEvent(athleteCheckin, true);
        expect(res.id).toBe(0);

        res = checkinService.getLastCheckInEvent(athleteCheckin);
        expect(res.id).toBe(0);
    });

    // test("isCheckInOpenForAthlete", () => {
    //
    //     const athleteCheckin: ICheckinAthlete = checkinService.factoryCheckinAthlete();
    //
    //     athleteCheckin.entries.push(
    //         {
    //             id: 44,
    //             name: "",
    //             checkedIn: false,
    //             dateTime: "2020-06-28T10:00:00+01:00",
    //             ageGroup: "",
    //             checkInFrom: -1,
    //             checkInTo: -1
    //         },
    //         {
    //             id: 1,
    //             name: "",
    //             checkedIn: false,
    //             dateTime: "2020-06-28T09:00:00+01:00",
    //             ageGroup: "",
    //             checkInFrom: -1,
    //             checkInTo: -1
    //         },
    //         {
    //             id: 66,
    //             name: "",
    //             checkedIn: false,
    //             dateTime: "2020-06-28T11:00:00+01:00",
    //             ageGroup: "",
    //             checkInFrom: -1,
    //             checkInTo: -1
    //         },
    //         {
    //             id: 67,
    //             name: "",
    //             checkedIn: false,
    //             dateTime: "",
    //             ageGroup: "",
    //             checkInFrom: -1,
    //             checkInTo: -1
    //         }
    //     )
    //     let pointInTime = parse("2020-06-28T08:00:00+01:00");
    //     let res = checkinService.isCheckInOpenForAthlete(athleteCheckin, pointInTime);
    //     expect(res).toBe(true);
    //
    //     pointInTime = parse("2020-06-28T09:00:00+01:00");
    //     res = checkinService.isCheckInOpenForAthlete(athleteCheckin, pointInTime);
    //     expect(res).toBe(false);
    // });

    // test("isCheckInOpenForAthlete adjusted", () => {
    //
    //     const athleteCheckin: ICheckinAthlete = checkinService.factoryCheckinAthlete();
    //
    //     athleteCheckin.entries.push(
    //         {
    //             id: 44,
    //             name: "",
    //             checkedIn: false,
    //             dateTime: "2020-06-28T10:00:00+01:00",
    //             ageGroup: "",
    //             checkInFrom: -1,
    //             checkInTo: -1
    //         },
    //         {
    //             id: 1,
    //             name: "",
    //             checkedIn: false,
    //             dateTime: "2020-06-28T09:00:00+01:00",
    //             ageGroup: "",
    //             checkInFrom: -1,
    //             checkInTo: -1
    //         },
    //         {
    //             id: 66,
    //             name: "",
    //             checkedIn: false,
    //             dateTime: "2020-06-28T11:00:00+01:00",
    //             ageGroup: "",
    //             checkInFrom: 180,       //  2020-06-28T08:00:00+01:00
    //             checkInTo: 30           //  2020-06-28T10:30:00+01:00
    //         },
    //         {
    //             id: 67,
    //             name: "",
    //             checkedIn: false,
    //             dateTime: "",
    //             ageGroup: "",
    //             checkInFrom: -1,
    //             checkInTo: -1
    //         }
    //     )
    //
    //     const checkInEvent = checkinService.getFirstCheckInEvent(athleteCheckin);
    //     expect(checkInEvent.id).toBe(66);
    //
    //     let res;
    //
    //     let pointInTime = parse("2020-06-28T08:01:00+01:00");
    //     // let res = checkinService.isCheckInOpenForAthlete(athleteCheckin, pointInTime);
    //     // expect(res).toBe(true);
    //
    //     const getCheckInTimesIso = checkinService.getCheckInTimesIso(checkInEvent);
    //     expect(getCheckInTimesIso.from).toBe("2020-06-28T08:00:00+01:00");
    //     expect(getCheckInTimesIso.to).toBe("2020-06-28T10:30:00+01:00");
    //
    //     expect(isAfter(pointInTime, parse(getCheckInTimesIso.from))).toBe(true);
    //
    //      // Too early
    //     pointInTime = parse("2020-06-28T07:01:00+01:00");
    //     res = checkinService.isCheckInOpenForAthlete(athleteCheckin, pointInTime);
    //     expect(res).toBe(false);
    //
    //     pointInTime = parse("2020-06-28T08:00:00+01:00");
    //     res = checkinService.isCheckInOpenForAthlete(athleteCheckin, pointInTime);
    //     expect(res).toBe(true);
    //
    //     pointInTime = parse("2020-06-28T10:00:00+01:00");
    //     res = checkinService.isCheckInOpenForAthlete(athleteCheckin, pointInTime);
    //     expect(res).toBe(true);
    //
    //     pointInTime = parse("2020-06-28T10:30:00+01:00");
    //     res = checkinService.isCheckInOpenForAthlete(athleteCheckin, pointInTime);
    //     expect(res).toBe(true);
    //
    //     pointInTime = parse("2020-06-28T10:31:00+01:00");
    //     res = checkinService.isCheckInOpenForAthlete(athleteCheckin, pointInTime);
    //     expect(res).toBe(false);
    //
    // });

    test("getAthleteCheckinDetails", () => {

        const athleteCheckin: ICheckinAthlete = checkinService.factoryCheckinAthlete();

        athleteCheckin.entries.push(
            {
                id: 44,
                name: "",
                checkedIn: false,
                dateTime: "2020-06-28T10:00:00+01:00",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 1,
                name: "",
                checkedIn: false,
                dateTime: "2020-06-28T09:00:00+01:00",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            },
            {
                id: 66,
                name: "",
                checkedIn: false,
                dateTime: "2020-06-28T11:00:00+01:00",
                ageGroup: "",
                checkInFrom: 180,       //  2020-06-28T08:00:00+01:00
                checkInTo: 30           //  2020-06-28T10:30:00+01:00
            },
            {
                id: 67,
                name: "",
                checkedIn: false,
                dateTime: "",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            }
        )
        let pointInTime;
        let details;

        pointInTime = parse("2020-06-28T07:31:00+01:00");



        details = checkinService.getAthleteCheckinDetails({defaultTo: 60} as ICheckinStats, athleteCheckin, pointInTime, null);
        expect(details.checkinEvent.id).toBe(66);
        expect(details.isCheckinOpen).toBe(false);
        expect(details.status).toBe("NOT_YET_OPEN");

        pointInTime = parse("2020-06-28T08:31:00+01:00");

        details = checkinService.getAthleteCheckinDetails({defaultTo: 60} as ICheckinStats, athleteCheckin, pointInTime, null);
        expect(details.checkinEvent.id).toBe(66);
        expect(details.isCheckinOpen).toBe(true);
        expect(details.status).toBe("OPEN");

        pointInTime = parse("2020-06-28T10:31:00+01:00");

        details = checkinService.getAthleteCheckinDetails({defaultTo: 60} as ICheckinStats, athleteCheckin, pointInTime, null);
        expect(details.checkinEvent.id).toBe(66);
        expect(details.isCheckinOpen).toBe(false);
        expect(details.status).toBe("CLOSED");


        pointInTime = parse("2020-06-28T07:31:00+01:00");

        details = checkinService.getAthleteCheckinDetails({defaultTo: 60} as ICheckinStats, athleteCheckin, pointInTime, parse("2020-06-28T06:31:00+01:00"));
        expect(details.checkinEvent.id).toBe(66);
        expect(details.isCheckinOpen).toBe(true);
        expect(details.status).toBe("OPEN");
        expect(details.checkInClosesIso).toBe("2020-06-28T10:30:00+01:00");

        pointInTime = parse("2020-06-28T07:31:00+01:00");

        details = checkinService.getAthleteCheckinDetails({defaultTo: -1} as ICheckinStats, athleteCheckin, pointInTime, parse("2020-06-28T06:31:00+01:00"));
        expect(details.checkinEvent.id).toBe(66);
        expect(details.isCheckinOpen).toBe(true);
        expect(details.status).toBe("OPEN");
        expect(details.checkInClosesIso).toBe("");

    });

    test("to and from = -1", () => {

        const athleteCheckin: ICheckinAthlete = checkinService.factoryCheckinAthlete();

        athleteCheckin.entries.push(
            {
                id: 44,
                name: "",
                checkedIn: false,
                dateTime: "2020-06-28T10:00:00+01:00",
                ageGroup: "",
                checkInFrom: -1,
                checkInTo: -1
            }
        )
        let pointInTime;
        let details;

        const checkinStats: ICheckinStats = {
            defaultFrom: 60,
            defaultTo: 30,
            checkInDateTimeOpens: "2020-06-28T07:00:00+01:00",
            collected: 0,
            expected: 100,
            terms: "",
            useTerms: false,
            text: "",
            codes: {}
        };

        pointInTime = parse("2020-06-28T09:10:00+01:00");

        details = checkinService.getAthleteCheckinDetails(checkinStats, athleteCheckin, pointInTime, null);
        expect(details.checkinEvent.id).toBe(44);
        expect(details.isCheckinOpen).toBe(true);
        expect(details.status).toBe("OPEN");

    });

});
