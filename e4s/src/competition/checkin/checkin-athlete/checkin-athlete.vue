<template>
    <div>

        <div v-show="!isLoaded">

            <div class="e4s-section-padding-separator"></div>

            <div class="row">
                <div class="col s12 m12 l12">
                    Loading...<LoadingSpinner></LoadingSpinner>
                </div>
            </div>
        </div>

        <div v-show="isLoaded">
            <div v-show="!showTermsAndConditions">
                <CollapseSection icon-name="accessibility"
                                 :allow-expand-collapse = "false"
                                 :header-message="'Athlete Check In for: ' + getHeaderTitle"
                                 :is-expanded="true">

                    <div slot="section-content">
                        <div class="row">
                            <div class="col s12 m12 l12">
                                <div v-text="getSearchMessage"></div>
                            </div>
                        </div>

                        <div class="e4s-section-padding-separator"></div>

                        <div class="row">
                            <div class="col s12 m12 l12">
                                <!--                        <a v-on:click.prevent="toggleSearchFields"><span v-text="getToggleSearchFieldsText"></span></a>-->
                                <button class="btn waves-effect waves green"
                                        :disabled="isLoading"
                                        v-on:click="toggleSearchFields">
                                    <span v-text="getToggleSearchFieldsText"></span>
                                </button>
                            </div>
                        </div>

                        <div class="e4s-section-padding-separator"></div>

                        <div v-show="shouldSearchFieldsBeShowing">
                            <div class="row">
                                <div class="input-field col s12 m12 l6">
                                    <input
                                            :id="PREFIX + 'first-name'"
                                            :name="PREFIX + 'first-name'"
                                            type="text"
                                            v-model="state.firstName"
                                            v-on:keyup.enter="search"
                                            placeholder=""/>
                                    <label class="active" :for="PREFIX + 'first-name'">
                                        First Name <span v-text="getSearchFieldMessage"></span>
                                        <FieldValidationLabel :validation-controller="validationController" :prop-path="'state.firstName'"/>
                                    </label>
                                </div>
                                <div class="input-field col s12 m12 l6">
                                    <input
                                            :id="PREFIX + 'sur-name'"
                                            :name="PREFIX + 'sur-name'"
                                            type="text"
                                            v-model="state.surName"
                                            v-on:keyup.enter="search"
                                            placeholder=""/>
                                    <label class="active" :for="PREFIX + 'sur-name'">
                                        Last Name <span v-text="getSearchFieldMessage"></span>
                                        <FieldValidationLabel :validation-controller="validationController" :prop-path="'state.surName'"/>
                                    </label>
                                </div>
                            </div>

                            <div class="row">
                                <div class="input-field col s12 m12 l6">
                                    <input
                                            :id="PREFIX + 'urn'"
                                            :name="PREFIX + 'urn'"
                                            type="text"
                                            v-model="state.urn"
                                            v-on:keyup.enter="search"
                                            placeholder=""/>
                                    <label class="active" :for="PREFIX + 'urn'">
                                        URN (Athlete's Association ID)
                                    </label>
                                </div>
                                <div class="input-field col s12 m12 l6">
                                    <DateEntryMat v-on:onSelected="setDob"></DateEntryMat>
                                    <label class="active" :for="PREFIX + 'dob'">
                                        Date Of Birth
                                    </label>
                                </div>
                            </div>

                            <div class="row">

                                <div class="col s12 m12 l6" v-if="isLoggedIn && userHasEntities">
                                    <label class="active" :for="PREFIX + 'entity'">
                                        Checking in for entity
                                    </label>
                                    <EntityDropDown  :id="PREFIX + 'entity'"
                                                     v-on:onSelected="onEntitySelected">
                                    </EntityDropDown>
                                </div>

                            </div>

                        </div>

                        <div class="row">

                            <div class="input-field col s12 m12 l6">
                                <label class="active" :for="PREFIX + 'date'">
                                    Date
                                </label>
                                <select v-model="state.date"
                                        v-on:change="search"
                                        class="browser-default">
                                    <option v-for="d in checkinSummary.dates" :value="d">
                                        <span v-text="d"></span>
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col s12 m12 l12">
                                <div class="right">
                                    <LoadingSpinner v-if="isLoading"></LoadingSpinner>
                                    <button class="btn waves-effect waves red" :disabled="isLoading" v-on:click="reset">Reset</button>
                                    <button class="btn waves-effect waves green" :disabled="isLoading" v-on:click="search">Search</button>
                                </div>
                            </div>
                        </div>

                    </div>

                </CollapseSection>


                <div class="row" v-if="athleteCheckin.length === 0">
                    <div class="col s12 m12 l12">
                        <div class="e4s-section-padding-separator"></div>
<!--                        <div v-text="message"></div>-->
                        No results Found
                        <div class="e4s-section-padding-separator"></div>
                    </div>
                </div>

                <div class="row" v-if="athleteCheckin.length > 0">
                    <div class="col s12 m12 l12">
                        <div class="e4s-section-padding-separator"></div>
                        Please verify which events your athletes are taking part in by ticking the box next to each
                        event, then click "Save" button.  Proceed to Registrations to pick up
                        your bib number and they will then confirm your entries.
                        <div class="e4s-section-padding-separator"></div>
                    </div>
                </div>

                <!--        isLoggedIn{{isLoggedIn}}-->

<!--                <CheckInUserGrid :checkin-summary="checkinSummary"-->
<!--                                 :athlete-checkin="athleteCheckin"-->
<!--                                 :disable-bib-collected="true"-->
<!--                                 :is-organiser="false"-->
<!--                                 v-on:onChange="onChangeAthleteCheckIn"-->
<!--                                 v-on:onSubmit="onSubmitAthleteCheckIn">-->
<!--                </CheckInUserGrid>-->
                <div v-for="athlete in athleteCheckin"
                     class="checkin--athlete"
                     :key="athlete.id">
                    <CheckinUser :athlete-checkin="athlete"
                                 :disable-bib-collected="true"
                                 :is-organiser="false"
                                 :checkin-comp-summary="checkinSummary"
                                 :is-loading="isLoading"
                                 :common-service="commonService"
                                 v-on:onChange="onChangeAthleteCheckIn"
                                 v-on:onSubmit="onSubmitAthleteCheckIn">
                    </CheckinUser>
                </div>
            </div>

            <div v-show="showTermsAndConditions">

                <div class="e4s-section-padding-separator"></div>

                <div class="row">
                    <div class="col s12 m12 l12">
                        This competition has terms and conditions applied to checking in.
                    </div>
                </div>

                <div class="e4s-section-padding-separator"></div>

                <div class="row">
                    <div class="col s12 m12 l12">
                        <div v-html="checkinSummary.checkIn.terms"></div>
                    </div>
                </div>

                <div class="row">
                    <div class="col s12 m12 l12">
                        <div class="right">
                            <button class="btn waves-effect waves red" v-on:click="cancelTermsAndConditions">Cancel</button>
                            <button class="btn waves-effect waves green" v-on:click="doTermsAndConditionsAccepted">I Agree</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Component from "vue-class-component";
    import Vue from "vue";
    import CollapseSection from "../../../common/ui/collapse/collapse-section.vue";
    import CheckinUser from "../checkin-organiser/checkin-user.vue";
    import FieldValidationLabel from "../../../validation/validation-field-lable.vue";
    import FieldHelp from "../../../common/ui/field/field-help/field-help.vue";
    import { ICheckinCompSummary, ICheckinAthlete, ICheckInStoreState } from "../checkin-models";
    import { IGetCheckinSearchParams, BIB_COLLECTED_STATUS } from "../checkin-data";
    import { ValidationController } from "../../../validation/validation-controller";
    import { CHECKIN_STORE_CONST } from "../checkin-store";
    // import { format } from "date-fns";
    import { mapGetters, mapState } from "vuex";
    import {CONFIG_STORE_CONST, IConfigStoreState} from "../../../config/config-store";
    import DateEntryMat from "../../../common/ui/datetime/date-entry-mat.vue";
    import {IConfigApp, IEntity, IUserInfo} from "../../../config/config-app-models";
    import EntityDropDown from "../../../builder/form/entity/entity-drop-down.vue";
    import {ConfigService} from "../../../config/config-service";
    import {AUTH_STORE_CONST, IAuthStoreState} from "../../../auth/auth-store";
    import {CheckinService} from "../checkin-service";
    import {LAUNCH_ROUTES_PATHS} from "../../../launch/launch-routes"
    import {CommonService} from "../../../common/common-service"

    @Component({
        name: "checkin-athlete",
        components: {
            EntityDropDown,
            DateEntryMat,
            CollapseSection,
            CheckinUser,
            FieldValidationLabel,
            FieldHelp
        },
        computed: {
            ...mapGetters(
                {
                    isAdmin: CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME + "/" + CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
                    userEntities: CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME + "/" + CONFIG_STORE_CONST.CONFIG_GETTERS_USER_ENTITIES,
                    getHeaderTitle: CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" + CHECKIN_STORE_CONST.CHECKIN_GETTERS_TITLE_HEADER
                }
            ),
            ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
                configApp: (state: IConfigStoreState) => state.configApp,
                userInfo: (state: IConfigStoreState) => state.configApp.userInfo,
                userEntity: (state: IConfigStoreState) => state.userEntity
            }),
            ...mapState(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME, {
                checkInStoreState: (state: ICheckInStoreState) => state,
                checkinSummary: (state: ICheckInStoreState) => state.checkinSummary,
                athleteCheckin: (state: ICheckInStoreState) => state.athleteCheckin,
                isLoading: (state: ICheckInStoreState) => state.isLoading,
                message: (state: ICheckInStoreState) => state.message
            }),
            ...mapState(AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME, {
                isLoggedIn: (state: IAuthStoreState) => state.isLoggedIn
            }),
        }
    })
    export default class CheckinAthlete extends Vue {
        public readonly checkinSummary: ICheckinCompSummary;
        public readonly athleteCheckin: ICheckinAthlete[];
        public readonly isLoading: boolean;
        public readonly message: string;

        public readonly checkInStoreState: ICheckInStoreState;

        public readonly userEntities: IEntity[];
        public readonly configApp: IConfigApp;
        public readonly userInfo: IUserInfo;
        public readonly userEntity: IEntity;
        public readonly isLoggedIn: boolean;

        public athleteCheckinInternal: ICheckinAthlete[] = [];

        public configService: ConfigService = new ConfigService();
        public entity: IEntity = this.configService.factoryEntity();
        public checkinService: CheckinService = new CheckinService();
        public commonService = new CommonService();

        public shouldSearchFieldsBeShowing: boolean = false;

        public showTermsAndConditions: boolean = false;
        public termsAndConditionsAccepted: boolean = false;
        public athleteToCheckIn: ICheckinAthlete = this.checkinService.factoryCheckinAthlete();

        public state = {
            compId: 0,
            firstName: "",
            surName: "",
            club: "",
            date: "",
            urn: "",
            bibNo: "",
            dob: "",
            showSaveAllConfirm: false
        };
        public PREFIX = Math.random().toString(36).substring(2);
        public validationController: ValidationController = new ValidationController();

        public isLoaded: boolean = false;

        public created() {
            const compId = isNaN(Number(this.$route.params.id)) ? 0 : parseInt(this.$route.params.id, 0);
            this.state.compId = isNaN(Number(this.$route.params.id)) ? 0 : parseInt(this.$route.params.id, 0);
            console.log("CheckinAthlete.created() this.state.compId: " + this.state.compId);

            //  reset "grid"
            this.$store.commit(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" + CHECKIN_STORE_CONST.CHECKIN_MUTATIONS_RESET_CHECKIN_ATHLETES);

            if (this.state.compId > 0) {
                this.$store.dispatch(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" +
                    CHECKIN_STORE_CONST.CHECKIN_ACTIONS_GET_SUMMARY, compId)
                    .then( () => {

                        this.isLoaded = true;

                        if (this.getIsTermsAndConditionsBeingUsed) {
                            this.showTermsAndConditions = true;
                        }

                        if (this.isLoggedIn) {
                            //  User has browsed to here, and/or been to home page logged in.
                            if (this.userEntities.length === 0) {
                                //  Only do for "parents", etc.  Aisling etc. need to select entity first.
                                this.search(true);
                            }
                        }
                        if (!this.isLoggedIn) {
                            //  user has arrived here from email link or copied url from print out.
                            if (this.checkInStoreState.nonce.length > 0) {
                                this.search(true);
                            } else {
                                this.shouldSearchFieldsBeShowing = true;
                            }
                        }
                      
                        const getDate = this.commonService.getNextDateInSequence(this.checkinSummary.dates, "");
                        this.state.date = getDate.length === 0 ? this.checkinSummary.dates[0] : getDate;
                    })
            }

            // this.state.date = format(new Date(), "YYYY-MM-DD");
            // this.state.date = "2020-05-28";
        }

        public getState() {
            return {
                compId: this.checkinSummary.id,
                firstName: "",
                surName: "",
                club: "",
                date: "",
                urn: "",
                bibNo: "",
                dob: "",
                showSaveAllConfirm: false
            };
        }

        public setDob(isoDatetime: string) {
            this.state.dob = isoDatetime.indexOf("T") > -1 ? isoDatetime.split("T")[0] : "";
        }

        public reset() {
            const stateNew = this.getState();
            stateNew.date = this.checkinSummary.dates[0];
            this.state = stateNew;
        }

        public getSearchPayload(): IGetCheckinSearchParams {
            return {
                compId: this.state.compId,
                isoDate: this.state.date,
                firstName: this.state.firstName,
                lastName: this.state.surName,
                club: this.state.club,
                entity: this.entity,
                urn: this.state.urn,
                bibNo: this.state.bibNo,
                dob: this.state.dob,
                eventName: "",
                pageNumber: 1,
                pageSize: 100,
                collected: BIB_COLLECTED_STATUS.ALL,
                nonce: this.checkInStoreState.nonce,
                isOrganiser: false,
                whois: false
            };
        }

        public search(emptyListData: boolean) {

            if (emptyListData) {
                this.$store.commit(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" +
                    CHECKIN_STORE_CONST.CHECKIN_MUTATIONS_SET_CHECKIN_ATHLETES, []);
            }

            this.$store.dispatch(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" +
                CHECKIN_STORE_CONST.CHECKIN_ACTIONS_SEARCH, this.getSearchPayload());
        }

        public onChangeAthleteCheckIn(athleteCheckIn: ICheckinAthlete) {
            const athleteCheckins = R.clone(this.athleteCheckinInternal);
            const indexAthlete = athleteCheckins.reduce((accum, checkinAthlete, index) => {
                if (checkinAthlete.bibNo === athleteCheckIn.bibNo) {
                    return index;
                }
                return accum;
            }, -1);
            console.log("indexAthlete: " + indexAthlete);
            if (indexAthlete > -1) {
                athleteCheckins[indexAthlete] = athleteCheckIn;
            }
            this.athleteCheckinInternal = athleteCheckins;
        }

        public doTermsAndConditionsAccepted() {
            this.termsAndConditionsAccepted = true;
            this.showTermsAndConditions = false;
        }

        public cancelTermsAndConditions() {
            this.$router.push({
                path: "/" + LAUNCH_ROUTES_PATHS.SHOW_ENTRIES
            });
        }

        public onSubmitAthleteCheckIn(athleteCheckIn: ICheckinAthlete) {
            this.athleteToCheckIn = athleteCheckIn;
            if (this.getIsTermsAndConditionsBeingUsed && !this.termsAndConditionsAccepted) {
                this.showTermsAndConditions = true;
                return;
            }
            this.onSubmitCheckIn();
            // this.onSubmitCheckIn([athleteCheckIn]);
        }

        public onSubmitCheckIn() {

            this.$store.dispatch(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" +
                CHECKIN_STORE_CONST.CHECKIN_ACTIONS_SUBMIT,
                {
                    compId: this.state.compId,
                    athleteCheckIns: [this.athleteToCheckIn],
                    isAthlete: true
                }
            )
            .then(() => {
                this.search(false);
            })
        }

        public onEntitySelected(entity: IEntity) {
            this.entity = entity;
        }

        public get getSearchMessage() {
            if (!this.isLoggedIn) {
                return "Login and we will retrieve the records for you, this will especially help if you are checking " +
                    "multiple people in.  If you don't login, to find and confirm your " +
                    "record please enter: your First and Last Name, then either URN or Date of Birth.";
            }
            if (this.isLoggedIn && this.userEntities.length === 0) {
                return "We will try and retrieve records for you. You can also find and confirm your " +
                    "record, please enter: your First and Last Name, then either URN or Date of Birth.";
            }
            if (this.isLoggedIn && this.userEntities.length > 0) {
                return "Select the entity you are checking in. You can also find and confirm your " +
                    "record, please enter: your First and Last Name, then either URN or Date of Birth.";
            }
            return "";
        }

        public get userHasEntities(): boolean {
            return this.userEntities.length > 0;
        }

        public get getToggleSearchFieldsText() {
            return ( this.shouldSearchFieldsBeShowing ? "Hide" : "Show" ) + " Search Fields";
        }

        public toggleSearchFields() {
            this.shouldSearchFieldsBeShowing = !this.shouldSearchFieldsBeShowing;
        }

        public get getSearchFieldMessage() {
            return this.entity.id > 0 ? " (Partial search values allowed)" : " (You must enter the exact value)";
        }

        public get getIsTermsAndConditionsBeingUsed() {
            return this.checkinSummary.checkIn.terms.length > 0;
        }
    }
</script>
