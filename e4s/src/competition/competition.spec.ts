import {
  ICompetitionInfo,
  ICompetitionSummaryPublic,
} from "../competition/competition-models";
import { CompetitionService } from "./competiton-service";
import { parse } from "date-fns";

const competitionService = new CompetitionService();

describe("Entry", () => {
  test("displayCompetitionFlyer", () => {
    let competition: ICompetitionInfo;

    competition = {
      link: "blah",
    } as ICompetitionInfo;
    expect(competitionService.displayCompetitionFlyer(competition)).toBe(true);

    competition = {
      link: "",
    } as ICompetitionInfo;
    expect(competitionService.displayCompetitionFlyer(competition)).toBe(false);
  });

  test("displayAddAthleteButton", () => {
    let competition: ICompetitionInfo;

    competition = {
      link: "blah",
      options: {
        allowAdd: {
          registered: true,
          unregistered: true,
        },
      },
    } as ICompetitionInfo;
    expect(competitionService.displayAddAthleteButton(competition)).toBe(true);

    competition = {
      link: "blah",
      options: {
        allowAdd: {
          registered: false,
          unregistered: false,
        },
      },
    } as ICompetitionInfo;
    expect(competitionService.displayAddAthleteButton(competition)).toBe(false);

    competition = {
      link: "blah",
      options: {
        allowAdd: {
          registered: true,
          unregistered: false,
        },
      },
    } as ICompetitionInfo;
    expect(competitionService.displayAddAthleteButton(competition)).toBe(true);
  });

  test("canAddRegisteredUsers", () => {
    let competition: ICompetitionInfo;

    competition = {
      link: "blah",
      options: {
        allowAdd: {
          registered: false,
          unregistered: true,
        },
      },
    } as ICompetitionInfo;
    expect(competitionService.canAddRegisteredUsers(competition)).toBe(false);
    expect(competitionService.canAddUnregisteredUsers(competition)).toBe(true);

    competition = {
      link: "blah",
      options: {
        allowAdd: {
          registered: true,
          unregistered: false,
        },
      },
    } as ICompetitionInfo;
    expect(competitionService.canAddRegisteredUsers(competition)).toBe(true);
    expect(competitionService.canAddUnregisteredUsers(competition)).toBe(false);
  });

  test("isCancelStillAvailable", () => {
    const competitionSummaryPublic: ICompetitionSummaryPublic =
      competitionService.factorySummaryPublic();

    competitionSummaryPublic.closedate = "2021-03-14T17:00:00+00:00";
    competitionSummaryPublic.options.cancelEvent.hrsBeforeClose = 2;

    expect(
      competitionService.isCancelStillAvailable(
        competitionSummaryPublic,
        parse("2021-03-14T13:00:00+00:00")
      )
    ).toBe(true);
    expect(
      competitionService.isCancelStillAvailable(
        competitionSummaryPublic,
        parse("2021-03-14T16:00:00+00:00")
      )
    ).toBe(false);
  });

  test("isLive", () => {
    let competition: ICompetitionInfo;

    competition = {
      active: true,
      link: "blah",
      options: {
        allowAdd: {
          registered: false,
          unregistered: true,
        },
      },
    } as ICompetitionInfo;
    expect(competitionService.isLive(competition)).toBe(true);

    competition = {
      active: true,
      link: "blah",
      options: {
        allowAdd: {
          registered: false,
          unregistered: true,
        },
        // live: true
      },
    } as ICompetitionInfo;
    expect(competitionService.isLive(competition)).toBe(true);

    competition = {
      active: false,
      link: "blah",
      options: {
        allowAdd: {
          registered: false,
          unregistered: true,
        },
        // live: false
      },
    } as ICompetitionInfo;
    expect(competitionService.isLive(competition)).toBe(false);
  });

  // test("showReportLink", () => {
  //     let competition: ICompetitionSummaryPublic;
  //
  //     competition = {
  //         reportLink: ""
  //     } as ICompetitionSummaryPublic;
  //     expect(competitionService.showReportLink(competition)).toBe(false);
  // });

  // test("showReportLink", () => {
  //     let competition: ICompetitionSummary;
  //
  //     competition = {
  //     } as ICompetitionSummary;
  //     expect(competitionService.hasContactInfo(competition)).toBe(false);
  //
  //     competition = {
  //         options: {}
  //     } as ICompetitionSummary;
  //     expect(competitionService.hasContactInfo(competition)).toBe(false);
  //
  //     competition = {
  //         options: {
  //             contact: {
  //                 id: 7
  //             }
  //         }
  //     } as ICompetitionSummary;
  //     expect(competitionService.hasContactInfo(competition)).toBe(false);
  //
  //     competition = {
  //         options: {
  //             contact: {
  //                 id: 7,
  //                 email: "<EMAIL>"
  //             }
  //         }
  //     } as ICompetitionSummary;
  //     expect(competitionService.hasContactInfo(competition)).toBe(true);
  //
  //     competition = {
  //         options: {
  //             contact: {
  //                 id: 7,
  //                 userName: "joe"
  //             }
  //         }
  //     } as ICompetitionSummary;
  //     expect(competitionService.hasContactInfo(competition)).toBe(true);
  //
  //     competition = {
  //         options: {
  //             contact: {
  //                 id: 7,
  //                 tel: "019991919"
  //             }
  //         }
  //     } as ICompetitionSummary;
  //     expect(competitionService.hasContactInfo(competition)).toBe(true);
  // });

  test("filterCompetitions", () => {
    const comps: ICompetitionSummaryPublic[] = [
      {
        compId: 1,
        compName: "a",
        club: "9999",
        location: {
          name: "loc a",
        },
      } as ICompetitionSummaryPublic,
      {
        compId: 2,
        compName: "b",
        club: "9999",
        location: {
          name: "loc shared",
        },
      } as ICompetitionSummaryPublic,
      {
        compId: 3,
        compName: "c",
        club: "9999",
        location: {
          name: "loc shared",
        },
      } as ICompetitionSummaryPublic,
      {
        compId: 4,
        compName: "d",
        club: "8888",
        location: {
          name: "loc d",
        },
      } as ICompetitionSummaryPublic,
      {
        compId: 5,
        compName: "e",
        club: "8888",
        location: {
          name: "loc e",
        },
      } as ICompetitionSummaryPublic,
    ];

    expect(competitionService.filterPublicCompetitions("1", comps).length).toBe(
      1
    );
    expect(competitionService.filterPublicCompetitions("b", comps).length).toBe(
      1
    );
    expect(
      competitionService.filterPublicCompetitions("loc d", comps).length
    ).toBe(1);
    expect(
      competitionService.filterPublicCompetitions("loc shared", comps).length
    ).toBe(2);
    expect(
      competitionService.filterPublicCompetitions("88", comps).length
    ).toBe(2);
  });
});
