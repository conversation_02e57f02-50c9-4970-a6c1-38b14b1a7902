<template>
    <div>
        <EventGroupAthleteGrid :comp-event-group-athletes="compEventGroupAthletes"></EventGroupAthleteGrid>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import EventGroupAthleteGrid from "./event-group-athlete-grid.vue";
import {ICompEventGroupAthletes} from "../../athlete/athlete-models";
import {AthleteService} from "../../athlete/athlete-service";
import {AthleteData} from "../../athlete/athlete-data";

@Component({
    name: "event-group-athlete-grid-route",
    components: {
        EventGroupAthleteGrid
    }
})
export default class EventGroupAthleteGridRoute extends Vue {
    public athleteService = new AthleteService();
    public athleteData = new AthleteData();
    public isLoading = false;

    public id: number = 0;
    public compEventGroupAthletes: ICompEventGroupAthletes = this.athleteService.factoryCompEventGroupAthletes();

    public created() {
        const id: number = isNaN(Number(this.$route.params.id)) ? 0 : parseInt(this.$route.params.id, 0);
        this.id = id;
        this.isLoading = true;
        this.athleteData.getAthletesInEventGroup(id)
        .then( (resp) => {
            if (resp.errNo === 0) {
                this.compEventGroupAthletes = resp.data;
            }
        })
        .finally(() => {
            this.isLoading = false;
        })
    }
}
</script>
