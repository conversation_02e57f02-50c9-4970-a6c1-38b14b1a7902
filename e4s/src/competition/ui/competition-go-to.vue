<template>
  <!--  <a :href="getPath"> Comp Admin!! </a>-->
  <!--  <PrimaryLink :link="getPath" :link-text="linkText" />-->
  <PrimaryHref :link="getPath" :link-text="linkText" />
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { LAUNCH_ROUTES_PATHS } from "../../launch/launch-routes";
import PrimaryHref from "../../common/ui/layoutV2/href/PrimaryHref.vue";

@Component({
  name: "competition-go-to",
  components: { PrimaryHref },
})
export default class CompetitionGoTo extends Vue {
  @Prop({
    default: 0,
  })
  public readonly compId!: number;

  @Prop({
    default: "Go To Comp Overview",
  })
  public readonly linkText!: string;

  public get getPath() {
    return "#/" + LAUNCH_ROUTES_PATHS.SHOW_ENTRY + "/" + this.compId;
  }
}
</script>
