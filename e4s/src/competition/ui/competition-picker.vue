<template>
  <div>
    Comp Picker
    <!--        <LoadingSpinner v-if="isLoading"></LoadingSpinner>-->
    <LoadingSpinnerV2 v-if="isLoading" />
    <select
      v-model="selectedValue"
      class="browser-default e4s-select"
      v-on:change="onChange"
    >
      <option v-for="comp in data" :value="comp">
        <span v-text="competitionService.getCompetitionTitle(comp)"></span>
      </option>
    </select>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
} from "@vue/composition-api";
import { CompetitionData } from "../competition-data";
import { ICompetitionSummaryPublic } from "../competition-models";
import { CompetitionService } from "../competiton-service";
// import { useHttpResponseController } from "../../common/handle-http-reponse";
import { IServerResponseList } from "../../common/common-models";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";
// import { useHttpResponseControllerReactive } from "../../common/useHttpController";

export default defineComponent({
  name: "competition-picker",
  components: { LoadingSpinnerV2 },
  props: {
    value: {},
    promiseGetComps: {
      type: Function as PropType<Promise<
        IServerResponseList<ICompetitionSummaryPublic>
      > | null>,
      default: null,
    },
    filter: {
      type: Function as PropType<
        (comps: ICompetitionSummaryPublic[]) => ICompetitionSummaryPublic[]
      >,
      default: (comps: ICompetitionSummaryPublic[]) => {
        // return function(comps: ICompetitionSummaryPublic[]): ICompetitionSummaryPublic[] {
        //   return comps;
        // }
        return comps;
      },
    },
  },
  setup(
    props: {
      filter: (
        comps: ICompetitionSummaryPublic[]
      ) => ICompetitionSummaryPublic[];
      promiseGetComps: Promise<
        IServerResponseList<ICompetitionSummaryPublic>
      > | null;
    },
    context: SetupContext
  ) {
    const competitionService = new CompetitionService();
    const isLoading = ref(false);
    let data = ref<ICompetitionSummaryPublic[]>([]);

    const selectedValue = ref<ICompetitionSummaryPublic>(
      competitionService.factorySummaryPublic()
    );

    console.log("props =================" + typeof props.promiseGetComps);
    let promGetComps = new CompetitionData().getAllComps;
    if (props.promiseGetComps) {
      //  @ts-ignore
      promGetComps = props.promiseGetComps;
    }

    isLoading.value = true;
    promGetComps()
      .then((resp) => {
        if (resp) {
          let dataServer = resp.data;
          dataServer = props.filter(dataServer);
          data.value = dataServer;
        }
      })
      .finally(() => {
        isLoading.value = false;
      });

    ///////////////////////

    // const responseController = useHttpResponseControllerReactive([]);

    // responseController.getData(promGetComps()).then((resp) => {
    // if (resp) {
    // let dataServer = resp.data;
    // dataServer = props.filter(dataServer);
    // data.value = dataServer;
    /*

        data.value = dataServer.filter((comp: ICompetitionSummaryPublic) => {
          //  TODO  we need an api that is "giveMeOpenCalls"
          //  @ts-ignore
          return comp.daysToComp >= 0;
        });
        */
    // }
    // });

    //////////////////////

    // const responseController = useHttpResponseController<
    //   ICompetitionSummaryPublic[]
    // >([]);
    // responseController.getData(promGetComps()).then((resp) => {
    //   if (responseController.data) {
    //     let dataServer = responseController.data;
    //     dataServer = props.filter(dataServer);
    //
    //     data.value = dataServer;
    //     /*
    //     data.value = dataServer.filter((comp: ICompetitionSummaryPublic) => {
    //       //  TODO  we need an api that is "giveMeOpenCalls"
    //       //  @ts-ignore
    //       return comp.daysToComp >= 0;
    //     });
    //     */
    //   }
    // });

    // const isLoading = ref(responseController.isLoading);
    // const data = ref<ICompetitionSummaryPublic[]>(
    //   responseController.data as ICompetitionSummaryPublic[]
    // );

    function onChange() {
      context.emit("input", selectedValue.value);
    }

    return {
      isLoading,
      selectedValue,
      data,
      competitionService,
      onChange,
    };
  },
});
</script>
