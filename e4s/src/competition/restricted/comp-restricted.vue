<template>
  <div>
    <div class="row">
      <div class="col s12">
        This competition is currently restricted to athletes from the following
        club<span v-text="athleteSecurity.clubs.length > 1 ? 's' : ''"></span>:

        <!--                <div v-if="athleteSecurity.clubs.length > maxRestrictedClubsDisplay">-->
        <!--                    <a href="#"-->
        <!--                       v-on:click.prevent="showAllRestrictedClubs = !showAllRestrictedClubs">-->
        <!--                        <span v-text="showAllRestrictedClubs ? 'Hide clubs...' : 'Show clubs...'"></span>-->
        <!--                    </a>-->
        <!--                </div>-->

        <li
          v-for="club in restrictedClubs"
          :key="club.id"
          v-text="club.name"
        ></li>
        <div class="e4s-section-padding-separator"></div>
        <div v-text="getCutOffDateTime"></div>
        Athlete's club or second claim club must be one of the above, please
        ensure athlete profiles are updated accordingly.
        <div v-if="athlete.id > 0">
          <!--                    <div class="e4s-section-padding-separator"></div>-->
          <!--                    If this is the athlete's club or second claim club, please ensure athlete profile is updated accordingly:-->
          <a :href="getAthleteProfileLink"
            >Edit <span v-text="athlete.firstName"></span>'s profile</a
          >.
        </div>
        <div class="e4s-section-padding-separator"></div>
        All of your athlete profiles can be found under
        <a :href="getMyAccountAthletes">My Account\ Athletes</a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { IAthleteSecurity } from "../../builder/builder-models";
import { IBase } from "../../common/common-models";
import { IAthlete } from "../../athlete/athlete-models";
import { AthleteService } from "../../athlete/athlete-service";
import { LAUNCH_ROUTES_PATHS } from "../../launch/launch-routes";
import { format, parse } from "date-fns";
import { simpleClone } from "../../common/common-service-utils";

@Component({
  name: "comp-restricted",
})
export default class CompRestricted extends Vue {
  @Prop({
    default: () => {
      const athleteSecurity: IAthleteSecurity = {
        areas: [],
        clubs: [],
        onlyClubsUpTo: "",
      };
      return athleteSecurity;
    },
  })
  public readonly athleteSecurity: IAthleteSecurity;

  @Prop({
    default: () => {
      return new AthleteService().factoryGetAthlete();
    },
  })
  public readonly athlete!: IAthlete;

  public maxRestrictedClubsDisplay: number = 2;
  public showAllRestrictedClubs = false;

  public restrictedClubs: IBase[] = [];

  public created() {
    this.restrictedClubs = this.getRestrictedClubsAll();
  }

  @Watch("athleteSecurity")
  public onAthleteSecurityChanged(newValue: IAthleteSecurity) {
    this.restrictedClubs = this.getRestrictedClubsAll();
  }

  public getRestrictedClubsAll(): IBase[] {
    const clubs = simpleClone(this.athleteSecurity.clubs);
    return clubs.sort((a: IBase, b: IBase) => {
      if (a.name && b.name) {
        return a.name.localeCompare(b.name);
      }
      return 1;
    });
  }

  public get getAthleteProfileLink(): string {
    return "#/" + LAUNCH_ROUTES_PATHS.ATHLETE + "/" + this.athlete.id;
  }

  public get getMyAccountAthletes(): string {
    return "#/" + LAUNCH_ROUTES_PATHS.ATHLETES;
  }

  public get getCutOffDateTime(): string {
    if (
      this.athleteSecurity.onlyClubsUpTo &&
      this.athleteSecurity.onlyClubsUpTo.length > 0
    ) {
      const onlyClubsUpTo = format(
        parse(this.athleteSecurity.onlyClubsUpTo),
        "Do MMM HH:mm"
      );
      return " Restriction Ends: " + onlyClubsUpTo;
    }
    //  No date set, so comp completely locked down to these clubs.
    return "";
  }
}
</script>
