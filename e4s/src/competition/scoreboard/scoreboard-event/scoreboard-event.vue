<template>
    <div class="scoreboard--score-wrapper">

        <div class="scoreboard--score-sep"></div>

        <div class="scoreboard--score-scoreboard-name scoreboard--border">
            <span v-text="getTitle"></span>
        </div>

        <div class="scoreboard--score-sep"></div>

        <div class="scoreboard--score-scoreboard-scores">
            <div class="scoreboard--score-scoreboard-score-wrapper scoreboard--border" :class="getPbClass">
                <span v-text="getScore"></span>
            </div>

            <div class="scoreboard--score-scoreboard-score-wrapper-right">
                <div class="scoreboard--score-scoreboard-bib-and-pb score-scoreboard--scoreboard-bib scoreboard--border">
                    POS: <span v-text="getPosition"></span>
                </div>

<!--                <div class="scoreboard&#45;&#45;score-sep-pb"></div>-->

                <div class="scoreboard--score-scoreboard-bib-and-pb scoreboard--border">
                    PB: <span v-text="scoreboardEvent.athlete.pb"></span>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import { Prop } from "vue-property-decorator";
    import { ScoreboardService } from "../scoreboard-service";
    import { ScoreboardData } from "../scoreboard-data";
    import { IScoreboardEvent } from "../scoreboard-models";

    const scoreboardService: ScoreboardService = new ScoreboardService();

    @Component({
        name: "scoreboard-event"
    })
    export default class ScoreboardEvent extends Vue {
        @Prop({
            default: () => {
                return scoreboardService.factoryScoreboardEvent();
            }
        }) public readonly scoreboardEvent: IScoreboardEvent;

        public scoreboardData: ScoreboardData = new ScoreboardData();

        public get getPbClass() {
            if (this.scoreboardEvent.score.score > this.scoreboardEvent.athlete.pb) {
                return "scoreboard--scoreboard-score-above-pb";
            }
            return "";
        }

        public get getTitle() {
            return this.scoreboardEvent.athlete.bibNo + ": " + this.scoreboardEvent.athlete.name;
        }

        public get getScore() {
            if (this.scoreboardEvent.score.score === 0) {
                return "-";
            }

            return this.scoreboardEvent.score.score;
        }

        public get getPosition() {

            return this.scoreboardEvent.ranking.reduce((accum, rank) => {

                if (rank.bibno === this.scoreboardEvent.athlete.bibNo) {
                    accum = rank.position.toString();
                }
                return accum;
            }, "");

        }

    }

</script>

<style scoped>

    .scoreboard--score-wrapper {
        width: 100%;
        /*border: solid 1px black;*/
        height: 85vh;
        padding: 1vw;
        background-color: black;
        color: white;
    }

    .scoreboard--score-scoreboard-bib {
        margin-bottom: 10vh;
    }

    .scoreboard--border {
        border: solid 0.5vh black;
    }

    .scoreboard--score-sep {
        height: 2vh;
    }

    .scoreboard--score-scoreboard-name {
        width:  100%;
        /*background-color: orange;*/
        height: 15vh;
        line-height: 15vh;
        font-size: 10vh;
        text-align: center;
        /*border: solid 2px black;*/
    }

    .scoreboard--score-scoreboard-scores {
        width:  100%;
        /*background-color: pink;*/
    }

    .scoreboard--score-scoreboard-score-wrapper {
        width:  46vw;
        height: 46vh;
        line-height: 45vh;
        /*background-color: yellow;*/
        display: inline-block;
        /*border: solid 2px black;*/
        vertical-align: top;
        text-align: center;
        vertical-align: middle;

        font-size: 20vh;
    }

    .scoreboard--score-scoreboard-score-wrapper-right {
        width:  46vw;
        height: 46vh;
        /*background-color: green;*/
        display: inline-block;
        /*border: solid 2px black;*/
        vertical-align: top;
        float: right;
    }

    .scoreboard--score-scoreboard-bib-and-pb {
        /*background-color: blue;*/
        height: 23vh;
        line-height: 23vh;
        text-align: center;
        vertical-align: middle;
        /*border: solid 2px black;*/
        font-size: 10vh;
    }

    .scoreboard--scoreboard-score-above-pb {
        /*background-color: #00C853;*/
    }
</style>

