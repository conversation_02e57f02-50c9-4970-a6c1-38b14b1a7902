import {IScoreboardSchedule, IScoreboardScheduleInbound} from "./scoreboard-schedule-models"
import {format, parse} from "date-fns"
// import {IValidationProp} from "../../../validation/validation-models"

export class ScoreboardScheduleService {
    public factoryScoreboardSchedule(): IScoreboardSchedule {
        return {
            id: 0,
            startdt: "",
            enddt: "",
            design: {
                id: 0,
                description: "",
                designNo: 3
            },
            output: {
                id: 0,
                description: "",
                outputNo: 0
            },
            events: []
        }
    }


    public factoryScoreboardScheduleInbound(): IScoreboardScheduleInbound {
        return {
            id: 0,
            startdt: "",
            enddt: "",
            outputid: 0,
            designid: 0,
            events: []
        }
    }

    public getInboundDatePattern(): string {
        return "YYYY-MM-DD HH:mm";
    }

    public convertScheduleToInbound(scoreboardSchedule: IScoreboardSchedule): IScoreboardScheduleInbound {
        const scoreboardScheduleInbound = this.factoryScoreboardScheduleInbound();
        const pattern = this.getInboundDatePattern();

        scoreboardScheduleInbound.id = scoreboardSchedule.id;
        scoreboardScheduleInbound.startdt = format(parse(scoreboardSchedule.startdt), pattern);
        scoreboardScheduleInbound.enddt = format(parse(scoreboardSchedule.enddt), pattern);
        scoreboardScheduleInbound.outputid = scoreboardSchedule.output.id;
        scoreboardScheduleInbound.designid = scoreboardSchedule.design.id;
        scoreboardScheduleInbound.events = scoreboardSchedule.events.map( (eventGroup) => {
            return {
                eventgroupid: eventGroup.id
            }
        });

        return scoreboardScheduleInbound;
    }

    // public validateSchedule(scoreboardSchedule: IScoreboardSchedule): Record<string, IValidationProp> {
    //
    // }

}
