<template>
  <div class="r4s-footer">
    <span v-if="compId === 0">
      Powered by <span class="r4s-footer-url">entry4sports.co.uk</span>
    </span>
    <span v-if="compId > 0">
      For full results:
      <span class="r4s-footer-url" v-text="getResultsUrl"></span>
    </span>

    <div style="float: right;padding-right: 1rem;">
      <span v-text="outputName"></span>
      <ScoreboardGoTo
        :style="currentSocketState !== 'OPEN' ? 'color: red;' : ''"
        :comp-id="compId"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { IConfigApp } from "../../../config/config-app-models";
import { CONFIG } from "../../../common/config";
import { Prop } from "vue-property-decorator";
import ScoreboardGoTo from "../scoreboard-go-to.vue";

@Component({
  name: "r4s-footer",
  components: {ScoreboardGoTo},
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class R4sFooter extends Vue {
  @Prop({
    default: 0,
  })
  public readonly compId: number;

  @Prop({
    default: "",
  })
  public readonly currentSocketState: string;

  @Prop({
    default: "",
  })
  public readonly outputName: string;

  public readonly configApp: IConfigApp;

  public get getResultsUrl() {
    return CONFIG.E4S_HOST + "/" + this.compId + "/results";
  }

}
</script>

<style>
.r4s-footer {
  height: 5vh;
  text-align: center;
  line-height: 5vh;
  font-weight: 500;
  font-size: 2vh;
  color: white;
  background-color: black;
}
.r4s-footer-url {
  color: white;
  padding-left: 1vw;
}
</style>
