import { Rs4Service } from "./rs4-service";
import {
  IR4sPhotoFinishResult,
  IR4sSocketDataMessage,
} from "./rs4-scoreboard-models";
import { IScoreboardSchedule } from "../scoreboard-schedule/scoreboard-schedule-models";
import { CommonService } from "../../../common/common-service";
import { parse } from "date-fns";
// import {scheduleMockDev369} from "../../results/public/schedule/v2/tests/scheduleMockDev369"
import { scheduleMockLive337 } from "../../results/public/schedule/v2/tests/scheduleMockLive337";

// import * as CommonServiceUtils from "../../../common/common-service-utils";

// import r4sMockResults from "./r4s-mock-data.json";
// import {IServerResponse} from "../../../common/common-models"

const rs4Service: Rs4Service = new Rs4Service();
const commonService: CommonService = new CommonService();

describe("Rs4Service", () => {
  test("displayCompetitionFlyer", () => {
    const r4sSocketDataMessage = {
      action: "sendmessage",
      data: {
        key: 10,
        comp: {
          id: 351,
          name: "Acme Winter Comp",
          date: "2021-04-01",
          organiser: "Acme AC",
          location: "Acme Another Track",
          options: {
            checkIn: {
              enabled: false,
              checkInDateTimeOpens: "",
              defaultFrom: 180,
              defaultTo: 60,
              qrCode: true,
              text: "",
              terms: "",
              useTerms: false,
            },
            heatOrder: "s",
            laneCount: 8,
            timetable: "provisional",
          },
        },
        action: "athlete-result",
        deviceKey: "Unknown 8739",
        securityKey: "9d2ce101643bb5ec190fdbd72abf48aa",
        payload: {
          entries: {
            "966": {
              entry: {
                pb: 9999,
                id: 806,
                bibNo: 18,
                checkedIn: true,
                collected: false,
                present: true,
              },
              eventGroup: {
                id: 25477,
                eventNo: 10,
                groupName: "Shotput",
              },
              athlete: {
                id: 966,
                name: "Lewis-Morgan Barton",
                gender: "M",
                club: "Kettering Town Harriers",
                pb: 4.42,
                sb: 0,
              },
              ageGroup: {
                ageGroupID: 12,
                eventAgeGroup: "",
                ageGroup: "Under 23",
              },
              heatInfo: {
                position: 7,
                seededPosition: 7,
              },
              results: {
                highScore: 13.77,
                scoreText: "(pb)",
                currentPosition: 2,
                isPb: true,
                isSb: true,
                data: {
                  t1: 12.87,
                  t5: 11.12,
                  t2: "x",
                  t6: 12.56,
                  t3: 13.77,
                  t4: "-",
                },
                highscore: 13.77,
              },
            },
            "1138": {
              entry: {
                pb: 8,
                id: 662,
                bibNo: 40,
                checkedIn: true,
                collected: false,
                present: false,
              },
              eventGroup: {
                id: 25477,
                eventNo: 10,
                groupName: "Shotput",
              },
              athlete: {
                id: 1138,
                name: "Rachel Crorken",
                gender: "F",
                club: "Wakefield District H and A.C.",
                pb: 7.93,
                sb: 0,
              },
              ageGroup: {
                ageGroupID: 12,
                eventAgeGroup: "Under 23",
                ageGroup: "Under 23",
              },
              heatInfo: {
                position: 8,
                seededPosition: 8,
              },
              results: {
                highScore: 13.78,
                scoreText: "(pb)",
                currentPosition: 8,
                isPb: true,
                isSb: true,
                data: {
                  t3: "r",
                  t1: "x",
                  t2: 13.78,
                },
                highscore: 13.78,
              },
            },
            "1298": {
              entry: {
                pb: 9999,
                id: 646,
                bibNo: 17,
                checkedIn: true,
                collected: false,
                present: true,
              },
              eventGroup: {
                id: 25477,
                eventNo: 10,
                groupName: "Shotput",
              },
              athlete: {
                id: 1298,
                name: "Lewis Ackroyd",
                gender: "M",
                club: "Halifax Harriers and A.C.",
                pb: 9.25,
                sb: 0,
              },
              ageGroup: {
                ageGroupID: 12,
                eventAgeGroup: "",
                ageGroup: "Under 23",
              },
              heatInfo: {
                position: 2,
                seededPosition: 2,
              },
              results: {
                highScore: 13.67,
                scoreText: "(pb)",
                currentPosition: 3,
                isPb: true,
                isSb: true,
                data: {
                  t3: 13.67,
                  t4: 12.89,
                  t1: 10.01,
                  t5: 13.05,
                  t2: 13.67,
                  t6: 12.86,
                },
                highscore: 13.67,
              },
            },
            "2826": {
              entry: {
                pb: 9999,
                id: 642,
                bibNo: 11,
                checkedIn: true,
                collected: false,
                present: true,
              },
              eventGroup: {
                id: 25477,
                eventNo: 10,
                groupName: "Shotput",
              },
              athlete: {
                id: 2826,
                name: "Matthew Edson",
                gender: "M",
                club: "Doncaster A.C.",
                pb: 4.88,
                sb: 0,
              },
              ageGroup: {
                ageGroupID: 11,
                eventAgeGroup: "",
                ageGroup: "Under 20",
              },
              heatInfo: {
                position: 3,
                seededPosition: 3,
              },
              results: {
                highScore: 12.88,
                scoreText: "(pb)",
                currentPosition: 6,
                isPb: true,
                isSb: true,
                data: {
                  t4: 11.88,
                  t1: "18.00",
                  t5: 12.88,
                  t2: 12.57,
                  t6: 12.4,
                  t3: 12.87,
                },
                highscore: 12.88,
                trial: 1,
              },
            },
            "3145": {
              entry: {
                pb: 6,
                id: 644,
                bibNo: 13,
                checkedIn: true,
                collected: false,
                present: true,
              },
              eventGroup: {
                id: 25477,
                eventNo: 10,
                groupName: "Shotput",
              },
              athlete: {
                id: 3145,
                name: "Kht, ,kg",
                gender: "M",
                club: "Dunleer A.C.",
                pb: 0,
                sb: 0,
              },
              ageGroup: {
                ageGroupID: 11,
                eventAgeGroup: "",
                ageGroup: "Under 20",
              },
              heatInfo: {
                position: 1,
                seededPosition: 1,
              },
              results: {
                highScore: 13.89,
                scoreText: "(pb)",
                currentPosition: 1,
                isPb: true,
                isSb: true,
                data: {
                  t5: 12.45,
                  t2: "x",
                  t6: 13.11,
                  t3: 11.11,
                  t4: 13.89,
                  t1: "x",
                },
                highscore: 13.89,
              },
            },
            "3771": {
              entry: {
                pb: 9999,
                id: 816,
                bibNo: 14,
                checkedIn: true,
                collected: false,
                present: false,
              },
              eventGroup: {
                id: 25477,
                eventNo: 10,
                groupName: "Shotput",
              },
              athlete: {
                id: 3771,
                name: "Miriam Abdelgader",
                gender: "F",
                club: "Ealing Southall and Middlesex A.C.",
                pb: 5.43,
                sb: 0,
              },
              ageGroup: {
                ageGroupID: 11,
                eventAgeGroup: "",
                ageGroup: "Under 20",
              },
              heatInfo: {
                position: 9,
                seededPosition: 9,
              },
              results: {
                highScore: 0,
                scoreText: "",
                currentPosition: 0,
                isPb: false,
                isSb: false,
              },
            },
            "4472": {
              entry: {
                pb: 9999,
                id: 643,
                bibNo: 12,
                checkedIn: true,
                collected: false,
                present: true,
              },
              eventGroup: {
                id: 25477,
                eventNo: 10,
                groupName: "Shotput",
              },
              athlete: {
                id: 4472,
                name: "Kolade Abiodun",
                gender: "M",
                club: "Donore Harriers",
                pb: 0,
                sb: 0,
              },
              ageGroup: {
                ageGroupID: 12,
                eventAgeGroup: "",
                ageGroup: "Under 23",
              },
              heatInfo: {
                position: 4,
                seededPosition: 4,
              },
              results: {
                highScore: 13.56,
                scoreText: "(pb)",
                currentPosition: 4,
                isPb: true,
                isSb: true,
                data: {
                  t5: 13.56,
                  t2: 11.23,
                  t6: 13.01,
                  t3: 11.99,
                  t4: 10.34,
                  t1: "89.00",
                },
                highscore: 13.56,
                trial: 1,
              },
            },
            "4532": {
              entry: {
                pb: 9999,
                id: 645,
                bibNo: 16,
                checkedIn: true,
                collected: false,
                present: true,
              },
              eventGroup: {
                id: 25477,
                eventNo: 10,
                groupName: "Shotput",
              },
              athlete: {
                id: 4532,
                name: "Jessica Afrakomaa",
                gender: "F",
                club: "Fingallians A.C.",
                pb: 0,
                sb: 0,
              },
              ageGroup: {
                ageGroupID: 11,
                eventAgeGroup: "",
                ageGroup: "Under 20",
              },
              heatInfo: {
                position: 5,
                seededPosition: 5,
              },
              results: {
                highScore: 13.12,
                scoreText: "(pb)",
                currentPosition: 5,
                isPb: true,
                isSb: true,
                data: {
                  t6: "x",
                  t3: 12.12,
                  t4: 13.12,
                  t1: 10,
                  t5: 10.53,
                  t2: 13.11,
                },
                highscore: 13.12,
              },
            },
            "62213": {
              entry: {
                pb: 9999,
                id: 817,
                bibNo: 39,
                checkedIn: true,
                collected: false,
                present: true,
              },
              eventGroup: {
                id: 25477,
                eventNo: 10,
                groupName: "Shotput",
              },
              athlete: {
                id: 62213,
                name: "yet a pulil",
                gender: "F",
                club: "Unregistered",
                pb: 0,
                sb: 0,
              },
              ageGroup: {
                ageGroupID: 12,
                eventAgeGroup: "",
                ageGroup: "Under 23",
              },
              heatInfo: {
                position: 6,
                seededPosition: 6,
              },
              results: {
                highScore: 11,
                scoreText: "(pb)",
                currentPosition: 7,
                isPb: true,
                isSb: true,
                data: {
                  t4: "x",
                  t1: 1.2,
                  t5: "x",
                  t2: 10.98,
                  t6: "x",
                  t3: 11,
                },
                highscore: 11,
              },
            },
          },
          competition: {
            id: 351,
            name: "Acme Winter Comp",
            logo: "/e4s_logo.png",
          },
          score: {
            athleteId: 4472, //  25477 no schedules. 25477 matches
            trial: 1,
            score: "89.00",
            nextAthleteId: 4532,
          },
          domain: "dev.entry4sports.com",
        },
      },
    } as any as IR4sSocketDataMessage;

    const scoreboardSchedules: Record<string, IScoreboardSchedule> = {
      "28": {
        id: 28,
        startdt: "2021-04-07T12:00:00",
        enddt: "2021-04-07T13:00:00",
        design: {
          id: 1,
          description: "Scoreboard",
        },
        output: {
          id: 12,
          description: "Main Scoreboard",
          outputNo: 10,
        },
        events: [
          {
            id: 25489,
            name: "High Jump",
          },
        ],
      },
      "29": {
        id: 29,
        startdt: "2021-04-07T15:00:00",
        enddt: "2021-04-07T16:00:00",
        design: {
          id: 1,
          description: "Scoreboard",
        },
        output: {
          id: 12,
          description: "Main Scoreboard",
          outputNo: 10,
        },
        events: [
          {
            id: 25489,
            name: "High Jump",
          },
        ],
      },
      "24": {
        id: 24,
        startdt: "2021-04-07T08:00:00",
        enddt: "2021-04-07T11:00:00",
        design: {
          id: 1,
          description: "Scoreboard",
        },
        output: {
          id: 12,
          description: "Main Scoreboard",
          outputNo: 10,
        },
        events: [
          {
            id: 25481,
            name: "1000m",
          },
        ],
      },
    } as any as Record<string, IScoreboardSchedule>;

    // expect(rs4Service.shouldDisplayScore(r4sSocketDataMessage, scheduleAsArray)).toBe(false);

    const sched: IScoreboardSchedule = {
      id: 24,
      startdt: "2021-04-07T08:00:00",
      enddt: "2021-04-07T11:00:00",
      design: {
        id: 1,
        description: "Scoreboard",
        designNo: 3,
      },
      output: {
        id: 12,
        description: "Main Scoreboard",
        outputNo: 10,
      },
      events: [
        {
          id: 25481,
          name: "1000m",
        },
      ],
    };

    expect(rs4Service.doesScheduleContainEventGroupId(33, sched)).toBe(false);
    expect(rs4Service.doesScheduleContainEventGroupId(25481, sched)).toBe(true);

    let scheduleAsArray =
      commonService.convertObjectToArray(scoreboardSchedules);
    expect(
      rs4Service.getSchedulesContainingEventGroupId(25481, scheduleAsArray)
        .length
    ).toBe(1);
    expect(
      rs4Service.getSchedulesContainingEventGroupId(25489, scheduleAsArray)
        .length
    ).toBe(2);

    expect(
      rs4Service.isEventInScheduleTimeRange(sched, parse("2021-04-07T09:00:00"))
    ).toBe(true);

    expect(
      rs4Service.isEventInScheduleTimeRange(sched, parse("2021-04-07T06:00:00"))
    ).toBe(false);

    expect(
      rs4Service.getValidSchedules(
        25489,
        scheduleAsArray,
        parse("2021-04-07T06:00:00")
      ).length
    ).toBe(0);

    expect(
      rs4Service.getValidSchedules(
        25489,
        scheduleAsArray,
        parse("2021-04-07T13:01:00")
      ).length
    ).toBe(0);

    expect(
      rs4Service.getValidSchedules(
        25489,
        scheduleAsArray,
        parse("2021-04-07T15:01:00")
      ).length
    ).toBe(1);

    //  25477 no schedules
    // expect(
    //     rs4Service.shouldDisplayScore(
    //         r4sSocketDataMessage,
    //         scheduleAsArray,
    //         parse("2021-04-07T09:00:00")
    //     )
    // ).toBe(false);

    scheduleAsArray = [
      {
        id: 99,
        startdt: "2021-04-07T08:00:00",
        enddt: "2021-04-07T11:00:00",
        design: {
          id: 1,
          description: "Scoreboard",
          designNo: 3,
        },
        output: {
          id: 12,
          description: "Main Scoreboard",
          outputNo: 10,
        },
        events: [
          {
            id: 25477,
            name: "Shot Put",
          },
        ],
      },
    ];

    expect(
      rs4Service.shouldDisplayScore(
        r4sSocketDataMessage,
        scheduleAsArray,
        parse("2021-04-07T08:59:00")
      ).code
    ).toBe("SUCCESS");

    const res = rs4Service.shouldDisplayScore(
      r4sSocketDataMessage,
      scheduleAsArray,
      parse("2021-04-07T07:59:00")
    );

    expect(res.code).toBe("POINT_IN_TIME_WRONG");
  });

  test("getEventNameFromSchedulesByEventGroupId", () => {
    const schedules = [
      {
        id: 32,
        startdt: "2021-04-16T07:00:00",
        enddt: "2021-04-16T23:00:00",
        design: {
          id: 3,
          designNo: 3,
          description: "Scoreboard and Ranking",
        },
        output: {
          id: 12,
          description: "ECC 3",
          outputNo: 10,
        },
        events: [
          {
            id: 25485,
            name: "Javelin Chargeable",
          },
          {
            id: 25476,
            name: "100m",
          },
          {
            id: 25481,
            name: "1000m",
          },
          {
            id: 25493,
            name: "300m",
          },
          {
            id: 25480,
            name: "Triple Jump",
          },
          {
            id: 25487,
            name: "Discus",
          },
          {
            id: 25477,
            name: "Shotput",
          },
        ],
      },
      {
        id: 33,
        startdt: "2021-04-10T08:23:00",
        enddt: "2021-04-10T23:30:00",
        design: {
          id: 3,
          designNo: 3,
          description: "Scoreboard and Ranking",
        },
        output: {
          id: 12,
          description: "ECC 3",
          outputNo: 10,
        },
        events: [
          {
            id: 25477,
            name: "Shotput",
          },
        ],
      },
    ] as IScoreboardSchedule[];

    expect(
      rs4Service.getEventNameFromSchedulesByEventGroupId(3, schedules)
    ).toBe("NA");
    expect(
      rs4Service.getEventNameFromSchedulesByEventGroupId(25487, schedules)
    ).toBe("Discus");
  });

  // test("mapR4sEntryToIResultRow", () => {
  //     const r4sEntry: IR4sEntry = {
  //         "entry": {
  //             "pb": 31,
  //             "id": 680,
  //             "bibNo": 214,
  //             "checkedIn": true,
  //             "collected": false,
  //             "present": true
  //         },
  //         "eventGroup": {
  //             "id": 25487,
  //             "eventNo": 4,
  //             "groupName": "Discus"
  //         },
  //         "athlete": {
  //             "id": 1298,
  //             "name": "Lewis Ackroyd",
  //             "aocode": "EA",
  //             "urn": 3223890,
  //             "gender": "M",
  //             "club": "Halifax Harriers and A.C.",
  //             "pb": 31.27,
  //             "sb": 0
  //         },
  //         "ageGroup": {
  //             "ageGroupID": 12,
  //             "eventAgeGroup": "Under 23",
  //             "ageGroup": "Under 23"
  //         },
  //         "heatInfo": {
  //             "position": 7,
  //             "seededPosition": 7
  //         },
  //         "results": {
  //             "highScore": 0,
  //             "scoreText": "",
  //             "currentPosition": 0,
  //             "isPb": false,
  //             "isSb": false
  //         }
  //     } as any as IR4sEntry;
  //     expect( rs4Service.mapR4sEntryToIResultRow(r4sEntry).athlete.name).toBe("Lewis Ackroyd");
  // })

  test("mapR4sEntryToIResultRowTrack", () => {
    const r4sEntry: IR4sPhotoFinishResult[] = [
      {
        id: 1774,
        compid: 313,
        athleteid: -634,
        eventno: 1,
        resultkey: "h1",
        resultvalue: "50.16",
        options: {
          place: 14,
          lane: 14,
          bibNo: 634,
          athlete: "Calvin Smith",
          club: "(Shaf)",
          ageGroup: "U20",
          ws: "N/A m/s",
          eventTime: "10/04/2022 - 17:26:50",
          time: "",
          scoreText: "",
        },
        timeInSeconds: 50.16,
      },
      {
        id: 1776,
        compid: 313,
        athleteid: -623,
        eventno: 1,
        resultkey: "h1",
        resultvalue: "12:31.01",
        options: {
          place: 16,
          lane: 16,
          time: "",
          bibNo: 623,
          athlete: "Reece Middleton",
          club: "(Cove)",
          ageGroup: "Sen",
          ws: "N/A m/s",
          eventTime: "10/04/2022 - 17:26:50",
          scoreText: "",
        },
        timeInSeconds: 751.01,
      },
    ];
    const res: IR4sPhotoFinishResult[] =
      rs4Service.sortPhotoFinishResults(r4sEntry);
    expect(res.length).toBe(2);
    //
    // expect( CommonServiceUtils.isNumeric(res[1])).toBe(2);
    //
    // if (!CommonServiceUtils.isNumeric(result.resultvalue.toString()));

    // expect( res.heat).toBe("Heat 23");
  });

  test("sortPhotoFinishResults", () => {
    const data = [
      {
        id: 2072,
        compid: 313,
        athleteid: 1189,
        eventno: 2,
        resultkey: "h1",
        resultvalue: "DQ",
        options: {
          scoreText: "Rule:TR 16.8",
          place: 0,
          bibNo: 189,
          lane: 6,
          athleteId: 1189,
          athlete: "Adeseye OGUNLEWE",
          club: "Newham & Essex",
          time: "DQ",
          ageGroup: "Inters",
          ws: 1.1,
          eventTime: "13:14:37",
        },
        timeInSeconds: 0,
      },
      {
        id: 2065,
        compid: 313,
        athleteid: 1120,
        eventno: 2,
        resultkey: "h1",
        resultvalue: "9.46",
        options: {
          scoreText: "",
          place: 1,
          bibNo: 120,
          lane: 3,
          athleteId: 1120,
          athlete: "Ronnie WELLS",
          club: "Yeovil Olympiads Ac",
          time: 9.46,
          ageGroup: "Inters",
          ws: 1.1,
          eventTime: "13:14:37",
        },
        timeInSeconds: 9.46,
      },
      {
        id: 2066,
        compid: 313,
        athleteid: 1119,
        eventno: 2,
        resultkey: "h1",
        resultvalue: "10.5",
        options: {
          scoreText: "",
          place: 2,
          bibNo: 119,
          lane: 7,
          athleteId: 1119,
          athlete: "Imranur RAHMAN",
          club: "Sheffield",
          time: 10.5,
          ageGroup: "Inters",
          ws: 1.1,
          eventTime: "13:14:37",
        },
        timeInSeconds: 10.5,
      },
      {
        id: 2067,
        compid: 313,
        athleteid: 1108,
        eventno: 2,
        resultkey: "h1",
        resultvalue: "10.56",
        options: {
          scoreText: "",
          place: 3,
          bibNo: 108,
          lane: 8,
          athleteId: 1108,
          athlete: "Nick PRENTICE",
          club: "Birchfield Harriers",
          time: 10.56,
          ageGroup: "Inters",
          ws: 1.1,
          eventTime: "13:14:37",
        },
        timeInSeconds: 10.56,
      },
      {
        id: 2068,
        compid: 313,
        athleteid: 1132,
        eventno: 2,
        resultkey: "h1",
        resultvalue: "10.58",
        options: {
          scoreText: "YC  Rule:TR 16.5.2",
          place: 4,
          bibNo: 132,
          lane: 4,
          athleteId: 1132,
          athlete: "Jimmy THORONKA",
          club: "London Heathside",
          time: 10.58,
          ageGroup: "Inters",
          ws: 1.1,
          eventTime: "13:14:37",
        },
        timeInSeconds: 10.58,
      },
      {
        id: 2069,
        compid: 313,
        athleteid: 1106,
        eventno: 2,
        resultkey: "h1",
        resultvalue: "10.71",
        options: {
          scoreText: "",
          place: 5,
          bibNo: 106,
          lane: 2,
          athleteId: 1106,
          athlete: "Ben SHIELDS",
          club: "City Of Sheffield And Dearne Ac",
          time: 10.71,
          ageGroup: "Inters",
          ws: 1.1,
          eventTime: "13:14:37",
        },
        timeInSeconds: 10.71,
      },
      {
        id: 2070,
        compid: 313,
        athleteid: 1107,
        eventno: 2,
        resultkey: "h1",
        resultvalue: "10.85",
        options: {
          scoreText: "",
          place: 6,
          bibNo: 107,
          lane: 5,
          athleteId: 1107,
          athlete: "Daniel ODERINDE",
          club: "Marshall Milton Keynes",
          time: 10.85,
          ageGroup: "Inters",
          ws: 1.1,
          eventTime: "13:14:37",
        },
        timeInSeconds: 10.85,
      },
      {
        id: 2071,
        compid: 313,
        athleteid: 1127,
        eventno: 2,
        resultkey: "h1",
        resultvalue: "10.88",
        options: {
          scoreText: "",
          place: 7,
          bibNo: 127,
          lane: 1,
          athleteId: 1127,
          athlete: "Kyron WILLIAMS",
          club: "Enfield & Haringey",
          time: 10.88,
          ageGroup: "Inters",
          ws: 1.1,
          eventTime: "13:14:37",
        },
        timeInSeconds: 10.88,
      },
    ];
    const res: IR4sPhotoFinishResult[] = rs4Service.sortPhotoFinishResults(
      data as any as IR4sPhotoFinishResult[]
    );
    expect(res.length).toBe(8);
    expect(res[0].athleteid).toBe(1120);

    expect(res[7].athleteid).toBe(1189);
  });

  test("sortAndFilterResultEntries", () => {
    const data = scheduleMockLive337;
    const res = rs4Service.sortScheduleMap(data.schedule);
    expect(res.T.length).toBe(43);
    expect(res.F.length).toBe(15);
    expect(res.X.length).toBe(0);

    expect(res.T![0].typeNo).toBe("T1");

    //  this comp has NO T2!
    expect(res.T![1].typeNo).toBe("T3");

    //  this comp has NO T5!
    expect(res.T![9].typeNo).toBe("T12");
  });
});
