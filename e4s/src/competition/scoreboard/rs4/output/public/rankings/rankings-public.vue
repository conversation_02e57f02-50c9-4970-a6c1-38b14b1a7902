<template>
    <div>
        <table class="r4s-ranking--table">

            <tr class="r4s-ranking--table-header">
                <th class="r4s-ranking--table-pos">Pos</th>
                <th class="r4s-ranking--table-bib">Bib</th>
                <th class="r4s-ranking--table-th-ath">Athlete</th>
                <th class="r4s-ranking--table-th-club">Club</th>
                <th class="r4s-ranking--table-th-sco">Result</th>
            </tr>

            <tr
                v-for="(r4sEntry, index) in entries"
                :key="r4sEntry.athlete.id"
            >
                <td class="r4s-ranking--table-pos"><span v-text="r4sEntry.results.currentPosition"></span></td>
                <td class="r4s-ranking--table-bib"><span v-text="r4sEntry.entry.bibNo"></span></td>
                <td class="r4s-ranking--table-th-ath"><span v-text="trimColumnText(r4sEntry.athlete.name)"></span></td>
                <td class="r4s-ranking--table-th-club"><span v-text="trimColumnText(r4sEntry.athlete.club)"></span></td>
                <td class="r4s-ranking--table-td-sco"><span v-text="getScore(r4sEntry)"></span></td>
            </tr>
        </table>
    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {Prop} from "vue-property-decorator"
import {Rs4Service} from "../../../rs4-service"
import {IR4sEntry} from "../../../rs4-scoreboard-models"

@Component({
    name: "rankings-public"
})
export default class RankingsPublic extends Vue {

    @Prop({
        default: () => {
            return [];
        }
    })
    public readonly entries!: IR4sEntry[];

    public rs4Service: Rs4Service = new Rs4Service();

    public getScore(r4sEntry: IR4sEntry) {
        return this.rs4Service.getScoreFromEntry(r4sEntry);
    }

    public trimColumnText(content: string): string {
        if (content.length > 30) {
            return content.slice(0, 31) + "..."
        }
        return content;
    }

}
</script>

<style>

.r4s-ranking--table {
    font-size: 1.4em;
}

.r4s-ranking--table-pos {
    width: 10vw;
    text-align: center;
}

.r4s-ranking--table-bib {
    width: 10vw;
}

.r4s-ranking--table-th-ath {
    width: 35vw;
}

.r4s-ranking--table-th-club {
    width: 35vw;
}

.r4s-ranking--table-td-sco {
    width: 10vw;
}

.r4s-ranking--page-count {
    float: right;
    margin-right: 1em;
    font-size: 1.25em;
    font-weight: 500;
}

</style>
