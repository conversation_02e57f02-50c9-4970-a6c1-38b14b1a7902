<template>
    <div>
        <RankingsController
            :entries-by-event-ids="entriesByEventIds"
            :entries-by-athlete-id="entriesByAthleteId"
        ></RankingsController>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import {handleResponseMessages} from "../../../../../../common/handle-http-reponse"
import {ScoreboardData} from "../../../../scoreboard-data"
import {IR4sEntry} from "../../../rs4-scoreboard-models"
import RankingsController from "../../../rankings/rankings-controller.vue"
import {Prop, Watch} from "vue-property-decorator"


@Component({
    name: "rankings-public-route",
    components: {RankingsController}
})
export default class RankingsPublicRoute extends Vue {

    @Prop({
        default: 0
    })
    public readonly compId!: number;

    @Prop({
        default: 0
    })
    public readonly eventGroupId!: number;

    public isLoading: boolean = false;

    public scoreboardData: ScoreboardData = new ScoreboardData();

    public entriesByEventIds: Record<number, Record<number, IR4sEntry>> = {};
    public entriesByAthleteId: Record<number, IR4sEntry> = {};

    public created() {
        if (this.compId > 0 && this.eventGroupId > 0) {
            this.getLatestScoreData();
        }
    }

    @Watch("compId")
    public onCompIdChanged() {
        this.getLatestScoreData();
    }

    @Watch("eventGroupId")
    public onEventGroupIdChanged() {
        this.getLatestScoreData();
    }

    public getLatestScoreData() {
        if (this.compId === 0 || this.eventGroupId === 0) {
            return;
        }
        this.isLoading = true;

        const prom = this.scoreboardData.getLatestScores(this.compId, [this.eventGroupId]);
        handleResponseMessages(prom);
        return prom
            .then((resp) => {
                if (resp.errNo === 0) {
                    this.entriesByEventIds = resp.data;
                }
                return;
            })
            .finally(() => {
                this.isLoading = false;
            })
    }
}
</script>
