<template>
    <div>
        <div class="row">
            <div class="col s12 m12 l12">
                <div class="r4s-public-results--event-name">
                    <div class="row">
                        <div class="col s12 m12 l12">
                            <span v-text="eventName"></span>
                        </div>
                    </div>
                </div>
<!--                <div class="right">-->
<!--                    <slot name="close"></slot>-->
<!--                </div>-->
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                <a href="#" v-on:click.prevent="close">Schedule</a>
            </div>
        </div>



        <table class="r4s-public-results--table">

            <tr class="r4s-public--table-header">
                <th class="r4s-public-results--table-pos">Pos#</th>
                <th class="r4s-public-results--table-bib">Bib</th>
                <th class="r4s-public-results--table-th-ath">Athlete</th>
                <th class="r4s-public-results--table-th-club">Club</th>
                <th class="r4s-public-results--table-th-sco">Result</th>
            </tr>

            <tr
                v-for="(result, index) in results"
                :key="result.athlete.id"
            >
                <td class="r4s-public-results--table-pos"><span v-text="index + 1"></span></td>
                <td class="r4s-public-results--table-bib"><span v-text="result.bibNo"></span></td>
                <td class="r4s-public-results--table-th-ath"><span v-text="trimColumnText(result.athlete.name)"></span></td>
                <td class="r4s-public-results--table-th-club"><span v-text="trimColumnText(result.club)"></span></td>
                <td class="r4s-public-results--table-td-sco">
                    <a v-if="result.athlete.urn && result.athlete.urn.toString().length > 0"
                       :href="'https://www.thepowerof10.info/athletes/profile.aspx?ukaurn=' + result.athlete.urn"
                       target="e4s-po10"
                       class="po10-link">
                        <img :src="require('../../../../../../images/po10.ico')"/>
                    </a>
                    <span v-text="result.result"></span>
                </td>
            </tr>
        </table>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import {Prop} from "vue-property-decorator"
import {IPublicHeatGroupMeta, IResultRow} from "../../../rs4-scoreboard-models"

@Component({
    name: "seeding-public-field"
})
export default class SeedingPublicField extends Vue {

    @Prop({
        default: () => {
            return {};
        }
    })
    public readonly publicHeatGroupMeta!: Record<number, IPublicHeatGroupMeta>;

    @Prop({
        default: () => {
            return [];
        }
    })
    public readonly results!: IResultRow[];

    @Prop({
        default: ""
    })
    public readonly eventName!: string;

    public trimColumnText(content: string): string {
        if (content.length > 30) {
            return content.slice(0, 31) + "..."
        }
        return content;
    }


    public close() {
        this.$emit("close");
    }

}
</script>

<style>

.r4s-public-results--event-name {
    margin: 0.5em 0;
    font-size: 1.25em;
}

.r4s-ranking--table {
    font-size: 1.4em;
}

.r4s-ranking--table-pos {
    width: 10vw;
    text-align: center;
}

.r4s-ranking--table-bib {
    width: 10vw;
}

.r4s-ranking--table-th-ath {
    width: 35vw;
}

.r4s-ranking--table-th-club {
    width: 35vw;
}

.r4s-ranking--table-td-sco {
    width: 10vw;
}

.r4s-ranking--page-count {
    float: right;
    margin-right: 1em;
    font-size: 1.25em;
    font-weight: 500;
}

</style>
