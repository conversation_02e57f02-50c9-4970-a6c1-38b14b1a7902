<template>
  <ScoreboardOutputList :comp-id="compId"/>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import ScoreboardOutputList from "./scoreboard-output-list.vue"

@Component({
    name: "scoreboard-output-list-route",
    components: {ScoreboardOutputList}
})
export default class ScoreboardOutputListRoute extends Vue {

    public compId: number = 0;

    public created() {
        this.compId = isNaN(Number(this.$route.params.compId)) ? 0 : parseInt(this.$route.params.compId, 0);
    }

}
</script>
