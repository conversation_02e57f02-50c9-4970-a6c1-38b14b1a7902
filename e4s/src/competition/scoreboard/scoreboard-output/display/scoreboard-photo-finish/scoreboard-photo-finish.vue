<template>
  <div class="scoreboard-photo-finish---wrapper">
    <div class="scoreboard-photo-finish---header">
<!--      <div-->
<!--        v-text="eventGroupName"-->
<!--        class="scoreboard-photo-finish-&#45;&#45;header-element"-->
<!--      ></div>-->
<!--      <div-->
<!--        v-text="heatTitle"-->
<!--        class="scoreboard-photo-finish-&#45;&#45;header-element"-->
<!--      ></div>-->
      <div
        v-text="'Wind Speed: ' + windSpeed"
        class="scoreboard-photo-finish---header-element"
      ></div>
      <div
        v-text="raceTime"
        class="scoreboard-photo-finish---header-element"
      ></div>
    </div>

    <table cellpadding="0">
      <tr>
        <td class="scoreboard-photo-finish---td-results">
          <table>
            <tr>
              <th class="scoreboard-photo-finish---td-centre">Pos</th>
              <th>Time</th>
              <th>Bib</th>
              <th>Athlete</th>
            </tr>

            <tr
              v-for="result in results"
              :key="result.athleteid"
              class="scoreboard-photo-finish---row"
              :style="rs4ScoreboardOutput.options.ui.photoFinish.row"
            >
              <td
                v-text="getPlace(result)"
                class="
                  scoreboard-photo-finish---td
                  scoreboard-photo-finish---td-centre
                "
              ></td>
              <td
                v-text="getResult(result)"
                class="scoreboard-photo-finish---td"
              ></td>
              <td
                v-text="result.options.bibNo"
                class="scoreboard-photo-finish---td"
              ></td>
              <td class="scoreboard-photo-finish---td">
                <span v-text="result.options.athlete"></span>
                <br />
                <span
                  v-text="result.options.club"
                  class="scoreboard-photo-finish---td-club"
                ></span>
              </td>
            </tr>
          </table>
        </td>

        <td class="scoreboard-photo-finish---td-image">
          <img
            :src="getPhotoFinishLink"
            class="scoreboard-photo-finish---image"
          />
        </td>
      </tr>
    </table>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  IR4sPhotoFinishResult,
  IR4sSocketDataMessage,
  IR4sSocketPhotoFinishMessage,
  IRs4ScoreboardOutput,
} from "../../../rs4/rs4-scoreboard-models";
import { Rs4Service } from "../../../rs4/rs4-service";
import { IScoreboardSchedule } from "../../../scoreboard-schedule/scoreboard-schedule-models";
import { CommonService } from "../../../../../common/common-service";
import {ScoreboardOutputService} from "../../scoreboard-output-service";

const commonService: CommonService = new CommonService();
const scoreboardOutputService = new ScoreboardOutputService();

@Component({
  name: "scoreboard-photo-finish",
})
export default class ScoreboardPhotoFinish extends Vue {
  @Prop({
    default: () => {
      return scoreboardOutputService.factoryScoreboardOutput();
    },
  })
  public readonly rs4ScoreboardOutput: IRs4ScoreboardOutput;

  @Prop({
    required: true,
  })
  public readonly socketDataMessage!: IR4sSocketDataMessage;

  @Prop({
    required: true,
  })
  public readonly scoreboardSchedules!: IScoreboardSchedule[];

  public eventGroupName: string = "";
  public heatTitle = "";
  public windSpeed = "";
  public raceTime = "";

  public socketPhotoFinishMessage: IR4sSocketPhotoFinishMessage | null = null;
  public rs4Service: Rs4Service = new Rs4Service();
  public results: IR4sPhotoFinishResult[] = [];

  public created() {
    console.log(
      "ScoreboardPhotoFinish.created() ",
      R.clone(this.socketDataMessage)
    );
    this.init();
  }

  @Watch("socketDataMessage")
  public onSocketDataMessageChanged(newValue: IR4sSocketDataMessage) {
    this.init();
  }

  public init() {
    this.socketPhotoFinishMessage = this.socketDataMessage
      .data as unknown as IR4sSocketPhotoFinishMessage;
    this.results = this.rs4Service.sortPhotoFinishResults(
      this.socketPhotoFinishMessage.payload.results
    );
    // this.eventGroupName = this.rs4Service.getEventNameFromSchedulesByEventGroupId(this.socketPhotoFinishMessage.payload.eventNo, this.scoreboardSchedules);

    this.eventGroupName = this.socketPhotoFinishMessage.payload.eventName;

    const pluckFirstResult = this.results[0];
    const raceType = pluckFirstResult.resultkey.slice(0, 1);
    this.heatTitle =
      (raceType.toUpperCase() === "H" ? "Heat" : "Race") +
      " " +
      pluckFirstResult.resultkey.slice(1);
    this.windSpeed = pluckFirstResult.options.ws;
    this.raceTime = pluckFirstResult.options.eventTime;
  }

  public get getPhotoFinishLink(): string {
    if (this.socketDataMessage && this.socketDataMessage.data) {
      const socketData: IR4sSocketPhotoFinishMessage = this.socketDataMessage
        .data as unknown as IR4sSocketPhotoFinishMessage;
      if (socketData.payload.picture) {
        return (
          "https://" + socketData.payload.domain + socketData.payload.picture
        );
      }
    }
    return "#";
  }

  public getResult(result: IR4sPhotoFinishResult): string {
    if (
      result.resultvalue.toString() ||
      result.resultvalue.toString() === "DNS"
    ) {
      return result.resultvalue.toString();
    }
    return commonService
      .roundNumberToDecimalPlaces(result.resultvalue, 2, false)
      .toString();
  }

  public getPlace(result: IR4sPhotoFinishResult): string {
    const resultValue: string = result.resultvalue.toString().toUpperCase();
    if (["DNS", "DNF"].indexOf(resultValue) > -1) {
      return "";
    }
    return result.options.place.toString();
  }
}
</script>

<style>
.scoreboard-photo-finish---header {
  /*float: left;*/
  font-weight: 500;
  /*position: absolute;*/
  width: 100%;
  padding: 0 0 0 1em;
  background-color: black;
  color: white;
}

.scoreboard-photo-finish---header-element {
  margin-right: 5em;
  display: inline-block;
  /*width: 25%;*/
  font-size: 1.25em;
  font-weight: 500;
}

.scoreboard-photo-finish---wrapper {
  height: 80vh;
}

.scoreboard-photo-finish---row {
  border-bottom: 1px solid white;
}

.scoreboard-photo-finish---td {
  padding: 5px;
  vertical-align: top;
}

.scoreboard-photo-finish---td-results {
  width: 40%;
  vertical-align: top;
  font-weight: 500;
}

.scoreboard-photo-finish---td-club {
  font-weight: 400;
}

.scoreboard-photo-finish---td-image {
  /*width: 75%;*/
  vertical-align: top;
}

.scoreboard-photo-finish---td-centre {
  text-align: center;
}

.scoreboard-photo-finish---results {
  height: 50vh;
  width: 40%;
}
.scoreboard-photo-finish---image {
  height: 75vh;
  width: 100%;
  border: 5px solid #fab001;
}
</style>
