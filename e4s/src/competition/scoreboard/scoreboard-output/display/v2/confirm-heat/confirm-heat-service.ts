import {
  IConfirmHeat,
  IConfirmHeatAthlete,
  IConfirmHeatParticipant,
  IConfirmHeatServerConfig,
  IConfirmHeatState,
} from "./confirm-heat-models";

import * as CommonServiceUtils from "../../../../../../common/common-service-utils";

export function factoryConfirmHeatState(): IConfirmHeatState {
  return {
    confirmHeat: factoryConfirmHeat(),
    // orderedParticipants: [],
    entriesMap: {},
    heatName: "",
  };
}

export function factoryConfirmHeat(): IConfirmHeat {
  return {
    competition: { id: 0, name: "" },
    domain: "",
    eventGroup: {
      date: "",
      event: "",
      eventno: 0,
      heatCount: 0,
      id: 0,
      type: "T",
      typeno: "",
      entries: [],
    },
    participants: [],
    config: factoryIConfirmHeatServerConfig(),
    scoreboardConfig: {
      displayTime: 15,
      force: "false",
    },
  };
}

export function factoryIConfirmHeatServerConfig(): IConfirmHeatServerConfig {
  return {
    pageSize: 2,
    pageCycleMs: 3000,
  };
}

export function factoryIConfirmHeatParticipant(): IConfirmHeatParticipant {
  return {
    athlete: factoryIConfirmHeatAthlete(),
    heatNo: 0,
    laneNo: 0,
  };
}

export function factoryIConfirmHeatAthlete(): IConfirmHeatAthlete {
  return {
    ageGroupId: 0,
    agegroup: "",
    ageshortgroup: "",
    bibno: 0,
    classification: 0,
    clubid: 0,
    clubname: "",
    county: "",
    dob: "",
    firstname: "",
    gender: "",
    id: 0,
    maxgroup: 0,
    region: "",
    surname: "",
    urn: "",
  };
}

export function ensureStructureOk(confirmHeat: IConfirmHeat): IConfirmHeat {
  const confirmHeatLocal = CommonServiceUtils.simpleClone(confirmHeat);
  if (!confirmHeatLocal.config) {
    confirmHeatLocal.config = factoryIConfirmHeatServerConfig();
  }
  if (confirmHeatLocal.config.pageCycleMs < 1000) {
    //  that can't ber right!?!?!  It will fry the CPU if less something silly
    //  like 10ms.  This here just to prevent it happening.
    confirmHeatLocal.config.pageCycleMs = 1000;
  }
  return confirmHeatLocal;
}

export function trimColumnText(content: string): string {
  if (content.length > 30) {
    return content.slice(0, 31) + "...";
  }
  return content;
}

export function getOrderedParticipants(
  confirmHeatParticipants: IConfirmHeatParticipant[]
): IConfirmHeatParticipant[] {
  return CommonServiceUtils.sortArray("laneNo", confirmHeatParticipants);
}

export function getTitle(confirmHeat: IConfirmHeat): string {
  let heatName = "";
  if (confirmHeat.participants.length > 0) {
    heatName = confirmHeat.participants[0].heatNo.toString();
  }

  return (
    confirmHeat.eventGroup.typeno +
    ": " +
    confirmHeat.eventGroup.event +
    ", heat: " +
    heatName
  );
}

export function getBibNo(
  confirmHeatParticipant: IConfirmHeatParticipant
): string {
  if (confirmHeatParticipant.athlete.bibno === 0) {
    return "";
  }
  return confirmHeatParticipant.athlete.bibno.toString();
}
