import {
  AthleteId,
  GenderType,
  IsoDate,
  IsoDateTime,
} from "../../../../../../common/common-models";
import { ISocketScoreboardConfigV3 } from "../../v3/useScoreboardOutputDisplayV3";

export interface IConfirmHeatState {
  confirmHeat: IConfirmHeat;
  // orderedParticipants: IConfirmHeatParticipant[];
  entriesMap: Record<AthleteId, IConfirmHeatAthleteEntry>;
  heatName: string;
}

export interface IConfirmHeat {
  competition: {
    id: number;
    name: string;
  };
  domain: string;
  eventGroup: {
    id: number;
    eventno: number;
    typeno: string;
    type: "T";
    date: IsoDateTime;
    event: string;
    heatCount: number;
    entries: IConfirmHeatAthleteEntry[];
  };
  participants: IConfirmHeatParticipant[];
  config: IConfirmHeatServerConfig;
  scoreboardConfig?: ISocketScoreboardConfigV3;
}

export interface IConfirmHeatServerConfig {
  pageSize: number; //  If 100m, easy, display all, but if a 5000m we might need to page.
  pageCycleMs: number;
}

export interface IConfirmHeatParticipant {
  athlete: IConfirmHeatAthlete;
  heatNo: number;
  laneNo: number;
}

export interface IConfirmHeatAthleteEntry {
  athleteid: number;
  entryId: number;
  checkedIn: "true" | "false";
  teambibno: string;
  eventGroupId: number;
  athlete: {
    id: number;
    bibno: number;
  };
}

export interface IConfirmHeatAthlete {
  id: number;
  firstname: string;
  surname: string;
  gender: GenderType;
  dob: IsoDate;
  urn: string;
  county: string;
  region: string;
  agegroup: string;
  ageGroupId: number;
  ageshortgroup: string;
  classification: number;
  clubid: number;
  clubname: string;
  maxgroup: number;
  bibno: number | string;
}
