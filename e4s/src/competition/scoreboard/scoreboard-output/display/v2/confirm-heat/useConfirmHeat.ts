import {
  reactive,
  SetupContext,
  onBeforeUnmount,
  computed,
} from "@vue/composition-api";
import {
  IConfirmHeat,
  IConfirmHeatParticipant,
  IConfirmHeatState,
} from "./confirm-heat-models";
import * as ConfirmHeatService from "./confirm-heat-service";
import * as CommonServiceUtils from "../../../../../../common/common-service-utils";
import {
  IUsePageCyclingConfig,
  usePageCycling,
} from "../../../../../../common/ui/page-cycling/usePageCycling";

export function useConfirmHeat(context: SetupContext) {
  const state: IConfirmHeatState = reactive(
    ConfirmHeatService.factoryConfirmHeatState()
  );

  const pageCyclingConfig: IUsePageCyclingConfig<IConfirmHeatParticipant> = {
    pageCycleMs: state.confirmHeat.config.pageCycleMs,
    pageSize: state.confirmHeat.config.pageSize,
    onFinishPageCycle: (pageNumber: number) => {
      context.emit("onFinishPageCycle", pageNumber);
    },
  };
  const pageCycling =
    usePageCycling<IConfirmHeatParticipant>(pageCyclingConfig);

  function setConfirmState(confirmHeat: IConfirmHeat) {
    state.confirmHeat = CommonServiceUtils.simpleClone(confirmHeat);
    init();
  }

  function init() {
    state.entriesMap = CommonServiceUtils.convertArrayToObject(
      "athleteid",
      state.confirmHeat.eventGroup.entries
    );

    const orderedParticipants: IConfirmHeatParticipant[] =
      ConfirmHeatService.getOrderedParticipants(
        state.confirmHeat.participants
      ).map((participant) => {
        //  TODO bib no's and structure changing so do not kow what strcuture/props to map.
        const athleteEntry = state.entriesMap[participant.athlete.id];
        const participantBibno = participant.athlete.bibno.toString();
        let athleteEntryBibNo = "";
        let athleteEntryTeamBibNo = "";
        if (athleteEntry && athleteEntry.athlete) {
          athleteEntryBibNo = athleteEntry.athlete.bibno.toString();
          athleteEntryTeamBibNo = athleteEntry.teambibno.toString();

          const useBibNo =
            participantBibno.length > 0 ? participantBibno : athleteEntryBibNo;

          participant.athlete.bibno =
            useBibNo +
            (athleteEntryTeamBibNo.length > 0
              ? " (" + athleteEntryTeamBibNo + ")"
              : "");
        }
        return participant;
      });

    if (orderedParticipants.length > 0) {
      state.heatName = state.confirmHeat.participants[0].heatNo.toString();
    }

    const pageSize = state.confirmHeat.config.pageSize;

    const pagesTotal = CommonServiceUtils.howManyPages(
      orderedParticipants,
      pageSize
    );

    const estimatedTimePerPageMs = state.confirmHeat.scoreboardConfig
      ? (state.confirmHeat.scoreboardConfig.displayTime * 1000) / pagesTotal
      : state.confirmHeat.config.pageCycleMs;

    // pageCycleMs: state.confirmHeat.config.pageCycleMs,
    pageCycling.setData(orderedParticipants, {
      pageCycleMs: estimatedTimePerPageMs,
      pageSize,
    });
  }

  const getEventName = computed(() => {
    return (
      state.confirmHeat.eventGroup.eventno +
      " - " +
      state.confirmHeat.eventGroup.event +
      " : " +
      state.heatName
    );
  });

  onBeforeUnmount(() => {
    pageCycling.stopPageCycle();
  });

  return {
    state,
    setConfirmState,
    getEventName,
    pageCycling,
  };
}
