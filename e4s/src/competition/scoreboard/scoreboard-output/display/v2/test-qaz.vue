<template>
  <div class="e4s-flex-column results-wrapper">
    <!--    <div class="e4s-flex-row results-header">-->
    <!--      <div class="e4s-flex-row">-->
    <!--        <img src="/content/e4s_logo.png" alt="" />-->
    <!--      </div>-->
    <!--      <div class="e4s-flex-row results-header-info-wrapper">-->
    <!--        <div class="e4s-flex-row results-header-info-container">-->
    <!--          <h3>Event Name</h3>-->
    <!--          <h3>Heat No</h3>-->
    <!--        </div>-->
    <!--        <div class="e4s-flex-row results-header-info-container">-->
    <!--          <h3>Wind Speed: <span>N/A m/s</span></h3>-->
    <!--          <h3>00/00/0000 - 00:00</h3>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->
    <ScoreboardHeaderV2
      event-name="Triple Jump"
      heat-no="3"
    />

    <div class="e4s-flex-row finish-results">
      <div class="finish-results-table-container">
        <table class="results-table">
          <thead>
            <tr>
              <th style="width: 3vw"></th>
              <th></th>
              <th>Bib</th>
              <th>Time</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="position-wrapper">
                <div class="position-container first-place">
                  <span>1</span>
                </div>
              </td>
              <td class="athlete-team-wrapper">
                <div class="e4s-flex-column">
                  <div class="athlete-container">
                    <span>Joseph-Anthony Macklesmere</span>
                  </div>
                  <div class="athlete-team-container">
                    <span>Basingstoke & Mid Hants</span>
                  </div>
                </div>
              </td>
              <td style="text-align: center">
                <div class="bib-container">
                  <span>123</span>
                </div>
              </td>
              <td style="text-align: center">
                <div class="time-container">
                  <span>00:00.00</span>
                </div>
              </td>
            </tr>
            <tr>
              <td class="position-wrapper">
                <div class="position-container second-place">
                  <span>2</span>
                </div>
              </td>
              <td class="athlete-team-wrapper">
                <div class="e4s-flex-column">
                  <div class="athlete-container">
                    <span
                      >Joseph-Anthony Macklesmere Joseph-Anthony
                      Macklesmere</span
                    >
                  </div>
                  <div class="athlete-team-container">
                    <span
                      >A really, really, really, really, really long name Club
                      Name</span
                    >
                  </div>
                </div>
              </td>
              <td style="text-align: center">
                <div class="bib-container">
                  <span>123</span>
                </div>
              </td>
              <td style="text-align: center">
                <div class="time-container">
                  <span>00:00.00</span>
                </div>
              </td>
            </tr>
            <tr>
              <td class="position-wrapper">
                <div class="position-container third-place">
                  <span>3</span>
                </div>
              </td>
              <td class="athlete-team-wrapper">
                <div class="e4s-flex-column">
                  <div class="athlete-container">
                    <span
                      >Joseph-Anthony Macklesmere Joseph-Anthony
                      Macklesmere</span
                    >
                  </div>
                  <div class="athlete-team-container">
                    <span
                      >A really, really, really, really, really long name Club
                      Name</span
                    >
                  </div>
                </div>
              </td>
              <td style="text-align: center">
                <div class="bib-container">
                  <span>123</span>
                </div>
              </td>
              <td style="text-align: center">
                <div class="time-container">
                  <span>00:00.00</span>
                </div>
              </td>
            </tr>
            <tr>
              <td class="position-wrapper">
                <div class="position-container">
                  <span>4</span>
                </div>
              </td>
              <td class="athlete-team-wrapper">
                <div class="e4s-flex-column">
                  <div class="athlete-container">
                    <span
                      >Joseph-Anthony Macklesmere Joseph-Anthony
                      Macklesmere</span
                    >
                  </div>
                  <div class="athlete-team-container">
                    <span
                      >A really, really, really, really, really long name Club
                      Name</span
                    >
                  </div>
                </div>
              </td>
              <td style="text-align: center">
                <div class="bib-container">
                  <span>123</span>
                </div>
              </td>
              <td style="text-align: center">
                <div class="time-container">
                  <span>00:00.00</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="finish-image-container">
        <img src="/components/e4s_picture_finish/picture_finish.png" alt="" />
      </div>
    </div>

    <div
      class="e4s-flex-row"
      style="align-items: center; justify-content: center; padding: 0.5%"
    >
      <h5 style="font-size: 2vh; color: var(--zinc-50)">
        For full result: https://entry4sports.co.uk/xxx/xxx
      </h5>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import ScoreboardHeaderV2 from "./scoreboard-header-v2.vue";

export default defineComponent({
  // type inference enabled
  name: "test-qaz",
  components: { ScoreboardHeaderV2 },
  props: {},
  setup(props: { consult: any }, context: SetupContext) {
    return {};
  },
});
</script>

<style>
@import url("https://fonts.googleapis.com/css2?family=Play:wght@400;700&display=swap");

.results-wrapper {
  height: 100vh;
  padding: 0 2%;
  background: black;
  overflow: hidden;
}

.finish-result {
  align-items: center;
  column-gap: 2vw;
}

.finish-results {
  width: 100%;
  flex-grow: 1;
}

.position-wrapper {
  width: 3vw;
}

.position-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 4vh;
  min-width: 50px;
  max-width: 3vw;
  width: 100%;
  border-radius: 10px 5px 10px 5px;
  background-color: var(--zinc-50);
  font-size: 1.5vw;
  font-weight: 700;
  text-align: center;
  color: var(--zinc-900);
}

.first-place {
  border-left: 0.3rem solid var(--yellow-600);
}

.second-place {
  border-left: 0.3rem solid var(--zinc-500);
}

.third-place {
  border-left: 0.3rem solid var(--yellow-900);
}

.athlete-team-wrapper {
  max-width: 35vw;
  width: 100%;
}

.athlete-container {
  max-width: 35vw;
  padding: 0 0 0 1vw;
  font-size: 1.5vw;
  font-weight: 700;
  color: var(--zinc-50);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.athlete-team-container {
  max-width: 35vw;
  padding: 0 0 0 1vw;
  font-size: 1vw;
  font-weight: 400;
  color: var(--zinc-50);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bib-container,
.time-container,
.diff-container {
  font-size: 1.5vw;
  font-weight: 700;
  color: var(--zinc-50);
}

.finish-results-table-container {
  width: 60%;
}

.results-table {
  margin: 2vh 0;
  border-collapse: collapse;
}

.results-table span {
  font-family: "Play", sans-serif;
}

.results-table th {
  padding-bottom: 2vh;
  font-size: 2vh;
  color: var(--zinc-50);
}

.results-table td {
  padding: 1vw;
  vertical-align: top;
}

.finish-image-container {
  width: 40%;
  height: 100%;
}

.finish-image-container img {
  width: 100%;
  object-fit: scale-down;
  object-position: top center;
}
</style>
