<template functional>
    <table class="r4s-output-schedule--table">

        <template v-for="schedule in props.scoreboardSchedules">

            <tr class="r4s-output-schedule--row" :key="schedule.id">

                <td class="r4s-output-schedule--td-each-end"></td>

                <td>
                    <div class="row">

                        <div class="col s1 m1 l1">
<!--                            <i class="material-icons bigger"><template v-text="$options.methods.getDescriptionIcon(schedule)"></template></i>-->
                            <span v-html="$options.methods.getDescriptionIcon(schedule)"></span>
                        </div>

                        <div class="col s5 m4 l4">
                            <span v-text="$options.methods.getDateOutput(schedule, true)"></span> -
                            <span v-text="$options.methods.getDateOutput(schedule, false)"></span>
                        </div>

<!--                        <div class="col s6 m2 l2">-->
<!--                            <span v-text="$options.methods.getDateOutput(schedule, false)"></span>-->
<!--                        </div>-->

                        <div class="col s12 m7 l7">
                            <span v-text="$options.methods.getEventsOutput(schedule)"></span>
                        </div>

                    </div>
                </td>

                <td class="r4s-output-schedule--td-each-end"></td>
            </tr>


        </template>

    </table>
</template>

<script lang="ts">

import {IScoreboardSchedule} from "../../scoreboard-schedule/scoreboard-schedule-models"
import { parse, format } from "date-fns";

export default {
    props: ["scoreboardSchedules"],
    methods: {
        getDateOutput: (scoreboardSchedule: IScoreboardSchedule, isStartDate: boolean) => {
            return format(
                parse(isStartDate ? scoreboardSchedule.startdt : scoreboardSchedule.enddt),
                isStartDate ? "Do MMM h:mm a" : "h:mm a"
            );
        },
        getEventsOutput: (scoreboardSchedule: IScoreboardSchedule) => {
            return scoreboardSchedule.events.map( (eventGroup) => {
                return eventGroup.name;
            }).join(", ");
        },
        // getSchedules: (scoreboardSchedules: IScoreboardSchedule[]): IScoreboardSchedule[] => {
        //     return commonService.sortArray("startdt", scoreboardSchedules);
        // },
        getDescriptionIcon: (scoreboardSchedule: IScoreboardSchedule): string => {
            let iconName = "radio_button_unchecked"
            // 1 = Scoreboard, 2 = Ranking, 3 = Scoreboard + Ranking, 4 = Picture

            if (scoreboardSchedule.design.designNo === 1) {
                iconName = "looks_one";
            }

            if (scoreboardSchedule.design.designNo === 2) {
                iconName = "grid_on";
            }

            if (scoreboardSchedule.design.designNo === 3) {
                iconName = "filter_1";
            }

            if (scoreboardSchedule.design.designNo === 4) {
                iconName = "photo";
            }

            return "<i class='material-icons r4s-output-schedule--design-icon' title='" + scoreboardSchedule.design.designNo + ": " +
                scoreboardSchedule.design.description +"'>" + iconName + "</template></i>"
        }
    }
};
</script>

<style>
.r4s-output-schedule--table {
    font-size: 2.0em;
}

.r4s-output-schedule--row {
    border-bottom: 0.5vh solid black;
}

.r4s-output-schedule--design-icon {
    font-size: 5vh !important;
}

.r4s-output-schedule--td-each-end {

}
</style>
