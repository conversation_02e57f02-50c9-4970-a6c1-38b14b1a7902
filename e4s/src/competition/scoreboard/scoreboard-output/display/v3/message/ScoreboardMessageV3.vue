<template>
  <div
    v-html="scoreboardMessage.message"
    :style="rs4ScoreboardOutput.options.ui.message.body"
  ></div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "@vue/composition-api";
import { ScoreboardOutputService } from "../../../scoreboard-output-service";
import {
  IRs4ScoreboardOutput,
  IScoreboardMessage,
} from "../../../../rs4/rs4-scoreboard-models";

const scoreboardOutputService = new ScoreboardOutputService();

export default defineComponent({
  name: "ScoreboardMessageV3",
  components: {},
  props: {
    rs4ScoreboardOutput: {
      type: Object as PropType<IRs4ScoreboardOutput>,
      default: () => {
        return scoreboardOutputService.factoryScoreboardOutput();
      },
    },
    scoreboardMessage: {
      type: Object as PropType<IScoreboardMessage>,
      default: () => {
        return {
          title: "",
          message: "",
        } as IScoreboardMessage;
      },
    },
  },
});
</script>
