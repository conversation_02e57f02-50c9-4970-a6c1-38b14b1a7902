import { PBService } from "../../../../../../athleteCompSched/pb-service";
import * as CommonServiceUtils from "../../../../../../common/common-service-utils";
import {
  IR4sPhotoFinishResult,
  IR4sSocketPhotoFinishMessage,
} from "../../../../rs4/rs4-scoreboard-models";
import { isValidHttpUrl } from "../../../../../../common/common-service-utils";
import { CONFIG } from "../../../../../../common/config";
const pbservice = new PBService();

export function getResultForDisplayFormatted(resultvalue: string): string {
  if (!CommonServiceUtils.isNumeric(resultvalue.toString())) {
    //  All we can do is return value.  So that means if we get DNS or 1.12:13 or whatever.
    return resultvalue.toString();
  }

  if (Number(resultvalue) <= 60) {
    return CommonServiceUtils.roundNumberToDecimalPlaces(
      resultvalue,
      2,
      false
    ).toString();
  }

  if (Number(resultvalue) <= 600) {
    return pbservice.convertSecondsToUserFormat(Number(resultvalue), "m:ss:SS");
  }

  return pbservice.convertSecondsToUserFormat(Number(resultvalue), "mm:ss:SS");
}

export function getResultForDisplay(result: IR4sPhotoFinishResult): string {
  if (CommonServiceUtils.hasAtLeastOneCharacter(result.resultvalue)) {
    //  This will pick up DNS, DQ whatever
    return result.resultvalue.toString();
  }

  if (!CommonServiceUtils.isNumeric(result.resultvalue.toString())) {
    //  This will pick up 4.56:13, 4.56.13
    return result.resultvalue.toString();
  }

  // const result2dp: string = CommonServiceUtils.roundNumberToDecimalPlaces(
  //   result.timeInSeconds,
  //   2,
  //   false
  // ).toString();

  if (result.timeInSeconds === 0) {
    return "-";
  }

  //  This will convert time in seconds to a format, e.g. 10.13, 231.89, 459 etc.
  return getResultForDisplayFormatted(result.timeInSeconds.toString());
}

export function getPhotoFinishLink(
  socketPhotoFinishMessage: IR4sSocketPhotoFinishMessage
): string {
  if (socketPhotoFinishMessage.payload.picture) {
    const isValid = isValidHttpUrl(socketPhotoFinishMessage.payload.picture);
    if (isValid) {
      return socketPhotoFinishMessage.payload.picture;
    }
    return CONFIG.E4S_HOST + socketPhotoFinishMessage.payload.picture;
  }
  return "#";
}
