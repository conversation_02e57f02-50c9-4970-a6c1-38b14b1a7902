import * as PhotoFinishService from "./photo-finish-service";
import {IR4sPhotoFinishResult} from "../../../../rs4/rs4-scoreboard-models";

describe("Photo-Finish", () => {
  test("convertSecondsToUserFormat", () => {
    let result;

    result = PhotoFinishService.getResultForDisplayFormatted("615.09");
    expect(result).toBe("10:15:09");

    result = PhotoFinishService.getResultForDisplayFormatted("599.00");
    expect(result).toBe("9:59:00");

    result = PhotoFinishService.getResultForDisplayFormatted("241.09");
    expect(result).toBe("4:01:09");

    result = PhotoFinishService.getResultForDisplayFormatted("10.09");
    expect(result).toBe("10.09");

    result = PhotoFinishService.getResultForDisplayFormatted("9.09");
    expect(result).toBe("9.09");

    result = PhotoFinishService.getResultForDisplayFormatted("9.09x");
    expect(result).toBe("9.09x");

    result = PhotoFinishService.getResultForDisplayFormatted("DNS");
    expect(result).toBe("DNS");

  })

  test("getResultForDisplay", () => {
    let result;
    result = PhotoFinishService.getResultForDisplay({
      "resultkey": "h1",
      "resultvalue": "DQ",
      "timeInSeconds": 0
    } as any as IR4sPhotoFinishResult);
    expect(result).toBe("DQ");

    result = PhotoFinishService.getResultForDisplay({
      "resultkey": "h1",
      "resultvalue": "10.12",
      "timeInSeconds": 10.12
    } as any as IR4sPhotoFinishResult);
    expect(result).toBe("10.12");

    result = PhotoFinishService.getResultForDisplay({
      "resultkey": "h1",
      "resultvalue": "451.09",
      "timeInSeconds": 451.09
    } as any as IR4sPhotoFinishResult);
    expect(result).toBe("7:31:09");

    result = PhotoFinishService.getResultForDisplay({
      "resultkey": "h1",
      "resultvalue": "1.20.09",
      "timeInSeconds": 80.09
    } as any as IR4sPhotoFinishResult);
    expect(result).toBe("1.20.09");

    result = PhotoFinishService.getResultForDisplay({
      "resultkey": "h1",
      "resultvalue": "0",
      "timeInSeconds": 0.0
    } as any as IR4sPhotoFinishResult);
    expect(result).toBe("-");

  })
});
