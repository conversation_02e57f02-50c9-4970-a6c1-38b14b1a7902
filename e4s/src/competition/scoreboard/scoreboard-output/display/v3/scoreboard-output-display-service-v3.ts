import {
  IR4sSocketDataMessageDatav3,
  IScoreboardOutputDisplayStateV3,
  ISocketAthletesMetaV3,
  ISocketMetaV3,
} from "./useScoreboardOutputDisplayV3";
import { simpleClone } from "../../../../../common/common-service-utils";
import {
  IConfirmHeat,
  IConfirmHeatParticipant,
} from "../v2/confirm-heat/confirm-heat-models";
import {
  IR4sSocketAthleteFieldResults,
  IR4sSocketPhotoFinishMessage,
  R4S_SOCKET_ACTIONS,
  SocketActionData,
} from "../../../rs4/rs4-scoreboard-models";
import { ScoreboardTrackResultV3 } from "./track/scoreboard-track-models-v3";
import { IScoreboardSchedule } from "../../../scoreboard-schedule/scoreboard-schedule-models";
import {
  EventGroupIdString,
  IBaseConcrete,
} from "../../../../../common/common-models";

export function factorySocketMetaV3(): ISocketMetaV3 {
  return {
    displayTimeOutMs: 0,
    displayedCount: 0,
    forceDisplay: false,
    minTimeToDisplayMs: 0,
    showNext: false,
    showNumberOfTimes: -1,
    updatedCount: 0,
  };
}

export function factorySocketAthletesMetaV3(): ISocketAthletesMetaV3 {
  return {
    ...factorySocketMetaV3(),
    pageAthleteCycleMs: 0,
    pageSize: 1,
  };
}

export function getSocketMessageKey(
  socketDataMessageData: IR4sSocketDataMessageDatav3
): string {
  let key: string = socketDataMessageData.key;
  const action = socketDataMessageData.action;

  if (action === "field-results") {
    const socketAthleteFieldResults: IR4sSocketAthleteFieldResults =
      socketDataMessageData as any as IR4sSocketAthleteFieldResults;

    const scoreboardTrackResultV3: ScoreboardTrackResultV3 =
      socketAthleteFieldResults.payload.ranking;
    key = scoreboardTrackResultV3.eventGroup.id.toString();
  }

  if (action === "confirmHeat") {
    const confirmHeat: IConfirmHeat =
      socketDataMessageData.payload as any as IConfirmHeat;
    key = confirmHeat.eventGroup.id.toString();
    const confirmHeatParticipant: IConfirmHeatParticipant =
      confirmHeat.participants[0];
    if (confirmHeatParticipant) {
      key += "-" + confirmHeatParticipant.heatNo.toString();
    }
  }

  if (key.length === 0) {
    key = createSocketMessageKey(socketDataMessageData);
  }
  return socketDataMessageData.comp.id + "-" + action + "-" + key;
}

export function createSocketMessageKey(
  socketDataMessageData: IR4sSocketDataMessageDatav3
): string {
  const action = socketDataMessageData.action;
  let key: string = "";
  if (action === "confirmHeat") {
    key = (socketDataMessageData.payload as any as IConfirmHeat).eventGroup
      .typeno;
  }
  //  if key is empty, create random string 8 characters long
  return key.length === 0 ? Math.random().toString(36).substring(2, 10) : key;
}

export function isMessageAlreadyInQueue(
  socketDataMessageData: IR4sSocketDataMessageDatav3,
  messagesQueue: IR4sSocketDataMessageDatav3[]
): boolean {
  const key = getSocketMessageKey(socketDataMessageData);
  return simpleClone(messagesQueue).some(
    (message) => getSocketMessageKey(message) === key
  );
}

export function getMessageInQueue(
  socketDataMessageData: IR4sSocketDataMessageDatav3,
  messagesQueue: IR4sSocketDataMessageDatav3[]
): IR4sSocketDataMessageDatav3 | null {
  const key = getSocketMessageKey(socketDataMessageData);
  return (
    simpleClone(messagesQueue).find(
      (message) => getSocketMessageKey(message) === key
    ) || null
  );
}

export function getMessagePositionInQueue(
  socketDataMessageData: IR4sSocketDataMessageDatav3,
  messagesQueue: IR4sSocketDataMessageDatav3[]
): number {
  const key = getSocketMessageKey(socketDataMessageData);
  return simpleClone(messagesQueue).findIndex(
    (message) => getSocketMessageKey(message) === key
  );
}

export function cancelMessage(
  socketDataMessageData: IR4sSocketDataMessageDatav3,
  messagesQueue: IR4sSocketDataMessageDatav3[]
): IR4sSocketDataMessageDatav3[] {
  const key = getSocketMessageKey(socketDataMessageData);
  return simpleClone(messagesQueue).filter((message) => {
    const isKeyMatch = getSocketMessageKey(message) !== key;
    return isKeyMatch;
  });
}

export function doesDomainMatch(
  messageDomain: string,
  acceptableDomainList: string[]
): boolean {
  return acceptableDomainList.some((domain) => {
    return domain.indexOf(messageDomain) > -1 || domain === "*";
  });
}

export function mergeInMessageMetaIfMissing(
  message: IR4sSocketDataMessageDatav3,
  defaultSocketMetaV3: ISocketMetaV3 | ISocketAthletesMetaV3
): IR4sSocketDataMessageDatav3 {
  if (message.meta) {
    return message;
  }

  return {
    ...message,
    meta: simpleClone(defaultSocketMetaV3),
  };
}

/**
 * Newest message go at bottom of queue, so the messages to display are on the top.
 * So messages flow from the bottom to the top and get displayed when in position zero
 * @param message
 * @param queue
 */
export function addMessageToQueue(
  message: IR4sSocketDataMessageDatav3,
  queue: IR4sSocketDataMessageDatav3[]
): IR4sSocketDataMessageDatav3[] {
  const meta = message.meta;

  //  If queue is empty, then just return message.
  if (queue.length === 0) {
    return [message];
  }

  queue = simpleClone(queue);

  const position = getMessagePositionInQueue(message, queue);

  if (meta.forceDisplay || meta.showNext) {
    if (position > -1) {
      //  Remove message from queue.
      queue = queue.filter((messageInQueue, index) => {
        return index !== position;
      });
    }
  }

  if (meta.forceDisplay) {
    return [message, ...queue];
  }

  if (meta.showNext) {
    //  E.g. if queue is [a, b, c, d] and  adding c, then queue becomes [a, c, b, d].
    queue =
      queue.length === 0 ? [message] : [queue[0], message, ...queue.slice(1)];
    return queue;
  }

  //  Message is already in the queue, so update it.
  if (position > -1) {
    const currentCount = queue[position].meta.updatedCount;
    queue[position] = simpleClone(message);
    queue[position].meta.updatedCount = currentCount + 1;
    return queue;
  }

  //  Stick message on the end of the queue.
  return [...queue, message];
}

export function getNextQueueState(
  messages: IR4sSocketDataMessageDatav3[],
  maxQueueLength: number
): IR4sSocketDataMessageDatav3[] {
  if (messages.length === 0) {
    return [];
  }
  messages = simpleClone(messages);

  //  New rule...if there is only one message in the queue, then don't do anything...
  //  Leave it there.
  if (messages.length === 1) {
    return messages;
  }

  const currentMessage = messages[0];

  //  Take the top position off.
  const newQueue = messages.slice(1);

  //  but if queue length is less than max...
  if (newQueue.length < maxQueueLength) {
    //  and message can be displayed again...
    if (canMessageBeDisplayedAgain(currentMessage)) {
      newQueue.push(currentMessage);
    }
  }

  //  N.B.  the newQueue might be bigger than the maxQueueLength, but that's ok
  //  as we haven't shown the message yet. as results are coming in thick and fast
  return newQueue;
}

// export function getNextQueueStateTest(
//   messages: IR4sSocketDataMessageDatav3[],
//   maxQueueLength: number
// ): any {
//   messages = simpleClone(messages);
//   const currentMessage = messages[0];
//
//   //  If queue length is less than max, then put the first element in last position, else take top position off.
//   const newQueue = messages.slice(1);
//
//   if (newQueue.length < maxQueueLength) {
//     if (canMessageBeDisplayedAgain(currentMessage)) {
//       newQueue.push(currentMessage);
//     }
//   }
//
//   return {
//     mLen: messages.length,
//     max: maxQueueLength,
//     is: messages.length < maxQueueLength,
//     can: canMessageBeDisplayedAgain(currentMessage),
//     nLen: newQueue.length,
//   };
// }

export function canMessageBeDisplayedAgain(
  message: IR4sSocketDataMessageDatav3
) {
  //  Message can be displayed permanently.
  if (message.meta.showNumberOfTimes === -1) {
    return true;
  }
  return message.meta.displayedCount < message.meta.showNumberOfTimes;
}

export function messageInQueueFinishedDisplaying(
  messages: IR4sSocketDataMessageDatav3[]
): IR4sSocketDataMessageDatav3[] {
  messages = simpleClone(messages);
  const message = messages[0];
  if (message) {
    messages[0] = setMessageAsDisplayed(message);
  }
  return messages;
}

export function setMessageAsDisplayed(
  message: IR4sSocketDataMessageDatav3
): IR4sSocketDataMessageDatav3 {
  message = simpleClone(message);
  message.meta.displayedCount++;
  return message;
}

export interface IShouldMessageBeDisplayedResult {
  identifier:
    | "OK"
    | "NO_COMP"
    | "NO_COMP_ID_MATCH"
    | "MESSAGE_TYPE_NO_MATCH"
    | "DOMAIN_NO_MATCH"
    | "EVENT_GROUP_NO_MATCH";
  should: boolean;
  reason: string;
  level: 1 | 2 | 3 | 4 | 5 | 100;
}

export function shouldMessageBeDisplayed(
  message: IR4sSocketDataMessageDatav3,
  scoreboardOutputDisplayStateV3: IScoreboardOutputDisplayStateV3
): IShouldMessageBeDisplayedResult {
  const action = message.action;

  //  First check...if not action we are interested in...then return false.
  if (
    scoreboardOutputDisplayStateV3.ui.listenForMessageTypes.indexOf(action) ===
    -1
  ) {
    return {
      identifier: "MESSAGE_TYPE_NO_MATCH",
      should: false,
      reason:
        message.action +
        ": is not in the list of message types to listen for: " +
        scoreboardOutputDisplayStateV3.ui.listenForMessageTypes.join(", "),
      level: 4,
    };
  }

  const messageFromDomainMatch = doesDomainMatch(
    message.domain,
    scoreboardOutputDisplayStateV3.ui.listenForMessageDomains
  );
  if (!messageFromDomainMatch) {
    return {
      identifier: "DOMAIN_NO_MATCH",
      should: false,
      reason:
        message.domain +
        ": is not in the list of domains to listen for: " +
        scoreboardOutputDisplayStateV3.ui.listenForMessageDomains.join(", "),
      level: 1,
    };
  }

  if (!message.comp) {
    return {
      identifier: "NO_COMP",
      should: false,
      reason: "No competition specified.",
      level: 2,
    };
  }

  if (
    scoreboardOutputDisplayStateV3.ui.listenForCompIds.indexOf(
      message.comp.id
    ) === -1
  ) {
    return {
      identifier: "NO_COMP_ID_MATCH",
      should: false,
      reason:
        message.comp.id +
        ": is not in the list of comps to listen for: " +
        scoreboardOutputDisplayStateV3.ui.listenForCompIds.join(", "),
      level: 3,
    };
  }

  const nonEventActions: R4S_SOCKET_ACTIONS[] = [
    "output-schedule--refresh",
    "output-schedule--reload",
    "output-schedule--reset",
  ];
  if (nonEventActions.indexOf(action) > -1) {
    return {
      identifier: "OK",
      should: true,
      reason: "Score board config action message",
      level: 100,
    };
  }

  //  TODO is event to monitor.
  const eventGroupID = getEventIdFromMessage(message);
  const eventGroupIds = Object.keys(
    scoreboardOutputDisplayStateV3.ui.listenForEventGroupIdsMap
  );

  const isEventMessageIdInEventGroupIds =
    scoreboardOutputDisplayStateV3.ui.listenForEventGroupIds.indexOf(
      eventGroupID
    ) > -1;
  const isEventMessageIdInEventGroupIdsMap =
    scoreboardOutputDisplayStateV3.ui.listenForEventGroupIdsMap[
      eventGroupID.toString()
    ];
  if (isEventMessageIdInEventGroupIds && !isEventMessageIdInEventGroupIdsMap) {
    return {
      identifier: "OK",
      should: true,
      reason:
        "Event specified in config but does ot exist as one to watch in schedule, but will show.",
      level: 100,
    };
  }

  if (eventGroupIds.length > 0) {
    if (eventGroupID === -1) {
      return {
        identifier: "OK",
        should: true,
        reason: "Could not get event group id from message, but will show",
        level: 100,
      };
    }
    if (
      !scoreboardOutputDisplayStateV3.ui.listenForEventGroupIdsMap[
        eventGroupID.toString()
      ]
    ) {
      return {
        identifier: "EVENT_GROUP_NO_MATCH",
        should: false,
        reason:
          eventGroupID +
          ": is not in the list of message event groups to listen for: " +
          Object.keys(
            scoreboardOutputDisplayStateV3.ui.listenForEventGroupIdsMap
          ).join(", "),
        level: 5,
      };
    }
  }

  //  TODO is event in time frame

  return {
    identifier: "OK",
    should: true,
    reason: "",
    level: 100,
  };
}

export function getHeaderText(message: IR4sSocketDataMessageDatav3): string {
  const actionMap: Partial<
    Record<SocketActionData, (message: IR4sSocketDataMessageDatav3) => string>
  > = {
    "field-results": getEventName,
    confirmHeat: getEventName,
    photofinish: getEventName,
    video: getEventName,
  };

  const actionFromMap = actionMap[message.action];
  if (actionFromMap) {
    return actionFromMap(message);
  }
  return "Header text....under construction";
}

export function getEventName(message: IR4sSocketDataMessageDatav3): string {
  if (message.action === "field-results") {
    return (message as any as IR4sSocketAthleteFieldResults).payload.ranking
      .eventGroup.name;
  }
  if (message.action === "confirmHeat") {
    const eventGroup = (message.payload as any as IConfirmHeat).eventGroup;
    const confirmHeatParticipant: IConfirmHeatParticipant = (
      message.payload as any as IConfirmHeat
    ).participants[0];
    const heatNo = confirmHeatParticipant
      ? confirmHeatParticipant.heatNo.toString()
      : "";

    return (
      (eventGroup.typeno.length > 0 ? eventGroup.typeno + ": " : "") +
      eventGroup.event +
      ", " +
      (eventGroup.type === "T" ? "Race" : "Heat") +
      ": " +
      heatNo
    );
  }
  if (message.action === "photofinish" || message.action === "video") {
    const payload = (message as any as IR4sSocketPhotoFinishMessage).payload;
    return (
      (payload.typeNo.length > 0 ? payload.typeNo + ": " : "") +
      payload.eventName
    );
  }
  return "Under construction";
}

export function getDisplayTime(
  message: IR4sSocketDataMessageDatav3
): number | null {
  if (message.action === "confirmHeat") {
    const confirmHeat = message.payload as any as IConfirmHeat;
    const scoreboardConfig = confirmHeat.scoreboardConfig;
    return scoreboardConfig
      ? scoreboardConfig.displayTime
      : confirmHeat.config.pageCycleMs;
  }
  return message.meta.displayTimeOutMs ? message.meta.displayTimeOutMs : null;
}

export function getEventsFromSchedules(
  schedules: IScoreboardSchedule[]
): Record<EventGroupIdString, IBaseConcrete> {
  const events: Record<EventGroupIdString, IBaseConcrete> = {};
  schedules.forEach((schedule) => {
    schedule.events.forEach((event: IBaseConcrete) => {
      events[event.id.toString()] = event;
    });
  });
  return events;
}

export function getEventIdFromMessage(
  message: IR4sSocketDataMessageDatav3
): number {
  if (message.action === "field-results") {
    return (message as any as IR4sSocketAthleteFieldResults).payload.ranking
      .eventGroup.id;
  }
  if (message.action === "confirmHeat") {
    return (message.payload as any as IConfirmHeat).eventGroup.id;
  }
  if (message.action === "photofinish" || message.action === "video") {
    return (message as any as IR4sSocketPhotoFinishMessage).payload.eventGroup
      .id;
  }
  return -1;
}
