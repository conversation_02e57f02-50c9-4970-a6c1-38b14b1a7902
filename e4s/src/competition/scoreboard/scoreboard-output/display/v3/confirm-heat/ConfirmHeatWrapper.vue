<template>
  <ConfirmHeat
    :rs4-scoreboard-output="rs4ScoreboardOutput"
    :confirm-heat="confirmHeat"
    v-on:onFinishPageCycle="$emit('onFinishPageCycle', $event)"
  />
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import ConfirmHeat from "../../v2/confirm-heat/confirm-heat.vue";
import { IRs4ScoreboardOutput } from "../../../../rs4/rs4-scoreboard-models";
import * as ConfirmHeatService from "../../v2/confirm-heat/confirm-heat-service";
import { IConfirmHeat } from "../../v2/confirm-heat/confirm-heat-models";
import { IR4sSocketDataMessageDatav3 } from "../useScoreboardOutputDisplayV3";

export default defineComponent({
  name: "ConfirmHeatWrapper",
  components: { ConfirmHeat },
  props: {
    rs4ScoreboardOutput: {
      type: Object as PropType<IRs4ScoreboardOutput>,
      required: true,
    },
    socketDataMessage: {
      type: Object as PropType<IR4sSocketDataMessageDatav3>,
      required: true,
    },
  },
  setup(
    props: {
      rs4ScoreboardOutput: IRs4ScoreboardOutput;
      socketDataMessage: IR4sSocketDataMessageDatav3;
    },
    context: SetupContext
  ) {
    const confirmHeat = ref(createConfirmHeat(props.socketDataMessage));

    watch(
      () => props.socketDataMessage,
      (newValue: IR4sSocketDataMessageDatav3) => {
        confirmHeat.value = createConfirmHeat(newValue);
      },
      {
        immediate: true,
      }
    );

    function createConfirmHeat(
      newValue: IR4sSocketDataMessageDatav3
    ): IConfirmHeat {
      const currentConfirmHeat = ConfirmHeatService.ensureStructureOk(
        props.socketDataMessage.payload as any as IConfirmHeat
      );

      currentConfirmHeat.config.pageSize =
        props.rs4ScoreboardOutput.options.timings.ranking.pageSize;
      currentConfirmHeat.config.pageCycleMs =
        props.rs4ScoreboardOutput.options.timings.ranking.pageCycleMs;

      return currentConfirmHeat;
    }

    return { confirmHeat };
  },
});
</script>
