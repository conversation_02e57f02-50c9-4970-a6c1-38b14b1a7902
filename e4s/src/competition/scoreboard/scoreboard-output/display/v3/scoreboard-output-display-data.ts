import { SocketAction } from "../../../rs4/rs4-scoreboard-models";
import https from "../../../../../common/https";
import { IServerResponse } from "../../../../../common/common-models";

export function sendSocketCommand(
  compId: number,
  outputId: number,
  socketAction: SocketAction,
  payload?: any
): Promise<IServerResponse<unknown>> {
  return https.post(
    "/v5/scoreboard/command/" + compId + "/" + outputId + "/" + socketAction,
    {
      data: payload,
    }
  ) as any as Promise<IServerResponse<unknown>>;
}

export function sendSocketPayload(
  compId: number,
  outputId: number,
  socketAction: SocketAction,
  payload?: any
): Promise<IServerResponse<unknown>> {
  return https.post("/v5/socket/command/" + compId, {
    data: payload,
  }) as any as Promise<IServerResponse<unknown>>;
}
