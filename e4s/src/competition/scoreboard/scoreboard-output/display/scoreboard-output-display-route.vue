<template>
    <div>
        <ScoreboardOutputDisplay :comp-id="compId" :output-id="outputId"></ScoreboardOutputDisplay>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import ScoreboardOutputDisplay from "./scoreboard-output-display.vue"

@Component({
    name: "scoreboard-output-display-route",
    components: {ScoreboardOutputDisplay}
})
export default class ScoreboardOutputDisplayRoute extends Vue {
    public compId: number = 0;
    public outputId: number = 0;
    public created() {
        this.compId = isNaN(Number(this.$route.params.compId)) ? 0 : parseInt(this.$route.params.compId, 0);
        this.outputId = isNaN(Number(this.$route.params.outputId)) ? 0 : parseInt(this.$route.params.outputId, 0);
    }
}
</script>
