import { LAUNCH_ROUTES_PATHS } from "../launch/launch-routes";
import { LAUNCH_ROUTES_PATHS_V2 } from "../launch/v2/launch-routes-v2";
import { RawLocation } from "vue-router";

import { ICompetitionSummaryPublic } from "./competition-models";
import { useRouter } from "../router/migrateRouterVue3";
import { simpleClone } from "../common/common-service-utils";
import { useAuthStore } from "../auth/useAuthStore";
import { reactive } from "@vue/composition-api";

export interface UseGotoCompControllerState {
  competitionSummaryPublic: ICompetitionSummaryPublic;
  isV2Route: boolean;
  useV2Route: boolean;
  isLoggedIn: boolean;
  entry: {
    v1: string;
    v2: string;
    compUrl: string;
  };
  login: {
    v1: string;
    v2: string;
    loginUrl: string;
  };
  shop: {
    v1: string;
    v2: string;
    shopUrl: string;
  };
}

export function useGotoCompControllerHybrid(
  competitionSummaryPublic: ICompetitionSummaryPublic
) {
  // E4S-386
  const routerInternal = useRouter();
  const authStore = useAuthStore();

  const state = reactive<UseGotoCompControllerState>({
    state: simpleClone(competitionSummaryPublic),
    isV2Route: false,
    useV2Route: false,
    isLoggedIn: false,
    entry: {
      v1: "",
      v2: "",
      compUrl: "",
    },
    shop: {
      v1: "",
      v2: "",
      shopUrl: "",
    },
    login: {
      v1: "",
      v2: "",
      loginUrl: "",
    },
  });

  init(competitionSummaryPublic);

  function init(competitionSummaryPublic: ICompetitionSummaryPublic) {
    state.competitionSummaryPublic = simpleClone(competitionSummaryPublic);
    state.isV2Route = routerInternal.currentRoute.path.startsWith("/v2/");
    state.useV2Route =
      competitionSummaryPublic.options.useV2Routes || state.isV2Route;
    state.isLoggedIn = authStore.isLoggedIn;

    if (competitionSummaryPublic.options.ui.entryDefaultPanel === "SHOP_ONLY") {
      state.shop.v1 =
        "/" + LAUNCH_ROUTES_PATHS.SHOP + "/" + competitionSummaryPublic.compId;
      state.shop.v2 = state.shop.v1;
      state.shop.shopUrl = state.useV2Route ? state.shop.v2 : state.shop.v1;
    } else {
      state.entry.v1 =
        "/" +
        LAUNCH_ROUTES_PATHS.ENTRY +
        "?comporgid=" +
        competitionSummaryPublic.compOrgId +
        "&compid=" +
        competitionSummaryPublic.compId;
      state.entry.v2 =
        "/v2/" +
        LAUNCH_ROUTES_PATHS_V2.ENTRY_V2 +
        "/" +
        competitionSummaryPublic.compId;
    }
    state.entry.compUrl = state.useV2Route ? state.entry.v2 : state.entry.v1;
    state.login.v1 = "/login";
    state.login.v2 = "/v2/" + LAUNCH_ROUTES_PATHS_V2.LOGIN_V2;
    state.login.loginUrl = state.useV2Route ? state.login.v2 : state.login.v1;
    console.log("useGotoComp() init() state: " + state);
  }

  // console.log("useGotoComp()");
  // const isV2Route = routerInternal.currentRoute.path.startsWith("/v2/");
  // console.log("useGotoComp() isV2Route: " + isV2Route);
  //
  // const useV2Route = competitionSummaryPublic.options.useV2Routes || isV2Route;
  // console.log("useGotoComp() useV2Route: " + useV2Route);

  function go() {
    // Shop only route has been converted in V1 to look like V2 don't have a dedicated V2 root for this.
    if (competitionSummaryPublic.options.ui.entryDefaultPanel === "SHOP_ONLY") {
      const shopUrlV1 =
        "/" + LAUNCH_ROUTES_PATHS.SHOP + "/" + competitionSummaryPublic.compId;
      console.log("useGotoComp() shopUrlV1: " + shopUrlV1);

      // const shopUrlV2 =
      //   "/v2/" +
      //   LAUNCH_ROUTES_PATHS_V2.SHOP_V2 +
      //   "/" +
      //   props.competitionSummaryPublic.compId;
      // console.log("PublicCompCardV2.goToComp: " + shopUrlV2);

      // const shopUrl = useV2Route ? shopUrlV2 : shopUrlV1;
      const shopUrl = shopUrlV1;
      console.log("useGotoComp() shopUrl: " + shopUrl);

      routerInternal.push({
        path: state.entry.compUrl,
      });
      return;
    }

    const entryUrlV1 =
      "/" +
      LAUNCH_ROUTES_PATHS.ENTRY +
      "?comporgid=" +
      competitionSummaryPublic.compOrgId +
      "&compid=" +
      competitionSummaryPublic.compId;
    console.log("useGotoComp() entryUrlV1: " + entryUrlV1);

    const entryUrlV2 =
      "/v2/" +
      LAUNCH_ROUTES_PATHS_V2.ENTRY_V2 +
      "/" +
      competitionSummaryPublic.compId;
    console.log("useGotoComp() entryUrlV2: " + entryUrlV2);

    const compUrl = state.useV2Route ? entryUrlV2 : entryUrlV1;
    console.log("useGotoComp() compUrl: " + compUrl);

    const loginUrlV1 = "/login";
    const loginUrlV2 = "/v2/" + LAUNCH_ROUTES_PATHS_V2.LOGIN_V2;

    const loginUrl = state.useV2Route ? loginUrlV2 : loginUrlV1;
    console.log("useGotoComp() loginUrl: " + loginUrl);

    let location: RawLocation;

    let isLoggedIn = authStore.isLoggedIn;

    if (!isLoggedIn) {
      location = {
        path: loginUrl,
        query: {
          redirectFrom: compUrl,
        },
      };
    } else {
      location = {
        path: compUrl,
      };
    }

    routerInternal.push(location);
  }

  return { go };
}
