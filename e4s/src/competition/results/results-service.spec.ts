import { ResultsService } from "./results-service";
import {
  resultAthletesSortMock,
  resultHeatSortMock,
} from "./results-service-mock";
import { simpleClone, sortArray } from "../../common/common-service-utils";
import { IResultAthlete } from "./results-models";

describe("ResultsService", () => {
  const resultsService = new ResultsService();

  test("sortResults low to high", () => {
    let athleteResults: IResultAthlete[] = [];
    athleteResults = simpleClone(resultHeatSortMock.results);

    expect(athleteResults[0].score).toBe(14.34);

    athleteResults = simpleClone(resultHeatSortMock.results);
    athleteResults = athleteResults.map((ath, idx) => {
      ath.position = idx + 1;
      return ath;
    });

    expect(athleteResults[0].position).toBe(1);
    expect(athleteResults[0].score).toBe(14.34);

    const res = resultsService.sortResults(athleteResults);
    // expect(res).toBe(999);
    expect(res.length).toBe(7);
    expect(res[0].score).toBe(13.49);
    expect(res[6].score).toBe(14.4);
  });

  test("sortResults high to low", () => {
    const res2 = sortArray("score", resultHeatSortMock.results, "ASC");
    expect(res2[0].score).toBe(13.49);
    expect(res2[6].score).toBe(14.4);

    const res3 = sortArray("score", resultHeatSortMock.results, "DESC");
    expect(res3[0].score).toBe(14.4);
    expect(res3[6].score).toBe(13.49);

    const res = resultsService.sortResults(resultHeatSortMock.results, "H");
    expect(res.length).toBe(7);
    expect(res[0].score).toBe(14.4);
  });

  test("resultAthletesSortMock", () => {
    const athleteResults = simpleClone(resultAthletesSortMock);

    expect(athleteResults[4].score).toBe(14.34);
    expect(athleteResults[4].scoreValue).toBe("15");

    const res2 = resultsService.sortResults(athleteResults);
    expect(res2.length).toBe(7);
    expect(res2[0].score).toBe(13.49);

    expect(res2[6].score).toBe(15);
    expect(res2[6].scoreValue).toBe("15");
    expect(res2[6].position).toBe(7);
    //  @ts-ignore
    // expect(res2[6].x).toBe(5);
  });
});
