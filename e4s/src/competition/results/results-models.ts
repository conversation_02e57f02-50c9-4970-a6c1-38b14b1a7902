import {GenderType, IBase, IBaseRaw, IsoDateTime, UrlFullPath, UrlPathOnly} from "../../common/common-models";
import {R4SEventType, R4SWindType} from "../scoreboard/rs4/rs4-scoreboard-models";
import {IEventSeed} from "../../athleteCompSched/athletecompsched-models";
import {EaLevel} from "../../common/ui/ea-award/eapb-models"

//  Results for an event E.g. 100m
export interface IResultEvent {
  comp: {
    id: number;
    name: string;
    logo: UrlPathOnly;
  };
  eventGroup: {
    id: number;
    name: string;
    eventNo: number;      //  The order the event will "run" inE.g. time TimeTronics event number #4
    scheduledTime: IsoDateTime;
    wind: R4SWindType;
    type: R4SEventType;
    seed: IEventSeed;
  };
  heats: IResultHeat[];
}

//    this is also SUBMIT...back end will delete all rows and replace.
export interface IResultHeat extends IBaseRaw {
  // header: IResultHeatHeader,
  scheduledTime: IsoDateTime;
  actualTime: IsoDateTime;
  heatNo: number;
  description: string;
  image: UrlFullPath | "";

  results: IResultAthlete[]
}

// export interface IResultHeatHeader {
//   resultHeaderId: number;
//   scheduledTime: IsoDateTime;
//   actualTime: IsoDateTime;
//   heatNumber: number;
// }

export interface IResultAthlete extends IBase {
  // resultHeaderId: number;
  laneNo: number;
  position: number;
  bibNo: number | string;
  bibText: string;
  gender: GenderType;
  athlete: string;
  athleteId: number;
  clubName: string;
  wind: string;
  score: number;
  scoreValue: string;
  scoreText: string;          //  E.g. Sb for Seasons best
  qualify: "Q" | "q" | "";   //  Q = qualify, q = slowest qualify
  urn: string | number;
  eaAward: EaLevel;
  ageGroup: string;           //  E.g. Under 20
}

// export interface IResultAthleteProfile extends IBaseConcrete {
//   urn: string;
// }

export type ResultAthleteTabType = "ALL" | "RESULT_ONLY";

export interface IResultOverall extends IResultAthlete {
  heatNo: number;
  heatName: string;
}






