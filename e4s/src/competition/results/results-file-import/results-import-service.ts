import { IResultsFileImportControllerState } from "./useResultsFileImport";
import { BuilderService } from "../../../builder/builder-service";
import { convertDateToIsoWithOffset } from "../../../common/common-service-utils";

const builderService: BuilderService = new BuilderService();

export function factoryResultsFileImportControllerState(): IResultsFileImportControllerState {
  return {
    builderCompetition: builderService.factoryGetBuilder({}),
    endPoint: "",
    isLoading: false,
    keepProcessedLog: false,
    copyBeforeProcessing: false,
    copyBeforeProcessingDirectoryName: "ToBeProcessed",
    copyBeforeProcessingDirectoryHandle: null,
    moveProcessed: false,
    pollEveryMilliSecs: 2000,
    pollLastTime: "",
    sourceDirectoryName: "",
    sourceDirectoryHandle: null,
    status: "STOPPED",
    isProcessing: false,
    processAllFilesCounter: 0,
    pollingTimeOut: 0,
    targetDirectoryName: "Processed",
    targetDirectoryHandle: null,
    currentFiles: [],
    fileTypesAllowed: [],
    fileTypesToMonitor: [],
    payloadFileLabel: "txtFileToUpload",
    editData: "",
    editFileName: "",
    editFileNameManual: "",
    editDataEntry: null,
    showSection: "FILES",
    videoParams: {
      process: false,
      startClip: 0.5,
      duration: 0.5,
      reverse: false,
      output: "mp4",
      speed: 6.0,
      text: "",
      text2: "",
    },
    sendVideoParams: false,
    processedFilesRecord: {},
  };
}

export function getProcessedFileName(
  fileName: string,
  date: Date = new Date()
): string {
  const uniqueIshSeparator = FILE_NAME_SEPARATOR;
  const fileNameParts: string[] = fileName.split(".");
  let fileDescription: string = fileNameParts[0];
  const fileType: string = fileNameParts[fileNameParts.length - 1];
  const uniqueIshSeparatorPosition = fileName.indexOf(uniqueIshSeparator);

  if (uniqueIshSeparatorPosition > -1) {
    //  already been edited, to prevent massive long file names, only want 1 iso stamp.
    fileDescription = fileName.split(uniqueIshSeparator)[0];
  }

  //  E.g. 2023-06-06T05-19-52-01-00  ...not iso compliant, purely to make legible in folder.
  const timeIso = convertDateToIsoWithOffset(date)
    .replace(/:/g, "-")
    .replace("+", "-");

  //  E.g. some-file-name_2023-06-06T05-20-50-01-00.txt
  return fileDescription + uniqueIshSeparator + timeIso + "." + fileType;
}

export function getOriginalFileName(
  fileName: string,
  fileNameSuffix: string = ""
): string {
  const fileNamePartsSep: string[] = fileName.split(FILE_NAME_SEPARATOR);
  const fileNameParts: string[] = fileName.split(".");
  const fileDescription: string =
    fileName.indexOf(FILE_NAME_SEPARATOR) === -1
      ? fileNameParts[0]
      : fileNamePartsSep[0];
  const fileType: string = fileNameParts[fileNameParts.length - 1];

  return fileDescription + fileNameSuffix + "." + fileType;
}

export function getFileName(fileName: string): string {
  const fileNameParts: string[] = fileName.split(".");
  return fileNameParts.slice(0, fileNameParts.length - 1).join(".");
}

export function getFileNameType(fileName: string): string {
  const fileNameParts: string[] = fileName.split(".");
  return fileNameParts[fileNameParts.length - 1];
}

export function getFileNameParts(fileName: string): string[] {
  return fileName.split(".");
}

export function isFileTypeOk(
  fileName: string,
  typesAllowed: string | string[],
  separator: string = ","
): boolean {
  const typesAllowedArray: string[] = Array.isArray(typesAllowed)
    ? typesAllowed
    : typesAllowed.split(separator);
  const ext = getFileNameType(fileName);
  return (
    typesAllowedArray.indexOf(ext) > -1 ||
    typesAllowedArray.indexOf("*") > -1 ||
    typesAllowedArray.join("").trim() === ""
  );
}

export function isFileEditable(fileName: string): boolean {
  const ext = getFileNameType(fileName);
  return ext === "txt" || ext === "json";
}

export function doesFileExistInArray(
  fileNames: string[],
  fileName: string,
  useOriginalName: boolean = false
): boolean {
  return (
    fileNames.findIndex(function (fName) {
      const fileNameToCheck = useOriginalName
        ? getOriginalFileName(fName)
        : fName;
      return fileNameToCheck === fileName;
    }) > -1
  );
}

export const FILE_NAME_SEPARATOR = "_ee44ss_";
