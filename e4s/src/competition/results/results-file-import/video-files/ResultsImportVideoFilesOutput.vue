<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div class="e4s-flex-row">
      <h1 class="e4s-header--400" v-if="showTitle">
        <div class="e4s-flex-row e4s-gap--standard e4s-flex-wrap">
          <div
            v-text="
              resultsFileImportController.state.builderCompetition.id + ':'
            "
          ></div>
          <div
            v-text="resultsFileImportController.state.builderCompetition.name"
          ></div>
          <div>-</div>
          <div
            v-text="resultsFileImportController.getFirstCompDate.value"
          ></div>
        </div>
      </h1>
    </div>

    <!--Setup-->
    <div class="e4s-flex-row e4s-gap--standard e4s-flex-center">
      <ButtonGenericV2
        text="Select Source Folder"
        style="width: auto"
        slot="field"
        :disabled="resultsFileImportController.state.status !== 'STOPPED'"
        v-on:click="resultsFileImportController.controller.pickDirectory()"
      />

      <span
        class="e4s-header--400"
        v-text="resultsFileImportController.state.sourceDirectoryName"
      ></span>
    </div>

    <hr class="dat-e4s-hr-only dat-e4s-hr--slightly-lighter" />

    <div
      class="e4s-flex-column e4s-gap--standard"
      v-if="resultsFileImportController.isSourceFolderSelected.value"
    >
      <FormGenericInputTemplateV2 form-label="File extension(s) to process">
        <div
          slot="field"
          class="e4s-flex-row e4s-gap--standard e4s-flex-center"
        >
          <div
            v-for="fileType in resultsFileImportController.state
              .fileTypesAllowed"
            class="e4s-flex-row e4s-gap--standard e4s-flex-center"
          >
            <input
              type="checkbox"
              class="browser-default e4s-input-field input-checkbox-v2--input"
              :id="'file-type-' + fileType"
              :value="fileType"
              v-model="resultsFileImportController.state.fileTypesToMonitor"
            />
            <label
              :for="'file-type-' + fileType"
              v-text="fileType"
              class="results-import-file-output--checkbox-label"
            ></label>
          </div>
        </div>
      </FormGenericInputTemplateV2>

      <hr class="dat-e4s-hr-only dat-e4s-hr--slightly-lighter" />

      <div class="e4s-flex-row e4s-gap--large e4s-flex-wrap">
        <FormGenericInputTemplateV2
          form-label="Process"
          class="results-import-video-files-output--small-fields"
        >
          <div
            slot="field"
            class="e4s-flex-row e4s-gap--standard e4s-flex-center"
          >
            <input
              type="checkbox"
              class="browser-default e4s-input-field input-checkbox-v2--input"
              id="video-process"
              v-model="resultsFileImportController.state.videoParams.process"
            />
          </div>
        </FormGenericInputTemplateV2>

        <FormGenericInputNumberV2
          class="results-import-video-files-output--small-fields"
          form-label="Start Clip"
          v-model="resultsFileImportController.state.videoParams.startClip"
        ></FormGenericInputNumberV2>

        <FormGenericInputNumberV2
          class="results-import-video-files-output--small-fields"
          form-label="Duration"
          v-model="resultsFileImportController.state.videoParams.duration"
        ></FormGenericInputNumberV2>

        <FormGenericInputNumberV2
          class="results-import-video-files-output--small-fields"
          form-label="Speed"
          v-model="resultsFileImportController.state.videoParams.speed"
        ></FormGenericInputNumberV2>

        <FormGenericInputTemplateV2
          form-label="Reverse"
          class="results-import-video-files-output--small-fields"
        >
          <div
            slot="field"
            class="e4s-flex-row e4s-gap--standard e4s-flex-center"
          >
            <input
              type="checkbox"
              class="browser-default e4s-input-field input-checkbox-v2--input"
              id="video-reverse"
              v-model="resultsFileImportController.state.videoParams.reverse"
            />
          </div>
        </FormGenericInputTemplateV2>
        <!--      </div>-->

        <!--      <div class="e4s-flex-row e4s-gap&#45;&#45;large">-->
        <FormGenericInputTextV2
          form-label="Text"
          v-model="resultsFileImportController.state.videoParams.text"
        />

        <FormGenericInputTextV2
          form-label="Text 2"
          v-model="resultsFileImportController.state.videoParams.text2"
        />
      </div>

      <hr class="dat-e4s-hr-only dat-e4s-hr--slightly-lighter" />

      <div
        slot="field"
        class="e4s-flex-row e4s-gap--standard e4s-flex-center"
        style="margin-top: 4px"
      >
        <ButtonGenericV2
          text="Start"
          :disabled="resultsFileImportController.state.status === 'POLLING'"
          v-on:click="resultsFileImportController.controller.startPolling()"
        />
        <ButtonGenericV2
          text="Stop"
          :disabled="resultsFileImportController.state.status === 'STOPPED'"
          v-on:click="resultsFileImportController.controller.stopPolling()"
        />

        <div
          v-text="
            'Polls source file every ' +
            resultsFileImportController.state.pollEveryMilliSecs / 1000 +
            ' seconds' +
            (resultsFileImportController.getLastPollTime.value
              ? ' (last poll: ' +
                resultsFileImportController.getLastPollTime.value +
                ')'
              : '')
          "
        ></div>
      </div>
      <!--/Setup-->

      <!--Edit file-->
      <div
        class="e4s-card e4s-card--generic"
        v-if="resultsFileImportController.state.editFileName.length > 0"
      >
        <div class="e4s-flex-column e4s-gap--standard">
          <div class="e4s-header--400">Edit File</div>

          <div class="e4s-flex-row e4s-gap--standard e4s-flex-center">
            <span>Save file as:</span>
            <FieldTextV2
              style="width: 500px"
              v-model="resultsFileImportController.state.editFileNameManual"
            />
          </div>
          <textarea
            style="min-height: 250px"
            v-model="resultsFileImportController.state.editData"
          ></textarea>
          <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
            <ButtonGenericV2
              text="Cancel"
              button-type="tertiary"
              v-on:click="resultsFileImportController.controller.editCancel()"
            />

            <ButtonGenericV2
              text="Save"
              v-on:click="resultsFileImportController.controller.saveDocument()"
            />
          </div>
        </div>
      </div>
      <!--/Edit file-->

      <!--Log Table-->
      <div class="e4s-flex-column e4s-gap--standard">
        <div class="e4s-header--400">Processed Files</div>

        <div
          class="e4s-flex-row e4s-gap--standard e4s-repeatable-grid--top"
          :class="
            loggingEvent.status === 'SUCCESS'
              ? ''
              : 'results-import-file--submit-error'
          "
          v-for="loggingEvent in getLoggingEvents"
          :key="loggingEvent.id"
        >
          <div
            style="min-width: 80px"
            v-text="
              resultsFileImportController.controller.loggingIsoToDisplay(
                loggingEvent.isoDateTime
              )
            "
          ></div>
          <div
            style="min-width: 80px"
            v-text="loggingEvent.status"
            :class="
              loggingEvent.status === 'SUCCESS'
                ? ''
                : 'results-import-file--submit-error-status'
            "
          ></div>
          <div>
            <!--            <a-->
            <!--              href="#"-->
            <!--              v-on:click.prevent="-->
            <!--                resultsFileImportController.controller.editDocument(-->
            <!--                  loggingEvent.targetFileName-->
            <!--                )-->
            <!--              "-->
            <!--              v-text="loggingEvent.message"-->
            <!--            ></a>-->
            <span v-text="loggingEvent.message"></span>
          </div>
          <div v-text="loggingEvent.serverMessage"></div>
        </div>
      </div>
      <!--/Log Table-->
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeUnmount,
  PropType,
  SetupContext,
} from "@vue/composition-api";

import { IBuilderCompetition } from "../../../../builder/builder-models";
import {
  IResultsFileImportControllerConfig,
  IResultsFileImportControllerInit,
  IResultsFileImportLoggingMessage,
  useResultsFileImportController,
} from "../useResultsFileImport";
import { CONFIG } from "../../../../common/config";
import { sortArray } from "../../../../common/common-service-utils";
import SectionLinkSimple from "../../../../common/ui/layoutV2/tabs/section-link-simple.vue";
import FormGenericInputTextV2 from "../../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FieldNumberV2 from "../../../../common/ui/layoutV2/fields/field-number-v2.vue";
import FieldTextV2 from "../../../../common/ui/layoutV2/fields/field-text-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import FormGenericInputNumberV2 from "../../../../common/ui/layoutV2/form/form-generic--input-number-v2.vue";

export default defineComponent({
  name: "ResultsImportVideoFilesOutput",
  components: {
    FormGenericInputNumberV2,
    SectionLinkSimple,
    FormGenericInputTextV2,
    FormGenericInputTemplateV2,
    FieldNumberV2,
    FieldTextV2,
    ButtonGenericV2,
  },
  props: {
    builderCompetition: {
      type: Object as PropType<IBuilderCompetition>,
      required: true,
    },
    showTitle: {
      type: Boolean,
      default: true,
    },
    fileTypesToMonitor: {
      type: Array as PropType<string[]>,
      default: () => ["*"],
    },
  },
  setup(
    props: {
      builderCompetition: IBuilderCompetition;
      showTitle: boolean;
      fileTypesToMonitor: string[];
    },
    context: SetupContext
  ) {
    const config: IResultsFileImportControllerConfig = {
      stateOptions: {
        copyBeforeProcessing: true,
        sendVideoParams: true,
      },
    };

    const resultsFileImportController = useResultsFileImportController(config);

    const url =
      CONFIG.E4S_HOST +
      "/wp-json/e4s/v5/otd/secure/track/" +
      props.builderCompetition.id +
      "/manual";

    const resultsFileImportControllerInit: IResultsFileImportControllerInit = {
      builderCompetition: props.builderCompetition,
      endPoint: url,
      fileTypesToMonitor: props.fileTypesToMonitor,
    };

    resultsFileImportController.controller.init(
      resultsFileImportControllerInit
    );

    const getLoggingEvents = computed<IResultsFileImportLoggingMessage[]>(
      () => {
        return sortArray<IResultsFileImportLoggingMessage, "isoDateTime">(
          "isoDateTime",
          resultsFileImportController.controller.logging.getLoggingEvents
            .value as IResultsFileImportLoggingMessage[],
          "DESC"
        );
      }
    );

    onBeforeUnmount(() => {
      resultsFileImportController.controller.destroy();
    });

    return { resultsFileImportController, getLoggingEvents };
  },
});
</script>

<style>
.results-import-video-files-output--small-fields {
  width: 100px;
}
</style>
