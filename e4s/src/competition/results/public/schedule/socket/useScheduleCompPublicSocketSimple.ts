import { R4S_SOCKET_ACTIONS } from "../../../../scoreboard/rs4/rs4-scoreboard-models";
import {
  doesDomainMatch,
  getEventIdFromMessage,
  IShouldMessageBeDisplayedResult,
} from "../../../../scoreboard/scoreboard-output/display/v3/scoreboard-output-display-service-v3";
import { IR4sSocketDataMessageDatav3 } from "../../../../scoreboard/scoreboard-output/display/v3/useScoreboardOutputDisplayV3";

export interface IScheduleCompPublicSocketSimpleInput {
  compIds: number[];
  domains: string[];
  eventGroupIds: number[];
  listenForMessageTypes: R4S_SOCKET_ACTIONS[];
}

export interface IScheduleCompPublicSocketSimpleState {
  ui: {
    listenForCompIds: number[];
    listenForMessageDomains: string[];
    listenForMessageTypes: R4S_SOCKET_ACTIONS[];
    listenForEventGroupIds: number[];
    listenForEventGroupIdsMap: { [key: string]: boolean };
  };
}

/*
        "photofinish",
        "video",
        "confirmHeat",
        "message",
        "field-results",
 */

/**
 * this is a total last minute hack to get the schedule to update in real time.
 * Lite version of the socket controller not being used.
 */
export function useScheduleCompPublicSocketSimple(
  scheduleCompPublicSocketSimpleInput: IScheduleCompPublicSocketSimpleInput
) {
  const state: IScheduleCompPublicSocketSimpleState = {
    ui: {
      listenForCompIds: [],
      listenForMessageDomains: [],
      listenForMessageTypes: [],
      listenForEventGroupIds: [],
      listenForEventGroupIdsMap: {},
    },
  };

  init(scheduleCompPublicSocketSimpleInput);

  function init(
    scheduleCompPublicSocketSimpleInput: IScheduleCompPublicSocketSimpleInput
  ) {
    state.ui.listenForCompIds = scheduleCompPublicSocketSimpleInput.compIds;
    state.ui.listenForMessageDomains =
      scheduleCompPublicSocketSimpleInput.domains;
    state.ui.listenForEventGroupIds =
      scheduleCompPublicSocketSimpleInput.eventGroupIds;

    state.ui.listenForMessageTypes =
      scheduleCompPublicSocketSimpleInput.listenForMessageTypes;
  }

  // function processMessage(
  //   message: IR4sSocketDataMessageData
  // ): IShouldMessageBeDisplayedResult {
  //   console.log(
  //     "useScheduleCompPublicSocket.processMessage() message: ",
  //     message
  //   );
  //
  //   const shouldMessageBeDisplayedResult: IShouldMessageBeDisplayedResult =
  //     shouldMessageBeDisplayed(message as IR4sSocketDataMessageDatav3);
  //
  //   return shouldMessageBeDisplayedResult;
  // }

  function shouldMessageBeDisplayed(
    message: IR4sSocketDataMessageDatav3
  ): IShouldMessageBeDisplayedResult {
    const action = message.action;

    //  First check...if not action we are interested in...then return false.
    if (state.ui.listenForMessageTypes.indexOf(action) === -1) {
      return {
        identifier: "MESSAGE_TYPE_NO_MATCH",
        should: false,
        reason:
          message.action +
          ": is not in the list of message types to listen for: " +
          state.ui.listenForMessageTypes.join(", "),
        level: 4,
      };
    }

    console.log(
      "shouldMessageBeDisplayed() message.action: " +
        message.action +
        ", MESSAGE_TYPE_MATCH"
    );

    const messageFromDomainMatch = doesDomainMatch(
      message.domain,
      state.ui.listenForMessageDomains
    );
    if (!messageFromDomainMatch) {
      console.log(
        "shouldMessageBeDisplayed() message.action: " +
          message.action +
          ", DOMAIN_NO_MATCH"
      );
      return {
        identifier: "DOMAIN_NO_MATCH",
        should: false,
        reason:
          message.domain +
          ": is not in the list of domains to listen for: " +
          state.ui.listenForMessageDomains.join(", "),
        level: 1,
      };
    }
    console.log(
      "shouldMessageBeDisplayed() message.action: " +
        message.action +
        ", DOMAIN_MATCH"
    );

    if (!message.comp) {
      console.log(
        "shouldMessageBeDisplayed() message.action: " +
          message.action +
          ", NO_COMP"
      );
      return {
        identifier: "NO_COMP",
        should: false,
        reason: "No competition specified.",
        level: 2,
      };
    }
    console.log(
      "shouldMessageBeDisplayed() message.action: " +
        message.action +
        ", COMP_RECEIVED"
    );

    if (state.ui.listenForCompIds.indexOf(message.comp.id) === -1) {
      console.log(
        "shouldMessageBeDisplayed() message.action: " +
          message.action +
          ", NO_COMP_ID_MATCH"
      );
      return {
        identifier: "NO_COMP_ID_MATCH",
        should: false,
        reason:
          message.comp.id +
          ": is not in the list of comps to listen for: " +
          state.ui.listenForCompIds.join(", "),
        level: 3,
      };
    }
    console.log(
      "shouldMessageBeDisplayed() message.action: " +
        message.action +
        ", COMP_ID_MATCH"
    );

    const nonEventActions: R4S_SOCKET_ACTIONS[] = [
      "output-schedule--refresh",
      "output-schedule--reload",
      "output-schedule--reset",
    ];
    if (nonEventActions.indexOf(action) > -1) {
      console.log(
        "shouldMessageBeDisplayed() message.action: " +
          message.action +
          ", SCOREBOARD_ACTION_MESSAGE"
      );
      return {
        identifier: "OK",
        should: false,
        reason: "Score board config action message",
        level: 100,
      };
    }

    //  TODO is event to monitor.
    const eventGroupID = getEventIdFromMessage(message);
    const eventGroupIds = Object.keys(state.ui.listenForEventGroupIdsMap);

    const isEventMessageIdInEventGroupIds =
      state.ui.listenForEventGroupIds.indexOf(eventGroupID) > -1;
    const isEventMessageIdInEventGroupIdsMap =
      state.ui.listenForEventGroupIdsMap[eventGroupID.toString()];

    console.log(
      "shouldMessageBeDisplayed() message.action: " +
        message.action +
        ", isEventMessageIdInEventGroupIds: " +
        isEventMessageIdInEventGroupIds +
        ", isEventMessageIdInEventGroupIdsMap: " +
        isEventMessageIdInEventGroupIdsMap
    );

    // if (
    //   isEventMessageIdInEventGroupIds &&
    //   !isEventMessageIdInEventGroupIdsMap
    // ) {
    //   console.log(
    //     "shouldMessageBeDisplayed() message.action: " +
    //       message.action +
    //       ", NOT_SCHEUDLED_EVENT_BUT_WATCHING"
    //   );
    //
    //   return {
    //     identifier: "OK",
    //     should: true,
    //     reason:
    //       "Event specified in config but does not exist as one to watch in schedule, but will show.",
    //     level: 100,
    //   };
    // }

    if (eventGroupIds.length > 0) {
      if (eventGroupID === -1) {
        console.log(
          "shouldMessageBeDisplayed() message.action: " +
            message.action +
            ", EVENT_GROUP_ID_NOT_FOUND"
        );
        return {
          identifier: "OK",
          should: false,
          reason: "Could not get event group id from message, but will show",
          level: 100,
        };
      } else {
        return {
          identifier: "OK",
          should: false,
          reason: "Event group id " + eventGroupID + " found in message, show.",
          level: 100,
        };
      }
      // if (!state.ui.listenForEventGroupIdsMap[eventGroupID.toString()]) {
      //   console.log(
      //     "shouldMessageBeDisplayed() message.action: " +
      //       message.action +
      //       ", EVENT_GROUP_MAP_NO_MATCH"
      //   );
      //   return {
      //     identifier: "EVENT_GROUP_NO_MATCH",
      //     should: false,
      //     reason:
      //       eventGroupID +
      //       ": is not in the list of message event groups to listen for: " +
      //       Object.keys(state.ui.listenForEventGroupIdsMap).join(", "),
      //     level: 5,
      //   };
      // }
    }

    //  TODO is event in time frame

    return {
      identifier: "OK",
      should: false,
      reason: "No matches found for any parameters.",
      level: 100,
    };
  }

  return {
    state,
    init,
    shouldMessageBeDisplayed,
  };
}
