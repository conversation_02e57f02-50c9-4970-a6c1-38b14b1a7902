<template>
  <div class="e4s-flex-column e4s-full-width">
    <div v-if="isLoading" class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-section-padding-separator"></div>
        Loading...<LoadingSpinner></LoadingSpinner>
      </div>
    </div>
    <ScheduleCompPublicV2
      :r4s-comp-schedule="r4sCompSchedule"
      v-if="r4sCompSchedule.compId > 0"
    />
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";

import { ScoreboardData } from "../../../scoreboard/scoreboard-data";
import { Rs4Service } from "../../../scoreboard/rs4/rs4-service";
import { IR4sCompSchedule } from "../../../scoreboard/rs4/rs4-scoreboard-models";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import { BuilderService } from "../../../../builder/builder-service";
import {Prop, Watch} from "vue-property-decorator"
import ScheduleCompPublicV2 from "./v2/schedule-comp-public-v2.vue"

@Component({
  name: "results-public-wrapper",
  components: {ScheduleCompPublicV2 },
})
export default class ResultsPublicRoute extends Vue {
  @Prop({
    default: () => {
      return 0;
    },
  })
  public readonly compId: number;

  public isLoading: boolean = false;

  public scoreboardData: ScoreboardData = new ScoreboardData();
  public rs4Service: Rs4Service = new Rs4Service();

  public r4sCompSchedule: IR4sCompSchedule =
    this.rs4Service.factoryR4sCompSchedule();

  @Watch("compId", {immediate: true})
  public onCompIdChanged(newValue: number, oldValue: number) {
    if (newValue > 0 && (newValue !== oldValue)) {
      this.getData();
    }
  }

  public getData() {
    this.isLoading = true;

    const prom = this.scoreboardData.getCompSchedule(this.compId, false);
    handleResponseMessages(prom);
    return prom
      .then((resp) => {
        if (resp.errNo === 0) {
          const r4sCompSchedule = resp.data;
          if (!r4sCompSchedule.autoEntries) {
            r4sCompSchedule.autoEntries =
              new BuilderService().factoryAutoEntries();
          }
          this.r4sCompSchedule = r4sCompSchedule;
        }
        return;
      })
      .finally(() => {
        this.isLoading = false;
      });
  }
}
</script>
