<template>
  <div class="only-print-this">
    <div class="qr--page-layout">
      <div class="qr--header">
        <div class="qr--header-message">
          Results for
          <span
            v-text="
              competitionSummaryPublic.compId +
              ': ' +
              competitionSummaryPublic.compName
            "
          ></span>
        </div>

        <!--                <div v-text="'Results'"></div>-->
      </div>
      <div class="qr--body">
        <div class="qr--body-url">
          <span v-text="getResultsUrl"></span>
        </div>
        <QrUrlGenerator
          :cell-size="10"
          class="qr--code-image"
          :url="getResultsUrl"
          v-if="competitionSummaryPublic.compId > 0"
        ></QrUrlGenerator>

        <div class="qr--footer-e4s">
          <img
            class="qr--e4s-logo"
            :src="require('../../../../images/e4s-black.png')"
          />
          <span class="qr--e4s-text">Powered by Entry4Sports</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import QrUrlGenerator from "../../../../qr-code/generator/QrUrlGenerator.vue";
import { LAUNCH_ROUTES_PATHS } from "../../../../launch/launch-routes";
import { CONFIG } from "../../../../common/config";
import { ICompetitionSummaryPublic } from "../../../competition-models";
import { CompetitionService } from "../../../competiton-service";

@Component({
  name: "schedule-qr",
  components: {
    QrUrlGenerator,
  },
})
export default class ScheduleQr extends Vue {
  @Prop({
    default: () => {
      return new CompetitionService().factorySummaryPublic();
    },
  })
  public readonly competitionSummaryPublic: ICompetitionSummaryPublic;

  public get getResultsUrl() {
    return (
      CONFIG.E4S_HOST +
      "/#/" +
      LAUNCH_ROUTES_PATHS.R4S_RESULTS_PUBLIC +
      "/" +
      this.competitionSummaryPublic.compId
    );
  }
}
</script>

<style scoped>
.qr--page-layout {
  border-radius: 2vw;
  border: solid 2px black;
  padding: 2vw;
  margin: 1vw;
  //height: 50vh;
}

.qr--header {
  margin-bottom: 2vw;
}

.qr--header-message {
  text-align: center;
  font-size: 3vw;
}

.qr--header-location {
  font-size: 2vw;
}

.qr--body {
  font-size: 2vw;
}

.qr--body-info-text {
  margin-bottom: 5vw;
}

.qr--body-check-type-sep {
  margin-bottom: 3vw;
}

.qr--code-number {
  font-weight: bold;
  letter-spacing: 1vw;
}

.qr--body-or {
  margin: 1em 0 1em 0;
}

.qr--code-image {
  text-align: center;
  margin-top: 2rem;
}

.qr--body-url {
  font-weight: bold;
  text-align: center;
}

.qr--footer-e4s {
  text-align: center;
}
.qr--e4s-logo {
  height: 100px;
  vertical-align: middle;
}

.qr--e4s-text {
  font-size: 2vw;
}

@page {
  size: A4;
  margin: 0;
  /*background-color: red;*/
}
@media print {
  /*html, body {*/
  /*    width: 210mm;*/
  /*    height: 297mm;*/
  /*    background-color: green;*/
  /*}*/

  /*.no-print {*/
  /*    display:none;*/
  /*}*/

  .only-print-this {
    width: 210mm;
    height: 290mm;
    /*background-color: green;*/
  }
}
</style>
