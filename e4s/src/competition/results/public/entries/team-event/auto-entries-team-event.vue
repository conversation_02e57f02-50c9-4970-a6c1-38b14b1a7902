<template>
  <div>
    <LoadingSpinnerModal :show-it="r4sCompScheduleTarget.compId === 0" />

    <div v-if="r4sCompScheduleTarget.compId > 0">
      <div class="row">
        <div class="col s12 m12 l12">
          <div class="right">
            <button
              class="e4s-button e4s-button--green e4s-button--pad"
              v-on:click="close"
            >
              Back to Schedule
            </button>

            <div class="e4s-button--separator"></div>

            <button
              class="
                e4s-button e4s-button--green e4s-button--pad e4s-button--10-wide
                right
              "
              :disabled="isLoading || !getIsDirty"
              v-on:click="showSaveConfirm = true"
            >
              Save
            </button>
          </div>
        </div>
      </div>

      <div
        class="row"
        :style="
          'visibility:' +
          (Object.keys(getTargetSummary).length === 0 ? 'hidden' : 'visible')
        "
      >
        <div class="col s12 m12 l12">
          <div class="auto-entries-event--summary-section">
            Target Event Team Count:
            <AutoEntriesSummary :entries="teamEntries" />
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <table class="auto-entries-event--table">
            <tr class="auto-entries-event--header">

              <th>
                <a href="#" v-on:click.prevent="sortBy('GENDER')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'GENDER'"
                  >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'GENDER'"
                  >arrow_upward</i
                  >Gender</a
                >
              </th>

              <th>
                <a href="#" v-on:click.prevent="sortBy('AGE')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'AGE'"
                  >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'AGE'"
                  >arrow_upward</i
                  >
                  Age</a
                >
              </th>

              <th class="center">
                <a href="#" v-on:click.prevent="sortBy('TEAM_NAME')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'TEAM_NAME'"
                    >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'TEAM_NAME'"
                  >
                    arrow_upward
                  </i>
                  Team Name
                </a>
              </th>

              <th>
                <a href="#" v-on:click.prevent="sortBy('ENTITY')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'ENTITY'"
                    >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'ENTITY'"
                    >arrow_upward</i
                  >Entity</a
                >
              </th>

              <th>
                <a href="#" v-on:click.prevent="sortBy('USER')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'USER'"
                    >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'USER'"
                    >arrow_upward</i
                  >User</a
                >
              </th>

              <th>
                <a href="#" v-on:click.prevent="sortBy('TARGET_EVENT')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'TARGET_EVENT'"
                    >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'TARGET_EVENT'"
                    >arrow_upward</i
                  >Target</a
                >
              </th>

              <th class="center">Qualify For</th>
            </tr>

            <tr class="auto-entries-event--header">
              <td class="">
                <select
                  v-model="filterGender"
                  class="browser-default"
                  v-on:change="setTeamEntries"
                >
                  <option
                    v-for="gender in getGenders"
                    :key="gender"
                    :value="gender"
                    v-text="gender"
                  ></option>
                </select>
              </td>

              <td>
                <select
                  v-model="filterAgeGroup"
                  class="browser-default"
                  v-on:change="setTeamEntries"
                >
                  <option
                    v-for="ageGroup in getAgeGroups"
                    :key="ageGroup"
                    :value="ageGroup"
                    v-text="ageGroup"
                  ></option>
                </select>
              </td>

              <td>
                <InputDebounce
                  class="e4s-input"
                  style="margin-top: 5px"
                  id="quick-search"
                  default-value=""
                  placeholder="Name..."
                  v-on:input="filterByTeamName"
                />
              </td>

              <td>
                <select
                  v-model="filterEntity"
                  class="browser-default"
                  v-on:change="setTeamEntries"
                >
                  <option
                    v-for="entityName in getEntityNames"
                    :key="entityName"
                    :value="entityName"
                    v-text="entityName"
                  ></option>
                </select>
              </td>

              <td></td>

              <td>
                <ScheduleEventGroupPicker
                  v-if="r4sCompScheduleTarget.compId > 0"
                  class="right"
                  :comp-schedule="r4sCompScheduleTarget"
                  :restrict-to-event-types="[scheduledEvent.type]"
                  :team-events-only="true"
                  default-text="ALL"
                  v-on:onSelected="doFilterByTargetEvent"
                />
              </td>

              <td class="auto-entries-event-row--header-buttons-cell">
                <LoadingSpinner v-if="r4sCompScheduleTarget.compId === 0" />
                <ScheduleEventGroupPicker
                  v-if="r4sCompScheduleTarget.compId > 0"
                  class="right"
                  :comp-schedule="r4sCompScheduleTarget"
                  :restrict-to-event-types="[scheduledEvent.type]"
                  :team-events-only="true"
                  v-on:onSelected="onEventGroupSelected"
                />
              </td>
            </tr>

            <AutoEntriesTeamEventRow
              v-for="teamEntry in teamEntriesFiltered"
              :key="teamEntry.entryId"
              :team-entry="teamEntry"
              :scheduled-event-selected="scheduledEventSelected"
              :show-qualify="getShowQualify"
              :class="getRowClass(teamEntry)"
              v-on:setTargetEvent="setTargetEvent"
              v-on:removeTargetEvent="removeTargetEvent"
            />

          </table>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div class="right">
            <button
              class="e4s-button e4s-button--green e4s-button--pad"
              v-on:click="close"
            >
              Back to Schedule
            </button>

            <div class="e4s-button--separator"></div>

            <button
              class="
                e4s-button e4s-button--green e4s-button--pad e4s-button--10-wide
                right
              "
              :disabled="isLoading || !getIsDirty"
              v-on:click="showSaveConfirm = true"
            >
              Save
            </button>
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>
    </div>

    <E4sModal
      v-if="showSaveConfirm"
      header-message="Save Confirm"
      :body-message="'Are you sure you would like to save?'"
      :isLoading="isLoading"
      v-on:closeSecondary="showSaveConfirm = false"
      v-on:closePrimary="save"
    />
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import { ResultsService } from "../../../results-service";
import {
  IR4sCompSchedule,
  IR4sScheduledEvent,
  IR4sTeamEntry,
} from "../../../../scoreboard/rs4/rs4-scoreboard-models";
import { IConfigApp } from "../../../../../config/config-app-models";
import {CONFIG_STORE_CONST, IConfigStoreState} from "../../../../../config/config-store";
import { AutoEntriesData } from "../auto-entries-data";
import { handleResponseMessages } from "../../../../../common/handle-http-reponse";
import {
  AutoEntriesTeamSortBy,
  IAutoEntrySubmit,
} from "../auto-entries-models";
import { Rs4Service } from "../../../../scoreboard/rs4/rs4-service";
import { CommonService } from "../../../../../common/common-service";
import { ConfigService } from "../../../../../config/config-service";
import { AutoEntriesService } from "../auto-entries-service";
import { CONFIG } from "../../../../../common/config";
import {DropDownAll, GenderType} from "../../../../../common/common-models";
import ScheduleEventGroupPicker from "../ScheduleEventGroupPicker.vue";
import StandardForm from "../../../../../common/ui/standard-form/standard-form.vue";
import LoadingSpinnerModal from "../../../../../common/ui/modal/loading-spinner-modal.vue";
import InputDebounce from "../../../../../common/ui/field/input-debounce.vue";
import E4sModal from "../../../../../common/ui/e4s-modal.vue";
import FieldHelp from "../../../../../common/ui/field/field-help/field-help.vue";
import AutoEntriesTeamEventRow from "./auto-entries-team-event-row.vue";
import AutoEntriesSummary from "../auto-entries-event-summary/auto-entries-summary.vue";

const resultsService = new ResultsService();
const rs4Service: Rs4Service = new Rs4Service();
const commonService = new CommonService();
const configService = new ConfigService();

@Component({
  name: "auto-entries-team-event",
  components: {
    LoadingSpinnerModal,
    E4sModal,
    StandardForm,
    InputDebounce,
    ScheduleEventGroupPicker,
    FieldHelp,
    AutoEntriesTeamEventRow,
    AutoEntriesSummary
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class AutoEntriesTeamEvent extends Vue {
  public readonly isAdmin: boolean;
  public readonly configApp: IConfigApp;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompSchedule!: IR4sCompSchedule;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompScheduleTarget!: IR4sCompSchedule;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sScheduledEvent();
    },
  })
  public readonly scheduledEvent: IR4sScheduledEvent;

  public autoEntriesService: AutoEntriesService = new AutoEntriesService();
  public teamEntries: IR4sTeamEntry[] = [];
  public teamEntriesFiltered: IR4sTeamEntry[] = [];
  public isLoading = false;
  public showSaveConfirm = false;

  public scheduledEventSelected: IR4sScheduledEvent =
    resultsService.factoryR4sScheduledEvent();

  @Watch("scheduledEvent", { immediate: true })
  public onScheduleEventChanged(
    newValue: IR4sScheduledEvent,
    oldValue: IR4sScheduledEvent
  ) {
    if (newValue.teamEntries) {
      this.teamEntries = R.clone(newValue.teamEntries).map((teamEntry) => {
        if (!teamEntry.entryOptions) {
          teamEntry.entryOptions = {
            autoEntries: resultsService.factoryAutoEntryOptions(),
          };
        }
        if (!teamEntry.entryOptions.autoEntries) {
          teamEntry.entryOptions.autoEntries =
            resultsService.factoryAutoEntryOptions();
        }
        return teamEntry;
      });
      this.setTeamEntries();
    }
  }

  public getRowClass(
    teamEntry: IR4sTeamEntry
  ):
    | "auto-entries-event--athlete-qualified"
    | "auto-entries-event--athlete-qualified-select-match"
    | "" {
    if (teamEntry.entryOptions.autoEntries.targetEventGroup.id > 0) {
      if (
        teamEntry.entryOptions.autoEntries.targetEventGroup.id ===
        this.scheduledEventSelected.id
      ) {
        return "auto-entries-event--athlete-qualified-select-match";
      }

      return "auto-entries-event--athlete-qualified";
    }
    return "";
  }

  public get getShowQualify(): boolean {
    if (!resultsService.canQualifyToAnotherComp(this.r4sCompSchedule)) {
      return false;
    }
    return this.isAdmin || this.getHasBuilderPermissionForComp;
  }

  public get getHasBuilderPermissionForComp(): boolean {
    return configService.hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.r4sCompSchedule.org.id,
      this.r4sCompSchedule.compId
    );
  }

  public setTargetEvent(teamEntry: IR4sTeamEntry) {
    teamEntry.entryOptions.autoEntries.targetEventGroup = {
      id: this.scheduledEventSelected.id,
      name: this.scheduledEventSelected.name,
    };
    this.updateTeamEntry(teamEntry);
  }

  public removeTargetEvent(teamEntry: IR4sTeamEntry) {
    teamEntry.entryOptions.autoEntries.targetEventGroup = {
      id: 0,
      name: "",
    };
    this.updateTeamEntry(teamEntry);
  }

  public updateTeamEntry(teamEntry: IR4sTeamEntry): void {
    this.teamEntries = this.teamEntries.map((team) => {
      if (team.entryId === teamEntry.entryId) {
        return teamEntry;
      }
      return team;
    });

    this.teamEntriesFiltered = this.teamEntriesFiltered.map((team) => {
      if (team.entryId === teamEntry.entryId) {
        return teamEntry;
      }
      return team;
    });
  }

  public get getLogoPath(): string {
    return CONFIG.E4S_HOST + this.r4sCompSchedule.logo;
  }

  public get getEventName() {
    return this.scheduledEvent.name;
  }
  public filterTeamName: string | DropDownAll = "ALL";
  public filterGender: GenderType | DropDownAll = "ALL";
  public filterAgeGroup: string | DropDownAll = "ALL";
  public filterEntity: string | DropDownAll = "ALL";
  public filterByTargetEvent: IR4sScheduledEvent =
    rs4Service.factoryR4sScheduledEvent();

  public filterByTeamName(searchTerm: string) {
    this.filterTeamName = searchTerm;
    this.setTeamEntries();
  }

  public doFilterByTargetEvent(scheduledEvent: IR4sScheduledEvent) {
    this.filterByTargetEvent = R.clone(scheduledEvent);
    this.setTeamEntries();
  }

  public getTeamEntries(): IR4sTeamEntry[] {
    let teamEntries = this.teamEntries;

    const filterPreds: { (teamEntry: IR4sTeamEntry): boolean }[] = [];

    if (this.filterTeamName !== "ALL") {
      filterPreds.push((teamEntry) => {
        return this.autoEntriesService.filterByTeamNameMatch(
          this.filterTeamName,
          teamEntry
        );
      });
    }

    if (this.filterGender !== "ALL") {
      filterPreds.push((teamEntry) => {
        return teamEntry.gender === this.filterGender;
      });
    }

    if (this.filterAgeGroup !== "ALL") {
      filterPreds.push((teamEntry) => {
        return teamEntry.ageGroup.name === this.filterAgeGroup;
      });
    }

    // if (this.filterEntity !== "ALL") {
    //   filterPreds.push((teamEntry => {
    //     return teamEntry.entity.entityName === this.filterEntity;
    //   });
    // }

    if (this.filterEntity !== "ALL") {
      const filterEntity = this.filterEntity.toUpperCase()
      filterPreds.push((teamEntry) => {
        const teamEntity = teamEntry.entity.teamEntity.toUpperCase();
        console.log("teamEntity: " + teamEntity);
        console.log("this.filterEntity: " + this.filterEntity);
        return teamEntry.entity.teamEntity.toUpperCase() === filterEntity;
      });
    }

    if (this.filterByTargetEvent.id > 0) {
      filterPreds.push((teamEntry) => {
        return (
          teamEntry.entryOptions.autoEntries.targetEventGroup.id ===
          this.filterByTargetEvent.id
        );
      });
    }

    if (filterPreds.length === 0) {
      return this.autoEntriesService.sortAutoTeamEntries(
        this.sortByProp,
        this.sortByDir,
        teamEntries
      );
    }

    teamEntries = teamEntries.filter((teamEntry) => {
      //  Basically, ALL preds need to pass.  This is like: pred1 && pred2 && pred3...
      return filterPreds.reduce<boolean>((accum, pred) => {
        const predResult = pred(teamEntry);
        if (!predResult) {
          accum = false;
        }
        return accum;
      }, true);
    });

    teamEntries = this.autoEntriesService.sortAutoTeamEntries(
      this.sortByProp,
      this.sortByDir,
      teamEntries
    );

    return teamEntries;
  }

  public setTeamEntries() {
    this.teamEntriesFiltered = R.clone(this.getTeamEntries());
  }

  public sortByProp: AutoEntriesTeamSortBy = "TEAM_NAME";
  public sortByDir: "ASC" | "DESC" = "ASC";

  public sortBy(prop: AutoEntriesTeamSortBy): void {
    this.sortByProp = prop;
    this.setTeamEntries();
  }

  public get getTeamNames() {
    return [
      "ALL",
      ...this.autoEntriesService.getTeamNames(this.teamEntries).sort(),
    ];
  }


  public get getEntityNames() {
    return [
      "ALL",
      ...this.autoEntriesService.getEntityNames(this.teamEntries).sort(),
    ];
  }


  public get getGenders() {
    return ["ALL", "M", "F"];
  }

  public get getAgeGroups() {
    return [
      "ALL",
      ...commonService.unique(
        this.autoEntriesService
          .getAgeGroups(this.teamEntries)
          .map((ageGroup) => ageGroup.name)
          .sort()
      ),
    ];
  }

  // public get getEventTypes(): Array<R4SEventType> {
  //   return this.scheduledEvent.type;
  // }

  public get getTargetSummary() {
    // return this.autoEntriesService.getTargetSummary(this.athleteEntries);
    return "Not yet developed";
  }

  public onEventGroupSelected(r4sScheduledEvent: IR4sScheduledEvent) {
    this.scheduledEventSelected = R.clone(r4sScheduledEvent);
  }

  public get getIsEventGroupSelected(): boolean {
    return this.scheduledEventSelected.id > 0;
  }

  public get getIsDirty() {
    if (!(this.scheduledEvent.teamEntries && this.teamEntries)) {
      return false;
    }

    const diffs = commonService.differenceBetweenTwoObjects(
      commonService.sortArray("teamName", this.scheduledEvent.teamEntries),
      commonService.sortArray("teamName", this.teamEntries)
    );

    console.log("AutoEntriesTeamEvent getIsDirty", diffs);

    return diffs;
  }

  public canShowRemoveButton(teamEntry: IR4sTeamEntry) {
    if (!teamEntry.entryOptions.autoEntries) {
      return false;
    }
    return (
      teamEntry.entryOptions.autoEntries.targetEventGroup.id > 0 &&
      teamEntry.entryOptions.autoEntries.targetEntry.paid === 0
    );
  }

  public close() {
    this.$emit("close");
  }

  public save() {
    //  TODO   move to centralised.
    const entries: IAutoEntrySubmit[] = this.teamEntries.map((teamEntry) => {
      return {
        sourceEntryId: teamEntry.entryId,
        targetEventGroupId:
          teamEntry.entryOptions.autoEntries.targetEventGroup.id,
      };
    });
    this.isLoading = true;
    const prom = new AutoEntriesData().submitEntries(entries, true);
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          this.$emit("save", resp.data);
        }
      })
      .finally(() => {
        // this.$emit("save", {});
        this.isLoading = false;
        this.showSaveConfirm = false;
      });
  }
}
</script>

<style>
.auto-entries-event--header {
  margin: 0.5em 0;
  font-size: 1.25em;
}

/*.auto-entries-event--header th {*/
/*  font-size: 1.25rem;*/
/*  color: grey;*/
/*}*/

.auto-entries-event--table {
}

.auto-entries-event--table th {
  font-size: 1.25rem;
  color: grey;
}

.auto-entries-event--table td {
  padding: 0;
}

.auto-entries-event--narrow {
  width: 10%;
}

.auto-entries-event--centre {
  text-align: center;
}

.auto-entries-event--athlete-qualified {
  background-color: #ebf3eb;
}

.auto-entries-event--athlete-qualified-select-match {
  background-color: #abf5ab;
  border: 2px solid #d78080;
}

.auto-entries-event--summary-section {
  font-size: 1.25em;
  border-top: 2px solid #46a300;
  border-bottom: 2px solid #46a300;
  margin-top: 1em;
  background-color: #ebf3eb;
  padding: 5px;
}

.auto-entries-event--summary-event-group {
  margin-left: 5px;
}

.auto-entries-event-row--header-buttons-cell {
  border-left: 1px solid lightgrey;
}

.auto-entries-event-row--sort {
  font-weight: bold;
}

.auto-entries-event-row--sort-selected {
  color: red;
  font-weight: bold;
}
</style>
