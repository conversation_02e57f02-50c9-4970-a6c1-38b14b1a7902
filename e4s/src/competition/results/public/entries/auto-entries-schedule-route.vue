<template>
    <div>
        <div v-if="isLoading" class="row">
            <div class="col s12 m12 l12">
                <div class="e4s-section-padding-separator"></div>
                Loading...<LoadingSpinner></LoadingSpinner>
            </div>
        </div>
      <AutoEntriesSchedule
        :r4s-comp-schedule="r4sCompSchedule"
        v-if="r4sCompSchedule.compId > 0"
        v-on:reload="getData"
      />
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";

import {ScoreboardData} from "../../../scoreboard/scoreboard-data";
import {Rs4Service} from "../../../scoreboard/rs4/rs4-service";
import {IR4sCompSchedule} from "../../../scoreboard/rs4/rs4-scoreboard-models";
import {handleResponseMessages} from "../../../../common/handle-http-reponse";
import AutoEntriesSchedule from "./auto-entries-schedule.vue";


@Component({
    name: "auto-entries-schedule-route",
    components: {AutoEntriesSchedule}
})
export default class ResultsPublicRoute extends Vue {
    public compId: number = 0
    public isLoading: boolean = false;

    public scoreboardData: ScoreboardData = new ScoreboardData();
    public rs4Service: Rs4Service = new Rs4Service();
    public r4sCompSchedule: IR4sCompSchedule = this.rs4Service.factoryR4sCompSchedule();

    public created() {
        this.compId = isNaN(Number(this.$route.params.compId)) ? 0 : parseInt(this.$route.params.compId, 0);

        if (this.compId > 0) {
            this.getData();
        }
    }

    public getData() {
        this.isLoading = true;

        const prom = this.scoreboardData.getCompSchedule(this.compId, true);
        handleResponseMessages(prom);
        return prom
            .then((resp) => {
                if (resp.errNo === 0) {
                    this.r4sCompSchedule = resp.data;
                }
                return;
            })
            .finally(() => {
                this.isLoading = false;
            })
    }
}
</script>
