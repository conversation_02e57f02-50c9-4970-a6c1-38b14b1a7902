import {IServerResponse} from "../../../../common/common-models";
import https from "../../../../common/https";
import {IAutoEntrySubmit} from "./auto-entries-models";
import {IR4sAthleteEntry} from "../../../scoreboard/rs4/rs4-scoreboard-models";

export class AutoEntriesData {
  public submitEntries(entries: IAutoEntrySubmit[], isTeamEvent: boolean = false): Promise<IServerResponse<Record<number,IR4sAthleteEntry>>> {
    return https.post( "/v5/results/autoentries", {
      isTeamEvent,
      entries
    });
  }
}
