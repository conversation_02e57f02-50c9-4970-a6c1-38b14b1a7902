<template>
  <div>
    <div class="r4s-schedule-public--header">
      <div class="row">
        <div class="col s12 m12 l12">
          <div
            class="r4s-schedule-public--comp-name left"
            v-text="getTitle"
          ></div>

          <div class="right">
            <img :src="getLogoPath" class="r4s-schedule-public--logo"  alt="Club Logo"/>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div
            class="auto-entries-schedule--selected-event"
            v-text="getSelectedEvent"
            :title="getSelectedEventTitle"
          ></div>
        </div>
      </div>

      <div class="row" v-if="showSection === sections.SCHEDULE">
        <div class="col s12 m12 l12">
          <div class="r4s-schedule-public--comp-date-section">
            <div
              class="r4s-schedule-public--comp-date"
              v-if="scheduleDates.length === 1"
            >
              <span v-text="getDate"></span>
            </div>

            <div v-if="scheduleDates.length > 1">
              <span>Competition Dates:</span>
              <span v-for="startDate in scheduleDates" :key="startDate.iso">
                <a
                  class="r4s-schedule-public--comp-date-link"
                  :class="
                    isDateSelected(startDate)
                      ? 'r4s-schedule-public--comp-date-link-selected'
                      : ''
                  "
                  href="#"
                  v-on:click.prevent="selectedScheduleByDate(startDate)"
                >
                  <span v-text="startDate.display"></span>
                </a>
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="row" v-if="showSection !== sections.ENTRIES">
        <div class="col s12 m12 l12">
          <div class="left">
            <InputDebounce
              class="e4s-input e4s-force-inline-block"
              v-if="true"
              id="quick-search"
              :default-value="defaultFilterValue"
              placeholder="Enter search..."
              v-on:input="doFilter"
            />
            <i class="normal material-icons">search</i>
            <a
              href="#"
              class="e4s-force-inline-block"
              v-on:click.prevent="doFilter"
            >
              <i class="normal material-icons">search</i>
            </a>
          </div>
        </div>
      </div>
    </div>

    <div v-show="showSection === sections.SCHEDULE">
      <div class="r4s-schedule-public--schedule-row-head">
        <div class="row">
          <div class="col s2 m2 l2">
            <span class="r4s-schedule-public--schedule-row-head-label"
              >Time</span
            >
          </div>
          <div class="col s4 m4 l4">
            <span class="r4s-schedule-public--schedule-row-head-label"
              >Event Name</span
            >
          </div>
          <div class="col s2 m2 l2">
            <div class="r4s-schedule-public--schedule-row-head-label center">
              Entries
            </div>
          </div>
          <div class="col s4 m4 l4">
            <span class="r4s-schedule-public--schedule-row-head-label right"
            >Qualified (Total/Paid)</span
            >
          </div>
        </div>
      </div>

      <div v-for="(schedule, index) in scheduledEvents" :key="schedule.id">
        <AutoEntriesScheduleRow
          class="row r4s-schedule-public--schedule-row"
          :class="
            index % 2 === 0 ? '' : 'r4s-schedule-public--schedule-row-odd'
          "
          :scheduled-event="schedule"
          :has-permission-to-edit="hasResultsPermissionForComp"
          v-on:showEntries="showEntries"
        />
      </div>
    </div>

    <div v-if="showSection === sections.ENTRIES">
      <div class="e4s-section--padding"></div>
      <AutoEntriesEvent
        v-if="!isScheduledEventDisplayTeamEvent"
        :r4s-comp-schedule="r4sCompSchedule"
        :r4s-comp-schedule-target="r4sCompScheduleTarget"
        :scheduled-event="scheduledEventDisplay"
        v-on:close="showSection = sections.SCHEDULE"
        v-on:save="onSave"
      />
      <AutoEntriesTeamEvent
        v-if="isScheduledEventDisplayTeamEvent"
        :r4s-comp-schedule="r4sCompSchedule"
        :r4s-comp-schedule-target="r4sCompScheduleTarget"
        :scheduled-event="scheduledEventDisplay"
        v-on:close="showSection = sections.SCHEDULE"
        v-on:save="onSave"
      />
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import { Rs4Service } from "../../../scoreboard/rs4/rs4-service";
import { CommonService } from "../../../../common/common-service";
import { ScoreboardData } from "../../../scoreboard/scoreboard-data";
import {
  IR4sAthleteEntry,
  IR4sCompSchedule,
  IR4sScheduledEvent,
  IScheduleTableRow,
} from "../../../scoreboard/rs4/rs4-scoreboard-models";
import { ISimpleDateModel } from "../../../../common/common-models";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../config/config-store";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import { format, parse } from "date-fns";
import { CONFIG } from "../../../../common/config";
import { ConfigService } from "../../../../config/config-service";
import { IConfigApp } from "../../../../config/config-app-models";
import { IResultsStoreState, RESULTS_STORE_CONST } from "../../results-store";
import InputDebounce from "../../../../common/ui/field/input-debounce.vue";
import { ResultsService } from "../../results-service";
import { ScoreboardService } from "../../../scoreboard/scoreboard-service";
import AutoEntriesEvent from "./auto-entries-event.vue";
import AutoEntriesScheduleRow from "./AutoEntriesScheduleRow.vue";
import { AutoEntriesService } from "./auto-entries-service";
import AutoEntriesTeamEvent from "./team-event/auto-entries-team-event.vue";

const rs4Service: Rs4Service = new Rs4Service();

@Component({
  name: "auto-entries-schedule",
  components: {
    AutoEntriesTeamEvent,
    AutoEntriesScheduleRow,
    AutoEntriesEvent,
    InputDebounce,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapState(RESULTS_STORE_CONST.RESULTS_CONST_MODULE_NAME, {
      dateSelectedStore: (state: IResultsStoreState) => state.dateSelected,
    }),
  },
})
export default class ScheduleCompPublic extends Vue {
  public readonly isAdmin!: boolean;
  public readonly configApp!: IConfigApp;
  public readonly dateSelectedStore!: ISimpleDateModel;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompSchedule!: IR4sCompSchedule;

  public rs4Service: Rs4Service = rs4Service;
  public commonService: CommonService = new CommonService();
  public scoreboardData: ScoreboardData = new ScoreboardData();
  public configService: ConfigService = new ConfigService();
  public resultsService: ResultsService = new ResultsService();
  public scoreboardService: ScoreboardService = new ScoreboardService();
  public autoEntriesService: AutoEntriesService = new AutoEntriesService();

  public r4sCompScheduleTarget: IR4sCompSchedule =
    this.rs4Service.factoryR4sCompSchedule();

  public sections = {
    SCHEDULE: "SCHEDULE",
    RANKING: "RANKING",
    ENTRIES: "ENTRIES",
  };
  public showSection: string = this.sections.SCHEDULE;

  public scheduleDates: ISimpleDateModel[] = [];
  public scheduleDateDisplay: ISimpleDateModel = {
    iso: "",
    display: "",
  };
  public scheduledEvents: IR4sScheduledEvent[] = [];
  public scheduledEventDisplay: IR4sScheduledEvent =
    this.rs4Service.factoryR4sScheduledEvent();

  public scheduledEventsQualifiedMap: Record<string, IR4sScheduledEvent[]> = {};

  // public eventGroupIdKeyEntries: Record<string, IR4sEntry[]> = {};
  // public eventGroupIdKeyMeta: Record<
  //   string,
  //   Record<number, IPublicHeatGroupMeta>
  // > = {};

  // public showTheseResultRows: IResultRow[] = [];
  // public showTheseResultRowsType: R4SEventType | "" = "";

  public defaultFilterValue = "";

  public created() {
    const scheduleDates = this.getCompResultDates();
    const scheduleDatesIso = scheduleDates.map((scheduleDate) => {
      return scheduleDate.iso;
    });

    this.scheduleDates = scheduleDates;
    if (this.dateSelectedStore.iso.length > 0) {
      this.scheduleDateDisplay = this.dateSelectedStore;
    } else {
      const todayDate = format(new Date(), "YYYY-MM-DD");
      const datePosition = scheduleDatesIso.indexOf(todayDate);
      if (datePosition > -1) {
        this.scheduleDateDisplay = scheduleDates[datePosition];
      } else {
        this.scheduleDateDisplay = this.scheduleDates[0];
      }
    }

    this.init(this.r4sCompSchedule);
  }

  @Watch("r4sCompSchedule")
  public onR4sCompScheduleChanged(newValue: IR4sCompSchedule) {
    this.init(newValue);
  }

  public get getTitle() {
    const sourceComp =
      this.r4sCompSchedule.compId + ": " + this.r4sCompSchedule.name;

    const targetComp =
      this.r4sCompSchedule.autoEntries.selectedTargetComp.id +
      ": " +
      this.r4sCompSchedule.autoEntries.selectedTargetComp.name;

    return sourceComp + " --> Qualifying to --> " + targetComp;
  }

  public selectedScheduleByDate(startDate: ISimpleDateModel) {
    this.scheduleDateDisplay = startDate;
    this.showSection = this.sections.SCHEDULE;
    this.init(this.r4sCompSchedule);
  }

  public getCompResultDates(): ISimpleDateModel[] {
    return this.commonService.sortArray(
      "iso",
      this.commonService.uniqueArrayById(
        this.r4sCompSchedule.schedule.map((schedule) => {
          return {
            iso: schedule.startDate.split("T")[0],
            display: format(parse(schedule.startDate), "Do MMM"),
          };
        }),
        "iso"
      )
    );
  }

  public isDateSelected(simpleDateModel: ISimpleDateModel): boolean {
    return simpleDateModel.iso === this.scheduleDateDisplay.iso;
  }

  public init(r4sCompSchedule: IR4sCompSchedule) {
    let scheduledEvents = R.clone(r4sCompSchedule.schedule);
    if (this.defaultFilterValue.length > 0) {
      const searchTerm = this.defaultFilterValue.toLowerCase();
      scheduledEvents = scheduledEvents.filter((scheduledEvent) => {
        return scheduledEvent.name.toLowerCase().indexOf(searchTerm) > -1;
      });
    }
    this.scheduledEvents = scheduledEvents;
    // this.scheduledEventsKey = commonService.convertArrayToObjectArray()
  }

  public get hasResultsPermissionForComp() {
    return this.configService.hasResultsPermissionForComp(
      this.configApp.userInfo,
      this.r4sCompSchedule.org.id,
      this.r4sCompSchedule.compId
    );
  }

  public get getLogoPath() {
    return CONFIG.E4S_HOST + this.r4sCompSchedule.logo;
  }

  public get getDate() {
    return format(parse(this.r4sCompSchedule.date), "Do MMM YYYY");
  }

  public getEventTime(r4sScheduledEvent: IR4sScheduledEvent) {
    return format(parse(r4sScheduledEvent.startDate), "HH:mm");
  }

  public getEventTimeForRow(scheduleTableRow: IScheduleTableRow) {
    const time = format(parse(scheduleTableRow.startTime), "HH:mm");
    return time === "00:00" ? "TBC" : time;
  }

  public doFilter(searchTerm: string) {
    this.defaultFilterValue = searchTerm;
    this.init(this.r4sCompSchedule);
  }

  public showEntries(scheduledEvent: IR4sScheduledEvent) {
    this.scheduledEventDisplay = R.clone(scheduledEvent);
    this.showSection = this.sections.ENTRIES;

    if (this.getIsThisAFeederComp && this.r4sCompScheduleTarget.compId === 0) {
      const prom = this.scoreboardData.getCompSchedule(
        this.r4sCompSchedule.autoEntries.selectedTargetComp.id,
        true
      );
      handleResponseMessages(prom);
      prom.then((resp) => {
        if (resp.errNo === 0) {
          this.r4sCompScheduleTarget = resp.data;
        }
      });
    }
  }

  public get getSelectedEvent() {
    if (this.showSection === this.sections.ENTRIES) {
      return (
        "Source Event: " +
        this.scheduledEventDisplay.name +
        " at " +
        this.autoEntriesService.getEventTimeDisplay(
          this.scheduledEventDisplay.startDate
        )
      );
    }
    return "";
  }

  public get getSelectedEventTitle() {
    return (
      this.scheduledEventDisplay.id + ": " + this.scheduledEventDisplay.name
    );
  }

  public get isScheduledEventDisplayTeamEvent() {
    return this.autoEntriesService.isTeamEvent(this.scheduledEventDisplay);
  }

  public onSave(entryRecord: Record<number, IR4sAthleteEntry>) {
    //  TODO the object coming back is not actually a IR4sAthleteEntry...!
    //  TODO so for now, just reload

    // const athleteEntries: IR4sAthleteEntry[] = Object.values(entryRecord);
    //
    // const scheduledEvents = this.scheduledEvents.map((scheduledEvent) => {
    //   if (scheduledEvent.id === this.scheduledEventDisplay.id) {
    //     scheduledEvent.entries = athleteEntries;
    //   }
    //   return scheduledEvent;
    // });
    // this.scheduledEvents = scheduledEvents;

    this.showSection = this.sections.SCHEDULE;

    console.log("onSave", entryRecord);
    this.$emit("reload");
  }

  public get getIsThisAFeederComp() {
    return this.rs4Service.isFeederComp(this.r4sCompSchedule);
  }
}
</script>

<style scoped>
.r4s-schedule-public--header {
  border-bottom: 1px solid gray;
  padding: 0.5em 0;
}

.r4s-schedule-public--comp-name {
  font-size: 1.5em;
}

.r4s-schedule-public--comp-date-section {
  margin-top: 1em;
}

.r4s-schedule-public--comp-date-link {
  padding: 0 0.5em;
  border-right: 1px solid #c5c5c5;
}

.r4s-schedule-public--comp-date-link-selected {
  color: black;
}

.r4s-schedule-public--logo {
  height: 4em;
}

.r4s-schedule-public--schedule-row-head {
  margin: 0.5em 0;
}

.r4s-schedule-public--schedule-row-head-label {
  font-weight: 600;
}

.r4s-schedule-public--schedule-row {
  border-top: 1px solid lightgrey;
  border-bottom: 1px solid lightgrey;
  padding: 0.5em 0;
}

.r4s-schedule-public--schedule-row-odd {
  background-color: #f7f7f6;
}

.r4s-schedule-public--ranking-header {
  margin: 0.5em 0;
  font-size: 1.25em;
}

.r4s-schedule-public--pending-results {
  color: darkgray;
}

.auto-entries-schedule--selected-event {
  font-size: 1.5em;
}
</style>
