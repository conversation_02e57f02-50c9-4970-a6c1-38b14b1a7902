<template>
  <div>
    <LoadingSpinnerModal :show-it="r4sCompScheduleTarget.compId === 0" />

    <div v-if="r4sCompScheduleTarget.compId > 0">
      <div class="row">
        <div class="col s12 m12 l12">
          <div class="right">
            <button
              class="e4s-button e4s-button--green e4s-button--pad"
              v-on:click="close"
            >
              Back to Schedule
            </button>

            <div class="e4s-button--separator"></div>

            <button
              class="
                e4s-button e4s-button--green e4s-button--pad e4s-button--10-wide
                right
              "
              :disabled="isLoading || !getIsDirty"
              v-on:click="showSaveConfirm = true"
            >
              Save
            </button>
          </div>
        </div>
      </div>

      <div
        class="row"
        :style="
          'visibility:' +
          (Object.keys(getTargetSummary).length === 0 ? 'hidden' : 'visible')
        "
      >
        <div class="col s12 m12 l12">
          <div class="auto-entries-event--summary-section">
            Target Event Athlete Count:
            <AutoEntriesSummary :entries="athleteEntries" />
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <table class="auto-entries-event--table">
            <tr class="auto-entries-event--header">
              <th class="center">
                <a href="#" v-on:click.prevent="sortBy('GENDER')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'GENDER'"
                    >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'GENDER'"
                  >
                    arrow_upward
                  </i>
                  M/F
                </a>
              </th>

              <th>
                <a href="#" v-on:click.prevent="sortBy('AGE')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'AGE'"
                    >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'AGE'"
                    >arrow_upward</i
                  >
                  Age</a
                >
              </th>

              <th>
                <a href="#" v-on:click.prevent="sortBy('FIRST_NAME')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'FIRST_NAME'"
                    >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'FIRST_NAME'"
                    >arrow_upward</i
                  >Name</a
                >
              </th>

              <th>
                <a href="#" v-on:click.prevent="sortBy('CLUB')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'CLUB'"
                    >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'CLUB'"
                    >arrow_upward</i
                  >Club</a
                >
              </th>

              <th>
                <a href="#" v-on:click.prevent="sortBy('USER')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'USER'"
                    >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'USER'"
                    >arrow_upward</i
                  >User</a
                >
              </th>

              <th>
                <a href="#" v-on:click.prevent="sortBy('TARGET_EVENT')">
                  <i
                    class="normal material-icons auto-entries-event-row--sort"
                    v-if="sortByProp !== 'TARGET_EVENT'"
                    >arrow_upward</i
                  >
                  <i
                    class="
                      normal
                      material-icons
                      auto-entries-event-row--sort-selected
                    "
                    v-if="sortByProp === 'TARGET_EVENT'"
                    >arrow_upward</i
                  >Target</a
                >
              </th>

              <th class="center">Qualify For</th>
            </tr>

            <tr class="auto-entries-event--header">
              <td class="">
                <select
                  v-model="filterGender"
                  class="browser-default"
                  v-on:change="setAthleteEntries"
                >
                  <option
                    v-for="gender in getGenders"
                    :key="gender"
                    :value="gender"
                    v-text="gender"
                  ></option>
                </select>
              </td>

              <td>
                <select
                  v-model="filterAgeGroup"
                  class="browser-default"
                  v-on:change="setAthleteEntries"
                >
                  <option
                    v-for="ageGroup in getAgeGroups"
                    :key="ageGroup"
                    :value="ageGroup"
                    v-text="ageGroup"
                  ></option>
                </select>
              </td>

              <td>
                <InputDebounce
                  class="e4s-input"
                  style="margin-top: 5px"
                  id="quick-search"
                  default-value=""
                  placeholder="Name..."
                  v-on:input="filterByAthleteName"
                />
              </td>

              <td>
                <select
                  v-model="filterClub"
                  class="browser-default"
                  v-on:change="setAthleteEntries"
                >
                  <option
                    v-for="club in getClubNames"
                    :key="club"
                    :value="club"
                    v-text="club"
                  ></option>
                </select>
              </td>

              <td></td>

              <td>
                <ScheduleEventGroupPicker
                  v-if="r4sCompScheduleTarget.compId > 0"
                  class="right"
                  :comp-schedule="r4sCompScheduleTarget"
                  :restrict-to-event-types="[scheduledEvent.type]"
                  default-text="ALL"
                  v-on:onSelected="doFilterByTargetEvent"
                />
              </td>

              <td class="auto-entries-event-row--header-buttons-cell">
                <LoadingSpinner v-if="r4sCompScheduleTarget.compId === 0" />
                <ScheduleEventGroupPicker
                  v-if="r4sCompScheduleTarget.compId > 0"
                  class="right"
                  :comp-schedule="r4sCompScheduleTarget"
                  :restrict-to-event-types="[scheduledEvent.type]"
                  :scheduled-event-target-default="scheduledEvent"
                  v-on:onSelected="onEventGroupSelected"
                />
              </td>
            </tr>

            <AutoEntriesEventRow
              v-for="athleteEntry in athleteEntriesFiltered"
              :key="athleteEntry.athleteId"
              :class="getRowClass(athleteEntry)"
              :athlete-entry="athleteEntry"
              :scheduled-event-selected="scheduledEventSelected"
              :show-qualify="getShowQualify"
              v-on:setTargetEvent="setTargetEvent"
              v-on:removeTargetEvent="removeTargetEvent"
            />
          </table>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div class="right">
            <button
              class="e4s-button e4s-button--green e4s-button--pad"
              v-on:click="close"
            >
              Back to Schedule
            </button>

            <div class="e4s-button--separator"></div>

            <button
              class="
                e4s-button e4s-button--green e4s-button--pad e4s-button--10-wide
                right
              "
              :disabled="isLoading || !getIsDirty"
              v-on:click="showSaveConfirm = true"
            >
              Save
            </button>
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>
    </div>

    <E4sModal
      v-if="showSaveConfirm"
      header-message="Save Confirm"
      :body-message="'Are you sure you would like to save?'"
      :isLoading="isLoading"
      v-on:closeSecondary="showSaveConfirm = false"
      v-on:closePrimary="save"
    />
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  IR4sAthleteEntry,
  IR4sCompSchedule,
  IR4sScheduledEvent,
} from "../../../scoreboard/rs4/rs4-scoreboard-models";
import FieldHelp from "../../../../common/ui/field/field-help/field-help.vue";
import PowerOfTenLink from "../../../../common/ui/power-of-ten-link.vue";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../config/config-store";
import { CONFIG } from "../../../../common/config";
import { Rs4Service } from "../../../scoreboard/rs4/rs4-service";
import { CommonService } from "../../../../common/common-service";
import { ResultsService } from "../../results-service";
import { ConfigService } from "../../../../config/config-service";
import { IConfigApp } from "../../../../config/config-app-models";
import ScheduleEventGroupPicker from "./ScheduleEventGroupPicker.vue";
import { AutoEntriesSortBy, IAutoEntrySubmit } from "./auto-entries-models";
import { AutoEntriesData } from "./auto-entries-data";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import { AutoEntriesService } from "./auto-entries-service";
import { DropDownAll, GenderType } from "../../../../common/common-models";
import InputDebounce from "../../../../common/ui/field/input-debounce.vue";
import AutoEntriesEventRow from "./auto-entries-event-row.vue";
import AutoEntriesSummary from "./auto-entries-event-summary/auto-entries-summary.vue";
import StandardForm from "../../../../common/ui/standard-form/standard-form.vue";
import E4sModal from "../../../../common/ui/e4s-modal.vue";
import LoadingSpinnerModal from "../../../../common/ui/modal/loading-spinner-modal.vue";

const resultsService = new ResultsService();
const rs4Service: Rs4Service = new Rs4Service();
const commonService = new CommonService();
const configService = new ConfigService();

@Component({
  name: "auto-entries-event",
  components: {
    LoadingSpinnerModal,
    E4sModal,
    StandardForm,
    AutoEntriesSummary,
    AutoEntriesEventRow,
    InputDebounce,
    ScheduleEventGroupPicker,
    PowerOfTenLink,
    FieldHelp,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class AutoEntriesEvent extends Vue {
  public readonly isAdmin: boolean;
  public readonly configApp: IConfigApp;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompSchedule!: IR4sCompSchedule;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompScheduleTarget!: IR4sCompSchedule;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sScheduledEvent();
    },
  })
  public readonly scheduledEvent: IR4sScheduledEvent;

  public autoEntriesService: AutoEntriesService = new AutoEntriesService();
  public athleteEntries: IR4sAthleteEntry[] = [];
  public athleteEntriesFiltered: IR4sAthleteEntry[] = [];
  public isLoading = false;
  public showSaveConfirm = false;

  public scheduledEventSelected: IR4sScheduledEvent =
    resultsService.factoryR4sScheduledEvent();

  @Watch("scheduledEvent", { immediate: true })
  public onScheduleEventChanged(
    newValue: IR4sScheduledEvent,
    oldValue: IR4sScheduledEvent
  ) {
    if (newValue.entries) {
      this.athleteEntries = R.clone(newValue.entries).map((athleteEntry) => {
        if (!athleteEntry.entryOptions) {
          athleteEntry.entryOptions = {
            autoEntries: resultsService.factoryAutoEntryOptions(),
          };
        }
        if (!athleteEntry.entryOptions.autoEntries) {
          athleteEntry.entryOptions.autoEntries =
            resultsService.factoryAutoEntryOptions();
        }
        return athleteEntry;
      });
      this.setAthleteEntries();
    }
  }

  public getRowClass(
    athleteEntry: IR4sAthleteEntry
  ):
    | "auto-entries-event--athlete-qualified"
    | "auto-entries-event--athlete-qualified-select-match"
    | "" {
    if (athleteEntry.entryOptions.autoEntries.targetEventGroup.id > 0) {
      if (
        athleteEntry.entryOptions.autoEntries.targetEventGroup.id ===
        this.scheduledEventSelected.id
      ) {
        return "auto-entries-event--athlete-qualified-select-match";
      }

      return "auto-entries-event--athlete-qualified";
    }
    return "";
  }

  public get getShowQualify(): boolean {
    if (!resultsService.canQualifyToAnotherComp(this.r4sCompSchedule)) {
      return false;
    }
    return this.isAdmin || this.getHasBuilderPermissionForComp;
  }

  public get getHasBuilderPermissionForComp(): boolean {
    return configService.hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.r4sCompSchedule.org.id,
      this.r4sCompSchedule.compId
    );
  }

  public setTargetEvent(athleteEntry: IR4sAthleteEntry) {
    athleteEntry.entryOptions.autoEntries.targetEventGroup = {
      id: this.scheduledEventSelected.id,
      name: this.scheduledEventSelected.name,
    };

    this.updateAthleteEntry(athleteEntry);
  }

  public removeTargetEvent(athleteEntry: IR4sAthleteEntry) {
    athleteEntry.entryOptions.autoEntries.targetEventGroup = {
      id: 0,
      name: "",
    };
    this.updateAthleteEntry(athleteEntry);
  }

  public updateAthleteEntry(athleteEntry: IR4sAthleteEntry): void {
    this.athleteEntries = this.athleteEntries.map((athlete) => {
      if (athlete.athleteId === athleteEntry.athleteId) {
        return athleteEntry;
      }
      return athlete;
    });

    this.athleteEntriesFiltered = this.athleteEntriesFiltered.map((athlete) => {
      if (athlete.athleteId === athleteEntry.athleteId) {
        return athleteEntry;
      }
      return athlete;
    });
  }

  public get getLogoPath(): string {
    return CONFIG.E4S_HOST + this.r4sCompSchedule.logo;
  }

  public get getEventName() {
    return this.scheduledEvent.name;
  }
  public filterAthleteName: string | DropDownAll = "ALL";
  public filterGender: GenderType | DropDownAll = "ALL";
  public filterAgeGroup: string | DropDownAll = "ALL";
  public filterClub: string | DropDownAll = "ALL";
  public filterByTargetEvent: IR4sScheduledEvent =
    rs4Service.factoryR4sScheduledEvent();

  public filterByAthleteName(searchTerm: string) {
    this.filterAthleteName = searchTerm;
    this.setAthleteEntries();
  }

  public doFilterByTargetEvent(scheduledEvent: IR4sScheduledEvent) {
    this.filterByTargetEvent = R.clone(scheduledEvent);
    this.setAthleteEntries();
  }

  public getAthleteEntries(): IR4sAthleteEntry[] {
    let athleteEntries = this.athleteEntries;

    const filterPreds: { (athleteEntry: IR4sAthleteEntry): boolean }[] = [];

    if (this.filterAthleteName !== "ALL") {
      filterPreds.push((athleteEntry) => {
        return this.autoEntriesService.filterByNameMatch(
          this.filterAthleteName,
          athleteEntry
        );
      });
    }

    if (this.filterGender !== "ALL") {
      filterPreds.push((athleteEntry) => {
        return athleteEntry.gender === this.filterGender;
      });
    }

    if (this.filterAgeGroup !== "ALL") {
      filterPreds.push((athleteEntry) => {
        return athleteEntry.ageGroup.shortName === this.filterAgeGroup;
      });
    }

    if (this.filterClub !== "ALL") {
      filterPreds.push((athleteEntry) => {
        return athleteEntry.clubname === this.filterClub;
      });
    }

    if (this.filterByTargetEvent.id > 0) {
      filterPreds.push((athleteEntry) => {
        return (
          athleteEntry.entryOptions.autoEntries.targetEventGroup.id ===
          this.filterByTargetEvent.id
        );
      });
    }

    if (filterPreds.length === 0) {
      return this.autoEntriesService.sortAutoEntries(
        this.sortByProp,
        this.sortByDir,
        athleteEntries
      );
    }

    athleteEntries = athleteEntries.filter((athleteEntry) => {
      //  Basically, ALL preds need to pass.  This is like: pred1 && pred2 && pred3...
      return filterPreds.reduce<boolean>((accum, pred) => {
        const predResult = pred(athleteEntry);
        if (!predResult) {
          accum = false;
        }
        return accum;
      }, true);
    });

    athleteEntries = this.autoEntriesService.sortAutoEntries(
      this.sortByProp,
      this.sortByDir,
      athleteEntries
    );

    return athleteEntries;
  }

  public setAthleteEntries() {
    this.athleteEntriesFiltered = R.clone(this.getAthleteEntries());
  }

  public sortByProp: AutoEntriesSortBy = "FIRST_NAME";
  public sortByDir: "ASC" | "DESC" = "ASC";

  public sortBy(prop: AutoEntriesSortBy): void {
    this.sortByProp = prop;
    this.setAthleteEntries();
  }

  public get getClubNames() {
    return [
      "ALL",
      ...this.autoEntriesService.getClubNames(this.athleteEntries).sort(),
    ];
  }

  public get getGenders() {
    return ["ALL", "M", "F"];
  }

  public get getAgeGroups() {
    return [
      "ALL",
      ...commonService.unique(
        this.autoEntriesService
          .getAgeGroups(this.athleteEntries)
          .map((ageGroup) => ageGroup.shortName)
          .sort()
      ),
    ];
  }

  public get getTargetSummary() {
    return this.autoEntriesService.getTargetSummary(this.athleteEntries);
  }

  public onEventGroupSelected(r4sScheduledEvent: IR4sScheduledEvent) {
    this.scheduledEventSelected = R.clone(r4sScheduledEvent);
  }

  public get getIsEventGroupSelected(): boolean {
    return this.scheduledEventSelected.id > 0;
  }

  public get getIsDirty() {
    if (!(this.scheduledEvent.entries && this.athleteEntries)) {
      return false;
    }

    return commonService.differenceBetweenTwoObjects(
      commonService.sortArray("athleteId", this.scheduledEvent.entries),
      commonService.sortArray("athleteId", this.athleteEntries)
    );
  }

  public canShowRemoveButton(athleteEntry: IR4sAthleteEntry) {
    if (!athleteEntry.entryOptions.autoEntries) {
      return false;
    }
    return (
      athleteEntry.entryOptions.autoEntries.targetEventGroup.id > 0 &&
      athleteEntry.entryOptions.autoEntries.targetEntry.paid === 0
    );
  }

  public close() {
    this.$emit("close");
  }

  public save() {
    //  TODO   move to centralised.
    const entries: IAutoEntrySubmit[] = this.athleteEntries.map(
      (athleteEntry) => {
        return {
          sourceEntryId: athleteEntry.entryId,
          targetEventGroupId:
            athleteEntry.entryOptions.autoEntries.targetEventGroup.id,
        };
      }
    );
    this.isLoading = true;
    const prom = new AutoEntriesData().submitEntries(entries);
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          this.$emit("save", resp.data);
        }
      })
      .finally(() => {
        // this.$emit("save", {});
        this.isLoading = false;
        this.showSaveConfirm = false;
      });
  }
}
</script>

<style>
.auto-entries-event--header {
  margin: 0.5em 0;
  font-size: 1.25em;
}

/*.auto-entries-event--header th {*/
/*  font-size: 1.25rem;*/
/*  color: grey;*/
/*}*/

.auto-entries-event--table {
}

.auto-entries-event--table th {
  font-size: 1.25rem;
  color: grey;
}

.auto-entries-event--table td {
  padding: 0;
}

.auto-entries-event--narrow {
  width: 10%;
}

.auto-entries-event--centre {
  text-align: center;
}

.auto-entries-event--athlete-qualified {
  background-color: #ebf3eb;
}

.auto-entries-event--athlete-qualified-select-match {
  background-color: #abf5ab;
  border: 2px solid #d78080;
}

.auto-entries-event--summary-section {
  font-size: 1.25em;
  border-top: 2px solid #46a300;
  border-bottom: 2px solid #46a300;
  margin-top: 1em;
  background-color: #ebf3eb;
  padding: 5px;
}

.auto-entries-event--summary-event-group {
  margin-left: 5px;
}

.auto-entries-event-row--header-buttons-cell {
  border-left: 1px solid lightgrey;
}

.auto-entries-event-row--sort {
  font-weight: bold;
}

.auto-entries-event-row--sort-selected {
  color: red;
  font-weight: bold;
}
</style>
