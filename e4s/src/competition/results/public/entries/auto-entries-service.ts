// import { IR4sScheduledEvent } from "../../../scoreboard/rs4/rs4-scoreboard-models";

import {
  IR4sAthleteEntry,
  IR4sAthleteEntryAgeGroup,
  IR4sBaseEntry,
  IR4sScheduledEvent,
  IR4sTeamEntry,
  IR4sTeamEntryEntity,
} from "../../../scoreboard/rs4/rs4-scoreboard-models";
import { CommonService } from "../../../../common/common-service";
import {
  AutoEntriesSortBase,
  AutoEntriesSortBy,
  AutoEntriesTeamSortBy,
  IAutoEntriesEventSummary,
} from "./auto-entries-models";
import { IsoDateTime } from "../../../../common/common-models";
import { format, parse } from "date-fns";

const commonService = new CommonService();

export class AutoEntriesService {
  public factoryAutoEntriesEventSummary(): IAutoEntriesEventSummary {
    return {
      count: 0,
      eventGroup: "",
      targetId: 0,
      paidCount: 0,
    };
  }

  // public scheduledEventToQualifiedMap(
  //   scheduledEvents: IR4sScheduledEvent[]
  // ): Record<string, IR4sScheduledEvent[]> {
  //   return scheduledEvents.reduce<Record<string, IR4sScheduledEvent[]>>(
  //     (accum, scheduledEvent) => {
  //
  //       const targetEventGroupId = scheduledEvent
  //
  //       return accum;
  //     },
  //     {}
  //   );
  // }

  // public filterByName(
  //   filterTerm: string,
  //   athleteEntries: IR4sAthleteEntry[]
  // ): IR4sAthleteEntry[] {
  //   filterTerm = filterTerm.toUpperCase();
  //   return athleteEntries.filter((athleteEntry) => {
  //     return this
  //   });
  // }

  public filterByNameMatch(
    filterTerm: string,
    athleteEntry: IR4sAthleteEntry
  ): boolean {
    filterTerm = filterTerm.toUpperCase();
    return (
      athleteEntry.firstname.toUpperCase().indexOf(filterTerm) > -1 ||
      athleteEntry.surname.toUpperCase().indexOf(filterTerm) > -1
    );
  }

  // public filterByNameClub(
  //   filterTerm: string,
  //   athleteEntry: IR4sAthleteEntry
  // ): boolean {
  //   filterTerm = filterTerm.toUpperCase();
  //   return athleteEntry.clubname.toUpperCase().indexOf(filterTerm) > -1;
  // }

  public getClubNames(
    athleteEntries: IR4sAthleteEntry[]
  ): Partial<IR4sAthleteEntry>[] {
    return commonService.pluckUnique("clubname", athleteEntries);
  }

  public getTeamNames(teamEntries: IR4sTeamEntry[]): Partial<IR4sTeamEntry>[] {
    return commonService.pluckUnique("teamName", teamEntries);
  }

  public getEntityNames(teamEntries: IR4sTeamEntry[]): string[] {
    return commonService
      .pluckUnique<IR4sTeamEntry, IR4sTeamEntryEntity>("entity", teamEntries)
      .map((teamEntryEntity: IR4sTeamEntryEntity) => {
        return teamEntryEntity.teamEntity;
      });
  }

  public filterByTeamNameMatch(
    filterTerm: string,
    teamEntry: IR4sTeamEntry
  ): boolean {
    filterTerm = filterTerm.toUpperCase();
    return teamEntry.teamName.toUpperCase().indexOf(filterTerm) > -1;
  }

  public getAgeGroups(entries: IR4sBaseEntry[]): IR4sAthleteEntryAgeGroup[] {
    return commonService.pluckUnique("ageGroup", entries);
  }

  public getTargetSummaryAsText(athleteEntries: IR4sAthleteEntry[]): string[] {
    return Object.values(this.getTargetSummary(athleteEntries)).map(
      (autoEntriesEventSummary: IAutoEntriesEventSummary) => {
        return this.autoEntriesEventSummaryAsHtml(autoEntriesEventSummary);
      }
    );
  }

  public autoEntriesEventSummaryAsHtml(
    autoEntriesEventSummary: IAutoEntriesEventSummary
  ): string {
    return (
      autoEntriesEventSummary.eventGroup +
      " (" +
      autoEntriesEventSummary.count +
      ")"
    );
  }

  public getTargetSummary(
    athleteEntries: IR4sAthleteEntry[]
  ): Record<string, IAutoEntriesEventSummary> {
    return athleteEntries.reduce<Record<string, IAutoEntriesEventSummary>>(
      (accum, athleteEntry: IR4sAthleteEntry) => {
        const targetId =
          athleteEntry.entryOptions.autoEntries.targetEventGroup.id;
        if (targetId > 0) {
          let autoEntriesEventSummary: IAutoEntriesEventSummary;
          autoEntriesEventSummary = accum[targetId];
          if (!autoEntriesEventSummary) {
            const targetEventGroup =
              athleteEntry.entryOptions.autoEntries.targetEventGroup;
            autoEntriesEventSummary = {
              targetId: targetEventGroup.id,
              eventGroup: targetEventGroup.name,
              count: 0,
              paidCount: 0,
            };
            accum[targetId] = autoEntriesEventSummary;
          }
          autoEntriesEventSummary.count++;

          if (athleteEntry.entryOptions.autoEntries.targetEntry.paid > 0) {
            autoEntriesEventSummary.paidCount++;
          }
        }
        return accum;
      },
      {}
    );
  }

  public getTargetEventSummary(
    entries: IR4sBaseEntry[]
  ): Record<string, IAutoEntriesEventSummary> {
    return entries.reduce<Record<string, IAutoEntriesEventSummary>>(
      (accum, entry: IR4sBaseEntry) => {
        const targetId = entry.entryOptions.autoEntries.targetEventGroup.id;
        if (targetId > 0) {
          let autoEntriesEventSummary: IAutoEntriesEventSummary;
          autoEntriesEventSummary = accum[targetId];
          if (!autoEntriesEventSummary) {
            const targetEventGroup =
              entry.entryOptions.autoEntries.targetEventGroup;
            autoEntriesEventSummary = {
              targetId: targetEventGroup.id,
              eventGroup: targetEventGroup.name,
              count: 0,
              paidCount: 0,
            };
            accum[targetId] = autoEntriesEventSummary;
          }
          autoEntriesEventSummary.count++;

          if (entry.entryOptions.autoEntries.targetEntry.paid > 0) {
            autoEntriesEventSummary.paidCount++;
          }
        }
        return accum;
      },
      {}
    );
  }

  public sortAutoEntries(
    sortByProp: AutoEntriesSortBy,
    order: "ASC" | "DESC",
    athleteEntries: IR4sAthleteEntry[]
  ): IR4sAthleteEntry[] {
    if (sortByProp === "FIRST_NAME") {
      return commonService.sortArray("firstname", athleteEntries, order);
    } else if (sortByProp === "CLUB") {
      return commonService.sortArray("clubname", athleteEntries, order);
    } else {
      return this.sortGeneric(
        sortByProp,
        order,
        athleteEntries
      ) as IR4sAthleteEntry[];
    }
  }

  public sortAutoTeamEntries(
    sortByProp: AutoEntriesTeamSortBy,
    order: "ASC" | "DESC",
    teamEntries: IR4sTeamEntry[]
  ): IR4sTeamEntry[] {
    if (sortByProp === "TEAM_NAME") {
      return commonService.sortArray("teamName", teamEntries, order);
    } else if (sortByProp === "ENTITY") {
      return commonService.sortArray(
        (teamEntry) => {
          return teamEntry.entity.entityName;
        },
        teamEntries,
        order
      );
    } else {
      return this.sortGeneric(
        sortByProp,
        order,
        teamEntries
      ) as IR4sTeamEntry[];
    }
  }

  public sortGeneric(
    sortByProp: AutoEntriesSortBase,
    order: "ASC" | "DESC",
    entries: IR4sBaseEntry[]
  ): IR4sBaseEntry[] {
    if (sortByProp === "GENDER") {
      return commonService.sortArray("gender", entries, order);
    } else if (sortByProp === "AGE") {
      const pred = (entry: IR4sBaseEntry) => {
        return entry.ageGroup.name;
      };
      return commonService.sortArray(pred, entries, order);
    } else if (sortByProp === "USER") {
      const pred = (entry: IR4sBaseEntry) => {
        return entry.user ? entry.user.name : "UNK";
      };
      return commonService.sortArray(pred, entries, order);
    } else if (sortByProp === "TARGET_EVENT") {
      const pred = (entry: IR4sBaseEntry) => {
        return entry.entryOptions.autoEntries.targetEventGroup.name;
      };
      return commonService.sortArray(pred, entries, order);
    } else {
      //  No idea how to sort
      return entries;
    }
  }

  public getEventTimeDisplay(iso: IsoDateTime): string {
    const time = format(parse(iso), "HH:mm");
    return time === "00:00" ? "TBC" : time;
  }

  public isTeamEvent(scheduledEvent: IR4sScheduledEvent): boolean {
    return !!(
      scheduledEvent.teamEntries && scheduledEvent.teamEntries.length > 0
    );
  }

  public hasEntryBeenPaid(r4sBaseEntry: IR4sBaseEntry): boolean {
    if (!r4sBaseEntry.entryOptions.autoEntries) {
      return false;
    }
    return r4sBaseEntry.entryOptions.autoEntries.targetEntry.paid === 1;
  }

  public canShowRemoveButton(r4sBaseEntry: IR4sBaseEntry): boolean {
    if (!r4sBaseEntry.entryOptions.autoEntries) {
      return false;
    }
    return (
      r4sBaseEntry.entryOptions.autoEntries.targetEventGroup.id > 0 &&
      r4sBaseEntry.entryOptions.autoEntries.targetEntry.paid === 0
    );
  }

  public isAlreadyInTargetEvent(r4sBaseEntry: IR4sBaseEntry): boolean {
    return r4sBaseEntry.entryOptions.autoEntries.targetEventGroup.id > 0;
  }
}
