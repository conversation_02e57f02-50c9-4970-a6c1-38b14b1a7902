<template>
  <tr>
    <td
      v-text="athleteEntry.gender"
      class="auto-entries-event-row--center"
    ></td>
    <td
      v-text="athleteEntry.ageGroup.shortName"
      class="result-entries--centre"
    ></td>
    <td>
      <span v-text="athleteEntry.firstname + ' ' + athleteEntry.surname"></span>
      <PowerOfTenLink :urn="athleteEntry.urn" />
    </td>
    <td>
      <div v-text="athleteEntry.clubname"></div>
    </td>
    <td>
      <div v-text="athleteEntry.user.name"></div>
    </td>
    <td>
      <div
        v-if="athleteEntry.entryOptions.autoEntries.targetEventGroup.id > 0"
        v-text="athleteEntry.entryOptions.autoEntries.targetEventGroup.name"
        :title="
          athleteEntry.entryOptions.autoEntries.targetEventGroup.id +
          ': ' +
          athleteEntry.entryOptions.autoEntries.targetEventGroup.name
        "
      ></div>
    </td>
    <td class="auto-entries-event-row--buttons-cell" v-if="showQualify">

      <span v-if="hasEntryBeenPaid">Paid</span>

      <button
        :disabled="!isTargetEventGroupSelected"
        v-if="!isAthleteAlreadyInTargetEvent"
        class="
          e4s-button e4s-button--green e4s-button--height-25
          auto-entries-event-row--button
          e4s-button--pad
        "
        v-on:click="setTargetEvent(athleteEntry)"
      >
        Qualify
      </button>
      <button
        v-if="canShowRemoveButton"
        class="
          e4s-button e4s-button--soft-red e4s-button--height-25
          auto-entries-event-row--button
          e4s-button--pad
        "
        v-on:click="removeTargetEvent(athleteEntry)"
      >
        Remove
      </button>
    </td>
  </tr>
</template>

<script lang="ts">
import * as R from "ramda";
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import {
  IR4sAthleteEntry,
  IR4sScheduledEvent,
} from "../../../scoreboard/rs4/rs4-scoreboard-models";
import { ResultsService } from "../../results-service";
import PowerOfTenLink from "../../../../common/ui/power-of-ten-link.vue";
import {AutoEntriesService} from "./auto-entries-service";

const resultsService: ResultsService = new ResultsService();
const autoEntriesService = new AutoEntriesService();

export default defineComponent({
  name: "auto-entries-event-row",
  components: { PowerOfTenLink },
  props: {
    athleteEntry: {
      type: Object as PropType<IR4sAthleteEntry>,
      default: () => {
        return resultsService.factoryR4sAthleteEntry();
      },
    },
    showQualify: {
      type: Boolean,
      default: false,
    },
    scheduledEventSelected: {
      type: Object as PropType<IR4sScheduledEvent>,
      default: () => {
        return resultsService.factoryR4sScheduledEvent();
      },
    },
  },
  setup(
    props: {
      athleteEntry: IR4sAthleteEntry;
      showQualify: boolean;
      scheduledEventSelected: IR4sScheduledEvent;
    },
    context: SetupContext
  ) {
    function setTargetEvent() {
      context.emit("setTargetEvent", R.clone(props.athleteEntry));
    }

    function removeTargetEvent() {
      context.emit("removeTargetEvent", R.clone(props.athleteEntry));
    }

    const isTargetEventGroupSelected = computed(() => {
      return props.scheduledEventSelected.id > 0;
    });

    const isAthleteAlreadyInTargetEvent = computed(() => {
      return autoEntriesService.isAlreadyInTargetEvent(props.athleteEntry);
    });

    const canShowRemoveButton = computed(() => {
      return autoEntriesService.canShowRemoveButton(props.athleteEntry);
    });

    const hasEntryBeenPaid = computed(() => {
      return autoEntriesService.hasEntryBeenPaid(props.athleteEntry);
    });


    return {
      setTargetEvent,
      removeTargetEvent,
      isTargetEventGroupSelected,
      isAthleteAlreadyInTargetEvent,
      canShowRemoveButton,
      hasEntryBeenPaid
    };
  },
});
</script>

<style>
.auto-entries-event-row--buttons-cell {
  border-left: 1px solid lightgrey;
  text-align: center;
}

.auto-entries-event-row--center {
  text-align: center;
}

.auto-entries-event-row--button {
  margin: 2px 0;
}
</style>
