<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <table class="result-heat-read--table">
          <tr class="result-heat-read--header">
            <th class="result-heat-read--narrow">Pos#</th>
            <th class="result-heat-read--narrow">Bib</th>
            <th class="result-heat-read--athlete">Athlete</th>
            <th class="result-heat-read--club">Affiliation</th>
            <th
              class="result-heat-read--narrow"
              v-if="resultEvent.eventGroup.type === 'T'"
            >
              Lane
            </th>

            <th class="result-heat-read--result e4s-flex-row">Result</th>

            <th class="result-heat-read--q" v-if="getShowQualify">
              <span class="right">
                Q
                <FieldHelp
                  title="Qualify"
                  help-key="result--qualify"
                  :get-from-server="true"
                ></FieldHelp>
              </span>
            </th>
          </tr>

          <tr
            v-for="(resultAthlete, index) in getResults"
            :key="resultAthlete.bibNo"
            :class="getRowClass(resultAthlete)"
          >
            <td class="result-heat-read--narrow">
              <span v-text="getPosition(resultAthlete)"></span>
            </td>
            <td class="result-heat-read--narrow">
              <span v-text="getBibNo(resultAthlete)"></span>
            </td>
            <td class="result-heat-read--athlete">
              <div class="result-heat-read--athlete-wrapper">
                <div v-text="trimColumnText(resultAthlete.athlete)"></div>
                <div
                  class="result-heat-read--athlete-subheader"
                  v-text="getAgeGroupDescription(resultAthlete)"
                ></div>
              </div>
            </td>
            <td class="result-heat-read--club">
              <span v-text="trimColumnText(resultAthlete.clubName)"></span>
            </td>
            <td
              class="result-heat-read--narrow"
              v-if="resultEvent.eventGroup.type === 'T'"
            >
              <span v-text="getLane(resultAthlete)"></span>
            </td>
            <td class="result-heat-read--result">
              <div
                class="
                  e4s-flex-row
                  e4s-gap--small
                  e4s-justify-flex-row-vert-center
                "
              >
                <EaAward
                  v-if="isThereAtLeastOneEaAward"
                  :ea-level="resultAthlete.eaAward"
                  style="width: 35px"
                />
                <span v-text="getScore(resultAthlete)"></span>
                <span v-if="resultEvent.eventGroup.wind === 'A'">
                  <i class="material-icons result-event-read--wind-icon"
                    >flag</i
                  >
                  <span v-text="getWind(resultAthlete)"></span>
                </span>
                <PowerOfTenLinkV2
                  :urn="resultAthlete.urn"
                  :show-urn-link="false"
                  image-height="16px"
                />
              </div>
            </td>

            <td class="result-heat-read--q" v-if="getShowQualify">
              <button
                class="e4s-button e4s-button--green e4s-button--pad right"
                v-on:click="setQ(resultAthlete)"
              >
                Q
              </button>
            </td>
          </tr>
        </table>
      </div>
    </div>

    <!--        <div v-if="getShowQualify">-->
    <!--            <div class="e4s-section-padding-separator"></div>-->

    <!--            <div class="row">-->
    <!--                <div class="col s12 m12 l12">-->
    <!--                    <button-->
    <!--                        :disabled="isLoading"-->
    <!--                        class="e4s-button e4s-button&#45;&#45;green e4s-button&#45;&#45;pad right"-->
    <!--                        v-on:click="submitHeat"-->
    <!--                    >-->
    <!--                        Submit-->
    <!--                    </button>-->
    <!--                </div>-->
    <!--            </div>-->
    <!--        </div>-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { format, parse } from "date-fns";
import { FormController } from "../../../../common/ui/form/form-controller/form-controller";
import {
  IResultAthlete,
  IResultEvent,
  IResultHeat,
} from "../../results-models";
import { ValidationController } from "../../../../validation/validation-controller";
import { ResultsService } from "../../results-service";
import { CommonService } from "../../../../common/common-service";
// import { POWER_OF_TEN_ATHLETE_LINK } from "../../../../common/config";
// import PowerOfTenLink from "../../../../common/ui/power-of-ten-link.vue";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../config/config-store";
import { IConfigApp } from "../../../../config/config-app-models";
import { ConfigService } from "../../../../config/config-service";
import { IR4sCompSchedule } from "../../../scoreboard/rs4/rs4-scoreboard-models";
import { Rs4Service } from "../../../scoreboard/rs4/rs4-service";
import FieldHelp from "../../../../common/ui/field/field-help/field-help.vue";
import EaAward from "../../../../common/ui/ea-award/EaAward.vue";
import PowerOfTenLinkV2 from "../../../../common/ui/layoutV2/PowerOfTenLinkV2.vue";

const resultsService: ResultsService = new ResultsService();
const rs4Service: Rs4Service = new Rs4Service();

@Component({
  name: "result-heat-read",
  components: { PowerOfTenLinkV2, EaAward, FieldHelp },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class ResultHeatRead extends Vue {
  public readonly isAdmin!: boolean;
  public configApp!: IConfigApp;

  @Prop({
    default: () => {
      return resultsService.factoryResultEvent();
    },
  })
  public readonly resultEvent: IResultEvent;

  @Prop({
    default: () => {
      return resultsService.factoryResultHeat();
    },
  })
  public readonly resultHeat: IResultHeat;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompSchedule!: IR4sCompSchedule;

  @Prop({
    default: false,
  })
  public readonly isLoading: boolean;

  public configService: ConfigService = new ConfigService();
  public resultHeatInternal: IResultHeat = resultsService.factoryResultHeat();
  public validationController: ValidationController =
    new ValidationController();
  public formController: FormController = new FormController({}, {});
  public resultsService = resultsService;
  public commonService: CommonService = new CommonService();
  public wind: string = "";

  // public POWER_OF_TEN_ATHLETE_LINK = POWER_OF_TEN_ATHLETE_LINK;

  public created() {
    this.resultHeatInternal = R.clone(this.resultHeat);
    this.init();
  }

  @Watch("resultHeat")
  public onResultHeatChanged(newValue: IResultHeat, oldValue: IResultHeat) {
    this.resultHeatInternal = R.clone(newValue);
    this.init();
  }

  public init() {
    if (this.resultHeatInternal.results.length === 0) {
    } else {
      this.wind = this.resultHeatInternal.results[0].wind;
    }
    this.formController.setSources(this.resultHeat, this.resultHeatInternal);
  }

  public get getScheduledTime() {
    if (this.resultHeatInternal.scheduledTime.length === 0) {
      return "NA";
    }
    const startTime = format(
      parse(this.resultHeatInternal.scheduledTime),
      "HH:mm"
    );
    return startTime === "00:00" ? "TBC" : startTime;
  }

  public get getActualTime() {
    if (this.resultHeatInternal.actualTime.length === 0) {
      return "";
    }
    const startTime = format(
      parse(this.resultHeatInternal.actualTime),
      "HH:mm"
    );
    return startTime === "00:00" ? "TBC" : startTime;
  }

  public trimColumnText(content: string): string {
    if (content.length > 30) {
      return content.slice(0, 31) + "...";
    }
    return content;
  }

  public getLane(resultAthlete: IResultAthlete) {
    return resultAthlete.laneNo === 0 ? "" : resultAthlete.laneNo;
  }

  public getWind(resultAthlete: IResultAthlete) {
    const wind = resultAthlete.wind;
    if (this.commonService.isEmpty(wind)) {
      return "";
    }
    return typeof wind === "string" ? wind : wind + "m/s";

    // return resultAthlete.wind && resultAthlete.wind.toString().length > 0 ? resultAthlete.wind : "";
  }

  public get getResults(): IResultAthlete[] {
    return this.resultsService
      .sortEntries(this.resultHeatInternal.results)
      .filter((resultAthlete: IResultAthlete) => {
        const isNullScore =
          resultAthlete.score === 0 ||
          resultAthlete.score.toString().length === 0;
        return !(resultAthlete.position === 0 && isNullScore);
      });
  }

  public getPosition(result: IResultAthlete): string {
    return result.position > 0 ? result.position.toString() : "-";
  }

  public getBibNo(result: IResultAthlete): string {
    return this.resultsService.getBibNo(result);
  }

  public getScore(result: IResultAthlete) {
    // let score = typeof result.score === "number" ?
    //   this.commonService.roundNumberToDecimalPlaces(result.score, 2, false) :
    //   result.scoreValue;

    return (
      result.scoreValue +
      (result.qualify.length > 0 ? " " + result.qualify : "") +
      (result.scoreText ? " " + result.scoreText : "")
    );
  }

  public get hasResultsPermissionForComp() {
    return this.configService.hasResultsPermissionForComp(
      this.configApp.userInfo,
      this.r4sCompSchedule.org.id,
      this.resultEvent.comp.id
    );
  }

  public setQ(resultAthlete: IResultAthlete) {
    const resultAthleteInternal: IResultAthlete | null =
      this.resultHeatInternal.results.reduce((accum, res) => {
        if (res.athleteId === resultAthlete.athleteId) {
          accum = res;
        }
        return accum;
      }, null as IResultAthlete | null);

    if (!resultAthleteInternal) {
      //  result can't be found...eh?!?
      return;
    }

    const hasBigQPos = this.isBigQ(resultAthleteInternal);
    const hasLittleQPos = this.isLittleQ(resultAthleteInternal);
    const hasNoQ = !hasBigQPos && !hasLittleQPos;

    if (hasNoQ) {
      resultAthleteInternal.qualify = "Q";
    }

    if (hasBigQPos) {
      resultAthleteInternal.qualify = "q";
    }

    if (hasLittleQPos) {
      resultAthleteInternal.qualify = "";
    }

    // const resultHeatInternalRes: IResultAthlete[] = this.resultHeatInternal.results.map( (res) => {
    //     return res.athleteId === resultAthlete.athleteId ? resultAthleteInternal : res;
    // });

    this.resultHeatInternal.results = this.resultHeatInternal.results.map(
      (res) => {
        return res.athleteId === resultAthlete.athleteId
          ? resultAthleteInternal
          : res;
      }
    );

    this.formController.processChangesDebounce();

    this.$emit("editedHeat", R.clone(this.resultHeatInternal));
  }

  public isBigQ(resultAthlete: IResultAthlete): boolean {
    return resultAthlete.qualify === "Q";
  }

  public isLittleQ(resultAthlete: IResultAthlete): boolean {
    return resultAthlete.qualify === "q";
  }

  public getRowClass(
    resultAthlete: IResultAthlete
  ): "result-event-read--big-q" | "result-event-read--little-q" | "" {
    if (this.isBigQ(resultAthlete)) {
      return "result-event-read--big-q";
    }
    if (this.isLittleQ(resultAthlete)) {
      return "result-event-read--little-q";
    }
    return "";
  }

  public submitHeat() {
    const resultHeat = R.clone(this.resultHeatInternal);
    this.$emit("submitHeat", resultHeat);
  }

  public get getShowQualify(): boolean {
    if (!resultsService.canQualificationsBeDone(this.resultEvent)) {
      return false;
    }
    return this.isAdmin || this.hasResultsPermissionForComp;
  }

  public get isThereAtLeastOneEaAward(): boolean {
    return this.resultHeatInternal.results.reduce<boolean>(
      (accum: boolean, resultAthlete: IResultAthlete) => {
        const eaAward: string = resultAthlete.eaAward
          ? resultAthlete.eaAward.toString()
          : "";
        if (eaAward.length > 0 && eaAward !== "0") {
          return true;
        }
        return accum;
      },
      false
    );
  }

  public getAgeGroupDescription(resultAthlete: IResultAthlete) {
    if (resultAthlete.ageGroup && resultAthlete.ageGroup.length > 0) {
      return "(" + resultAthlete.ageGroup + ")";
    }
    return "";
  }
}
</script>

<style>
/*.result-heat-edit--table td, th{*/
/*    padding: 0 !important;*/
/*}*/

/*.result-heat-edit--table-action {*/
/*    width: 10%;*/
/*}*/

/*.result-heat-edit--table-pos {*/
/*    width: 10%;*/
/*}*/
/*.result-heat-edit--table-bib {*/
/*    width: 10%;*/
/*}*/
/*.result-heat-edit--table-ath {*/
/*    !*width: 10%;*!*/
/*}*/
/*.result-heat-edit--table-club {*/
/*    !*width: 10%;*!*/
/*}*/
/*.result-heat-edit--table-lane {*/
/*    width: 10%;*/
/*}*/
/*.result-heat-edit--table-sco {*/
/*    width: 10%;*/
/*}*/

/*.result-heat-edit--validation {*/
/*    font-weight: 600;*/
/*    color: darkred;*/
/*}*/

.result-heat-read--table {
  color: black;
}

.result-heat-read--table tr {
  border-bottom: 1px solid var(--slate-300);
}

.result-heat-read--table td {
  padding: 5px 0;
  vertical-align: middle;
}

.result-heat-read--header {
  font-size: 1.25rem;
}

.result-heat-read--narrow {
  width: fit-content;
}

.result-event-read--wind-icon {
}

.result-heat-read--athlete-wrapper {
  display: flex;
  flex-direction: row;
  gap: var(--e4s-gap--standard);
}

.result-heat-read--athlete-subheader {
  color: var(--slate-400);
}

.result-event-read--big-q {
  background-color: #c9e6c9;
}

.result-event-read--little-q {
  background-color: #ecf1ec;
}

@media screen and (max-width: 768px) {
  .result-heat-read--athlete-wrapper {
    flex-direction: column;
    gap: 0;
  }
}
</style>
