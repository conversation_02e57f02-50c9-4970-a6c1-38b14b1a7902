<template>
  <tr>
    <td class="result-heat-edit--table-action result-athlete--td">
      <a href="#" v-on:click.prevent="deleteResultAthlete">
        <i class="material-icons red-text">delete_forever</i>
      </a>
      <!--            {{resultAthleteInternal.wind}}-->
    </td>
    <td class="result-heat-edit--table-number result-athlete--td">
      <input
        class="e4s-input"
        v-model.number="resultAthleteInternal.position"
        v-on:change="inputChanged"
        type="number"
        :tabindex="index * 10 + 1"
        placeholder=""
      />
    </td>
    <td class="result-heat-edit--table-number result-athlete--td">
      <!--            {{isLookingUpAthlete}}-->

      <input
        v-if="!allowTextBibEntry"
        class="e4s-input"
        :class="isLookingUpAthlete ? 'result-athlete--looking-up' : ''"
        v-model.number="resultAthleteInternal.bibNo"
        v-on:blur="inputChangedBibNo"
        :tabindex="index * 10 + 2"
        type="number"
        placeholder=""
      />

      <input
        v-if="allowTextBibEntry"
        class="e4s-input"
        :class="isLookingUpAthlete ? 'result-athlete--looking-up' : ''"
        v-model="resultAthleteInternal.bibNo"
        v-on:blur="inputChangedBibNo"
        :tabindex="index * 10 + 2"
        type="text"
        placeholder=""
      />
    </td>
    <td class="result-heat-edit--table-number result-athlete--td">
      <input
        class="e4s-input"
        v-model="resultAthleteInternal.scoreValue"
        v-on:change="inputChanged"
        :tabindex="index * 10 + 3"
        type="text"
        placeholder=""
      />
    </td>
    <td
      class="result-heat-edit--table-number result-athlete--td"
      v-if="showWind"
    >
      <input
        class="e4s-input"
        :class="isLookingUpAthlete ? 'result-athlete--looking-up' : ''"
        v-model.number="resultAthleteInternal.wind"
        v-on:change="inputChanged"
        :tabindex="index * 10 + 4"
        type="number"
        placeholder="wind"
      />
    </td>
    <td
      class="result-heat-edit--table-number result-athlete--td"
      v-if="showLane"
    >
      <input
        class="e4s-input"
        :class="isLookingUpAthlete ? 'result-athlete--looking-up' : ''"
        v-model.number="resultAthleteInternal.laneNo"
        v-on:change="inputChanged"
        :tabindex="index * 10 + 5"
        type="number"
        placeholder="Lane"
      />
    </td>
    <td class="result-athlete--td">
      <select
        v-model="resultAthleteInternal.gender"
        class="browser-default e4s-input e4s-input-select"
        :class="isLookingUpAthlete ? 'result-athlete--looking-up' : ''"
        :tabindex="
          fieldTabType === 'ALL'
            ? index * 10 + 6
            : bibLookupAthleteFound
            ? -1
            : index * 10 + 6
        "
        v-on:change="inputChanged"
      >
        <option value=""></option>
        <option value="F">F</option>
        <option value="M">M</option>
      </select>

      <!--            <input-->
      <!--              class="e4s-input"-->
      <!--              :class="isLookingUpAthlete ? 'result-athlete&#45;&#45;looking-up' : ''"-->
      <!--              v-model="resultAthleteInternal.gender"-->
      <!--              v-on:change="inputChanged"-->
      <!--              :tabindex="fieldTabType === 'ALL' ? ((index * 10) + 6) : (bibLookupAthleteFound ? -1 : (index * 10) + 6)"-->
      <!--              type="text"-->
      <!--              placeholder="">-->
    </td>
    <td class="result-athlete--td">
      <input
        class="e4s-input"
        :class="isLookingUpAthlete ? 'result-athlete--looking-up' : ''"
        v-model="resultAthleteInternal.athlete"
        v-on:change="inputChanged"
        :tabindex="
          fieldTabType === 'ALL'
            ? index * 10 + 7
            : bibLookupAthleteFound
            ? -1
            : index * 10 + 7
        "
        type="text"
        placeholder=""
      />
    </td>
    <td class="result-athlete--td">
      <input
        class="e4s-input"
        :class="isLookingUpAthlete ? 'result-athlete--looking-up' : ''"
        v-model="resultAthleteInternal.clubName"
        v-on:change="inputChanged"
        :tabindex="
          fieldTabType === 'ALL'
            ? index * 10 + 8
            : bibLookupAthleteFound
            ? -1
            : index * 10 + 8
        "
        type="text"
        placeholder=""
      />
    </td>
  </tr>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  IResultAthlete,
  IResultEvent,
  IResultHeat,
  ResultAthleteTabType,
} from "./results-models";
import { ResultsService } from "./results-service";
import {
  BIB_COLLECTED_STATUS,
  CheckinData,
  IGetCheckinSearchParams,
} from "../checkin/checkin-data";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import { ICheckinAthlete } from "../checkin/checkin-models";
import { CommonService } from "../../common/common-service";

const resultsService: ResultsService = new ResultsService();
const commonService: CommonService = new CommonService();

@Component({
  name: "result-athlete",
})
export default class ResultAthlete extends Vue {
  @Prop({
    default: 0,
  })
  public readonly index!: number;

  @Prop({
    default: () => {
      return resultsService.factoryResultEvent();
    },
  })
  public readonly resultEvent!: IResultEvent;

  @Prop({
    default: () => {
      return resultsService.factoryResultHeat();
    },
  })
  public readonly resultHeat: IResultHeat;

  @Prop({
    default: () => {
      return resultsService.factoryResultAthlete();
    },
  })
  public readonly resultAthlete!: IResultAthlete;

  @Prop({
    default: true,
  })
  public readonly doBibLookup!: boolean;

  @Prop({
    default: true,
  })
  public readonly showLane!: boolean;

  @Prop({
    default: true,
  })
  public readonly showWind!: boolean;

  @Prop({
    default: "ALL" as ResultAthleteTabType,
  })
  public readonly fieldTabType!: ResultAthleteTabType;

  @Prop({
    default: true,
  })
  public readonly allowTextBibEntry!: boolean;

  public resultAthleteInternal: IResultAthlete =
    resultsService.factoryResultAthlete();
  public checkinData: CheckinData = new CheckinData();
  public isLookingUpAthlete = false;
  public bibLookupAthleteFound = false;

  public created() {
    this.resultAthleteInternal = R.clone(this.resultAthlete);
  }

  @Watch("resultAthlete")
  public onMessageChanged(newValue: IResultAthlete, oldValue: IResultAthlete) {
    this.resultAthleteInternal = R.clone(newValue);
  }

  public inputChanged() {
    this.$emit("inputChanged", {
      index: this.index,
      resultAthlete: R.clone(this.resultAthleteInternal),
    });
  }

  public deleteResultAthlete() {
    this.$emit("deleteResultAthlete", {
      index: this.index,
      resultAthlete: R.clone(this.resultAthleteInternal),
    });
  }

  public inputChangedBibNo() {
    this.inputChanged();
    if (this.doBibLookup) {
      this.doAthleteLookup(this.resultAthleteInternal);
    }
  }

  public doAthleteLookup(resultAthlete: IResultAthlete) {
    this.bibLookupAthleteFound = false;
    if (
      commonService.isEmpty(resultAthlete.bibNo) ||
      resultAthlete.bibNo === 0
    ) {
      return;
    }

    // const onlyNumbers = commonService.isOnlyNumbers(
    //   resultAthlete.bibNo.toString()
    // );
    // if (!onlyNumbers) {
    //   return;
    // }

    if (this.doBibLookup) {
      // const searchDate = "2021-06-02";  //  this.resultHeat.scheduledTime
      const searchDate =
        this.resultEvent.eventGroup.scheduledTime.split("T")[0];

      const searchPayload: IGetCheckinSearchParams = {
        compId: this.resultEvent.comp.id,
        isoDate: searchDate,
        firstName: "",
        lastName: "",
        club: "",
        entity: {
          name: "",
          entityLevel: 0,
          id: 0,
          entityName: "",
          clubType: "",
        },
        urn: "",
        bibNo: resultAthlete.bibNo.toString(),
        dob: "",
        eventName: "",
        pageNumber: 1,
        pageSize: 100,
        collected: BIB_COLLECTED_STATUS.ALL,
        nonce: "",
        isOrganiser: true,
        whois: false,
        egid: this.resultEvent.eventGroup.id,
      };
      this.isLookingUpAthlete = true;
      const prom = this.checkinData.getCheckin(searchPayload);
      handleResponseMessages(prom);
      prom
        .then((resp) => {
          if (resp.errNo === 0) {
            if (resp.data.length === 1) {
              this.bibLookupAthleteFound = true;
              const checkinAthlete: ICheckinAthlete = resp.data[0];
              this.resultAthleteInternal.gender = checkinAthlete.gender;
              this.resultAthleteInternal.athleteId = checkinAthlete.athleteId;
              this.resultAthleteInternal.athlete =
                checkinAthlete.firstName + " " + checkinAthlete.surName;
              this.resultAthleteInternal.clubName = checkinAthlete.club;
              this.resultAthleteInternal.urn = checkinAthlete.urn;

              this.inputChanged();
            }
            // this.resultAthleteInternal.athleteId === resp.data.
          }
        })
        .finally(() => {
          this.isLookingUpAthlete = false;
        });
    }
  }
}
</script>

<style scoped>
input:focus {
  background-color: #bdf5bd !important;
  border: 1px solid #00ff1f !important;
}

select:focus {
  background-color: #bdf5bd !important;
  border: 1px solid #00ff1f !important;
}
</style>
