import https from "../../../common/https";
import { CONFIG } from "../../../common/config";
import { IServerBaseResponse } from "../../../common/common-models";

export class BibData {
  public processBibNumbers(
    compId: number,
    action: "CLEAR" | "NEW" | "ADD",
    printForDate: string = ""
  ): Promise<IServerBaseResponse> {
    // const status = action === "CLEAR" ? 0
    //     : action === "NEW" ? 1
    //     : 2;

    // return https.get( CONFIG.E4S_HOST + "/wp-json/e4s/v5/public/reports/output/" + compId + "?bibgen=" + status) as any as Promise<any>;

    const url =
      CONFIG.E4S_HOST +
      "/wp-json/e4s/v5/competition/" +
      compId +
      "/bibs?date=" +
      printForDate;

    if (action === "NEW") {
      return https.post(url) as any as Promise<IServerBaseResponse>;
    }
    if (action === "ADD") {
      return https.put(url) as any as Promise<IServerBaseResponse>;
    }
    if (action === "CLEAR") {
      return https.delete(url) as any as Promise<IServerBaseResponse>;
    }
    return Promise.resolve({
      errNo: 99,
      error: "Action: " + action + " not coded for",
    });
  }
}
