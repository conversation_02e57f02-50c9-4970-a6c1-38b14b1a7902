import { IUserInfo } from "../config-app-models";

export const johnathan<PERSON>serInfo: IUserInfo = {
  e4s_lastCheckedPB: "2024-11-19 15:06:53",
  orgs: [],
  areas: [],
  clubs: [],
  user: {
    id: 14805,
    user_login: "AshfordGreen",
    user_nicename: "ashfordgreen",
    user_email: "<EMAIL>",
    display_name: "Ashford Green",
    role: "USER",
    google_email: "",
    impersonating: false,
    e4sCredit: [],
    permissions: [
      {
        id: 60,
        userid: 14805,
        role: {
          id: 1,
          name: "finance",
        },
        comp: {
          id: 0,
          name: "",
        },
        org: {
          id: 0,
          name: "All",
        },
        permLevels: [],
      },
    ],
    version: {
      current: "v1",
      toggle: false,
    },
  },
  wp_role: "editor",
  security: {
    permissions: [
      {
        id: 60,
        userid: 14805,
        roleid: 1,
        orgid: 0,
        compid: 0,
        role: "finance",
        orgname: "All",
      },
    ],
    permLevels: {
      "": [],
    },
  },
} as any as IUserInfo;
