<template>

    <div>

        <div class="e4s-section-padding-separator"></div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div class="e4s-form-header">
                    Cheques
                </div>
            </div>
        </div>

        <div class="e4s-section-padding-separator"></div>

        <div class="row">
            <div class="col s6 m6 l6">
                <div>Filter</div>
                <SimpleObjectDropDown :simple-objects="getFilterStatuses"
                                      :current-value="filterStatus"
                                      v-on:onSelected="setFilterStatus">
                </SimpleObjectDropDown>

<!--                {{chequesCard.length}} found-->
            </div>

            <div class="col s6 m6 l6">
                <div class="right">
                    <div class="e4s-force-inline-block">
                        Status
                        <SimpleObjectDropDown :simple-objects="statuses"
                                              :current-value="status"
                                              v-on:onSelected="setStatus">
                        </SimpleObjectDropDown>
                    </div>
                    <div class="e4s-force-inline-block">
                        <button :disabled="getIsPaidButtonDisabled"
                                class="btn waves-effect waves green e4s-bold"
                                v-on:click.prevent="bulkPaidConfirm">
                            <span>Save</span>
                        </button>
                    </div>


                </div>
            </div>
        </div>

        <div class="e4s-section-padding-separator"></div>

        <div class="row e4s-form-header">
            <div class="col s2 m2 l2">
                Date
            </div>
            <div class="col s2 m2 l2">
                Order #
            </div>
            <div class="col s2 m2 l2">
                Value
            </div>
            <div class="col s2 m2 l2">
                Email
            </div>
            <div class="col s2 m2 l2">
                Status
            </div>
            <div class="col s2 m2 l2">
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div v-for="chequeCard in chequesCard" :key="chequeCard.cheque.id">
                    <cheque-card :cheque-card="chequeCard" v-on:onSelected="onChequeSelected"></cheque-card>
                </div>
                <div v-if="chequesCard.length === 0">
                    <div class="e4s-section-padding-separator"></div>
                    None found
                </div>
            </div>
        </div>

        <e4s-modal v-if="showBulkPaidConf"
                   :header-message="'Set as Paid'"
                   :body-message="'Set these as ' + status.name + ', continue?'"
                   :button-primary-text="'Continue'"
                   :isLoading="isLoading"
                   v-on:closeSecondary="showBulkPaidConf = false"
                   v-on:closePrimary="submitPaidConfirm">
        </e4s-modal>

    </div>

</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {ICheque, IChequeCard} from "./cheque-models";
    import {Prop, Watch} from "vue-property-decorator";
    import ChequeCard from "./cheque-card.vue";
    import {ChequeService} from "./cheque-service";
    import E4sModal from "../../common/ui/e4s-modal.vue";
    import {IBase, IServerResponseList} from "../../common/common-models";
    import {ChequeData} from "./cheque-data";
    import {USER_MESSAGE_LEVEL} from "../../user-message/user-message-models";
    import {messageDispatchHelper} from "../../user-message/user-message-store";
    import {CommonService} from "../../common/common-service";
    import SimpleObjectDropDown from "../../common/ui/simple-object-drop-down.vue";

    @Component({
        name: "cheques-grid",
        components: {
            "cheque-card": ChequeCard,
            "e4s-modal": E4sModal,
            SimpleObjectDropDown
        }
    })
    export default class ChequesGrid extends Vue {
        @Prop({
            default: () => []
        }) public chequesProp: ICheque[];
        public chequeData: ChequeData = new ChequeData();
        public chequeService: ChequeService = new ChequeService();
        public commonService: CommonService = new CommonService();
        public chequesCard: IChequeCard[] = [];
        public cheques: ICheque[] = [];


        public showBulkPaidConf: boolean = false;
        public isLoading: boolean = false;

        public filterStatuses: IBase[] = [];
        public filterStatus: IBase = {id: 0};
        public statuses: IBase[] = [];
        public status: IBase = {id: 0};

        public created() {
            this.chequeData.getStatuses()
                .then((statuses: IBase[]) => {
                    this.statuses = statuses;
                    this.status = statuses[0];
                    this.filterStatuses = R.clone(statuses);
                    this.filterStatus = this.filterStatuses.filter( (status) => status.id === 2)[0];
                });
        }

        public mounted() {
            this.loadData();
        }

        public loadData() {
            this.isLoading = true;
            this.chequeData.getCheques()
                .then( (response: IServerResponseList<ICheque>) => {
                    if (response.errNo > 0) {
                        messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return;
                    }
                    this.init(response.data);
                    return;
                })
                .catch((error) => {
                    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                    return {};
                })
                .finally(() => {
                    this.isLoading = false;
                });
        }

        public init(cheques: ICheque[]) {
            this.cheques = R.clone(cheques);
            cheques = cheques.filter( (cheque) => {
                if (this.filterStatus.name === "ALL") {
                    return true;
                }
                return cheque.status === this.filterStatus.name;
            });
            this.chequesCard = this.chequeService.mapChequesToCheckCards(cheques);
        }

        public get getFilterStatuses(): IBase[] {
            return this.filterStatuses.map((status) => {
                if (status.id === -1) {
                    status.name = "ALL";
                }
                return status;
            });
        }

        public setFilterStatus(status: IBase) {
            this.filterStatus = status;
            this.onFilterChange();
        }

        public onFilterChange() {
            this.init(this.cheques);
        }

        @Watch("chequesProp")
        public onChequesPropChanged(cheques: ICheque[]) {
            this.init(this.cheques);
        }

        public onChequeSelected(chequeCard: IChequeCard) {
            this.chequesCard = this.chequesCard.map( (chCard) => {
                if (chCard.cheque.id === chequeCard.cheque.id) {
                    chCard.selected = !chCard.selected;
                }
                return chCard;
            });
        }

        public get getIsPaidButtonDisabled() {
            return this.isLoading || this.getSelected.length === 0;
        }

        public bulkPaidConfirm() {
            this.showBulkPaidConf = true;
        }

        public submitPaidConfirm() {
            const ids: IBase[] = this.getSelected.map( (chCard: IChequeCard) => {
                return {
                    id: chCard.cheque.id
                };
            });
            this.isLoading = true;
            this.chequeData.submitChequesPaid(ids, this.status.id)
                .then( (response: IServerResponseList<ICheque>) => {
                    if (response.errNo > 0) {
                        messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return;
                    }
                    messageDispatchHelper("Saved.", USER_MESSAGE_LEVEL.INFO.toString());
                    this.loadData();
                    return;
                })
                .catch((error) => {
                    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                    return {};
                })
                .finally(() => {
                    this.isLoading = false;
                    this.showBulkPaidConf = false;
                });
        }

        public get getSelected() {
            return this.chequesCard.filter( (chCard) => {
                return chCard.selected;
            });
        }

        public setStatus(status: IBase) {
            this.status = status;
        }

    }

</script>
