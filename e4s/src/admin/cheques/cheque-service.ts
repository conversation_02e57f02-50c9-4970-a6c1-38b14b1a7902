import {ICheque, IChequeCard} from "./cheque-models";
import * as R from "ramda";

export class ChequeService {
    public factory(): ICheque {
        return {
            id: 0,
            total: 0,
            userEmail: "",
            userId: 0
        } as ICheque;
    }

    public mapChequesToCheckCards(cheques: ICheque[]): IChequeCard[] {
        return cheques.map(this.mapChequeToChequeCard);
    }

    public mapChequeToChequeCard(cheque: ICheque): IChequeCard {
        return {
            selected: false,
            cheque: R.clone(cheque)
        };
    }

}
