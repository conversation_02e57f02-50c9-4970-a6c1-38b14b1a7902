import {ResourceData} from "../../common/resource/resource-service";
import {ICheque} from "./cheque-models";
import https from "../../common/https";
import {IBase, IServerPagingResponseList} from "../../common/common-models";

export class ChequeData extends ResourceData<ICheque> {
    constructor() {
        super("/v5/admin/cheques");
    }

    public getCheques() {
        return https.get( this.getEndPoint() + "") as any as Promise<IServerPagingResponseList<ICheque>>;
    }

    public submitChequesPaid(ids: IBase[], paid: number) {
        const payload  = {
            orderIds: ids,
            paid
        };
        return https.post( this.getEndPoint() + "", payload) as any as Promise<IServerPagingResponseList<ICheque>>;
    }

    public getStatuses(): Promise<IBase[]> {
        return Promise.resolve(
            [
                {
                    id: -1,
                    name: "PLEASE SELECT"
                },
                {
                    id: 0,
                    name: "Cancel order and put back in basket"
                },
                {
                    id: 2,
                    name: "Awaiting Cheque"
                },
                {
                    id: 1,
                    name: "Cheque Received"
                }
            ]
        );
    }
}
