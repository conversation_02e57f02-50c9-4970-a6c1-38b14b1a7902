<template>
  <div class="e4s-flex-row e4s-gap--standard">
    <FieldCheckboxV2 v-model="version.toggle" form-label="Allow Toggle" />

    <select
      class="browser-default e4s-input-field e4s-input-field--primary"
      v-model="version.current"
    >
      <option value="v1">V1</option>
      <option value="v2">V2</option>
    </select>

    <ButtonGenericV2 text="Submit" v-on:click="doSubmit" />
    <LoadingSpinnerV2 v-if="configVersionSwitch.isLoading.value" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  reactive,
  SetupContext, watch,
} from "@vue/composition-api"
import FieldCheckboxV2 from "../../../common/ui/layoutV2/fields/field-checkbox-v2.vue";
import { IUserProfile } from "../user-models";
import { useConfigVersionSwitch } from "../../../config/useConfigVersionSwitch";
import { UserService } from "../user-service";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import { useConfigStore } from "../../../config/useConfigStore";

const userService = new UserService();

export default defineComponent({
  name: "user-version-switch",
  components: { LoadingSpinnerV2, ButtonGenericV2, FieldCheckboxV2 },
  props: {
    userProfile: {
      type: Object as PropType<IUserProfile>,
      default: () => {
        return userService.factoryUserProfile();
      },
    },
  },
  setup(props: { userProfile: IUserProfile }, context: SetupContext) {
    const version = reactive({
      current: props.userProfile.user.version.current,
      toggle: props.userProfile.user.version.toggle,
    });

    const configStore = useConfigStore();
    const configVersionSwitch = useConfigVersionSwitch(
      version.current,
      configStore.configApp.userInfo.user.version
    );


    watch(
      () => props.userProfile,
      (newValue: IUserProfile) => {
        version.current = newValue.user.version.current;
        version.toggle = newValue.user.version.toggle;
      },
      {
        immediate: true,
      }
    );


    function doSubmit() {
      configVersionSwitch.setUserVersionAdmin(
        version.current,
        version.toggle,
        props.userProfile.user.id
      );
    }

    return { configVersionSwitch, version, doSubmit };
  },
});
</script>
