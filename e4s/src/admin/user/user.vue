<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <SectionLinksWrapper>
      <template slot="default">
        <SectionLinkSimple
          link-title="General"
          :is-active="showSection === 'GENERAL'"
          @selected="setshowSection('GENERAL')"
        />
        <SectionLinkSimple
          link-title="Security"
          :is-active="showSection === 'SECURITY'"
          @selected="setshowSection('SECURITY')"
        />
        <SectionLinkSimple
          link-title="Athletes"
          :is-active="showSection === 'ATHLETES'"
          @selected="setshowSection('ATHLETES')"
        />
        <SectionLinkSimple
          link-title="Entries"
          :is-active="showSection === 'ENTRIES'"
          @selected="setshowSection('ENTRIES')"
        />
      </template>
    </SectionLinksWrapper>

    <div v-if="showSection === 'GENERAL'">
      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputNumberV2
            form-label="ID"
            v-model="userProfile.user.id"
            :is-disabled="true"
          />
          <FormGenericInputTextV2
            form-label="Display Name"
            v-model="userProfile.user.displayName"
            :is-disabled="true"
          />
        </template>
      </FormGenericFieldGridV2>

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputTextV2
            form-label="Login"
            v-model="userProfile.user.login"
            :is-disabled="true"
          />
          <FormGenericInputTextV2
            form-label="Nice Name"
            v-model="userProfile.user.niceName"
            :is-disabled="true"
          />
        </template>
      </FormGenericFieldGridV2>

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputTextV2
            form-label="Email"
            v-model="userProfile.user.email"
            :is-disabled="true"
          />
          <FormGenericInputTextV2
            form-label="Registered"
            v-model="userProfile.user.registered"
            :is-disabled="true"
          />
        </template>
      </FormGenericFieldGridV2>

      <FormGenericFieldGridV2 v-if="isAdmin">
        <template slot="content">
          <PrimaryHref
            link-text="Switch to user"
            :href="
              '/wp-json/e4s/v5/admin/setpwd?email=' + userProfile.user.email
            "
          />
          <PrimaryHref
            link-text="Set user password"
            :href="'/wp-admin/user-edit.php?user_id=' + userProfile.user.id"
          />
        </template>
      </FormGenericFieldGridV2>

      <Credit :user-profile="userProfile" v-if="isAdmin" />

      <!--      <UserVersionSwitch :user-profile="userProfile" v-if="isAdmin" />-->
    </div>

    <!--        <div class="e4s-section-padding-separator"></div>-->
    <div
      v-if="showSection === 'SECURITY'"
      class="e4s-flex-column e4s-gap--large"
    >
      <div class="e4s-card e4s-flex-column e4s-card--generic">
        <!--        <div class="row">-->
        <!--          <div class="col s12 m12 l12">-->
        <!--            <div class="e4s-header&#45;&#45;400">User Areas</div>-->
        <!--          </div>-->
        <!--        </div>-->
        <div class="e4s-flex-row">
          <div class="e4s-header--400">User Areas</div>
        </div>

        <table class="responsive-table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Name</th>
              <th>Type</th>
              <th>Parent</th>
              <th></th>
            </tr>
          </thead>

          <tbody>
            <tr v-for="area of userProfile.areas" :key="area.id">
              <td><span v-text="area.id"></span></td>
              <td><span v-text="area.name"></span></td>
              <td><span v-text="area.entityName"></span></td>
              <td><span v-text="area.parentName"></span></td>
              <td>
                <ButtonGenericV2
                  text="X"
                  button-type="destructive"
                  @click="onAreaRemoveConfirm(area)"
                />
              </td>
            </tr>
          </tbody>
        </table>

        <div class="row">
          <div class="e4s-flex-row">
            <AreaLookupV2 @input="onAreaSelected" v-if="showAreaAdd" />
            <span v-if="!showAreaAdd" v-text="areaToAdd.name"></span>

            <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
              <loading-spinner v-if="isLoading"></loading-spinner>
              <ButtonGenericV2
                text="Cancel"
                :disabled="isLoading"
                button-type="tertiary"
                v-if="areaToAdd.id > 0"
                @click="cancelArea"
              />
              <ButtonGenericV2
                text="Add"
                class="e4s-flex-row--end"
                :disabled="isLoading"
                v-if="areaToAdd.id === 0 && !showAreaAdd"
                @click="addArea"
              />
              <ButtonGenericV2
                text="Save"
                :disabled="isLoading"
                v-if="areaToAdd.id > 0"
                @click="submitArea"
              />
            </div>
          </div>

          <!--          <div class="col s8 m8 l8">-->
          <!--            <AreaLookupV2 @input="onAreaSelected" v-if="showAreaAdd" />-->
          <!--            <span v-if="!showAreaAdd" v-text="areaToAdd.name"></span>-->
          <!--          </div>-->
          <!--          <div class="col s4 m4 l4">-->
          <!--            <div class="e4s-flex-row e4s-justify-flex-row-vert-center">-->
          <!--              <loading-spinner v-if="isLoading"></loading-spinner>-->
          <!--              <ButtonGenericV2-->
          <!--                text="Cancel"-->
          <!--                :disabled="isLoading"-->
          <!--                button-type="tertiary"-->
          <!--                v-if="areaToAdd.id > 0"-->
          <!--                @click="cancelArea"-->
          <!--              />-->
          <!--              <ButtonGenericV2-->
          <!--                text="Add"-->
          <!--                class="e4s-flex-row&#45;&#45;end"-->
          <!--                :disabled="isLoading"-->
          <!--                v-if="areaToAdd.id === 0 && !showAreaAdd"-->
          <!--                @click="addArea"-->
          <!--              />-->
          <!--              <ButtonGenericV2-->
          <!--                text="Save"-->
          <!--                :disabled="isLoading"-->
          <!--                v-if="areaToAdd.id > 0"-->
          <!--                @click="submitArea"-->
          <!--              />-->
          <!--            </div>-->
          <!--          </div>-->
        </div>
      </div>

      <div class="e4s-card e4s-flex-column e4s-card--generic">
        <!--        <div class="row">-->
        <!--          <div class="col s12 m12 l12">-->
        <!--            <div class="e4s-header&#45;&#45;400">User Clubs</div>-->
        <!--          </div>-->
        <!--        </div>-->
        <div class="e4s-flex-row">
          <div class="e4s-header--400">User Clubs</div>
        </div>

        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>Name</th>
              <th>County</th>
              <th></th>
            </tr>
          </thead>

          <tbody>
            <tr v-for="club of userProfile.clubs" :key="club.id">
              <td><span v-text="club.id"></span></td>
              <td><span v-text="club.clubName"></span></td>
              <td><span v-text="club.county"></span></td>
              <td>
                <div class="e4s-flex-row">
                  <ButtonGenericV2
                    class="e4s-flex-row--end e4s-button--destructive-x"
                    text="X"
                    :disabled="isLoading"
                    button-type="destructive"
                    @click="onClubRemoveConfirm(club)"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <div class="e4s-flex-row">
          <div
            class="
              e4s-flex-row e4s-justify-flex-row-vert-center
              e4s-gap--standard
            "
            v-if="showClubAdd"
          >
            <FieldRadioV2
              :option-value="false"
              v-model="isSchoolSearch"
              label="Club"
            />
            <FieldRadioV2
              :option-value="true"
              v-model="isSchoolSearch"
              label="School"
            />
            <ClubTypeAheadByType
              @input="onClubSelected"
              :club-type="isSchoolSearch ? 'S' : 'C'"
            />
          </div>

          <ButtonGenericV2
            text="Add"
            :disabled="isLoading"
            v-if="clubToAdd.id === 0 && !showClubAdd"
            @click="addClub"
            class="e4s-flex-row--end"
          />
        </div>
      </div>

      <div class="e4s-card e4s-flex-column e4s-card--generic">
        <user-permissions-form
          :user-prop="userProfile.user"
          :user-permissions-prop="userProfile.user.permissions"
        >
        </user-permissions-form>
      </div>

      <div class="e4s-card e4s-flex-column e4s-card--generic e4s-gap--standard">
        <div class="e4s-header--400">Club Competitions</div>

        <!--      <UserClubsTable :user-club-comps="userProfile.clubComps" />-->

        <!--      <hr class="dat-e4s-hr-only"/>-->

        <!--      <div class="e4s-header&#45;&#45;500">Add Club Competition</div>-->

        <UserClubPickerWrapper
          :user-club-comps="userProfile.clubComps"
          :user-profile="userProfile"
          v-on:onSubmit="onSelectedClubComp"
        />
      </div>
    </div>
    <!--/SECURITY-->

    <!--        <div class="e4s-section-padding-separator"></div>-->
    <div v-if="showSection === 'ATHLETES'">
      <div class="e4s-card e4s-flex-column e4s-card--generic">
        <div class="e4s-flex-row">
          <div class="e4s-header--400">Athletes</div>
        </div>

        <div v-for="athlete in userProfile.athletes" :key="athlete.id">
          <AthleteMaintCard
            :athlete="athlete"
            v-on:deleteAthlete="deleteAthlete"
          ></AthleteMaintCard>
          <div class="e4s-card-standard-sep"></div>
        </div>
      </div>
    </div>
    <!--/ATHLETES-->
    <user-events
      v-if="showSection === 'ENTRIES'"
      :user-profile="userProfile"
      v-on:eventPaidToggle="reload"
      v-on:eventRemoved="reload"
    >
    </user-events>

    <e4s-modal
      v-if="showAreaRemoveConf"
      :header-message="'Area Delete'"
      :body-message="'Are you sure you would like to delete area, continue?'"
      :button-primary-text="'Continue'"
      :isLoading="isLoading"
      v-on:closeSecondary="showAreaRemoveConf = false"
      v-on:closePrimary="onAreaRemove"
    >
    </e4s-modal>

    <e4s-modal
      v-if="showClubRemoveConf"
      :header-message="'Club Delete'"
      :body-message="'Are you sure you would like to delete club, continue?'"
      :button-primary-text="'Continue'"
      :isLoading="isLoading"
      v-on:closeSecondary="showClubRemoveConf = false"
      v-on:closePrimary="onClubRemove"
    >
    </e4s-modal>

    <AthleteDelete
      :athlete="athleteToDelete"
      :user-id="userProfile.user.id"
      v-on:athleteDeleted="deleteAthleteResponse"
    >
    </AthleteDelete>

    <!--        {{areaToAdd}}-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {
  IUserClubComp,
  IUserProfile,
  UserProfileShowSection,
} from "./user-models";
import { Prop, Watch } from "vue-property-decorator";
import { UserService } from "./user-service";
import AreaLookup from "../../area/area-lookup.vue";
import { IArea } from "../../area/area-models";
import { UserProfileData } from "./user-data";
import { IServerResponse } from "../../common/common-models";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import LoadingSpinner from "../../common/ui/loading-spinner.vue";
import E4sModal from "../../common/ui/e4s-modal.vue";
import { IClub } from "../../club/club-models";
import ClubSearch from "../../club/club-search.vue";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import AthleteMaintCard from "../../athlete/athletes/athlete-maint-card.vue";
import { IConfigApp, IUserApplication } from "../../config/config-app-models";
import UserPermissionsForm from "../permissions/user-permission-form.vue";
import { ATH_COMP_SCHED_STORE_CONST } from "../../athletecompsched/store/athletecompsched-store";
import { ICartEvent } from "../../cart/cart-models";
import UserEvents from "./user-events.vue";
import { IAthleteSummary } from "../../athlete/athlete-models";
import AthleteDelete from "../../athlete/athlete-delete.vue";
import { AthleteService } from "../../athlete/athlete-service";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import * as CommonServiceUtils from "../../common/common-service-utils";
import Credit from "./credit/credit.vue";
import UserVersionSwitch from "./v2/UserVersionSwitch.vue";
import UserClubPickerWrapper from "../../entry/v2/schools/user-club-picker/UserClubPickerWrapper.vue";
import SectionLinksWrapper from "../../common/ui/layoutV2/tabs/section-links-wrapper.vue";
import SectionLinkSimple from "../../common/ui/layoutV2/tabs/section-link-simple.vue";
import FormGenericInputTextV2 from "../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FormGenericFieldGridV2 from "../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputNumberV2 from "../../common/ui/layoutV2/form/form-generic--input-number-v2.vue";
import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import PrimaryHref from "../../common/ui/layoutV2/href/PrimaryHref.vue";
import AreaLookupV2 from "../../area/AreaLookupV2.vue";
import ClubTypeAheadByType from "../../club/v2/clubtypeahead/ClubTypeAheadByType.vue";
import InputCheckboxV2 from "../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import FieldRadioV2 from "../../common/ui/layoutV2/fields/field-radio-v2.vue";

const athleteService: AthleteService = new AthleteService();
const userService = new UserService();

@Component({
  name: "user",
  components: {
    FieldRadioV2,
    InputCheckboxV2,
    ClubTypeAheadByType,
    AreaLookupV2,
    PrimaryHref,
    ButtonGenericV2,
    FormGenericInputTemplateV2,
    FormGenericInputNumberV2,
    FormGenericFieldGridV2,
    FormGenericInputTextV2,
    SectionLinkSimple,
    SectionLinksWrapper,
    UserClubPickerWrapper,
    UserVersionSwitch,
    Credit,
    AthleteDelete,
    "area-lookup": AreaLookup,
    "loading-spinner": LoadingSpinner,
    "e4s-modal": E4sModal,
    "club-search": ClubSearch,
    AthleteMaintCard,
    "user-permissions-form": UserPermissionsForm,
    "user-events": UserEvents,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
      userApplication: (state: IConfigStoreState) =>
        state.configApp.userInfo.user,
    }),
  },
})
export default class User extends Vue {
  public readonly configApp!: IConfigApp;
  public readonly userApplication!: IUserApplication;

  @Prop({
    default: () => {
      return userService.factoryUserProfile();
    },
  })
  public userProfileProp: IUserProfile;

  public userProfileData: UserProfileData = new UserProfileData();
  public userService: UserService = new UserService();
  public userProfile: IUserProfile = this.userService.factoryUserProfile();
  // public userProfile: IUserProfile = simpleClone(this.userProfileProp);
  public PREFIX = Math.random().toString(36).substring(2);
  public isLoading: boolean = false;

  public showAreaAdd: boolean = false;
  public areaToAdd: IArea = { id: 0 } as IArea;

  public showAreaRemoveConf: boolean = false;
  public areaToRemove: IArea = { id: 0 } as IArea;

  public showClubAdd: boolean = false;

  public clubToAdd: IClub = { id: 0 } as IClub;
  public showClubRemoveConf: boolean = false;
  public clubToRemove: IClub = { id: 0 } as IClub;

  public isSchoolSearch: boolean = false;
  public athleteToDelete: IAthleteSummary =
    athleteService.factoryAthleteSummary();

  public currentUserCredit: number = 0;
  public currentUserCreditReason = "";

  public showSection: UserProfileShowSection = "GENERAL";

  @Watch("userProfileProp", { immediate: true })
  public onUerProfilePropChanged(userProfile: IUserProfile) {
    console.log("User.onUerProfilePropChanged()", userProfile);
    const userProfileTemp = R.clone(userProfile);
    if (R.isNil(userProfile.user.permissions)) {
      userProfileTemp.user.permissions = [];
    }
    this.userProfile = R.clone(userProfileTemp);
  }

  // @Watch("configApp")
  // public onConfigAppChanged(newValue: IConfigApp, oldValue: IConfigApp) {
  //     this.currentUserCredit = newValue.userInfo.e4s_credit;
  // }

  public onAreaSelected(area: IArea) {
    this.areaToAdd = R.clone(area);
    this.showAreaAdd = false;
  }

  public cancelArea() {
    this.areaToAdd = { id: 0 } as IArea;
  }

  public addArea() {
    console.log("User.addArea()");
    this.showAreaAdd = true;
    this.submitArea();
  }

  public submitArea() {
    console.log("User.submitArea()");
    this.isLoading = true;
    this.userProfileData
      .addAreaToUserProfile(this.userProfile.user.id, this.areaToAdd.id)
      .then((response: IServerResponse<any>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        this.loadProfileById(this.userProfile.user.id);
        this.cancelArea();
        return;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return {};
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public onAreaRemoveConfirm(area: IArea) {
    this.areaToRemove = R.clone(area);
    this.showAreaRemoveConf = true;
  }

  public onAreaRemove() {
    console.log("User.onAreaRemove() ");
    this.isLoading = true;
    this.userProfileData
      .removeAreaFromUserProfile(this.userProfile.user.id, this.areaToRemove.id)
      .then((response: IServerResponse<IUserProfile>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        messageDispatchHelper(
          "Area removed from user.",
          USER_MESSAGE_LEVEL.INFO.toString()
        );
        this.loadProfileById(this.userProfile.user.id);
        return;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return {};
      })
      .finally(() => {
        this.isLoading = false;
        this.showAreaRemoveConf = false;
      });
  }

  public loadProfileById(id: number) {
    console.log("User.loadProfileById() ");
    this.isLoading = true;
    this.userProfileData
      .getUserProfileByProp(id.toString(), "id")
      .then((response: IServerResponse<IUserProfile>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        this.userProfile = response.data;
        return;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return {};
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public onClubSelected(club: IClub) {
    this.clubToAdd = R.clone(club);
    this.showClubAdd = false;
    this.submitClub();
  }

  public cancelClub() {
    this.clubToAdd = { id: 0 } as IClub;
  }

  public addClub() {
    console.log("User.addClub()");
    this.showClubAdd = true;
  }

  public submitClub() {
    console.log("User.submitClub()");
    this.isLoading = true;
    this.userProfileData
      .addClubToUserProfile(this.userProfile.user.id, this.clubToAdd.id)
      .then((response: IServerResponse<any>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        this.loadProfileById(this.userProfile.user.id);
        this.cancelClub();
        return;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return {};
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public onClubRemoveConfirm(club: IClub) {
    this.showClubRemoveConf = true;
    this.clubToRemove = R.clone(club);
  }

  public onClubRemove() {
    console.log("User.onClubRemove() ");
    this.isLoading = true;
    this.userProfileData
      .removeClubFromUserProfile(this.userProfile.user.id, this.clubToRemove.id)
      .then((response: IServerResponse<IUserProfile>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        messageDispatchHelper(
          "Club removed from user.",
          USER_MESSAGE_LEVEL.INFO.toString()
        );
        this.loadProfileById(this.userProfile.user.id);
        return;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return {};
      })
      .finally(() => {
        this.isLoading = false;
        this.showClubRemoveConf = false;
      });
  }

  public impersonate() {
    this.isLoading = true;
    this.$store
      .dispatch(
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
          "/" +
          CONFIG_STORE_CONST.CONFIG_ACTIONS_IMPERSONATE,
        this.userApplication.impersonating ? { id: 0 } : this.userProfile
      )
      .then(() => {
        this.isLoading = false;
        // this.$router.push({path: ""});
        window.location.href = "/#/";
      });
  }

  public onRemoveEvent(event: ICartEvent) {
    this.$store.dispatch(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_REMOVE_FROM_SELECTED,
      { event }
    );
  }

  public reload() {
    const userProfileData: UserProfileData = new UserProfileData();
    this.isLoading = true;
    userProfileData
      .getUserProfileById(this.userProfile.user.id)
      .then((resp) => {
        this.userProfile = resp.data;
      })
      .catch((error: any) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return {};
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public deleteAthlete(athlete: IAthleteSummary) {
    this.athleteToDelete = athlete;
  }

  public deleteAthleteResponse(wasSuccess: boolean) {
    if (wasSuccess) {
      this.reload();
    }
  }

  public setCredit() {
    if (
      !CommonServiceUtils.hasAtLeastOneCharacter(this.currentUserCreditReason)
    ) {
      messageDispatchHelper("Please enter a credit reason");
      return;
    }

    this.isLoading = true;
    const prom = this.userProfileData.setUserCredit(
      this.userProfile.user.id,
      this.userProfile.e4sCredit,
      this.currentUserCreditReason
    );
    handleResponseMessages(prom);
    prom.finally(() => {
      this.isLoading = false;
    });
  }

  public onSelectedClubComp(outputUserClubComp: IUserClubComp) {
    this.reload();
  }

  public setshowSection(section: UserProfileShowSection) {
    this.showSection = section;
  }
}
</script>

<style scoped>
.button-width {
  width: 5rem;
}
</style>
