<template>
    <div>
        <slot>
            <admin-header></admin-header>
        </slot>
        <main>
            <div class="row" :class="userApplication.impersonating ? 'impersonating-user' : ''">
                <router-view></router-view>
                <toaster></toaster>
            </div>
        </main>
    </div>
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import Toaster from "../user-message/toaster.vue";
    import AdminHeader from "./admin-header.vue";
    import { IConfigStoreState, CONFIG_STORE_CONST } from "../config/config-store";
    import { mapState } from "vuex";

    @Component({
        name: "admin-section",
        components: {
            "admin-header": AdminHeader,
            "toaster": Toaster
        },
        computed: {
            ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
                userApplication: (state: IConfigStoreState) => state.configApp.userInfo.user
            })
        }
    })
    export default class AdminSection extends Vue {

    }
</script>
