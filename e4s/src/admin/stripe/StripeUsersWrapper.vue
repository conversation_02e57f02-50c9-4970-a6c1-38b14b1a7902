<template>
  <div style="margin: var(--e4s-gap--standard)">
    <LoadingSpinnerV2 v-if="stripeUsers.state.ui.isLoading" />
    <div class="e4s-flex-row e4s-header--200">
      Stripe Users
    </div>
    <StripeUsers
      :stripe-users="stripeUsers.state.users"
      v-on:doApproval="stripeUsers.controller.doApproval"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import { useStripeUsers } from "./useStripeUsersController";
import StripeUsers from "./StripeUsers.vue";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";

export default defineComponent({
  name: "StripeUsersWrapper",
  components: { LoadingSpinnerV2, StripeUsers },
  props: {},
  setup(props: any, context: SetupContext) {
    const stripeUsers = useStripeUsers();
    stripeUsers.controller.getUsers();

    return {
      stripeUsers,
    };
  },
});
</script>
