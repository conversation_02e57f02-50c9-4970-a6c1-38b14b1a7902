<template>
  <div>
    <div class="e4s-flex-row e4s-gap--standard">
      <span v-text="stripeUser.club.id"></span>
      <span v-text="stripeUser.club.name"></span>
    </div>
    <div class="e4s-flex-row e4s-gap--standard">
      <span v-text="stripeUser.user.id"></span>
      <span v-text="stripeUser.user.email"></span>
    </div>
    <span v-text="connectedTime"></span>
    <span v-text="approvedTime"></span>
    <ButtonGenericV2
      text="Approve"
      v-on:click="doApproval"
      :disabled="!stripeUser.stripe.requiresApproval"
    />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import { IStripeUser } from "./stripe-users-data";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { getE4sStandardHumanDateTimeOutPut } from "../../common/common-service-utils";
import { IsoDateTime } from "../../common/common-models";

export default defineComponent({
  name: "StripeUser",
  components: { ButtonGenericV2 },
  props: {
    stripeUser: {
      type: Object as PropType<IStripeUser>,
      required: true,
    },
  },
  setup(props: { stripeUser: IStripeUser }, context: SetupContext) {
    function fixDuffDate(dateTime: string): IsoDateTime {
      return dateTime.replace(" ", "T");
    }

    const connectedTime = computed(() => {
      if (props.stripeUser.stripe.connected === "") {
        return;
      }
      const dateTime = fixDuffDate(props.stripeUser.stripe.connected);
      return getE4sStandardHumanDateTimeOutPut(dateTime);
    });

    const approvedTime = computed(() => {
      if (props.stripeUser.stripe.approved === "") {
        return;
      }
      const dateTime = fixDuffDate(props.stripeUser.stripe.approved);
      return getE4sStandardHumanDateTimeOutPut(dateTime);
    });

    function doApproval() {
      context.emit("doApproval", props.stripeUser);
    }

    return {
      connectedTime,
      approvedTime,
      doApproval,
    };
  },
});
</script>
