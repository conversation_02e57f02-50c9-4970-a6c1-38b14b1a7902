import {IBaseConcrete, IServerResponse, IsoDateTime} from "../../common/common-models"
import https from "../../common/https"

export interface IStripeUser {
  club: IBaseConcrete;
  stripe: {
    approved: IsoDateTime;
    connected: IsoDateTime;
    requiresApproval: boolean;
  }
  user: {
    id: number;
    login: string;
    email: string;
  }
}

export class StripeUsersData {


  public getUsers(): Promise<IServerResponse<IStripeUser[]>> {
    return https.get( "/v5/stripe/list") as any as Promise<IServerResponse<IStripeUser[]>>;
  }

  public doApproval(stripeUser: IStripeUser): Promise<IServerResponse<unknown>> {
    return https.get( "/v5/stripe/approve/" + stripeUser.user.id) as any as Promise<IServerResponse<unknown>>;
  }

}
