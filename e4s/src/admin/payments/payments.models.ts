export interface IRefundEvent {
  compid: number;
  orderid: number;
  reason: string;
  productid: number; //  For specific event, specify this.
  test: boolean; //  won't actually do refund
  removeEntry: boolean; //
  email: boolean; //  Send an email to user
  value: number; //  Value to refund/credit   0 = refund/credit in full.
  credit: boolean; //  Is it a credit else refund
  price: number; //  ????
  refundStripeFee: boolean; //
  refundE4SFee: boolean; //
  text: string;
}

export interface IRefundEventPayloadUser {
  compid: number;
  orderid: number;
  reason: string;
  productid: number; //  For specific event, specify this.
  credit: boolean; //  Is it a credit else refund
}

export interface IRefundAdmin extends IRefundEvent {
  eventids: number[];
  multi: boolean; //  Leave as false.
}
