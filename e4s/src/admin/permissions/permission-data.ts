import https from "../../common/https";
import { IServerResponse } from "../../common/common-models";
import { IPermission, IPermLevel, IRole } from "./permission-models";
import { ResourceData } from "../../common/resource/resource-service";

export class PermissionData extends ResourceData<IPermission> {
  constructor() {
    super("/v5/permissions");
  }

  public getRoles(): Promise<IServerResponse<IRole[]>> {
    const response: IServerResponse<IRole[]> = {
      errNo: 0,
      data: [
        {
          id: 1,
          name: "Admin",
        } as IRole,
        {
          id: 2,
          name: "Builder",
        } as IRole,
        {
          id: 3,
          name: "Reports",
        } as IRole,
      ],
    } as IServerResponse<IRole[]>;
    return Promise.resolve(response) as any as Promise<
      IServerResponse<IRole[]>
    >;
  }

  public getPermLevels(): Promise<IServerResponse<IPermLevel[]>> {
    const response: IServerResponse<IPermLevel[]> = {
      errNo: 0,
      data: [
        {
          id: 1,
          name: "General",
        } as IRole,
        {
          id: 2,
          name: "Config",
        } as IR<PERSON>,
        {
          id: 3,
          name: "Schedule",
        } as IRole,
        {
          id: 4,
          name: "Comp Events",
        } as IRole,
      ],
    } as IServerResponse<IRole[]>;
    return Promise.resolve(response) as any as Promise<
      IServerResponse<IPermLevel[]>
    >;
  }

  public getUserPermissions(userId: number) {
    return https.get("/v5/user/permissions/" + userId) as any as Promise<
      IServerResponse<IPermission[]>
    >;
  }

  public submitPermission(
    userId: number,
    permission: IPermission
  ): Promise<IServerResponse<IPermission[]>> {
    return https.post(
      "/v5/user/permissions/" + userId,
      permission
    ) as any as Promise<IServerResponse<IPermission[]>>;
  }

  /**
   * Delete a permission
   * @param userId    Completely ignored.
   * @param permission
   */
  public deletePermission(
    userId: number,
    permission: IPermission
  ): Promise<IServerResponse<IPermission[]>> {
    return https.delete(
      "/v5/user/permissions/" + permission.id
    ) as any as Promise<IServerResponse<IPermission[]>>;
  }

  /**
   * Delete a permission
   * @param permission
   */
  public deleteUserPermission(
    permId: number
  ): Promise<IServerResponse<IPermission[]>> {
    return https.delete("/v5/user/permissions/" + permId) as any as Promise<
      IServerResponse<IPermission[]>
    >;
  }
}
