<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-form-header">Permission Form</div>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m12 l12">
        <!--                roles{{roles}}-->
        <label class="active" :for="PREFIX + 'role'">Role</label>
        <simple-object-drop-down
          :id="PREFIX + 'role'"
          :simple-objects="getRoles"
          :current-value="permission.role"
          :please-select="true"
          v-on:onSelected="onRoleSelected"
        >
        </simple-object-drop-down>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <org-picker
          :org-prop="permission.org"
          v-on:onSelect="onOrgSelected"
        ></org-picker>
      </div>
    </div>

    <div class="row" v-show="permission.org.id > 0">
      <div class="col s12 m12 l12">
        <label class="active" :for="PREFIX + 'comp'"
          >Competition (Optional)
          <span v-if="competitionsLoading">Loading...</span></label
        >
        <!--                <simple-object-drop-down :id="PREFIX + 'comp'"-->
        <!--                                         :simple-objects="competitions"-->
        <!--                                         :current-value="permission.comp"-->
        <!--                                         :is-loading="competitionsLoading"-->
        <!--                                         v-on:onSelected="onCompSelected">-->
        <!--                </simple-object-drop-down>-->
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <label class="active" :for="PREFIX + 'perm-level'"
          >Permission Levels</label
        >
        <div>
          <div
            v-for="permLevel in permLevelsSelect"
            :key="permLevel.id"
            :id="PREFIX + 'perm-level'"
            class="e4s-force-inline-block"
          >
            <div class="chip">
              <span v-text="permLevel.name"></span>
              <i
                class="close material-icons"
                v-on:click="addPermLevel(permLevel)"
                >check</i
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div
          v-for="permLevel in permission.permLevels"
          :key="permLevel.id"
          class="e4s-force-inline-block"
        >
          <div class="chip green">
            <span v-text="permLevel.name"></span>
            <i
              class="close material-icons"
              v-on:click="removePermLevel(permLevel.id)"
              >close</i
            >
          </div>
        </div>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div class="right">
          <loading-spinner v-if="isLoading"></loading-spinner>
          <button
            class="btn waves-effect waves grey"
            :disabled="isLoading"
            v-on:click="cancel"
          >
            <span v-text="$t('buttons.close')"></span>
          </button>
          <button
            class="btn waves-effect waves green"
            :disabled="isLoading"
            v-on:click="submit"
          >
            <span v-text="$t('buttons.save')"></span>
          </button>
        </div>
      </div>
    </div>

    <!--        <div class="row">-->
    <!--            <div class="col s12 m12 l12">-->
    <!--                {{permission}}<br>-->
    <!--                roles{{roles}}-->
    <!--            </div>-->
    <!--        </div>-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { IPermission, IRole, IPermLevel } from "./permission-models";
import { mapState } from "vuex";
import SimpleObjectDropDown from "../../common/ui/simple-object-drop-down.vue";
import { PermissionsService } from "./permissions-service";
import {
  IConfigStoreState,
  CONFIG_STORE_CONST,
} from "../../config/config-store";
import OrgPicker from "../../org/org-picker.vue";
import { ENTRY_STORE_CONST, IEntryStoreState } from "../../entry/entry-store";
import { IOrg } from "../../org/org-models";
import { ICompetitionInfo } from "../../competition/competition-models";
import LoadingSpinner from "../../common/ui/loading-spinner.vue";
import { IObjectKeyType } from "../../common/common-models";
import { CommonService } from "../../common/common-service";

const commonService: CommonService = new CommonService();

@Component({
  name: "permission-form",
  components: {
    "loading-spinner": LoadingSpinner,
    "simple-object-drop-down": SimpleObjectDropDown,
    "org-picker": OrgPicker,
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      competitions: (state: IEntryStoreState) => state.entryForm.competitions,
      competitionsLoading: (state: IEntryStoreState) =>
        state.entryForm.competitionsLoading,
    }),
  },
})
export default class PermissionForm extends Vue {
  @Prop({
    default: () => {
      return [];
    },
  })
  public permissionProp: IPermission;
  @Prop({
    default: () => {
      return [];
    },
  })
  public roles: IObjectKeyType<IRole>;
  @Prop({ default: false }) public isLoading: boolean;

  public permissionsService: PermissionsService = new PermissionsService();
  public PREFIX = Math.random().toString(36).substring(2);
  public permission: IPermission = this.permissionsService.factoryPermission();

  // public rolesSelect: IRole[] = [];
  public permLevels: IPermLevel[] = [];
  public permLevelsSelect: IPermLevel[] = [];
  public permLevel: IPermLevel = {
    id: 0,
  } as IPermLevel;

  public created() {
    this.permission = R.clone(this.permissionProp);
    // this.rolesSelect = R.clone(this.roles);
    // this.permLevelsSelect = R.clone(this.roles.p);
  }

  // @Watch("roles")
  // public onRolesChange(newValue: IRole[]) {
  //     this.rolesSelect = R.clone(newValue);
  // }

  @Watch("permissionProp")
  public onUserPermissionsPropChange(newValue: IPermission) {
    this.permission = R.clone(newValue);
  }

  @Watch("permLevels")
  public onPermLevelsChange(newValue: IPermLevel[]) {
    this.permLevelsSelect = R.clone(newValue);
  }

  public onRoleSelected(role: IRole) {
    this.permission.role = R.clone(role);
    this.permLevels = R.clone(role.permLevels);
  }

  public onOrgSelected(org: IOrg) {
    // this doesn't make sense without a competition ID.
    // The payload should be orgId and compId, e.g. {orgId: org.id, compId: 12344}
    // this.permission.org = R.clone(org);
    // this.$store.dispatch(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME + "/"
    //     + ENTRY_STORE_CONST.ENTRY_STORE_ACTIONS_GET_COMPETITIONS_BY_ID, org.id);
  }

  public onCompSelected(competitionLookup: ICompetitionInfo) {
    this.permission.comp = competitionLookup;
  }

  public addPermLevel(permLevel?: IPermLevel) {
    permLevel = permLevel ? permLevel : this.permLevel;
    this.permission.permLevels.push(permLevel ? permLevel : this.permLevel);
    this.recalcPermSelect();
  }

  public recalcPermSelect() {
    const permLevels = this.permLevels.filter((permLev) => {
      const index = R.findIndex(R.propEq("id", permLev.id))(
        this.permission.permLevels
      );
      return index === -1;
    });
    this.permLevelsSelect = R.clone(permLevels);
  }

  public removePermLevel(id: number) {
    const permLevels = this.permission.permLevels.filter((permLevel) => {
      return permLevel.id !== id;
    });
    this.permission.permLevels = R.clone(permLevels);
    this.recalcPermSelect();
  }

  public get getRoles() {
    if (this.roles) {
      return commonService
        .convertObjectToArray(this.roles)
        .sort((a: IRole, b: IRole) => {
          return a.name.localeCompare(b.name);
        });
    }
    return [];
  }

  public submit() {
    this.$emit("onSubmit", R.clone(this.permission));
  }

  public cancel() {
    this.$emit("onCancel");
  }
}
</script>
