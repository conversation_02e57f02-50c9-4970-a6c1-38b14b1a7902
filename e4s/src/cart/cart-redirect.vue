<template>
  <div class="row">
    <div class="col s12 m12 l12">Loading...</div>
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import { AthleteCompSchedData } from "../athleteCompSched/athletecompsched-data";
import { CONFIG } from "../common/config";

export default defineComponent({
  name: "cart-redirect",
  components: {},
  props: {},
  setup(props: any, context: SetupContext) {
    new AthleteCompSchedData().getCart().then(() => {
      window.location.href = CONFIG.E4S_HOST + "/basket";
    }).catch( () => {
      window.location.href = CONFIG.E4S_HOST;
    });

    return {};
  },
});
</script>
