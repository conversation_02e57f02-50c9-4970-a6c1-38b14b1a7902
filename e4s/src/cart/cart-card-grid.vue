<template>
    <div>
        <div class="sort-by-wrapper">
            <div class="right-align">
                <span class="sort-by-label">Sort By:</span>
                <select class="e4s-select e4s-select-mobile" v-model="sortBy">
                    <option v-for="option in sortByOptions" :key="option.id" :value="option">
                        {{ option.label}}
                    </option>
                </select>
            </div>
        </div>
        <div class="e4s-card-standard-sep"></div>

        <div v-for="event in cartEventsOrdered">
            <CartCard :event="event"
                       v-on:togglePaid="togglePaid"
                       v-on:removeEvent="removeFromCart">
            </CartCard>
            <div class="e4s-card-standard-sep"></div>
        </div>

    </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";
import {Prop, Watch} from "vue-property-decorator";
import {ICartEvent} from "../cart/cart-models";

import {IAthleteCompSchedRuleEvent} from "../athletecompsched/athletecompsched-models";
import {AthleteCompSchedService} from "../athletecompsched/athletecompsched-service";
import CartCard from "./cart-card.vue";
import {CartService} from "./cart-service";


@Component({
    name: "cart-card-grid",
    components: {
        CartCard
    }
})
export default class CartCardGrid extends Vue {
    @Prop({
        default: () => {
            return [];
        }
    }) public cartEvents: ICartEvent[];
    public cartEventsOrdered: ICartEvent[] = [];

    public athleteCompSchedService: AthleteCompSchedService = new AthleteCompSchedService();
    public cartService: CartService  = new CartService();

    public sortBy = {} as any;

    public sortByOptions = [
        {id: 1, label: "Time added", value: "timeSelected"},
        {id: 2, label: "Event Name", value: "Name"},
        {id: 3, label: "Start time", value: "startdate"},
        {id: 4, label: "Club", value: "club"},
        {id: 5, label: "First Name", value: "firstName"},
        {id: 6, label: "Last Name", value: "surName"},
    ];

    // public created() {
    //     this.sortBy = this.sortByOptions[0];
    //     this.sortEvents(this.cartEvents);
    // }

    public mounted() {
        this.sortBy = this.sortByOptions[0];
        this.sortEvents(this.cartEvents);
    }

    public sortEvents(cartEvents: ICartEvent[]) {
        this.cartEventsOrdered = this.cartService.sortCart(this.sortBy.value, cartEvents);
    }

    @Watch("cartEvents")
    public onCartEventsChanged(newValue: ICartEvent[]) {
        this.sortEvents(this.cartEvents);
    }

    @Watch("sortBy")
    public onSortByChanged(newValue: ICartEvent[]) {
        this.sortEvents(this.cartEvents);
    }


    public removeFromCart(event: IAthleteCompSchedRuleEvent) {
        this.$emit("onRemoveEvent", event);
    }

    public togglePaid(cartEvent: ICartEvent) {
        this.$emit("togglePaid", cartEvent);
    }

}
</script>

<style scoped>

    .sort-by-wrapper {
        margin: 1rem;
    }

    .sort-by-label {
        font-weight: 500;
    }

    .sort-by{
        background-color: white !important;
        height: 2rem !important;
        color: black !important;
        display: inline !important;
        font-size: 1rem !important;
        width: auto !important;
    }

</style>
