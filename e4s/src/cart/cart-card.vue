<template>
  <div class="cart-card">
    <div class="row">
      <div class="col s10 m6 l6">
        <a href="#" v-if="event.paid === 0">
          <i class="material-icons red-text" @click.prevent="removeEvent"
            >delete_forever</i
          >
        </a>
        <span class="cart-card-event" v-text="event.Name"></span>
        <span class="cart-card-event">@</span>
        <span class="cart-card-event" v-text="getEventTime"></span>
        <i
          v-if="athleteCompSchedService.isTeamEvent(event)"
          class="material-icons e4s-small-icon"
          >group</i
        >
      </div>

      <div class="col m4 l4 hide-on-small-and-down">
<!--        <span v-text="getEventDate"></span>-->
        <span v-text="getOrderDateTime"></span>
        <!--<span v-text="getEventDateTime"></span>-->
      </div>

      <div class="col s2 m2 l2">
        <div class="right">
          <a
            v-if="hasBuilderPermissionForComp"
            href="#"
            v-on:click.prevent="togglePaid"
          >
            <span v-text="getTogglePaidMessage"></span>
          </a>

          <a
            v-if="isAdmin && getOrderId > 0"
            target="_order"
            :href="'/wp-admin/post.php?post=' + getOrderId + '&action=edit'"
          >
            <span v-text="getOrderId"></span>
          </a>

          <span class="cart-card-price" v-text="getPrice"></span>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col m6 l6 hide-on-small-and-down">
        <span v-text="athleteName"></span>,
        <span v-text="event.club"></span>
      </div>

      <div class="col m6 l6 hide-on-small-and-down">
        <span class="right" v-text="getCompName"></span>
      </div>

      <div class="col s8 hide-on-med-and-up">
        <span v-text="getCompName"></span>
      </div>

      <div class="col s4 hide-on-med-and-up">
<!--        <span class="right" v-text="getEventDate"></span>-->
        <span class="right" v-text="getOrderDateTime"></span>
      </div>
    </div>

    <div class="row hide-on-med-and-up">
      <div class="col s6">
        <span v-text="athleteName"></span>
      </div>

      <div class="col s6">
        <span class="right" v-text="event.club"></span>
      </div>
    </div>

    <div class="row" v-if="hasCompEventAthletes">
      <div class="col s12 m12 l12">
        <span v-text="getCompEventTeamAthleteNames"></span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { mapState, mapGetters } from "vuex";
import {
  IAthleteCompSchedRuleEvent,
  ICompEventTeamAthlete,
} from "../athletecompsched/athletecompsched-models";
import { AthleteCompSchedService } from "../athletecompsched/athletecompsched-service";
import { IConfigApp } from "../config/config-app-models";
import { CONFIG_STORE_CONST, IConfigStoreState } from "../config/config-store";
import { CartService } from "./cart-service";
import { ENTRY_STORE_CONST } from "../entry/entry-store";

const cartService: CartService = new CartService();

@Component({
  name: "cart-card",
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
      hasBuilderPermissionForComp:
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_GETTERS_HAS_BUILDER_PERM_FOR_COMP,
    }),
  },
})
export default class CartCard extends Vue {
  @Prop() public event: IAthleteCompSchedRuleEvent;
  public athleteCompSchedService: AthleteCompSchedService =
    new AthleteCompSchedService();

  public configApp: IConfigApp;

  public get getEntryCountDisplay() {
    return (
      this.event.entrycnt +
      (this.event.maxathletes > 0 ? " of " + this.event.maxathletes : "")
    );
  }

  public get athleteName() {
    return this.event.firstName + " " + this.event.surName;
  }

  public get getEventDateTime() {
    return this.getEventDate + " @ " + this.getEventTime;
  }

  public get getEventDate() {
    return this.athleteCompSchedService.getEventDate(this.event);
  }

  public get getEventTime() {
    return this.athleteCompSchedService.getEventTime(this.event);
  }

  public get getOrderDateTime() {
    return this.athleteCompSchedService.getOrderDateTime(this.event);
  }

  public get getPrice() {
    //  "€"
    return this.event.price && this.event.price.actualPrice
      ? this.configApp.currency + this.event.price.actualPrice.toFixed(2)
      : "";
  }

  public removeEvent() {
    this.$emit("removeEvent", this.event);
  }

  public get getCompEventTeamAthleteNames() {
    const athletes: ICompEventTeamAthlete[] | undefined = this.event.athletes;
    if (R.isNil(athletes)) {
      return "";
    }
    return athletes
      .map((ath) => {
        return ath.athlete;
      })
      .join(", ");
  }

  public get hasCompEventAthletes() {
    return !R.isNil(this.event.athletes);
  }

  public togglePaid() {
    this.$emit("togglePaid", this.event);
  }

  public get getTogglePaidMessage() {
    return cartService.getPaidToggleMessage(this.event);
  }

  public get getOrderId() {
    // @ts-ignore
    return this.event.orderid ? this.event.orderid : "";
  }

  public get getCompName() {
    return (
      (this.event.compid ? "(" + this.event.compid + ") " : "") +
      this.athleteCompSchedService.getEventDate(this.event) + " " +
      (this.event.compName ? this.event.compName : "")
    );
  }
}
</script>
