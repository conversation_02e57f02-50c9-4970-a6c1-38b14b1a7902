<template>
  <div>
    <span v-text="message"></span>
  </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";

@Component({
  name: "test-template",
  components: {
    HtmlEditor: () => {
      return import(
        /* webpackPrefetch: true */
        /* webpackChunkName: "html-editor" */
        "../../common/ui/html-editor/html-editor.vue");
    }
  }
})
export default class TestTemplate extends Vue {
  @Prop({default: ""})
  public readonly message: string;

  @Watch("message")
  public onMessageChanged(newValue: string, oldValue: string) {
  }

  public id: number = 0
  public isLoading = false;

  public created() {
    const id: number = isNaN(Number(this.$route.params.id)) ? 0 : parseInt(this.$route.params.id, 0);
    this.id = id;
  }
}
</script>
