// const keys: (keyof IKeywordsRefData)[] = Object.keys(keywordsRefData) as (keyof IKeywordsRefData)[];
// type userPreview = Pick<User, "name" | "age">
// ================================================
// interface Client {
//     name: string;
//     dateOfBirth: Date;
//     active: boolean;
// }
//
// type ClientSummary = Pick<Client, "name" | "active">;
//
// const clients: ClientSummary = {
//     name: '<PERSON>',
//     active: true,
// };
// ================================================

// const user = {
//   name: "jon",
//   age: 22,
//   address: {
//     linre1: "2123213"
//   }
// } as const;  // The ...as const bit make it immutable!!

//  For types

/////////////////////////
// const user = {
//   name: "bob",
//   age: 33
// };
//
// type User = typeof user;

// export const PaccsAgeGroups = [
//   "Adult",
//   "Child",
//   "Toddler",
//   "Infant",
//   "Neonate"
// ] as const;
// export type PaccsAgeGroup = typeof PaccsAgeGroups[number];
// ====================================================
// export const payGrades = {
//     low: "1",
//     average: "2",
//     high: "3"
// } as const;
//
// type t = typeof payGrades;
// type payGradeType = keyof t; // 'low' | 'average' | 'high'
// type payValueType =  t[keyof t]; // '1' | '2' | '3'
//
// const hisPay: payValueType = '3'; //okay
// const myPay:  payValueType = '4'; // error
// ====================================================
//  Function taking partial object keys
// submitSimpleFields: (
//   payload: Partial<Record<keyof ICallDetail, unknown>>
// ) => Promise<ICleoServerResponse>;
// ====================================================
// Opaque
// type OpaqueType<K, T> = K & { _brand: T }
// type Customer = OpaqueType<Person, "Customer">
// type VIPCustomer = OpaqueType<Person, "VIP">
//
// function getVIPName(vip: VIPCustomer) {
//     return vip.name;
// }
//
// const cust = {name: 'John'} as Customer;
// const vip = {name: 'Mark'} as VIPCustomer;
//
// console.log('vip name:', getVIPName(vip)); //vip name: Mark
// // Error: Argument of type 'Customer' is not assignable to parameter of type 'VIPCustomer'.
// console.log('vip name:', getVIPName(cust));

// function update<K extends keyof Person>(key: K) {
//     person[key] = anotherPerson[key]; // 👍
// }

// const props: (keyof ITicketFormData)[] = ["name", "telNo", "address", "email"];

//  export type LaunchRouteKey = "one" | "two" | "three"

// subLinksV1: Partial<Record<LaunchRouteKey, ILinkV2>>

/**
 * Get a subset of an objects keys.
 */
// export type PriceValidationProps = keyof Pick<IPrice, "salePrice" | "price" | "saleEndDate">;

// type Role = "admin" | "user" | "guest";
// type PermissionLevel = "read" | "write" | "execute";
// type RolePermission = `${Role}-${PermissionLevel}`;

// let rolePermission: RolePermission = "admin-read"; // Valid
// let invalidRolePermission: RolePermission = "manager-read";
// Error: Type '"manager-read"' is not assignable to type 'RolePermission'.
