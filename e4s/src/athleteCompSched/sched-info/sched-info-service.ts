import {ISchedInfo, ISchedInfoDetail} from "../athletecompsched-models"

export class SchedInfoService {
    public factorySchedInfo(): ISchedInfo {
        return {
            title: "",
            shortDescription: "",
            autoExpand: true,
            showLinks: false,
            schedInfoDetails: [],
            rowOptions: {
                autoExpandHelpText: true,
                showPB: true,
                showPrice: true,
                showEntryCount: true
            }
        };
    }

    public factorySchedInfoDetail(): ISchedInfoDetail {
        return {
            title: "",
            body: ""
        };
    }

    public hasSchedInfoDetails(schedInfo: ISchedInfo): boolean {
        if (!schedInfo.schedInfoDetails) {
            return false;
        }
        return  schedInfo.schedInfoDetails.length > 0;
    }

    public canShowLinks(schedInfo: ISchedInfo): boolean {
        if (!this.hasSchedInfoDetails(schedInfo)) {
            return false;
        }

        if (schedInfo.showLinks === false) {
            return false;
        }
        if (schedInfo.showLinks ) {
            return schedInfo.showLinks;
        }
        return this.hasSchedInfoDetails(schedInfo);
    }

    public shouldAutoExpand(schedInfo: ISchedInfo): boolean {
        return this.hasSchedInfoDetails(schedInfo);
        // if (!this.hasSchedInfoDetails(schedInfo)) {
        //     return false;
        // }
        // if (schedInfo.showLinks === false) {
        //     //  showLinks ====false  but we have some messages to show, so got to auto expand to see them!
        //     return true;
        // }
        // return (schedInfo.autoExpand || schedInfo.autoExpand === 1 ? true : false );
    }
}
