<template>
  <div class="e4s-flex-column">
    <div class="e4s-flex-row">
      <slot name="title">
        <span v-text="schedInfo.title"></span>
      </slot>
    </div>

    <slot name="short-description">
      <p
        v-if="schedInfo.shortDescription.length > 0"
        v-text="schedInfo.shortDescription"
      ></p>
    </slot>

    <slot name="detail">
      <div
        class="e4s-flex-column e4s-gap--standard"
        v-show="showDetailsSection"
      >
        <SchedInfoDetailV2
          v-for="(schedInfoDetail, index) in schedInfo.schedInfoDetails"
          :key="index"
          v-bind:schedInfoDetail="schedInfoDetail"
        />
      </div>
    </slot>

    <slot name="extra"></slot>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import SchedInfoDetailV2 from "./sched-info-detail-v2.vue";
import {
  IRowOptions,
  ISchedInfo,
  ISchedInfoDetail,
} from "../../athletecompsched-models";
import { SchedInfoService } from "../sched-info-service";
import CardGenericV2 from "../../../common/ui/layoutV2/card-generic-v2.vue";

@Component({
  name: "sched-info-v2",
  components: {
    CardGenericV2,
    SchedInfoDetailV2,
  },
})
export default class SchedInfo extends Vue {
  @Prop({
    default: () => {
      return {
        title: "",
        shortDescription: "",
        autoExpand: false,
        showLinks: false,
        schedInfoDetails: [] as ISchedInfoDetail[],
        rowOptions: {} as IRowOptions,
      } as ISchedInfo;
    },
  })
  public schedInfo: ISchedInfo;

  public schedInfoService: SchedInfoService = new SchedInfoService();

  public displayMore: boolean = false;

  public mounted() {
    this.displayMore = this.schedInfoService.shouldAutoExpand(this.schedInfo);
  }

  @Watch("schedInfo")
  public onSchedInfoChanged(newValue: ISchedInfo) {
    this.displayMore = this.schedInfoService.shouldAutoExpand(newValue);
  }

  public get hasSchedInfoDetails(): boolean {
    return this.schedInfoService.hasSchedInfoDetails(this.schedInfo);
  }

  public get showLinks() {
    return this.schedInfoService.canShowLinks(this.schedInfo);
  }

  public handleDisplayMore(showMore: boolean) {
    this.displayMore = showMore;
  }

  public get shouldAutoExpand() {
    return this.schedInfoService.shouldAutoExpand(this.schedInfo);
  }

  public get showDetailsSection() {
    return this.displayMore;
  }
}
</script>
