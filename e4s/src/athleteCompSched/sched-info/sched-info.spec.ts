import {SchedInfoService} from "./sched-info-service";
import {ISchedInfo, ISchedInfoDetail} from "../athletecompsched-models";


describe("sched-info", () => {

    const schedInfoService = new SchedInfoService();

    test("shouldAutoExpand simple", () => {

        const schedInfo: ISchedInfo = {
            title: "Schedule",
            shortDescription: "Th",
            autoExpand: 0
        } as ISchedInfo;

        expect(schedInfoService.hasSchedInfoDetails(schedInfo)).toBe(false);
        expect(schedInfoService.shouldAutoExpand(schedInfo)).toBe(false);
        expect(schedInfoService.canShowLinks(schedInfo)).toBe(false);
    });

    test("shouldAutoExpand simple", () => {

        const schedInfo: ISchedInfo = {
            title: "Schedule information",
            shortDescription: "This schedule is bespoke to H,. Please contact the Competition organiser if there is an issue with the available events.",
            autoExpand: 0,
            schedInfoDetails: [] as ISchedInfoDetail[]
        } as ISchedInfo;

        expect(schedInfoService.hasSchedInfoDetails(schedInfo)).toBe(false);
        expect(schedInfoService.shouldAutoExpand(schedInfo)).toBe(false);
        expect(schedInfoService.canShowLinks(schedInfo)).toBe(false);
    });

    test("shouldAutoExpand", () => {

        const schedInfo: ISchedInfo = {
            title: "Schedule information",
            shortDescription: "This schedule is bespoke to H,. Please contact the Competition organiser if there is an issue with the available events.",
            autoExpand: 0,
            rowOptions: {
                showPrice: false,
                autoExpandHelpText: false,
                showPB: true,
                showEntryCount: true
            },
            schedInfoDetails: [
                {
                    title: "Standard prices unless specified:",
                    body: "Relay events £15.50 (inc £0.50 fee)"
                },
                {
                    title: "Price Discounts:",
                    body: "After 1 entry,  the entry price will be reduced by £4"
                }
            ]
        } as ISchedInfo;

        expect(schedInfoService.shouldAutoExpand(schedInfo)).toBe(true);
        expect(schedInfoService.canShowLinks(schedInfo)).toBe(true);
    });

    test("show links", () => {

        const schedInfo: ISchedInfo = {
            title: "Schedule information",
            shortDescription: "This schedule is bespoke to H,. Please contact the Competition organiser if there is an issue with the available events.",
            autoExpand: 0,
            showLinks: false,
            schedInfoDetails: [
                {
                    title: "Standard prices unless specified:",
                    body: "Relay events £15.50 (inc £0.50 fee)"
                }
            ]
        } as ISchedInfo;

        expect(schedInfoService.canShowLinks(schedInfo)).toBe(false);
    });

});



