<template>
    <E4sModal>
        <div slot="body">
            <div class="row">
                <div class="col s12 m12 l12">
                    <span v-text="getToggleSubmitDialogMessage"></span>
                </div>
                <div class="col s12 m12 l12">
                    <label class="active" for="reason">
                        Reason
                        <span>Required</span>
                    </label>
                    <input
                            id="reason"
                            name="reason"
                            v-model="reason"
                            placeholder="">
                </div>
            </div>
        </div>

        <div slot="buttons">
            <button class="btn btn-flat red-text e4s-bold"
                    :disabled="isLoading"
                    v-on:click.stop="cancelDialog">
                <span>Cancel</span>
            </button>

            <button v-if="!pleaseConfirm"
                    class="btn btn-flat green-text e4s-bold"
                    :disabled="isLoading"
                    v-on:click.stop="pleaseConfirm = true">
                <span>Cancel Entered Event</span>
            </button>

            <button v-if="pleaseConfirm"
                    class="btn btn-flat green-text e4s-bold"
                    :disabled="isLoading"
                    v-on:click.stop="e4sCancelEvent">
                <span>Confirm</span>
            </button>
        </div>

    </E4sModal>
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import E4sModal from "../../common/ui/e4s-modal.vue";
    import {Prop} from "vue-property-decorator"
    import {IAthleteCompSchedRuleEvent} from "../athletecompsched-models"
    import {AthleteCompSchedService} from "../athletecompsched-service"
    import { AthleteCompSchedData } from "../athletecompsched-data";
    import { messageDispatchHelper } from "../../user-message/user-message-store";
    import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";

    const athleteCompSchedService: AthleteCompSchedService = new AthleteCompSchedService();

    @Component({
        name: "e4s-event-cancel",
        components: {
            E4sModal
        }
    })
    export default class E4sEventCancel extends Vue {

        @Prop({
            default: () => {
                return athleteCompSchedService.factoryAthleteCompSchedRuleEvent();
            }
        }) public readonly compEventProp: IAthleteCompSchedRuleEvent;

        public isLoading: boolean = false;
        public reason: string = "";
        public pleaseConfirm: boolean = false;

        public get getToggleSubmitDialogMessage(): string {
            return "Please enter a reason why toggling paid status of: " + this.compEventProp.eventGroup;
        }
        public e4sCancelEvent() {
            this.isLoading = true;
            new AthleteCompSchedData().cancelEvent(this.compEventProp.entryId, this.reason)
                .then((response) => {
                    if (response.errNo > 0) {
                        messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return;
                    }

                    this.$emit("onSubmitted", this.compEventProp);

                }).catch((error) => {
                    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                    return {};
                })
                .finally(() => {
                    this.isLoading = false;
                })
        }

        public cancelDialog() {
            this.$emit("cancelDialog");
        }

    }
</script>
