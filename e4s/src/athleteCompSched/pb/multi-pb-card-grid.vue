<template>
    <div class="multi-pb-card-grid">
        <div class="multi-pb-card-grid-title">
            Combined Events PBs
            <a class="multi-pb-card-grid-close" v-on:click="close">Close</a>
        </div>
        <div
                v-for="event in athleteMultiPb"
                :key="event.id"
                :id="event.ceid">

            <multi-pb-card
                    class="row multi-pb-card"
                    :event="event"
                    :athlete="athlete"></multi-pb-card>
            <div class="e4s-card-standard-sep"></div>
        </div>
        <div class="multi-pb-card-grid-message">
            Message: <span v-text="submittedPbMessage.message"></span>
            <span class="event-card-loading" v-if="isSubmittingPb">Processing...</span>
            <span class="event-card-loading" v-if="athleteMultiPbLoading">Loading...</span>
        </div>
    </div>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop} from "vue-property-decorator";
    import {mapState} from "vuex";
    import {ENTRY_STORE_CONST, IEntryStoreState} from "../../entry/entry-store";
    import {IAthleteCompSchedRuleEvent} from "../athletecompsched-models";
    import {ATH_COMP_SCHED_STORE_CONST, IAthCompSchedStoreState} from "../store/athletecompsched-store";
    import MultiPbCard from "./multi-pb-card.vue";
    // import {IUserMessage} from "../../user-message/user-message-models";

    @Component({
        name: "multi-pb-card-grid",
        components: {
            "multi-pb-card": MultiPbCard
        },
        computed: {
            ...mapState(
                ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME,
                {
                    athlete: (state: IEntryStoreState) => state.entryForm.selectedAthlete,
                    athleteMultiPb: (state: IEntryStoreState) => state.entryForm.athleteMultiPb,
                    athleteMultiPbLoading: (state: IEntryStoreState) => state.entryForm.athleteMultiPbLoading
                }
            ),
            ...mapState(
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME,
                {
                    eventsSelected: (state: IAthCompSchedStoreState) => state.eventsSelected,
                    isSubmittingPb: (state: IAthCompSchedStoreState) => state.isSubmittingPb,
                    submittedPbMessage: (state: IAthCompSchedStoreState) => state.submittedPbMessage
                }
            )
        }
    })
    export default class MultiPbCardGrid extends Vue {

        // @Prop() public athlete: IAthlete;
        @Prop() public event: IAthleteCompSchedRuleEvent;
        // @Prop() public athleteMultiPb: IPbMultiItem[];

        // public submittedPbMessage: IUserMessage;

        public mounted() {
            this.$store.dispatch(
                ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME + "/" +
                ENTRY_STORE_CONST.ENTRY_STORE_ACTIONS_GET_ATHLETE_MULTI_PB,
                {
                    event: this.event
                }
            );
        }

        public close() {
            this.$emit("closeMultiPb");
        }

    }
</script>
