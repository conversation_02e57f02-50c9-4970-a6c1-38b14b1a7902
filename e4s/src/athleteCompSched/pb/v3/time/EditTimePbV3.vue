<template>
  <div class="e4s-flex-column">
    <!--    {{ pbEditTimeV3.state.fieldPatternToShowMap }}-->
    <div class="e4s-flex-row e4s-flex-row--end">
      <div
        v-if="
          pbEditTimeV3.state.fieldPatternToShowMap.h ||
          pbEditTimeV3.state.convertedSecondsToHMS.hours > 0
        "
        class="edit-pb-v3--cell edit-pb-v3--header"
      >
        hrs
      </div>
      <div
        v-if="
          pbEditTimeV3.state.fieldPatternToShowMap.m ||
          pbEditTimeV3.state.convertedSecondsToHMS.minutes > 0
        "
        class="edit-pb-v3--cell edit-pb-v3--header"
      >
        mins
      </div>
      <div
        v-if="
          pbEditTimeV3.state.fieldPatternToShowMap.s ||
          pbEditTimeV3.state.convertedSecondsToHMS.seconds > 0
        "
        class="edit-pb-v3--cell edit-pb-v3--header"
      >
        secs
      </div>
      <div
        v-if="
          pbEditTimeV3.state.fieldPatternToShowMap.S ||
          pbEditTimeV3.state.convertedSecondsToHMS.hundredths > 0
        "
        class="edit-pb-v3--cell edit-pb-v3--header"
        style="font-size: 16px !important; margin-top: 2px !important"
      >
        1/100
      </div>
    </div>

    <div class="e4s-flex-row e4s-flex-row--end">
      <FieldNumberV2
        style="width: 60px"
        v-if="
          pbEditTimeV3.state.fieldPatternToShowMap.h ||
          pbEditTimeV3.state.convertedSecondsToHMS.hours > 0
        "
        :value="pbEditTimeV3.state.convertedSecondsToHMS.hours"
        @input="pbEditTimeV3.onUserChangeHours($event)"
        @keyUp="pbEditTimeV3.onUserChangeHours($event)"
        class="edit-pb-v3--cell edit-pb-v3--input e4s-square--right"
        title="Hours"
        :min="0"
        :max="24"
      />
      <FieldNumberV2
        style="width: 60px"
        v-if="
          pbEditTimeV3.state.fieldPatternToShowMap.m ||
          pbEditTimeV3.state.convertedSecondsToHMS.minutes > 0
        "
        :value="pbEditTimeV3.state.convertedSecondsToHMS.minutes"
        @input="pbEditTimeV3.onUserChangeMinutes($event)"
        @keyUp="pbEditTimeV3.onUserChangeMinutes($event)"
        class="edit-pb-v3--cell edit-pb-v3--input e4s-square--right"
        :class="
          pbEditTimeV3.state.fieldPatternToShowMap.h ? 'e4s-square--left' : ''
        "
        title="Minutes"
        :min="0"
        :max="59"
      />
      <FieldNumberV2
        style="width: 60px"
        v-if="
          pbEditTimeV3.state.fieldPatternToShowMap.s ||
          pbEditTimeV3.state.convertedSecondsToHMS.seconds > 0
        "
        :value="pbEditTimeV3.state.convertedSecondsToHMS.seconds"
        @input="pbEditTimeV3.onUserChangeSeconds($event)"
        @keyUp="pbEditTimeV3.onUserChangeSeconds($event)"
        class="edit-pb-v3--cell edit-pb-v3--input"
        :class="[
          pbEditTimeV3.state.fieldPatternToShowMap.m ? 'e4s-square--left' : '',
          pbEditTimeV3.state.fieldPatternToShowMap.S ? 'e4s-square--right' : '',
        ]"
        title="Seconds"
        :min="0"
        :max="59"
      />
      <FieldNumberV2
        style="width: 60px"
        v-if="
          pbEditTimeV3.state.fieldPatternToShowMap.S ||
          pbEditTimeV3.state.convertedSecondsToHMS.hundredths > 0
        "
        :value="pbEditTimeV3.state.convertedSecondsToHMS.hundredths"
        @input="pbEditTimeV3.onUserChangeHundredths($event)"
        @keyUp="pbEditTimeV3.onUserChangeHundredths($event)"
        class="edit-pb-v3--cell edit-pb-v3--input e4s-square--left"
        title="Hundredths"
        :min="0"
        :max="999"
      />
    </div>

    <div class="e4s-flex-column e4s-info-text--warn">
      <div
        v-for="(err, prop) in pbEditTimeV3.state.errors"
        :key="prop"
        v-text="err"
      ></div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { usePbEditTimeV3 } from "./usePbEditTimeV3";
import FieldNumberV2 from "../../../../common/ui/layoutV2/fields/field-number-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { IEditPbV3Props } from "../../pb-models";

export default defineComponent({
  name: "EditTimePbV3",
  components: { ButtonGenericV2, FieldNumberV2 },
  props: {
    editTimePbV3Props: {
      type: Object as PropType<IEditPbV3Props>,
      required: true,
    },
  },
  setup(props: { editTimePbV3Props: IEditPbV3Props }, context: SetupContext) {
    const pbEditTimeV3 = usePbEditTimeV3(context);
    pbEditTimeV3.controller.init(props.editTimePbV3Props);

    watch(
      () => props.editTimePbV3Props,
      (newValue: IEditPbV3Props) => {
        pbEditTimeV3.controller.init(newValue);
      }
    );

    const canShowMinutesField = computed<boolean>(() => {
      return !!(
        pbEditTimeV3.state.fieldPatternToShowMap.m ||
        pbEditTimeV3.state.convertedSecondsToHMS.minutes > 0
      );
    });

    const canShowSecondsField = computed<boolean>(() => {
      return !!(
        pbEditTimeV3.state.fieldPatternToShowMap.s ||
        pbEditTimeV3.state.convertedSecondsToHMS.seconds > 0
      );
    });

    const canShowHundredthsField = computed<boolean>(() => {
      return !!(
        pbEditTimeV3.state.fieldPatternToShowMap.S ||
        pbEditTimeV3.state.convertedSecondsToHMS.hundredths > 0
      );
    });

    function submit() {
      context.emit("input", pbEditTimeV3.controller.getUserOutput());
    }

    return {
      pbEditTimeV3,
      canShowMinutesField,
      canShowSecondsField,
      canShowHundredthsField,
      submit,
    };
  },
});
</script>
