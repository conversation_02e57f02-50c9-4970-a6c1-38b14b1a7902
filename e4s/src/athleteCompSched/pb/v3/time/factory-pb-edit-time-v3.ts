import * as CommonServiceUtils from "../../../../common/common-service-utils";
import { IConvertedSecondsToHMS } from "../../../../common/common-service-utils";
import * as PbServiceV3 from "../pb-service-v3";
import { IPbEditBaseV3State } from "../edit-pb-v3-models";
import { IEditPbV3Props, IPbEditV3Output } from "../../pb-models";
import { IEventUom } from "../../../athletecompsched-models";
import { PBErrorMessageType } from "../pb-service-v3";

export type TimeFieldType = "h" | "m" | "s" | "S";

export interface IPbEditTimeV3State extends IPbEditBaseV3State {
  convertedSecondsToHMS: IConvertedSecondsToHMS;
  outputSeconds: number;
  howManyFieldsToShow: number;
  fieldPatternToUse: string;
  fieldPatternToShowMap: Partial<Record<TimeFieldType, TimeFieldType>>;
}

export function factoryPbEditTimeV3() {
  const state: IPbEditTimeV3State = {
    ...PbServiceV3.factoryIPbBaseV3State(),
    convertedSecondsToHMS: {
      hours: 0,
      minutes: 0,
      seconds: 0,
      hundredths: 0,
    },
    outputSeconds: 0,
    howManyFieldsToShow: 0,
    fieldPatternToUse: "",
    fieldPatternToShowMap: {},
  };

  function init(editTimePbV3Props: IEditPbV3Props) {
    // if (!editTimePbV3Props.athletePb) {
    //  throw new Error("editTimePbV3Props.athletePb is required.");
    // return;
    // }
    // state.athletePb = CommonServiceUtils.simpleClone(
    //   editTimePbV3Props.athletePb
    // );

    state.editTimePbV3Props = CommonServiceUtils.simpleClone(editTimePbV3Props);

    const inputSeconds = PbServiceV3.getDefaultValue(
      state.editTimePbV3Props.performance
    );
    state.convertedSecondsToHMS =
      CommonServiceUtils.convertSecondsToHMS(inputSeconds);

    state.howManyFieldsToShow = howManyFieldsToshow(
      editTimePbV3Props.performance.uom as any as IEventUom[]
    );

    state.fieldPatternToUse = fieldPatternToUse(
      editTimePbV3Props.performance.uom as any as IEventUom[]
    );

    state.fieldPatternToShowMap = PbServiceV3.getFieldPatternMap(
      state.fieldPatternToUse
    );
  }

  function howManyFieldsToshow(uoms: IEventUom[]): number {
    return uoms.reduce<number>((acc, curr) => {
      const pattern = curr.pattern;
      const patternSize = PbServiceV3.getPatternArray(pattern).length;
      if (patternSize > acc) {
        acc = patternSize;
      }
      return acc;
    }, 2);
  }

  function fieldPatternToUse(options: IEventUom[]): string {
    return options.reduce<string>((acc, curr) => {
      const pattern = curr.pattern;
      if (pattern.length > acc.length) {
        acc = pattern;
      }
      return acc;
    }, "");
  }

  // function getFieldPatternMap(
  //   patternToUse: string
  // ): Partial<Record<TimeFieldType, TimeFieldType>> {
  //   return PbServiceV3.getFieldPatternMap(patternToUse);

  // return getPatternArray(patternToUse).reduce<
  //   Partial<Record<TimeFieldType, TimeFieldType>>
  // >((acc, cur) => {
  //   const firstChar: TimeFieldType = cur.charAt(0) as any as TimeFieldType;
  //   acc[firstChar] = firstChar;
  //   return acc;
  // }, {});
  // }

  /**
   * Given a string e.g. "m:ss.SS" split this by non alpha characters
   */
  // function getPatternArray(pattern: string): string[] {
  //   return pattern.split(/[^a-zA-Z]/);
  // }

  function convertUserInputToSeconds(): void {
    state.outputSeconds = calcUserInputToSeconds(state.convertedSecondsToHMS);
  }

  function calcUserInputToSeconds(
    convertedSecondsToHMS: IConvertedSecondsToHMS
  ): number {
    return CommonServiceUtils.convertedSecondsToHMSToSeconds(
      convertedSecondsToHMS
    );
  }

  function onUserChange(
    propName: keyof IConvertedSecondsToHMS,
    newValue: number
  ): void {
    state.convertedSecondsToHMS[propName] = newValue;
    validate();
    convertUserInputToSeconds();
    state.outputText = getUserOutputText(state.convertedSecondsToHMS);
  }

  function getErrorMessages(): string[] {
    return Object.values(state.errors) as string[];
  }

  function getUserOutputText(
    convertedSecondsToHMS: IConvertedSecondsToHMS
  ): string {
    const keys: (keyof IConvertedSecondsToHMS)[] = [
      "hours",
      "minutes",
      "seconds",
      "hundredths",
    ];
    const res: string = keys.reduce<string>((accum, key) => {
      const value = convertedSecondsToHMS[key];
      if (accum.length > 0) {
        accum += key === "hundredths" ? "." : ":";
      }
      if (key === "hours" && value === 0) {
        return accum;
      }
      if (
        key === "minutes" &&
        value === 0 &&
        convertedSecondsToHMS.hours === 0
      ) {
        //  E.g. the 100m, if hours and mins are 0, don't show the hours, minutes.
        return accum;
      }
      accum += value.toString().padStart(2, "0");
      return accum;
    }, "");

    return res;
  }

  function getUserOutput(): IPbEditV3Output {
    return {
      outputPb: state.outputSeconds,
      outputText: state.outputText,
      errors: state.errors,
    };
  }

  function validate(): boolean {
    let errors: Partial<Record<PBErrorMessageType, string>> = {};
    const inputResultAsSeconds = calcUserInputToSeconds(
      state.convertedSecondsToHMS
    );

    //  Zero = valid, the athlete does not have a PB.
    if (inputResultAsSeconds === 0) {
      state.errors = {};
      state.isValid = true;
      return true;
    }

    errors = PbServiceV3.genericValidationEstimatedPerformance(
      inputResultAsSeconds,
      state.editTimePbV3Props.athleteCompSchedMini,
      state.editTimePbV3Props.performance
    );

    if (
      state.convertedSecondsToHMS.hours > 24 ||
      state.convertedSecondsToHMS.hours < 0
    ) {
      errors.RANGE_HRS = "Hours must be between 0 and 24.";
    }

    if (
      state.convertedSecondsToHMS.minutes > 59 ||
      state.convertedSecondsToHMS.minutes < 0
    ) {
      errors.RANGE_MINS = "Minutes must be between 0 and 59.";
    }

    if (
      state.convertedSecondsToHMS.seconds > 59 ||
      state.convertedSecondsToHMS.seconds < 0
    ) {
      errors.RANGE_SECS = "Seconds must be between 0 and 59.";
    }

    if (
      state.convertedSecondsToHMS.hundredths > 99 ||
      state.convertedSecondsToHMS.hundredths < 0
    ) {
      errors.RANGE_HUNDS = "Hundredths must be between 0 and 99.";
    }

    if (errors.MIN_MAX) {
      // For time PBs, the error message is different.
      const min = state.editTimePbV3Props.performance.limits.min;
      const max = state.editTimePbV3Props.performance.limits.max;
      const minAsHMS = CommonServiceUtils.convertSecondsToHMS(Number(min));
      const minOutputText = getUserOutputText(minAsHMS);
      const maxAsHMS = CommonServiceUtils.convertSecondsToHMS(Number(max));
      const maxOutputText = getUserOutputText(maxAsHMS);
      errors.MIN_MAX =
        "EP must be between " +
        minOutputText +
        " and " +
        maxOutputText +
        " (" +
        min +
        "secs and " +
        max +
        "secs)" +
        ".";
    }

    state.errors = errors;
    state.isValid = Object.keys(state.errors).length <= 0;
    return state.isValid;
  }

  return {
    state,
    init,
    validate,
    convertUserInputToSeconds,
    onUserChange,
    // onUserChangeHours,
    // onUserChangeMinutes,
    // onUserChangeSeconds,
    // onUserChangeHundredths,
    getUserOutputText,
    getUserOutput,
    getErrorMessages,
  };
}
