import { factoryEditLengthPbV3 } from "./factory-edit-length-pb-v3";
import { simpleClone } from "../../../../common/common-service-utils";
import { IEditPbV3Props } from "../../pb-models";

const editTimePbV3Props: IEditPbV3Props = {
  athleteMini: {
    id: 0,
    firstName: "",
    surName: "",
    URN: "",
    aocode: "",
  },
  athleteCompSchedMini: {
    split: 0,
  },
  performance: {
    perf: 4.59,
    perfText: "!#!4.59",
    pb: 4.59,
    pbText: "!#!4.59",
    pbAchieved: "2021-07-17",
    sb: 4.61,
    sbText: "!#!4.61",
    sbAchieved: null,
    ageGroup: "",
    curAgeGroup: "U20",
    eventName: "Long Jump",
    uom: [
      {
        pattern: 0.99,
        text: "metres",
        short: "m",
      },
    ],
    limits: {
      min: 1,
      max: 10,
    },
  },
} as any as IEditPbV3Props;

describe("edit-length-pb-v3", () => {
  test("1.21", () => {
    const editLengthPbV3 = factoryEditLengthPbV3();
    editLengthPbV3.init(simpleClone(editTimePbV3Props));

    let res;
    expect(editLengthPbV3.state.convertedLengthToMCM.metres).toBe(4);
    expect(editLengthPbV3.state.convertedLengthToMCM.centimetres).toBe(59);

    res = editLengthPbV3.getUserOutputText(
      editLengthPbV3.state.convertedLengthToMCM
    );
    expect(res).toBe("4.59");

    res = editLengthPbV3.convertUserInputToLength(
      editLengthPbV3.state.convertedLengthToMCM
    );
    expect(res).toBe(4.59);
  });

  test("1.09", () => {
    const editLengthPbV3 = factoryEditLengthPbV3();
    const data = simpleClone(editTimePbV3Props);
    data.performance.perf = 1.09;
    data.performance.perfText = "1.09";
    data.performance.pb = 1.09;
    data.performance.pbText = "1.09";

    data.performance.limits.min = 0.5;
    data.performance.limits.max = 2.5;

    editLengthPbV3.init(data);

    // let res;
    expect(editLengthPbV3.state.convertedLengthToMCM.metres).toBe(1);
    expect(editLengthPbV3.state.convertedLengthToMCM.centimetres).toBe(9);

    let res;
    res = editLengthPbV3.getUserOutputText(
      editLengthPbV3.state.convertedLengthToMCM
    );
    expect(res).toBe("1.09");

    res = editLengthPbV3.convertUserInputToLength(
      editLengthPbV3.state.convertedLengthToMCM
    );
    expect(res).toBe(1.09);

    editLengthPbV3.onUserChange("centimetres", 32);
    expect(editLengthPbV3.state.convertedLengthToMCM.metres).toBe(1);
    expect(editLengthPbV3.state.convertedLengthToMCM.centimetres).toBe(32);
    expect(editLengthPbV3.state.outputText).toBe("1.32");
    expect(editLengthPbV3.state.outputLength).toBe(1.32);
    expect(editLengthPbV3.state.isValid).toBe(true);

    editLengthPbV3.onUserChange("metres", 3);
    expect(editLengthPbV3.state.convertedLengthToMCM.metres).toBe(3);
    expect(editLengthPbV3.state.convertedLengthToMCM.centimetres).toBe(32);
    expect(editLengthPbV3.state.outputText).toBe("3.32");
    expect(editLengthPbV3.state.outputLength).toBe(3.32);
    expect(editLengthPbV3.state.isValid).toBe(false);
    expect(editLengthPbV3.state.errors.MIN_MAX).toBe(
      "PB must be between 0.5 and 2.5."
    );
  });
});
