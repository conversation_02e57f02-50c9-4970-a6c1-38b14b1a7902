import { GENDER } from "../../../common/common-models";
import { IAgeGroupBase } from "../../../agegroup/agegroup-models";
import { HAS_TEAMS_TYPES } from "../event-teams-models";

export interface IEventTeamHeaderFilter {
  eventName: string;
  gender: GENDER | "ALL";
  ageGroup: IAgeGroupBase;
  hasTeams: HAS_TEAMS_TYPES;
}

export interface IEventTeamHeaderFilterV2 extends IEventTeamHeaderFilter {
  freeText: string;
}
