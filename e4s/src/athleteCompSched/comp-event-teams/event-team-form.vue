<template>
    <div>
        <div class="row" v-for="(field, index) in formInfo">
            <div class="col s12 m12 l12">
                <label class="e4s-label">{{field.label}}</label>
                <input @change="handleChange"
                       class="autocomplete e4s-input-mat"
                       :disabled="!editMode"
                       v-model="formInfo[index].value"/>
            </div>
        </div>
    </div>
</template>


<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import * as R from "ramda";
    import {IEventTeamFormField} from "./event-teams-models";

    @Component({
        name: "event-team-form"
    })
    export default class EventTeamForm extends Vue {
        @Prop({default: true}) public editMode: boolean;
        @Prop(
            {
                default: () => {
                    return [
                        {
                            label: "Club",
                            value: "",
                            req: true
                        },
                        {
                            label: "Contact",
                            value: "",
                            req: true
                        },
                        {
                            label: "Phone",
                            value: "",
                            req: true
                        },
                        {
                            label: "Email",
                            value: "",
                            req: true
                        }
                    ];
                }
            }
        ) public formInfoProp: IEventTeamFormField[];
        public formInfo: IEventTeamFormField[] = [];


        public created() {
            this.formInfo = R.clone(this.formInfoProp);
        }

        @Watch("formInfoProp")
        public onFormInfoPropChanged(newValue: IEventTeamFormField[]) {
            this.formInfo = R.clone(newValue);
        }

        public handleChange() {
            this.$emit("onChange", this.formInfo);
        }

    }
</script>
