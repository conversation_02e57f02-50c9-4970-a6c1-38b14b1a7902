import { IServerPagingResponseList } from "../../common/common-models";
import https from "../../common/https";
import { ResourceData } from "../../common/resource/resource-service";
import { IEventTeamHeader } from "./event-teams-models";
import { ShowFormType } from "../athletecompsched-models";
import { simpleClone } from "../../common/common-service-utils";

export class CompEventTeamHeaderData extends ResourceData<IEventTeamHeader> {
  constructor() {
    super("/v5/eventteams");
  }

  public getEventHeaders(
    compId: number,
    entityLevel: number,
    entityId: number
  ): Promise<IServerPagingResponseList<IEventTeamHeader>> {
    return https
      .get(
        "/v5/eventteams/" +
          compId +
          "?entityid=" +
          entityId +
          "&entitylevel=" +
          entityLevel
      )
      .then((response) => {
        const serverResponse =
          response as any as IServerPagingResponseList<IEventTeamHeader>;

        serverResponse.data = serverResponse.data.map((eventTeamHeader) => {
          return cleanUpEventHeader(eventTeamHeader);
        });

        return response as any as Promise<
          IServerPagingResponseList<IEventTeamHeader>
        >;
      });
  }
}

/**
 * Cleans up and normalizes the `eventTeam.formType` in the given event header object.
 * Ensures that `formType` has a default value if it is missing or invalid.
 *
 * @param {IEventTeamHeader} eventHeader - The event header object to be cleaned and normalized.
 * @return {IEventTeamHeader} The cleaned and normalized event header object.
 */
export function cleanUpEventHeader(
  eventHeader: IEventTeamHeader
): IEventTeamHeader {
  const eventHeaderInternal = simpleClone(eventHeader);
  const eventTeam = eventHeaderInternal.ceoptions.eventTeam;
  if (eventTeam) {
    //  this will actually return "falsy" if formType is not defined or "".
    if (!eventTeam.formType) {
      eventTeam.formType = "DEFAULT";
    }
    if (eventTeam.formType === ("" as any as ShowFormType)) {
      eventTeam.formType = "DEFAULT";
    }
  }

  return eventHeaderInternal;
}
