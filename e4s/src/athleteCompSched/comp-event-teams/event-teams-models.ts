import {IAgeGroup} from "../../agegroup/agegroup-models";
import {IAthleteSummary} from "../../athlete/athlete-models";
import {GENDER, IBase, IsoDateTime} from "../../common/common-models";
import {EVENT_TYPE, ICeoptions, IEoptions, IFormRow, IPrice} from "../athletecompsched-models";

export interface IEventTeamFormField {
    label: string;
    value: any;
    req?: boolean;
}

export interface ICompEventTeamBase extends IBase {
    ceid: number;
    teamName: string;
    userId: number;
    userName: string;
    paid: number;
    prodId: number;
    entityLevel: number;
    entityId: number;
    entity?: {
        id: number;
        name: string;
        entityLevel: number;
        entityId: number;
    }
}

export interface ICompEventTeam extends ICompEventTeamBase {
    athletes: IAthleteSummary[];
    formInfo: IEventTeamFormField[];
    formRows: IFormRow[];
}

export interface IEventTeamHeader {
    id: number;
    eventName: string;
    eventGroup: {
        id: number;
        name: string;
        eventNo: number;
        startDate: IsoDateTime;
    };
    eventid: number;
    // ceid: number;
    startdate: IsoDateTime;
    gender: GENDER;
    tf: EVENT_TYPE;                         //  Track or Field
    price: IPrice;
    IsOpen: number;                         //  If max athletes reached, or event organiser closes event
    maxAthletes: -1 | number;               //  -1 = schedule only, don't need to display.
    isDisabled: boolean;                    //  Does user have access to enter.
    helpText: string;                       //  Reason disabled, or any other help text.
    maxgroup: 0;                       //  E.g. "100mu15gfi" find all other events with this ID,
                                            //  sum all current athletes, reaches max?
    // ageInfo: IAgeInfo;                   //  TODO renamed to ageGroup Target age group, but see ceoptions for upscaling.
    ageGroup: IAgeGroup;
    ceoptions: ICeoptions;
    eoptions: IEoptions;

    // maxEventTeams: number;
    compEventTeams: ICompEventTeam[];
}

export interface IAthleteEventCompSummary {
    athId: number;
    athName: string;
    eventId: number;
    eventName: string;
    teamId: number;
    teamName: string;
}

export interface ITeamNamePatternModel {
    default: string;
    stringToReplace: string;                //  E.g. {{club}} {{club;<some text>}} {{club;blah blah}}
}

export interface IEventTeamUserSecuritySummary {
    userHasAccess: boolean;
    label: string;
}

export type HAS_TEAMS_TYPES = "ALL" | "WITH" | "WITHOUT";


