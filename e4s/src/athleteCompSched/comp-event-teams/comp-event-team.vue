<template>
    <div>

        <div class="row">
            <div class="col s12 m12 l12">
                displayWhichFormType{{displayWhichFormType}}
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">

<!--                <event-team-form :formInfoProp="compEventTeam.formInfo"-->
<!--                                 :editMode="hasBeenEdited || isNew || editMode"-->
<!--                                 v-on:onChange="eventFormChange">-->
<!--                </event-team-form>-->

                <school-team-form v-if="displayWhichFormType==='SCHOOL'"></school-team-form>

                <event-team-default
                        v-if="displayWhichFormType==='DEFAULT'"
                        v-bind:isLoading="isLoading"
                        v-bind:competition="competition"
                        v-bind:comp-event-team-prop="compEventTeamProp"
                        v-bind:event-team-header="eventTeamHeader"
                        v-bind:is-new="isNew"
                        v-on:submit="submitDefault">
                </event-team-default>

            </div>
        </div>

<!--        <div class="row">-->
<!--            <div class="col s6 m6 l6">-->
<!--                <a v-if="canDelete"-->
<!--                   class="comp-event-team-anchor-buttons"-->
<!--                   href="#"-->
<!--                   v-on:click.prevent="deleteCompEventTeamAsk">-->
<!--                    Delete-->
<!--                </a>-->
<!--                <a v-if="!isInEditMode && showForm"-->
<!--                   class="comp-event-team-anchor-buttons"-->
<!--                   href="#"-->
<!--                   v-on:click.prevent="setEditMode(true)">-->
<!--                    Edit-->
<!--                </a>-->
<!--                <a v-if="showCancel"-->
<!--                   class="comp-event-team-anchor-buttons"-->
<!--                   href="#"-->
<!--                   v-on:click.prevent="cancelCompEventTeam">-->
<!--                    Cancel-->
<!--                </a>-->
<!--            </div>-->
<!--            <div class="col s6 m6 l6">-->
<!--                <div class="right">-->
<!--                    <loading-spinner v-show="isLoading"></loading-spinner>-->
<!--                    <button v-if="showSubmit"-->
<!--                            :disabled="isSubmitDisabled"-->
<!--                            class="btn waves-effect waves green xxx-btn-small"-->
<!--                            v-on:click.prevent="submitCompEventTeam">-->
<!--                        Submit-->
<!--                    </button>-->
<!--                    <button v-if="showDeleteConfirmation"-->
<!--                            :disabled="isDeleteDisabled"-->
<!--                            class="btn waves-effect waves red xxx-btn-small"-->
<!--                            v-on:click.prevent="deleteCompEventTeam">-->
<!--                        Confirm Delete-->
<!--                    </button>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->

    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {mapState} from "vuex";
    import {AthleteService} from "../../athlete/athlete-service";
    import LoadingSpinner from "../../common/ui/loading-spinner.vue";
    import {ICompetitionInfo} from "../../competition/competition-models";
    import {IConfigApp, IEntity} from "../../config/config-app-models";
    import {CONFIG_STORE_CONST, IConfigStoreState} from "../../config/config-store";
    import {IUserMessage} from "../../user-message/user-message-models";
    import CompEventNamePicker from "./comp-event-name-picker.vue";
    import {EventTeamService} from "./event-team-service";
    import {ICompEventTeam, IEventTeamHeader} from "./event-teams-models";
    import {COMP_EVENT_TEAMS_STORE_CONST, ICompEventTeamsStoreState} from "./comp-event-store";
    import EventTeamForm from "./event-team-form.vue";
    import SchoolTeamForm from "./school/school-team-form.vue";
    import EventTeamDefault from "./event-team-default.vue";

    @Component({
        name: "comp-event-team",
        components: {
            "comp-event-name-picker": CompEventNamePicker,
            "loading-spinner": LoadingSpinner,
            "event-team-default": EventTeamDefault,
            "event-team-form": EventTeamForm,
            "school-team-form": SchoolTeamForm
        },
        computed: {
            ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
                configApp: (state: IConfigStoreState) => state.configApp,
                userEntity: (state: IConfigStoreState) => state.userEntity
            }),
            ...mapState(
                COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME,
                {
                    eventTeamHeaders: (state: ICompEventTeamsStoreState) => state.eventTeamHeaders
                }
            )
        }
    })
    export default class CompEventTeam extends Vue {
        public configApp: IConfigApp;
        public eventTeamHeaders: IEventTeamHeader[];
        public userEntity: IEntity;

        @Prop({default: false}) public isLoading: boolean;
        @Prop() public competition: ICompetitionInfo;
        @Prop() public compEventTeamProp: ICompEventTeam;
        @Prop() public eventTeamHeader: IEventTeamHeader;
        @Prop({default: true}) public isNew: boolean;

        public athleteService: AthleteService = new AthleteService();
        public eventTeamService: EventTeamService = new EventTeamService();
        public compEventTeam: ICompEventTeam = this.eventTeamService.factoryGetCompEventTeam(this.eventTeamHeader, this.userEntity);
        public prefix: string = Math.random().toString(36).substring(2);

        public userMessages: IUserMessage[] = [];
        public editingLine: number = -1;

        public editMode: boolean = false;
        public editTeamName: boolean = false;
        public hasBeenEdited: boolean = false;
        public showDeleteConfirmation: boolean = false;

        public mounted() {
            this.initForm();
        }

        public initForm() {
            this.compEventTeam = R.clone(this.compEventTeamProp);
            this.compEventTeam.ceid = this.eventTeamHeader.id;

            //  TODO remove
            if (!this.eventTeamService.showForm(this.eventTeamHeader)) {
                this.compEventTeam.athletes = this.eventTeamService.setUpAthleteArray(this.eventTeamHeader, this.compEventTeam);
            }
        }

        public reset() {
            this.editingLine = -1;
            this.hasBeenEdited = false;
            this.editTeamName = false;
            this.showDeleteConfirmation = false;
            this.initForm();
        }

        public setEditTeamName(canEdit: boolean) {
            this.editTeamName = canEdit;
            this.hasBeenEdited = true;
        }

        @Watch("compEventTeamProp")
        public onCompEventTeamProp(newValue: ICompEventTeam) {
            this.compEventTeam = newValue;
            this.reset();
        }

        public get displayWhichFormType() {
            return this.eventTeamService.displayWhichFormType(this.eventTeamHeader);
        }

        public submitDefault(compEventTeam: ICompEventTeam) {
            this.$emit("submitDefault", compEventTeam);
        }

        public get isDeleteDisabled() {
            return this.isLoading;
        }

        public deleteCompEventTeamAsk() {
            this.showDeleteConfirmation = true;
        }

        public deleteCompEventTeam() {
            this.$emit("deleteCompEventTeam", this.compEventTeam);
        }

    }

</script>

<style scoped>
    .e4s-name-edit {
        margin-left: 1rem !important;
    }
</style>
