<template>
  <div class="col s12 m12 l12">
    <div class="e4s-flex-row e4s-justify-flex-row-vert-center">
      <slot name="left-buttons">
        <div
          class="e4s-flex-row e4s-gap--small e4s-justify-flex-row-vert-center"
        >
          <slot name="left-buttons-other"></slot>
          <div
            class="e4s-flex-row e4s-gap--small e4s-justify-flex-row-vert-center"
          >
            <PrimaryLink
              v-if="canDelete"
              link-text="Delete"
              @onClick="deleteCompEventTeamAsk"
            />
            <div v-if="getCanEdit">
              |
              <PrimaryLink link-text="Edit" @onClick="setEditMode(true)" />
            </div>
            <!--          |-->
            <!--          <PrimaryLink-->
            <!--            v-if="getCanEdit"-->
            <!--            link-text="Edit"-->
            <!--            @onClick="setEditMode(true)"-->
            <!--          />-->
            <div v-if="showCancel || showDeleteConfirmation">
              |
              <PrimaryLink link-text="Cancel" @onClick="cancelCompEventTeam" />
            </div>

            <div v-if="getCanAddToUserCart">
              |
              <PrimaryLink
                v-if="getCanAddToUserCart"
                link-text="Assign to User"
                @onClick="addUserCart"
              />
            </div>
          </div>
        </div>
      </slot>

      <slot name="right-buttons">
        <div
          class="
            e4s-flex-row
            e4s-gap--small
            e4s-justify-flex-row-vert-center
            e4s-flex-row--end
          "
        >
          <slot name="confirm-delete">
            <ButtonGenericV2
              v-if="showDeleteConfirmation"
              class="e4s-button--auto"
              :disabled="isDeleteDisabled"
              button-type="destructive"
              @click="deleteCompEventTeam"
              text="Confirm Delete"
            />
          </slot>
          <slot name="submit">
            <ButtonGenericV2
              v-if="showSubmit && !showDeleteConfirmation"
              :disabled="isSubmitDisabled"
              @click="submitCompEventTeam"
              text="Submit"
            />
          </slot>
        </div>
      </slot>
    </div>
  </div>

  <!--  <div>-->
  <!--    <slot name="left-buttons">-->
  <!--      <div class="col s6 m6 l6">-->
  <!--        <slot name="left-buttons-other"></slot>-->
  <!--        <div-->
  <!--          class="e4s-flex-row e4s-gap&#45;&#45;small e4s-justify-flex-row-vert-center"-->
  <!--        >-->
  <!--          <PrimaryLink-->
  <!--            v-if="canDelete"-->
  <!--            link-text="Delete"-->
  <!--            @onClick="deleteCompEventTeamAsk"-->
  <!--          />-->
  <!--          <div v-if="getCanEdit">-->
  <!--            |-->
  <!--            <PrimaryLink link-text="Edit" @onClick="setEditMode(true)" />-->
  <!--          </div>-->
  <!--          <div v-if="showCancel || showDeleteConfirmation">-->
  <!--            |-->
  <!--            <PrimaryLink link-text="Cancel" @onClick="cancelCompEventTeam" />-->
  <!--          </div>-->

  <!--          <div v-if="getCanAddToUserCart">-->
  <!--            |-->
  <!--            <PrimaryLink-->
  <!--              v-if="getCanAddToUserCart"-->
  <!--              link-text="Assign to User"-->
  <!--              @onClick="addUserCart"-->
  <!--            />-->
  <!--          </div>-->
  <!--        </div>-->
  <!--      </div>-->
  <!--    </slot>-->

  <!--    <slot name="right-buttons">-->
  <!--      <div class="col s6 m6 l6">-->
  <!--        <div class="right">-->
  <!--          <loading-spinner v-show="isLoading"></loading-spinner>-->
  <!--          <slot name="confirm-delete">-->
  <!--            <ButtonGenericV2-->
  <!--              v-if="showDeleteConfirmation"-->
  <!--              class="e4s-button&#45;&#45;auto"-->
  <!--              :disabled="isDeleteDisabled"-->
  <!--              button-type="destructive"-->
  <!--              @click="deleteCompEventTeam"-->
  <!--              text="Confirm Delete"-->
  <!--            />-->
  <!--          </slot>-->
  <!--          <slot name="submit">-->
  <!--            <ButtonGenericV2-->
  <!--              v-if="showSubmit && !showDeleteConfirmation"-->
  <!--              :disabled="isSubmitDisabled"-->
  <!--              @click="submitCompEventTeam"-->
  <!--              text="Submit"-->
  <!--            />-->
  <!--          </slot>-->
  <!--        </div>-->
  <!--      </div>-->
  <!--    </slot>-->
  <!--  </div>-->
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { ICompEventTeamBase, IEventTeamHeader } from "./event-teams-models";
import { EventTeamService } from "./event-team-service";
import LoadingSpinner from "../../common/ui/loading-spinner.vue";
import { ICompetitionInfo } from "../../competition/competition-models";
import { CompetitionService } from "../../competition/competiton-service";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { ConfigService } from "../../config/config-service";
import { IConfigApp, IEntity, IUserInfo } from "../../config/config-app-models";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import PrimaryLink from "../../common/ui/layoutV2/href/PrimaryLink.vue";

const eventTeamService: EventTeamService = new EventTeamService();
const competitionService: CompetitionService = new CompetitionService();
const configService: ConfigService = new ConfigService();

@Component({
  name: "event-team-buttons",
  components: {
    PrimaryLink,
    ButtonGenericV2,
    "loading-spinner": LoadingSpinner,
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      userEntity: (state: IConfigStoreState) => state.userEntity,
      configApp: (state: IConfigStoreState) => state.configApp,
      userInfo: (state: IConfigStoreState) => state.configApp.userInfo,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class EventTeamButtons extends Vue {
  public readonly configApp: IConfigApp;
  public readonly userInfo: IUserInfo;
  public readonly userEntity: IEntity;

  @Prop({
    default: () => {
      return eventTeamService.factoryGetCompEventTeamBase(
        {
          id: 0,
        } as IEventTeamHeader,
        configService.factoryEntity()
      );
    },
  })
  public compEventTeam: ICompEventTeamBase;
  @Prop({ default: false }) public readonly isLoading: boolean;
  @Prop({ default: false }) public readonly isUserOwner: boolean;
  @Prop({ default: false }) public readonly editMode: boolean;
  @Prop({ default: true }) public readonly isSubmitDisabled: boolean;
  @Prop({
    default: () => {
      return competitionService.factoryCompetitionInfo();
    },
  })
  public readonly competitionSummary: ICompetitionInfo;

  public isAdmin: boolean;

  public showDeleteConfirmation: boolean = false;

  public configService = new ConfigService();

  public deleteCompEventTeamAsk() {
    this.showDeleteConfirmation = true;
  }

  public get showCancel() {
    return this.editMode;
  }

  public get showSubmit() {
    return this.editMode;
  }

  public get isDeleteDisabled() {
    return this.isLoading;
  }

  public get canDelete() {
    if (this.isAdmin) {
      return true;
    }
    if (competitionService.isClosed(this.competitionSummary.entriesClose)) {
      return false;
    }

    if (!this.isUserOwner) {
      return false;
    }
    return this.compEventTeam.id > 0 && !this.showDeleteConfirmation;
  }

  public get getCanEdit(): boolean {
    if (this.isAdmin) {
      return true;
    }
    if (competitionService.isClosed(this.competitionSummary.entriesClose)) {
      return false;
    }
    return !this.editMode;
  }

  public get getHasBuilderPermissionForComp(): boolean {
    const hasAppAdminPermissionForNational =
      this.configService.hasAppAdminPermissionForNational(
        this.configApp.userInfo
      );

    const hasAppAdminPermissionForNationalOrg =
      this.configService.hasAppAdminPermissionForNational(
        this.configApp.userInfo
      );

    const hasBuilderPermissionForComp =
      this.configService.hasBuilderPermissionForComp(
        this.configApp.userInfo,
        this.competitionSummary.compOrgId,
        this.competitionSummary.compId
      );

    return (
      hasAppAdminPermissionForNational ||
      hasAppAdminPermissionForNationalOrg ||
      hasBuilderPermissionForComp
    );
  }

  public get getCanAddToUserCart(): boolean {
    if (this.compEventTeam.paid !== 0) {
      return false;
    }
    return this.getHasBuilderPermissionForComp;
  }

  public setEditMode(editMode: boolean) {
    this.$emit("edit", editMode);
  }

  public cancelCompEventTeam() {
    this.showDeleteConfirmation = false;
    this.$emit("cancel");
  }

  public deleteCompEventTeam() {
    this.$emit("delete", this.compEventTeam);
  }

  public submitCompEventTeam() {
    this.$emit("submit");
  }

  public addUserCart() {
    console.log("EventTeamButtons.addUserCart", this.compEventTeam);
    this.$emit("addUserCart", this.compEventTeam);
  }
}
</script>
