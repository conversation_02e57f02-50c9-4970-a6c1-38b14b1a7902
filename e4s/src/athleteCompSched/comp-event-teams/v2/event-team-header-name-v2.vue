<template>
  <div class="e4s-flex-row e4s-gap--small">
    <slot name="pre-text"></slot>
    <template>
      <div class="e4s-flex-row e4s-gap--small">
        <slot name="pre-text"></slot>
        <span>
          {{ eventTeamHeader.eventGroup.name }}: {{ getGender }}
          {{ eventTeamHeader.ageGroup.name }}
        </span>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import { IEventTeamHeader } from "../event-teams-models";
import { getGenderLabel } from "../../../common/common-service-utils";

export default defineComponent({
  name: "event-team-header-name-v2",
  components: {},
  props: {
    eventTeamHeader: {
      type: Object as PropType<IEventTeamHeader>,
      required: true,
    },
  },
  setup(props: { eventTeamHeader: IEventTeamHeader }, context: SetupContext) {
    const getGender = computed(() => {
      return getGenderLabel(props.eventTeamHeader.gender);
    });

    const getXiText = computed(() => {
      return props.eventTeamHeader.ceoptions &&
        props.eventTeamHeader.ceoptions.xiText
        ? props.eventTeamHeader.ceoptions.xiText
        : "";
    });

    return { getGender, getXiText };
  },
});
</script>
