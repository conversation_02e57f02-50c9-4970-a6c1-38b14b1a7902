<template>
  <CardGenericV2>
    <div slot="content" class="e4s-flex-column e4s-gap--standard">
      <!--      <FormGenericSectionTitleV2 title-size="400"-->
      <!--                                 section-title="Your Teams">-->

      <!--      </FormGenericSectionTitleV2>-->

      <div class="e4s-flex-row e4s-justify-flex-space-between e4s-flex-start">
        <div class="e4s-flex-column">
          <EventTeamHeaderNameV2
            class="e4s-input--label"
            :event-team-header="eventTeamHeader"
          />

          <p
            v-if="userEntity.entityLevel > 0"
            class="e4s-subheader--general e4s-subheader--500"
            v-text="userEntity.name"
          ></p>
        </div>

        <slot name="top-right-back">
          <EventTeamHeaderStatusPillV2 :event-team-header="eventTeamHeader" />
        </slot>
      </div>

      <div class="e4s-flex-row">
        <FormGenericInputTextV2
          class="e4s-full-width"
          form-label="Team Name"
          v-model="compEventTeam.teamName"
          :is-disabled="
            this.eventTeamHeader.ceoptions.eventTeam.disableTeamNameEdit
          "
        >
          <span
            slot="after-label"
            class="e4s-subheader--general e4s-flex-row--end"
            v-text="eventTeamHeader.ceoptions.eventTeam.formType"
          ></span>
        </FormGenericInputTextV2>
      </div>

      <!--      <div class="e4s-flex-row e4s-justify-flex-end">-->
      <!--        <ButtonGenericBackV2 v-on:click="cancel" />-->
      <!--      </div>-->

      <div
        class="e4s-flex-row e4s-full-width e4s-gap--standard"
        v-if="!showEligibility"
      >
        Can't find athlete?
        <!--        <a-->
        <!--          class="anchor-buttons"-->
        <!--          href="#"-->
        <!--          v-on:click.prevent="showEligibility = true"-->
        <!--        >-->
        <!--          Click here to check why.-->
        <!--        </a>-->
        <PrimaryLink
          link-text="Click here to check why."
          @onClick="showEligibility = true"
        />
      </div>

      <EventAthleteEligibleV2
        v-if="showEligibility"
        :comp-event-team="compEventTeamProp"
        :ceid="eventTeamHeader.id"
        v-on:cancel="showEligibility = false"
      />

      <div
        class="e4s-flex-row e4s-gap--standard e4s-full-width"
        v-for="index in getAthleteSlots"
      >
        <FormGenericInputTemplateV2
          :form-label="getTeamPositionNameEditMode(index - 1).toString()"
          class="e4s-full-width"
        >
          <template slot="field">
            <AthleteTypeAheadV2
              :competition-id="competition.compId"
              :disabled="isLoading"
              :user-entity="userEntity"
              :gender="eventTeamHeader.gender"
              :age-group-ids="getAgeGroupIds"
              :field-label="'Enter name or URN'"
              :position="index - 1"
              :athlete-default="compEventTeam.athletes[index - 1]"
              :is-disabled="isLoading"
              place-holder=""
              v-on:athleteSelected="athleteSelected"
              v-on:reset="athleteRemoved"
            />
          </template>
        </FormGenericInputTemplateV2>
      </div>

      <div class="e4s-flex-column e4s-info-text--error">
        <div
          v-for="message in userMessages"
          v-text="message.message"
          :key="message.id"
        ></div>
      </div>

      <!--      :is-submit-disabled="!editMode || isLoading"-->
      <EventTeamButtonsV2
        :comp-event-team="compEventTeam"
        :competition-summary="competition"
        :is-loading="isLoading"
        :edit-mode="true"
        :is-user-owner="isUserOwner"
        :is-submit-disabled="false"
        @cancel="cancelEdit"
        @edit="editCompEventTeam"
        @delete="deleteCompEventTeam"
        @submit="submitCompEventTeam"
        @addUserCart="addUserCart"
      />
    </div>
  </CardGenericV2>
</template>

<script lang="ts">
import * as R from "ramda";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { EventTeamService } from "../../event-team-service";
import { CompetitionService } from "../../../../competition/competiton-service";
import { AthleteService } from "../../../../athlete/athlete-service";
import { AthleteData } from "../../../../athlete/athlete-data";
import { ConfigService } from "../../../../config/config-service";
import {
  ENTRY_STORE_CONST,
  IEntryStoreState,
} from "../../../../entry/entry-store";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../config/config-store";
import EventTeamBase from "../../event-team-base";
import { ICompetitionInfo } from "../../../../competition/competition-models";
import {
  ICompEventTeam,
  ICompEventTeamBase,
  IEventTeamFormField,
  IEventTeamHeader,
} from "../../event-teams-models";
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue";
import FormGenericInputTextV2 from "../../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import {
  IUserMessage,
  USER_MESSAGE_LEVEL,
} from "../../../../user-message/user-message-models";
import {
  IAthlete,
  IAthleteSearch,
  IAthleteSummary,
} from "../../../../athlete/athlete-models";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import {
  IServerGenericResponse,
  IServerPagingResponseList,
} from "../../../../common/common-models";
import { IConfigApp, IEntity } from "../../../../config/config-app-models";
import { IEventTeam } from "../../../athletecompsched-models";
import EventAthleteEligibleV2 from "./event-athlete-eligible-v2.vue";
import ButtonGenericBackV2 from "../../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import AthleteTypeAheadV2 from "../athlete-type-ahead-v2.vue";
import { simpleClone } from "../../../../common/common-service-utils";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import EventTeamHeaderStatusPillV2 from "../event-team-header-status-pill-v2.vue";
import FieldTextV2 from "../../../../common/ui/layoutV2/fields/field-text-v2.vue";
import FormGenericFieldLabelV2 from "../../../../common/ui/layoutV2/form/form-generic--field-label-v2.vue";
import EventTeamHeaderNameV2 from "../event-team-header-name-v2.vue";
import FormGenericButtonBar from "../../../../common/ui/layoutV2/form/form-generic-button-bar.vue";
import EventTeamDefaultV2 from "./event-team-default-v2.vue";
import PrimaryLink from "../../../../common/ui/layoutV2/href/PrimaryLink.vue";
import EventTeamButtonsV2 from "../event-team-buttons-v2.vue";

const eventTeamService: EventTeamService = new EventTeamService();
const competitionService: CompetitionService = new CompetitionService();
const athleteService: AthleteService = new AthleteService();
const athleteData: AthleteData = new AthleteData();
const configService: ConfigService = new ConfigService();

type SectionName = "NEW" | "EXISTING" | "EDIT";

@Component({
  name: "event-team-default-edit-v2",
  components: {
    EventTeamButtonsV2,
    PrimaryLink,
    EventTeamDefaultV2,
    FormGenericButtonBar,
    EventTeamHeaderNameV2,
    FormGenericFieldLabelV2,
    FieldTextV2,
    EventTeamHeaderStatusPillV2,
    FormGenericInputTemplateV2,
    AthleteTypeAheadV2,
    ButtonGenericBackV2,
    EventAthleteEligibleV2,
    ButtonGenericV2,
    FormGenericInputTextV2,
    CardGenericV2,
  },
  computed: {
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      selectedCompetition: (state: IEntryStoreState) =>
        state.entryForm.selectedCompetition,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
      userInfo: (state: IConfigStoreState) => state.configApp.userInfo,
      userEntityStore: (state: IConfigStoreState) => state.userEntity,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class EventTeamDefaultEditV2 extends EventTeamBase {
  public readonly selectedCompetition: ICompetitionInfo;
  public readonly userEntityStore: IEntity;
  public readonly configApp: IConfigApp;
  public readonly eventTeamHeaders: IEventTeamHeader[];
  public readonly isAdmin: boolean;

  @Prop()
  public eventTeamHeader: IEventTeamHeader;

  @Prop({
    required: true,
  })
  public compEventTeamProp: ICompEventTeam;

  @Prop({ default: false })
  public readonly isLoading: boolean;

  @Prop({
    default: () => {
      return configService.factoryEntity();
    },
  })
  public readonly userEntity: IEntity;

  @Prop({ default: false })
  public readonly isNew: boolean;

  public compEventTeam: ICompEventTeam = R.clone(this.compEventTeamProp);
  public showEligibility: boolean = false;
  public showAddAthleteDialog: boolean = false;

  public athleteForAddButtonToShow = 0;
  public athleteForAdd = athleteService.factoryGetAthlete();
  public athleteForAddIndex: number = -1;

  public showSection: SectionName = this.isNew ? "NEW" : "EXISTING";

  public created() {
    console.log("EventTeamDefault.created() userEntity: ", this.userEntity);
    this.compEventTeam = R.clone(this.compEventTeamProp);
  }

  public mounted() {
    this.initForm();
  }

  public initForm() {
    this.compEventTeam = R.clone(this.compEventTeamProp);
    this.compEventTeam.ceid = this.eventTeamHeader.id;
    this.compEventTeam.athletes = eventTeamService.setUpAthleteArray(
      this.eventTeamHeader,
      this.compEventTeam
    );
    this.setClubSchoolForAthleteAdd(this.userEntity);
  }

  @Watch("compEventTeamProp")
  public onCompEventTeamProp(newValue: ICompEventTeam) {
    this.compEventTeam = R.clone(newValue);
    this.reset();
  }

  @Watch("userEntity")
  public onUserEntityPropChanged(newValue: IEntity) {
    this.setClubSchoolForAthleteAdd(newValue);
  }

  public setClubSchoolForAthleteAdd(entity: IEntity) {
    const athlete = athleteService.factoryGetAthlete();
    if (this.isSchool) {
      athlete.schoolid = entity.id;
      athlete.school = entity.name;
    } else {
      athlete.clubid = entity.id;
      athlete.club = entity.name;
    }
    athlete.aocode = this.configApp.defaultao.code;
    this.athleteForAdd = R.clone(athlete);
  }

  public reset() {
    this.editingLine = -1;
    this.hasBeenEdited = false;
    this.editTeamName = false;
    this.showDeleteConfirmation = false;
    this.initForm();
  }

  public get getAthleteSlots(): number {
    const eventTeam: IEventTeam = eventTeamService.getEventTeamOptions(
      this.eventTeamHeader
    );
    return eventTeam ? eventTeam.max : 0;
  }

  public get isSchool() {
    return competitionService.isSchool(this.competition);
  }

  public get allowAddAthletes(): boolean {
    return this.isSchool;
  }

  public addAthlete(index: number) {
    this.showAddAthleteDialog = true;
    this.athleteForAddIndex = index;
  }

  public onAddAthleteSubmit(athlete: IAthlete) {
    console.log("EventTeamDefault.onAddAthleteSubmit()", athlete);
    this.showAddAthleteDialog = false;

    const athleteSearch: IAthleteSearch =
      athleteService.factoryGetAthleteSearch();
    athleteSearch.athleteid = athlete.id;
    messageDispatchHelper(
      "Validating athlete.",
      USER_MESSAGE_LEVEL.INFO.toString()
    );
    athleteData
      .findAthletes(this.competition.id, 0, 1, 10, "surname", athleteSearch)
      .then((response: IServerPagingResponseList<IAthleteSummary>) => {
        console.log(
          "onAddAthleteSubmit() athleteData.findAthletes()",
          response
        );
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return null;
        }
        if (response.data.length !== 1) {
          messageDispatchHelper(
            "A unique record with same demographics could not be found, please use search box to add name.",
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return null;
        }
        return response.data[0];
      })
      .then((athleteSummary: IAthleteSummary | null) => {
        if (!athleteSummary) {
          return;
        }
        return athleteData
          .checkAthleteEventEligible(athleteSummary.id, this.eventTeamHeader.id)
          .then((response: IServerGenericResponse) => {
            if (response.errNo > 0) {
              messageDispatchHelper(
                response.error,
                USER_MESSAGE_LEVEL.ERROR.toString()
              );
              return;
            }
            const message = response.data;
            messageDispatchHelper(message, USER_MESSAGE_LEVEL.INFO.toString());
            if (message.indexOf("is eligible for this event") > -1) {
              const position = this.athleteForAddIndex - 1;
              this.setAthlete(athleteSummary, position);
            }
            return;
          })
          .catch((error) => {
            messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
            return;
          });
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return;
      });
  }

  public onAddAthleteCancel() {
    this.showAddAthleteDialog = false;
  }

  // public athleteSelected(payload: {
  //   autoCompleteValue: IAutoCompleteValue;
  //   position: number;
  // }) {
  //   this.userMessages = [];
  //   console.log("CompEventTeam.athleteSelected", payload);
  //   if (payload.autoCompleteValue.value) {
  //     const athlete: IAthleteSummary = payload.autoCompleteValue.value;
  //     this.setAthlete(athlete, payload.position);
  //   }
  // }

  public athleteSelected(payload: {
    athlete: IAthleteSummary;
    position: number;
  }) {
    this.userMessages = [];
    console.log("CompEventTeam.athleteSelected", payload);
    this.setAthlete(payload.athlete, payload.position);
  }

  public athleteRowSelected(index: number) {
    console.log("athleteRowSelected() index: " + index);
    this.athleteForAddButtonToShow = index;
  }

  public setAthlete(athlete: IAthleteSummary, position: number) {
    const result: IUserMessage[] = eventTeamService.canAddAthlete(
      this.eventTeamHeader,
      this.compEventTeam,
      athlete,
      position
    );
    this.userMessages = result;
    const athletes = R.clone(this.compEventTeam.athletes);
    athletes[position] = R.clone(athlete);
    this.compEventTeam.athletes = athletes;
    this.onFieldChanged();
    this.editLine(-1);
  }

  public athleteRemoved(position: number) {
    this.userMessages = [];
    console.log("CompEventTeam.athleteRemoved: " + position);
    const athletes = R.clone(this.compEventTeam.athletes);
    athletes[position] = this.athleteService.factoryGetAthlete();
    this.compEventTeam.athletes = athletes;
    this.onFieldChanged();
  }

  public get getTeamPositionName() {
    return eventTeamService.getOptionsTeamPositionName(this.eventTeamHeader);
  }

  public get getTeamPositionSubName() {
    return eventTeamService.getOptionsTeamSubstituteName(this.eventTeamHeader);
  }

  public getTeamPositionNameReadMode(index: number) {
    const position: number = index + 1;
    return this.getTeamPositionName.length > 0
      ? this.getTeamPositionName + " " + position
      : "Athlete " + position;
  }

  public isOptionalPosition(index: number) {
    const position: number = index + 1;
    return position > eventTeamService.getOptionsMin(this.eventTeamHeader);
  }

  public getTeamPositionNameEditMode(index: number) {
    // const position: number = index + 1;
    const isOptional: boolean = this.isOptionalPosition(index);
    // position > eventTeamService.getOptionsMin(this.eventTeamHeader);
    if (isOptional) {
      const substituteName = eventTeamService.getOptionsTeamSubstituteName(
        this.eventTeamHeader
      );
      return (
        (substituteName === "" ? "Substitute" : substituteName) + " (Optional)"
      );
    } else {
      return this.getTeamPositionNameReadMode(index);
    }
  }

  public getAthleteDescriptorRight(athlete: IAthleteSummary): string {
    return eventTeamService.getAthleteDescriptorRightV2(
      athlete,
      this.eventTeamHeader.ceoptions.eventTeam
    );
  }

  public editLine(index: number) {
    this.editingLine = index;
    this.hasBeenEdited = true;
  }

  public get canShowEligibility() {
    return eventTeamService.getOptionsMin(this.eventTeamHeader) > 0;
  }

  public get getTeamAthletes() {
    return this.compEventTeam.athletes.filter((athlete) => {
      return athlete.id > 0;
    });
  }

  public get getAgeGroupIds(): number[] {
    return eventTeamService.getOptionsAgeGroupIdsForAthleteSearch(
      this.eventTeamHeader
    );
  }

  public get canDelete() {
    if (this.isAdmin) {
      return true;
    }
    if (competitionService.isClosed(this.selectedCompetition.entriesClose)) {
      return false;
    }

    if (!this.isUserOwner) {
      return false;
    }
    // return this.compEventTeam.id > 0 && !this.showDeleteConfirmation;
    return this.compEventTeam.id > 0;
  }

  public submitCompEventTeam() {
    console.log("CompEventTeam.submitCompEventTeam()");
    this.userMessages = [];
    const userMessages: IUserMessage[] = eventTeamService.validateCompEventTeam(
      this.eventTeamHeader,
      this.compEventTeam,
      this.competition,
      this.eventTeamHeaders
    );
    if (userMessages.length > 0) {
      this.userMessages = userMessages;
      return;
    }

    this.$emit("submit", this.compEventTeam);
  }

  public cancelEdit() {
    // if (this.compEventTeam.id == 0) {
    //   this.$emit("cancel");
    //   return;
    // }
    this.$emit("cancel");
    // this.showSection = "EXISTING";
  }

  public cancel() {
    this.$emit("cancel");
  }

  public eventFormChange(formInfo: IEventTeamFormField[]) {
    this.compEventTeam.formInfo = formInfo;
  }

  public doEdit() {
    this.$emit("edit", simpleClone(this.compEventTeam));
  }

  public editTeam(compEventTeam: ICompEventTeamBase) {
    this.$emit("editTeam", compEventTeam);
  }

  public deleteTeam(compEventTeam: ICompEventTeamBase) {
    this.$emit("deleteTeam", compEventTeam);
  }

  public addUserCart() {
    console.log("EventTeamDefaultEditV2.addUserCart()", this.compEventTeam);
    this.$emit("addUserCart", this.compEventTeam);
  }

  public get canEditTeamName() {
    // return (
    //   props.eventTeamHeader.ceoptions.eventTeam.disableTeamNameEdit &&
    //   (props.isNew || isUserOwner.value || configController.isAdmin.value)
    // );
    return (
      (this.eventTeamHeader.ceoptions.eventTeam.disableTeamNameEdit &&
        (this.isNew || this.isUserOwner)) ||
      this.isAdmin
    );
  }
}
</script>

<style scoped>
.e4s-name-edit {
  margin-left: 1rem !important;
}
</style>
