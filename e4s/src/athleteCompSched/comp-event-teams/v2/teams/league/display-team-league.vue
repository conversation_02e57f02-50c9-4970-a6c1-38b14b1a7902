<template>
  <div class="e4s-flex-column e4s-gap--small">
    <div
      v-for="(formRow, index) in formRows"
      :key="index"
      class="e4s-flex-column"
    >
      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <div class="e4s-flex-row e4s-gap--standard">
          <div v-if="formRow.athlete && formRow.athlete.id > 0">
            {{ formRow.athlete.firstName }} {{ formRow.athlete.surName }}
          </div>
          <div v-else>-</div>
        </div>

        <div class="e4s-text--right">
          {{ getEventName(formRow) }}
        </div>
      </div>

      <hr class="dat-e4s-hr-only dat-e4s-hr--lighter" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "@vue/composition-api";
import { format, parse } from "date-fns";
import { IFormRow } from "../../../../athletecompsched-models";
import { CommonService } from "../../../../../common/common-service";

const commonService = new CommonService();

export default defineComponent({
  name: "DisplayTeamLeague",
  props: {
    formRows: {
      type: Array as PropType<IFormRow[]>,
      required: true,
    },
  },
  setup() {
    /**
     * Format the event name with date and time
     */
    function getEventName(formRow: IFormRow): string {
      if (!formRow || !formRow.eventDef) {
        return "";
      }

      let evtDate = "";
      let evtTime = "";

      if (formRow.dateTime && formRow.dateTime.length > 0) {
        evtDate = format(parse(formRow.dateTime), "Do MMM");
        evtTime = commonService.getE4sStandardTimeOutPut(formRow.dateTime);
        evtTime = evtTime === "00:00" ? "TBC" : evtTime;
      }

      return `${formRow.eventDef.name} ${evtDate} @${evtTime}`;
    }

    return {
      getEventName,
    };
  },
});
</script>

<style scoped>
.e4s-text--right {
  text-align: right;
}
</style>
