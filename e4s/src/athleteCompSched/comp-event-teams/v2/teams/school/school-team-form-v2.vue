<template>
  <div class="e4s-flex-column">
    <!--    <event-team-name-->
    <!--      :name-prop="state.compEventTeam.teamName"-->
    <!--      :edit-mode-prop="editMode"-->
    <!--      :can-edit="state.isUserOwner"-->
    <!--      :user-info="userInfo"-->
    <!--      v-on:changed="onTeamNameChangedSchool"-->
    <!--    >-->
    <!--    </event-team-name>-->

    <div class="e4s-flex-row e4s-justify-flex-space-between e4s-flex-start">
      <div class="e4s-flex-column">
        <EventTeamHeaderNameV2
          class="e4s-input--label"
          :event-team-header="eventTeamHeader"
        />
        <p
          v-if="userEntity.entityLevel > 0"
          class="e4s-subheader--general e4s-subheader--500"
          v-text="userEntity.name"
        ></p>
      </div>

      <EventTeamHeaderStatusPillV2 :event-team-header="eventTeamHeader" />
    </div>

    <div class="e4s-flex-row">
      <FormGenericInputTextV2
        class="e4s-full-width"
        form-label="Team Name"
        v-model="schoolTeamController.state.compEventTeam.teamName"
        :is-disabled="!teamController.state.canEditTeamName"
      >
        <span
          slot="after-label"
          class="e4s-subheader--general e4s-flex-row--end"
          v-text="eventTeamHeader.ceoptions.eventTeam.formType"
        ></span>
      </FormGenericInputTextV2>
    </div>

    <div
      v-for="(athlete, index) in schoolTeamController.state.compEventTeam
        .athletes"
      :key="index"
      class="e4s-flex-column e4s-gap--standard"
    >
      <school-team-athlete
        :position-index="index"
        :school-team-athlete-prop="athlete"
        :positionLabel="schoolTeamController.getAthleteTeamPositionName(index)"
        @onChange="schoolTeamController.onAthleteUpdate"
      >
      </school-team-athlete>
    </div>

    <user-validation-messages
      :validation-messages="schoolTeamController.userMessages.value"
    />

    <EventTeamButtonsV2
      :comp-event-team="schoolTeamController.state.compEventTeam"
      :competition-summary="competition"
      :is-loading="isLoading"
      :edit-mode="editMode"
      :is-user-owner="teamController.state.isUserOwner"
      :is-submit-disabled="!editMode || isLoading"
      @cancel="cancel"
      @edit="editCompEventTeam"
      @delete="deleteCompEventTeam"
      @submit="submitCompEventTeam"
      @addUserCart="addUserCart"
    />

    <!--    {{ state.compEventTeam }}-->
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  watch,
} from "@vue/composition-api";
import SchoolTeamAthlete from "../../../school/school-team-athlete.vue";
import { ICompEventTeamSchool } from "../../../school/school-team-models";
import { SchoolTeamService } from "../../../school/school-team-service";
import {
  ICompEventTeamBase,
  IEventTeamHeader,
} from "../../../event-teams-models";
import UserValidationMessages from "../../../../../user-message/user-validation-messages.vue";
import { CONFIG_STORE_CONST } from "../../../../../config/config-store";
import { IEntity } from "../../../../../config/config-app-models";
import { ConfigService } from "../../../../../config/config-service";
import EventTeamName from "../../../event-team-name.vue";
import { simpleClone } from "../../../../../common/common-service-utils";
import { ICompetitionSummaryPublic } from "../../../../../competition/competition-models";
import { useStore } from "../../../../../app.store";
import ButtonGenericV2 from "../../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import PrimaryLink from "../../../../../common/ui/layoutV2/href/PrimaryLink.vue";
import EventTeamButtonsV2 from "../../event-team-buttons-v2.vue";
import EventTeamHeaderNameV2 from "../../event-team-header-name-v2.vue";
import EventTeamHeaderStatusPillV2 from "../../event-team-header-status-pill-v2.vue";
import FormGenericInputTextV2 from "../../../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import { useTeamController } from "../../useTeamController";
import { useSchoolTeamController } from "./useSchoolTeamController";
import { useConfigController } from "../../../../../config/useConfigStore";
import { ISchoolTeamControllerInput } from "./models/school-team-models";

const schoolTeamService: SchoolTeamService = new SchoolTeamService();
const configService: ConfigService = new ConfigService();

export default defineComponent({
  name: "SchoolTeamFormV2",
  components: {
    FormGenericInputTextV2,
    EventTeamHeaderStatusPillV2,
    EventTeamHeaderNameV2,
    EventTeamButtonsV2,
    PrimaryLink,
    ButtonGenericV2,
    "school-team-athlete": SchoolTeamAthlete,
    "user-validation-messages": UserValidationMessages,
    "event-team-name": EventTeamName,
  },
  props: {
    competition: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
    compEventTeamSchoolProp: {
      type: Object as () => ICompEventTeamSchool,
      default: () => {
        return schoolTeamService.factoryCompEventTeamSchool(
          {
            id: 0,
          } as IEventTeamHeader,
          configService.factoryEntity()
        );
      },
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    isNew: {
      type: Boolean,
      default: false,
    },
    editMode: {
      type: Boolean,
      default: false,
    },
    eventTeamHeader: {
      type: Object as () => IEventTeamHeader,
      required: true,
    },
    eventTeamHeaders: {
      type: Array as PropType<IEventTeamHeader[]>,
      required: true,
    },
    userEntity: {
      type: Object as () => IEntity,
      default: () => {
        return configService.factoryEntity();
      },
    },
  },
  setup(
    props: {
      competition: ICompetitionSummaryPublic;
      compEventTeamSchoolProp: ICompEventTeamSchool;
      isLoading: boolean;
      isNew: boolean;
      editMode: boolean;
      eventTeamHeader: IEventTeamHeader;
      eventTeamHeaders: IEventTeamHeader[];
      userEntity: IEntity;
    },
    { emit }
  ) {
    const configController = useConfigController();
    const storeInternal = useStore();

    const teamController = useTeamController();
    teamController.init({
      competition: {
        compId: props.competition.compId,
        compOrgId: props.competition.compOrgId,
      },
      eventTeamHeader: props.eventTeamHeader,
      compEventTeam: props.compEventTeamSchoolProp as ICompEventTeamBase,
      userEntity: props.userEntity,
    });

    const schoolTeamControllerInput: ISchoolTeamControllerInput = {
      competition: {
        compId: props.competition.compId,
        compOrgId: props.competition.compOrgId,
      },
      eventTeamHeader: props.eventTeamHeader,
      compEventTeam: props.compEventTeamSchoolProp,
      userEntity: props.userEntity,
    };

    const schoolTeamController = useSchoolTeamController(
      schoolTeamControllerInput
    );

    // watch compEventTeamSchoolProp and update state.compEventTeam on change
    watch(
      () => props.compEventTeamSchoolProp,
      (newValue) => {
        const compEventTeam = schoolTeamService.cleanSchoolTeam(
          simpleClone(newValue)
        );

        console.warn(
          "SchoolTeamFormV2.watch compEventTeamSchoolProp!!!!!!!!!",
          {
            newValue,
            compEventTeam,
          }
        );

        // state.compEventTeam = compEventTeam;

        schoolTeamController.init({
          competition: {
            compId: props.competition.compId,
            compOrgId: props.competition.compOrgId,
          },
          eventTeamHeader: props.eventTeamHeader,
          compEventTeam: compEventTeam,
          userEntity: props.userEntity,
        });
      }
    );

    const userInfo = configController.getStore.value.configApp.userInfo;

    // Add the getHasBuilderPermissionForComp computed property
    const getHasBuilderPermissionForComp = computed((): boolean => {
      const configApp =
        storeInternal.state[CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME]
          .configApp;

      const hasAppAdminPermissionForNational =
        configService.hasAppAdminPermissionForNational(configApp.userInfo);

      const hasAppAdminPermissionForNationalOrg =
        configService.hasAppAdminPermissionForNational(configApp.userInfo);

      const hasBuilderPermissionForComp =
        configService.hasBuilderPermissionForComp(
          configApp.userInfo,
          props.competition.compOrgId,
          props.competition.compId
        );

      return (
        hasAppAdminPermissionForNational ||
        hasAppAdminPermissionForNationalOrg ||
        hasBuilderPermissionForComp
      );
    });

    function submitTeam() {
      console.log("SchoolTeamFormV2.submitTeam()");
      schoolTeamController.validate();
      if (schoolTeamController.userMessages.value.length > 0) {
        return;
      }
      const compEventTeam = schoolTeamService.stripEmptyAthletes(
        schoolTeamController.state.compEventTeam
      );
      // if (state.userMessages.length > 0) {
      //   return;
      // }
      emit("submit", compEventTeam);
    }

    function addUserCart() {
      console.log(
        "SchoolTeamForm.addUserCart()",
        schoolTeamController.state.compEventTeam
      );
      emit("addUserCart", schoolTeamController.state.compEventTeam);
    }

    function cancel() {
      emit("cancel");
    }

    function editCompEventTeam() {
      emit("edit");
    }

    function deleteCompEventTeam() {
      emit("delete", schoolTeamController.state.compEventTeam);
    }

    function cancelCompEventTeam() {
      emit("cancel");
    }

    function submitCompEventTeam() {
      submitTeam();
    }

    // const isUserOwner = computed(() => {
    //   return props.compEventTeamSchoolProp.entityId === props.userEntity.id;
    // });

    // const canEditTeamName = computed(() => {
    //   return (
    //     (props.eventTeamHeader.ceoptions.eventTeam.disableTeamNameEdit &&
    //       (props.isNew || isUserOwner.value)) ||
    //     configController.isAdmin.value
    //   );
    // });

    // Watch
    watch(
      () => props.compEventTeamSchoolProp,
      (newValue) => {
        // state.compEventTeam = simpleClone(newValue);
        schoolTeamController.init({
          competition: {
            compId: props.competition.compId,
            compOrgId: props.competition.compOrgId,
          },
          eventTeamHeader: props.eventTeamHeader,
          compEventTeam: newValue,
          userEntity: props.userEntity,
        });
      }
    );

    return {
      teamController,
      schoolTeamController,

      userInfo,
      getHasBuilderPermissionForComp,

      submitTeam,
      addUserCart,
      cancel,
      editCompEventTeam,
      deleteCompEventTeam,
      cancelCompEventTeam,
      submitCompEventTeam,
    };
  },
});
</script>
