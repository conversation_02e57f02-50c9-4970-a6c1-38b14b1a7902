<template>
  <div class="e4s-flex-column e4s-gap--small">
    <div
      v-for="(athlete, index) in athletes"
      :key="index"
      class="e4s-flex-column"
      v-if="athlete.name && athlete.name.length > 0"
    >
      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <div class="e4s-flex-row e4s-gap--standard">
          <div v-text="getPositionLabel(index)"></div>
          <div v-text="athlete.name"></div>
        </div>
      </div>

      <hr class="dat-e4s-hr-only dat-e4s-hr--lighter" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { ISchoolTeamAthlete } from "../../../school/school-team-models";
import { IEventTeamHeader } from "../../../event-teams-models";
import { EventTeamService } from "../../../event-team-service";

const eventTeamService = new EventTeamService();

export default defineComponent({
  name: "DisplayTeamSchool",
  props: {
    athletes: {
      type: Array as PropType<ISchoolTeamAthlete[]>,
      required: true,
    },
    eventTeamHeader: {
      type: Object as PropType<IEventTeamHeader>,
      required: true,
    },
  },
  setup(
    props: {
      athletes: ISchoolTeamAthlete[];
      eventTeamHeader: IEventTeamHeader;
    },
    context: SetupContext
  ) {
    /**
     * Get the position label for an athlete
     */
    function getPositionLabel(index: number): string {
      const position: number = index + 1;
      const isOptional: boolean =
        position > eventTeamService.getOptionsMin(props.eventTeamHeader);

      if (isOptional) {
        const substituteName = eventTeamService.getOptionsTeamSubstituteName(
          props.eventTeamHeader
        );
        return substituteName === "" ? "Sub" : substituteName;
      } else {
        return getTeamPositionNameReadMode(index);
      }
    }

    /**
     * Get the team position name for read mode
     */
    function getTeamPositionNameReadMode(index: number): string {
      const position: number = index + 1;
      const positionName = eventTeamService.getOptionsTeamPositionName(
        props.eventTeamHeader
      );

      return positionName.length > 0
        ? positionName + " " + position
        : position.toString();
    }

    return {
      getPositionLabel,
      getTeamPositionNameReadMode,
    };
  },
});
</script>

<style scoped>
.e4s-text--right {
  text-align: right;
}
</style>
