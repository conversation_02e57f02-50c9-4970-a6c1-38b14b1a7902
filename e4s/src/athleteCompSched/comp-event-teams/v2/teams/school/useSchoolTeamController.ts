import { computed, reactive, ref } from "@vue/composition-api";
import { simpleClone } from "../../../../../common/common-service-utils";
import {
  ICompEventTeamSchool,
  ISchoolTeamAthleteOutput,
} from "../../../school/school-team-models";
import { IEventTeamHeader } from "../../../event-teams-models";
import { EventTeamService } from "../../../event-team-service";
import { SchoolTeamService } from "../../../school/school-team-service";
import { IUserMessage } from "../../../../../user-message/user-message-models";
import { ICompetitionInfo } from "../../../../../competition/competition-models";
import { ISchoolTeamControllerInput } from "./models/school-team-models";

const eventTeamService: EventTeamService = new EventTeamService();
const schoolTeamService: SchoolTeamService = new SchoolTeamService();

/**
 * Initializes and provides control methods for managing the state of a school team within
 * a competition's event team context. This hook operates with reactive state, ensuring
 * dynamic updates and validations for the team structure and athletes.
 *
 * @param {ISchoolTeamControllerInput} input - Input object containing the initial
 * configuration and data of the school team controller.
 * @return {Object} An object containing the reactive state of the school team controller,
 * and control methods like `init` and `onAthleteUpdate`.
 */
export function useSchoolTeamController(input: ISchoolTeamControllerInput) {
  /**
   * Represents the reactive state of the school team controller.
   *
   * This variable holds a reactive object generated from cloning the input.
   * It is designed to manage and track the state of the school team controller dynamically,
   * allowing the UI or other reactive components to respond to changes appropriately.
   *
   * @type {ISchoolTeamControllerInput}
   */
  const state = reactive<ISchoolTeamControllerInput>(simpleClone(input));

  const userMessages = ref([] as IUserMessage[]);

  state.compEventTeam = ensureTeamCorrectSize(
    state.eventTeamHeader,
    state.compEventTeam
  );

  /**
   * A computed property that retrieves the available team position options name.
   * The value is generated based on the event team header state by invoking
   * a method from the eventTeamService.
   *
   * @constant {Function} getOptionsTeamPositionName
   * @returns {Array|string|Object} The team position options name fetched from the service.
   */
  const getOptionsTeamPositionName = computed(() => {
    return eventTeamService.getOptionsTeamPositionName(state.eventTeamHeader);
  });

  /**
   *
   * @param schoolTeamControllerInput
   */
  function init(schoolTeamControllerInput: ISchoolTeamControllerInput) {
    // assign state to reactive component
    Object.assign(state, schoolTeamControllerInput);
    state.compEventTeam = ensureTeamCorrectSize(
      state.eventTeamHeader,
      state.compEventTeam
    );
  }

  /**
   * Ensures that the given competition event team matches the required team size by
   * adding empty athletes if necessary.
   * The method checks the maximum team size allowed for the specified event and fills
   * the team with empty athletes
   * to meet the required size.
   *
   * @param {IEventTeamHeader} eventTeamHeader - The header information of the event team,
   * containing event-specific data.
   * @param {ICompEventTeamSchool} compEventTeamSchool - The competition event team object
   * representing the existing team configuration.
   * @returns {ICompEventTeamSchool} A team object adjusted to meet the correct size
   * requirements for the event.
   */
  function ensureTeamCorrectSize(
    eventTeamHeader: IEventTeamHeader,
    compEventTeamSchool: ICompEventTeamSchool
  ) {
    // create a local copy of compEventTeamSchool
    const compEventTeam = simpleClone(compEventTeamSchool);

    console.log(
      "useSchoolTeamController.ensureTeamCorrectSize()",
      compEventTeamSchool
    );

    // check eventTeam max size and add empty athletes if needed
    const eventTeam = eventTeamService.getEventTeamOptions(eventTeamHeader);
    const max = eventTeam ? eventTeam.max : 0;
    if (max === 0) {
      return compEventTeam;
    }
    if (compEventTeam.athletes.length < max) {
      for (let i = compEventTeam.athletes.length; i < max; i++) {
        compEventTeam.athletes.push(
          schoolTeamService.factorySchoolTeamAthlete(i + 1)
        );
      }
    }

    return compEventTeam;
  }

  /**
   * Updates the athlete information in the competition event team based on the provided output.
   *
   * @param {ISchoolTeamAthleteOutput} schoolTeamAthleteOutput - The output
   * object containing athlete details and position index.
   * @return {void} No return value.
   */
  function onAthleteUpdate(schoolTeamAthleteOutput: ISchoolTeamAthleteOutput) {
    console.log(
      "useSchoolTeamController.onAthleteUpdate",
      schoolTeamAthleteOutput
    );

    const athletes = simpleClone(state.compEventTeam.athletes);

    if (
      schoolTeamAthleteOutput.positionIndex <
      state.compEventTeam.athletes.length
    ) {
      athletes[schoolTeamAthleteOutput.positionIndex] =
        schoolTeamAthleteOutput.schoolTeamAthlete;

      // state.compEventTeam.athletes[schoolTeamAthleteOutput.positionIndex] =
      //   schoolTeamAthleteOutput.schoolTeamAthlete;
    } else {
      athletes.push(schoolTeamAthleteOutput.schoolTeamAthlete);

      // state.compEventTeam.athletes.push(
      //   schoolTeamAthleteOutput.schoolTeamAthlete
      // );
    }

    state.compEventTeam.athletes = athletes;

    validate();
  }

  /**
   * Determines the name of the team position for an athlete based on their
   * index and the current mode (edit or read).
   *
   * @param {number} index - The index of the athlete in the team list.
   * @param {boolean} editMode - Indicates whether the team position name should
   * reflect editable mode or read-only mode.
   * @return {string} The name of the team position for the athlete, including
   * optional/substitute designation if applicable.
   */
  function getAthleteTeamPositionName(index: number) {
    const position: number = index + 1;
    const isOptional: boolean =
      position > eventTeamService.getOptionsMin(state.eventTeamHeader);
    if (isOptional) {
      const substituteName = eventTeamService.getOptionsTeamSubstituteName(
        state.eventTeamHeader
      );
      return (substituteName === "" ? "Sub" : substituteName) + " (Optional)";
    } else {
      return getOptionsTeamPositionName.value.length > 0
        ? getOptionsTeamPositionName.value + " " + position
        : position;
    }
  }

  /**
   * Validates the competition event team using the provided event team header,
   * competition event team, and competition info.
   * Processes user messages based on validation results and updates the user messages state.
   *
   * @return {void} Executes validation logic, processes related user
   * messages, and updates the state; does not return a value.
   */
  function validate() {
    console.log("CompEventTeam.submitCompEventTeam()");

    // const compEventTeam = schoolTeamService.stripEmptyAthletes(
    //   state.compEventTeam
    // );

    const messages: IUserMessage[] = schoolTeamService.validateCompEventTeam(
      state.eventTeamHeader,
      state.compEventTeam,
      undefined as any as ICompetitionInfo,
      []
    );
    userMessages.value = messages;
  }

  return {
    state,
    userMessages,

    init,
    onAthleteUpdate,
    getAthleteTeamPositionName,
    validate,
  };
}
