<template>
  <div>
    <event-team-name
      class="row"
      :name-prop="compEventTeam.teamName"
      :edit-mode-prop="editMode"
      :can-edit="isUserOwner"
      :user-info="userInfo"
      v-on:changed="onTeamNameChangedSchool"
    >
    </event-team-name>

    <!--    <div class="row">-->
    <!--      <div class="col s1 m1 l1">-->
    <!--        <div class="left">-->
    <!--          <label class="e4s-label">#</label>-->
    <!--        </div>-->
    <!--      </div>-->

    <!--      <div class="col s11 m11 l11">-->
    <!--        <label class="e4s-label">Name</label>-->
    <!--      </div>-->

    <!--            <div class="col s3 m3 l3">-->
    <!--                <div class="right">-->
    <!--                    <label class="e4s-label">Consent</label>-->
    <!--                </div>-->
    <!--            </div>-->
    <!--    </div>-->

    <!--        <div v-for="(athlete, index) in compEventTeam.athletes" :key="index">-->
    <div
      v-for="(athlete, index) in getSchoolAthletes"
      :key="index"
      style="margin-top: 5px"
    >
      <school-team-athlete
        class="row"
        v-if="editMode"
        :school-team-athlete-prop="athlete"
        :positionLabel="getAthleteTeamPositionName(index, editMode)"
        v-on:onChange="onAthleteUpdate"
      >
      </school-team-athlete>
      <school-team-athlete-read
        class="row"
        :position-label="getAthleteTeamPositionName(index, editMode)"
        v-if="!editMode && athlete.name.length > 0"
        :school-team-athlete="athlete"
      >
      </school-team-athlete-read>
    </div>

    <user-validation-messages
      :validation-messages="userMessages"
    ></user-validation-messages>

    <event-team-buttons
      class="row e4s-section-padding-separator"
      :comp-event-team="compEventTeam"
      :isLoading="isLoading"
      :edit-mode="editMode"
      :is-user-owner="isUserOwner"
      :isSubmitDisabled="!editMode || isLoading"
      v-on:cancel="cancelCompEventTeam"
      v-on:edit="editCompEventTeam"
      v-on:delete="deleteCompEventTeam"
      v-on:submit="submitCompEventTeam"
    >
      <template slot="left-buttons-other">
        <a
          v-if="canAddAthlete"
          class="comp-event-team-anchor-buttons"
          href="#"
          v-on:click.prevent="addAthlete"
        >
          Add
        </a>
        <a
          class="comp-event-team-anchor-buttons"
          href="#"
          v-on:click.prevent="addUserCart"
        >
          Add To Cart
        </a>
      </template>
    </event-team-buttons>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import SchoolTeamAthlete from "./school-team-athlete.vue";
import {
  ICompEventTeamSchool,
  ISchoolTeamAthleteOutput,
} from "./school-team-models";
import { SchoolTeamService } from "./school-team-service";
import { IEventTeamHeader } from "../event-teams-models";
import EventTeamButtons from "../event-team-buttons.vue";
import EventTeamBase from "../event-team-base";
import SchoolTeamAthleteRead from "./school-team-athlete-read.vue";
import { IUserMessage } from "../../../user-message/user-message-models";
import UserValidationMessages from "../../../user-message/user-validation-messages.vue";
import {
  IConfigStoreState,
  CONFIG_STORE_CONST,
} from "../../../config/config-store";
import { mapState } from "vuex";
import {
  ICompEventTeamsStoreState,
  COMP_EVENT_TEAMS_STORE_CONST,
} from "../comp-event-store";
import { IEntity, IUserInfo } from "../../../config/config-app-models";
import { ConfigService } from "../../../config/config-service";
import { EventTeamService } from "../event-team-service";

const schoolTeamService: SchoolTeamService = new SchoolTeamService();
const configService: ConfigService = new ConfigService();
const eventTeamService: EventTeamService = new EventTeamService();

@Component({
  name: "school-team-form",
  components: {
    "school-team-athlete": SchoolTeamAthlete,
    "school-team-athlete-read": SchoolTeamAthleteRead,
    "event-team-buttons": EventTeamButtons,
    "user-validation-messages": UserValidationMessages,
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      userInfo: (state: IConfigStoreState) => state.configApp.userInfo,
      userEntityStore: (state: IConfigStoreState) => state.userEntity,
    }),
    ...mapState(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME,
      {
        eventTeamHeaders: (state: ICompEventTeamsStoreState) =>
          state.eventTeamHeaders,
      }
    ),
  },
})
export default class SchoolTeamForm extends EventTeamBase {
  public eventTeamHeaders: IEventTeamHeader[];
  public userInfo: IUserInfo;
  public userEntityStore: IEntity;

  @Prop({
    default: () => {
      return schoolTeamService.factoryCompEventTeamSchool(
        {
          id: 0,
        } as IEventTeamHeader,
        configService.factoryEntity()
      );
    },
  })
  public readonly compEventTeamSchoolProp: ICompEventTeamSchool;

  @Prop({ default: false })
  public readonly isLoading: boolean;

  @Prop()
  public readonly eventTeamHeader: IEventTeamHeader;

  @Prop({
    default: () => {
      return configService.factoryEntity();
    },
  })
  public readonly userEntity: IEntity;

  public compEventTeam: ICompEventTeamSchool =
    schoolTeamService.factoryCompEventTeamSchool(
      this.eventTeamHeader,
      this.userEntity
    );

  public created() {
    this.editMode = this.isNew;
    this.compEventTeam = R.clone(this.compEventTeamSchoolProp);
    this.addAthlete();
  }

  public get canAddAthlete() {
    return schoolTeamService.canAddAthlete(
      this.compEventTeam,
      this.eventTeamHeader
    );
  }

  public addAthlete() {
    //  N.B. The "id" is only for front end, gets stripped on back end.
    this.compEventTeam.athletes = schoolTeamService.addEmptyAthlete(
      this.compEventTeam.athletes,
      this.compEventTeam.athletes.length + 1
    );
  }

  @Watch("compEventTeamSchoolProp")
  public onCompEventTeamSchoolPropChanged(newValue: ICompEventTeamSchool) {
    this.compEventTeam = R.clone(newValue);
  }

  public onAthleteUpdate(schoolTeamAthleteOutput: ISchoolTeamAthleteOutput) {
    console.log("onAthleteUpdate", schoolTeamAthleteOutput);
    this.compEventTeam.athletes = this.compEventTeam.athletes.map(
      (schoolTeamAth) => {
        if (schoolTeamAth.id === schoolTeamAthleteOutput.schoolTeamAthlete.id) {
          return schoolTeamAthleteOutput.schoolTeamAthlete;
        }
        return schoolTeamAth;
      }
    );
    if (
      schoolTeamService.canAddAthlete(this.compEventTeam, this.eventTeamHeader)
    ) {
      this.addAthlete();
    }
    this.validate();
  }

  public get getSchoolAthletes() {
    return this.compEventTeam.athletes;
    // return (this.compEventTeam.athletes as any as IAthleteSummary[]).map( (athlete: IAthleteSummary) => {
    //     return {
    //         id: athlete.id,
    //         name: athlete.firstName + " " + athlete.surName,
    //         consent: false
    //     } as ISchoolTeamAthlete;
    // })
  }

  public getAthleteTeamPositionName(index: number, editMode: boolean) {
    const position: number = index + 1;
    const isOptional: boolean =
      position > eventTeamService.getOptionsMin(this.eventTeamHeader);
    if (isOptional) {
      const substituteName = eventTeamService.getOptionsTeamSubstituteName(
        this.eventTeamHeader
      );
      return (
        (substituteName === "" ? "Sub" : substituteName) +
        (editMode ? " (Optional)" : "")
      );
    } else {
      return this.getTeamPositionNameReadMode(index);
    }
  }

  public get getTeamPositionName() {
    return eventTeamService.getOptionsTeamPositionName(this.eventTeamHeader);
  }

  public getTeamPositionNameReadMode(index: number) {
    const position: number = index + 1;
    return this.getTeamPositionName.length > 0
      ? this.getTeamPositionName + " " + position
      : position;
  }

  public validate() {
    console.log("CompEventTeam.submitCompEventTeam()");
    const compEventTeam = schoolTeamService.stripEmptyAthletes(
      this.compEventTeam
    );
    this.userMessages = [];
    const userMessages: IUserMessage[] =
      schoolTeamService.validateCompEventTeam(
        this.eventTeamHeader,
        compEventTeam,
        this.competition,
        this.eventTeamHeaders
      );
    this.userMessages = userMessages;
  }

  public onTeamNameChangedSchool(teamName: string) {
    this.onTeamNameChanged(teamName);
    this.validate();
  }

  public submitCompEventTeam() {
    console.log("CompEventTeam.submitCompEventTeam()");
    const compEventTeam = schoolTeamService.stripEmptyAthletes(
      this.compEventTeam
    );
    this.validate();
    if (this.userMessages.length > 0) {
      return;
    }
    this.$emit("submit", compEventTeam);
  }

  public addUserCart() {
    console.log("SchoolTeamForm.addUserCart()", this.compEventTeam);
    this.$emit("addUserCart", this.compEventTeam);
  }
}
</script>
