<template>
  <div>
      <span>Max Events:</span>
      <span v-text="rules.maxEvents"></span>

      <span>Max Comp Events:</span>
      <span v-text="rules.maxCompEvents"></span>

      <span>Max Field:</span>
      <span v-text="rules.maxField"></span>

      <span>Max Track:</span>
      <span v-text="rules.maxTrack"></span>

      Other: 
      <span v-for="uni in rules.unique" :key="uni.text">{{uni.text}}</span>
  </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";

import {ICompShedRuleOptions} from "./athletecompsched-models";

@Component({
    name: "comp-rules",
    props: {
        rules: {
            type: Object as () => ICompShedRuleOptions
        }
    }
})
export default class CompRules extends Vue {

}
</script>