import {
  IMultiEventEventDef,
  IMultiEventOptions,
  ScoringSystem,
  ScoringSystemOptionsMap,
} from "../athletecompsched-models";
import { reactive } from "@vue/composition-api";
import {
  simpleClone,
  uniqueArrayById,
} from "../../common/common-service-utils";
import { GenderType } from "../../common/common-models";
import { IEventDef } from "../../eventdef/eventdef-models";
import { CompEventData } from "../../compevent/compevent-data";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";

export interface IMultiEVentOptionsControllerInput {
  genders: GenderType[];
}

export interface IMultiEVentOptionsControllerState {
  scoringSystemOptions: ScoringSystem[];
  multiEventOptions: IMultiEventOptions;
}

export function useMultiEVentOptionsController(
  multiEVentOptionsControllerInput: IMultiEVentOptionsControllerInput
) {
  const state = reactive<IMultiEVentOptionsControllerState>({
    scoringSystemOptions: Object.values(ScoringSystemOptionsMap),
    multiEventOptions: {
      childEvents: [],
    },
  });

  const uiState = reactive({
    isLoading: false,
  });

  function setMultiOptions(multiEventOptions: IMultiEventOptions) {
    state.multiEventOptions = simpleClone(multiEventOptions);
  }

  function addDiscipline(eventDef: IEventDef) {
    if (eventDef.id === 0) {
      return;
    }

    let childEvents: IMultiEventEventDef[] = simpleClone(
      state.multiEventOptions.childEvents
    );

    const eventDefToAdd: IMultiEventEventDef = {
      eventDefId: eventDef.id,
      egId: 0, //  the Event Group Id has not been created by back end yet
      name: eventDef.name,
    };

    childEvents.push(eventDefToAdd);

    //  ensure no duplicates in childEvents
    childEvents = uniqueArrayById<IMultiEventEventDef>(
      childEvents,
      "eventDefId"
    );

    state.multiEventOptions.childEvents = childEvents;
  }

  function removeEvents(childEvent: IMultiEventEventDef): Promise<void> {
    uiState.isLoading = true;

    const childEventsCalc: IMultiEventEventDef[] = simpleClone(
      state.multiEventOptions.childEvents
    ).filter((ce) => {
      return ce.eventDefId !== childEvent.eventDefId;
    });

    if (childEvent.egId === 0) {
      state.multiEventOptions.childEvents = childEventsCalc;
      uiState.isLoading = false;
      return Promise.resolve();
    }

    const prom = deleteEvent(childEvent);
    handleResponseMessages(prom);

    return prom
      .then((resp) => {
        if (resp.errNo > 0) {
          messageDispatchHelper(
            resp.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }

        state.multiEventOptions.childEvents = childEventsCalc;
      })
      .finally(() => {
        uiState.isLoading = false;
      });
  }

  function deleteEvent(multiEventEventDef: IMultiEventEventDef) {
    return new CompEventData().deleteEventGroup(multiEventEventDef.egId);
  }

  return {
    state,
    uiState,
    setMultiOptions,
    addDiscipline,
    removeEvents,
  };
}
