<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div
      v-for="childEvent in childEvents"
      :key="childEvent.id"
      class="e4s-flex-row e4s-gap--large e4s-flex-center"
    >
      <ButtonGenericV2
        button-type="destructive"
        text="X"
        style="width: 35px"
        v-on:click="removeEvent(childEvent)"
      />
      <div v-text="childEvent.name"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { IMultiEventEventDef } from "../athletecompsched-models";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";

export default defineComponent({
  name: "MultiEventOptionsTable",
  components: { ButtonGenericV2 },
  props: {
    childEvents: {
      type: Array as PropType<IMultiEventEventDef[]>,
      default: () => {
        return [];
      },
    },
  },
  setup(props: { childEvents: IMultiEventEventDef[] }, context: SetupContext) {
    function removeEvent(multiEventEventDef: IMultiEventEventDef) {
      context.emit("remove", multiEventEventDef);
    }

    return {
      removeEvent,
    };
  },
});
</script>
