import { AthleteCompSchedService } from "../../athletecompsched-service";
import { simpleClone } from "../../../common/common-service-utils";
import {
  athleteMaxEntered,
  maxEnteredAgeInfo,
  maxEnteredCompetition,
  maxEnteredCompRules,
  maxEnteredEvents,
  maxEnteredPbKey,
  maxEnteredUserApplication,
  maxEnteredUserInfo,
} from "./maxentered-mock";
import {
  EVENT_TYPE,
  IAthleteCompSchedRuleEvent,
} from "../../athletecompsched-models";

const athleteCompSchedService: AthleteCompSchedService =
  new AthleteCompSchedService();

describe("MaxEntered", () => {
  test("processEvents", () => {
    const results = athleteCompSchedService.processEvents(
      simpleClone(athleteMaxEntered),
      simpleClone(maxEnteredPbKey),
      simpleClone(maxEnteredCompRules),
      null,
      [],
      simpleClone(maxEnteredEvents),
      simpleClone(maxEnteredUserApplication),
      simpleClone(maxEnteredAgeInfo),
      simpleClone(maxEnteredCompetition),
      simpleClone(maxEnteredUserInfo)
    );

    expect(results.length).toBe(5);
  });

  test("compRuleTrackFieldMaxInDay", () => {
    let events = simpleClone(maxEnteredEvents);

    const results: Record<EVENT_TYPE, number> = simpleClone(events).reduce(
      (acc, evt) => {
        if (evt.tf === EVENT_TYPE.TRACK) {
          acc[EVENT_TYPE.TRACK] = (acc[EVENT_TYPE.TRACK] || 0) + 1;
        } else if (evt.tf === EVENT_TYPE.FIELD) {
          acc[EVENT_TYPE.FIELD] = (acc[EVENT_TYPE.FIELD] || 0) + 1;
        }
        return acc;
      },
      {} as Record<EVENT_TYPE, number>
    );

    // three track events, two field events.
    expect(results.T).toBe(3);
    expect(results.F).toBe(2);

    const isEventSelectedOrInCart = events.filter((evt) => {
      return athleteCompSchedService.isEventSelectedOrInCart(evt);
    });
    expect(isEventSelectedOrInCart.length).toBe(1);
    expect(isEventSelectedOrInCart[0].tf).toBe("T");

    const compRules = simpleClone(maxEnteredCompRules);

    //  can potentially enter 1 track event.
    expect(compRules.options.maxCompTrack).toBe(1);

    const dateIso = isEventSelectedOrInCart[0].startdate;
    expect(dateIso).toBe("2024-03-31T00:00:00+01:00");

    let event200m: IAthleteCompSchedRuleEvent | null | undefined;
    event200m = events.find((evt) => {
      return evt.Name === "200m";
    });

    expect(event200m?.Name).toBe("200m");

    const getEventsEntered = athleteCompSchedService.getEventsEntered(
      "T" as EVENT_TYPE,
      events
    );

    expect(getEventsEntered.length).toBe(1);

    let getEventsEnteredForDateTest =
      athleteCompSchedService.getEventsEnteredForDate(dateIso, events);

    expect(getEventsEnteredForDateTest.length).toBe(1);

    getEventsEnteredForDateTest =
      athleteCompSchedService.getEventsEnteredForDate("", events);

    expect(getEventsEnteredForDateTest.length).toBe(1);

    const currentEnteredTrackFieldEvents =
      athleteCompSchedService.getTrackFieldEventsEntered(
        dateIso,
        "T" as EVENT_TYPE,
        events
      );

    expect(currentEnteredTrackFieldEvents.length).toBe(1);

    const compRuleTrackFieldMaxInDayTest =
      athleteCompSchedService.compRuleTrackFieldMaxInDayTest(
        simpleClone(maxEnteredCompRules),
        "T" as EVENT_TYPE,
        dateIso,
        event200m!,
        events
      );

    expect(compRuleTrackFieldMaxInDayTest.eventsInternal.length).toBe(5);

    event200m = compRuleTrackFieldMaxInDayTest.eventsInternal.find(
      (evt: IAthleteCompSchedRuleEvent) => {
        return evt.Name === "200m";
      }
    );
    expect(event200m?.Name).toBe("200m");
    expect(event200m?.ruleIsDisabledBy).toBe(true);

    let compRuleTrackFieldMaxInDay: IAthleteCompSchedRuleEvent[];
    compRuleTrackFieldMaxInDay =
      athleteCompSchedService.compRuleTrackFieldMaxInDay(
        simpleClone(maxEnteredCompRules),
        "T" as EVENT_TYPE,
        dateIso,
        event200m!,
        events
      );

    expect(compRuleTrackFieldMaxInDay.length).toBe(5);

    event200m = compRuleTrackFieldMaxInDay.find(
      (evt: IAthleteCompSchedRuleEvent) => {
        return evt.Name === "200m";
      }
    );
    expect(event200m?.Name).toBe("200m");
    expect(event200m?.ruleIsDisabledBy).toBe(true);

    let discus: IAthleteCompSchedRuleEvent | null | undefined;
    discus = events.find((evt) => {
      return evt.Name === "Discus";
    });
    expect(discus?.Name).toBe("Discus");
    expect(discus?.ruleIsDisabledBy).toBe(false);

    discus = simpleClone(discus);

    discus!.order.productId = 1234;

    events = events.map((evt) => {
      if (evt.Name === "Discus") {
        return discus!;
      } else {
        return evt;
      }
    });

    let hammer: IAthleteCompSchedRuleEvent | null | undefined;
    hammer = events.find((evt) => {
      return evt.Name === "Hammer";
    });

    compRuleTrackFieldMaxInDay =
      athleteCompSchedService.compRuleTrackFieldMaxInDay(
        simpleClone(maxEnteredCompRules),
        "T" as EVENT_TYPE,
        dateIso,
        hammer!,
        events
      );

    expect(compRuleTrackFieldMaxInDay.length).toBe(5);

    discus = compRuleTrackFieldMaxInDay.find((evt) => {
      return evt.Name === "Discus";
    });
    expect(discus?.Name).toBe("Discus");
    expect(discus?.ruleIsDisabledBy).toBe(false);

    hammer = compRuleTrackFieldMaxInDay.find((evt) => {
      return evt.Name === "Hammer";
    });
    expect(hammer?.Name).toBe("Hammer");
  });

  test("compRuleTrackFieldMaxInDay 2", () => {
    let events = simpleClone(maxEnteredEvents);
    const dateIso = events[0].startdate;

    let isEventSelectedOrInCart = events.filter((evt) => {
      return athleteCompSchedService.isEventSelectedOrInCart(evt);
    });

    expect(isEventSelectedOrInCart.length).toBe(1);

    const defaultOrder = {
      orderId: 0,
      productId: 0,
      e4sLineValue: 0,
      wcLineValue: 0,
      isRefund: false,
      refundValue: 0,
      isCredit: false,
      creditValue: 0,
      discountId: 0,
      dateOrdered: "",
    };

    events = events.map((evt) => {
      evt.userSelected = false;
      evt.order = simpleClone(defaultOrder);
      return evt;
    });

    isEventSelectedOrInCart = events.filter((evt) => {
      return athleteCompSchedService.isEventSelectedOrInCart(evt);
    });
    expect(isEventSelectedOrInCart.length).toBe(0);

    events = events.map((evt) => {
      if (evt.Name === "200m") {
        evt.userSelected = true;
        evt.order.productId = 1234;
      }
      if (evt.Name === "100m") {
        evt.userSelected = true;
        evt.order.productId = 12345;
      }
      return evt;
    });

    isEventSelectedOrInCart = events.filter((evt) => {
      return athleteCompSchedService.isEventSelectedOrInCart(evt);
    });
    expect(isEventSelectedOrInCart.length).toBe(2);

    let tr400m: IAthleteCompSchedRuleEvent | null | undefined;
    tr400m = events.find((evt) => {
      return evt.Name === "400m";
    });
    expect(tr400m?.ruleIsDisabledBy).toBe(false);

    const compRuleTrackFieldMaxInDay =
      athleteCompSchedService.compRuleTrackFieldMaxInDay(
        simpleClone(maxEnteredCompRules),
        "T" as EVENT_TYPE,
        dateIso,
        tr400m!,
        events
      );

    tr400m = compRuleTrackFieldMaxInDay.find((evt) => {
      return evt.Name === "400m";
    });
    expect(tr400m?.ruleIsDisabledBy).toBe(true);
  });
});
