<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <LoadingSpinnerV2 v-if="isLoading" />

    <FormGenericSectionHeader value="Add to User Cart" />

    <FormGenericInputTemplateV2
      form-label="Select user"
      :is-required-field="true"
    >
      <UserTypeAhead slot="field" @onSelected="onUserSelected" />
    </FormGenericInputTemplateV2>

    <div
      v-if="userProfile.user.id > 0"
      class="e4s-flex-column e4s-gap--standard"
    >
      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputTextV2
            form-label="Display Name"
            :value="userProfile.user.displayName"
            :is-disabled="true"
          />
          <FormGenericInputTextV2
            form-label="Email"
            :value="userProfile.user.email"
            :is-disabled="true"
          />
        </template>
      </FormGenericFieldGridV2>

      <FormGenericFieldGridV2 v-if="!onlyAddToUserCart">
        <template slot="content">
          <FormGenericInputTemplateV2
            :form-label="'User Areas (' + userProfile.areas.length + ')'"
          >
            <!--a select displaying any user clubs-->
            <FieldSelectV2
              slot="field"
              :is-disabled="userProfile.areas.length === 0"
              :data-array="userProfile.areas"
              :value="userEntityArea"
              @input="onAreaSelected"
            >
              <template slot-scope="{ obj }">
                {{ obj.name }}
              </template>
            </FieldSelectV2>
          </FormGenericInputTemplateV2>

          <FormGenericInputTemplateV2
            :form-label="'User Clubs (' + userProfile.clubs.length + ')'"
          >
            <!--a select displaying any user clubs-->
            <FieldSelectV2
              slot="field"
              :is-disabled="userProfile.clubs.length === 0"
              :data-array="userProfile.clubs"
              :value="userEntityClub"
              @input="onClubSelected"
            >
              <template slot-scope="{ obj }">
                {{ obj.clubName }}
              </template>
            </FieldSelectV2>
          </FormGenericInputTemplateV2>

          <FormGenericInputTemplateV2
            :form-label="
              'Club Competitions (' + getUserProfileCompClubs.length + ')'
            "
          >
            <FieldSelectV2
              slot="field"
              :is-disabled="getUserProfileCompClubs.length === 0"
              :data-array="getUserProfileCompClubs"
              :value="userEntityClubComp"
              @input="onClubCompSelected"
            >
              <template slot-scope="{ obj }">
                {{ obj.club ? obj.club.name : "" }}
              </template>
            </FieldSelectV2>
          </FormGenericInputTemplateV2>
        </template>
      </FormGenericFieldGridV2>
    </div>

    <div
      v-if="validationMessage.length > 0"
      class="e4s-validation-message e4s-text--error"
      v-text="validationMessage"
    />

    <div class="e4s-flex-row">
      <div class="e4s-flex-row--end e4s-gap--standard">
        <ButtonGenericV2 button-type="tertiary" @click="cancel" text="Cancel" />

        <ButtonGenericV2
          v-if="!onlyAddToUserCart"
          @click="acceptUserAndEntity"
          text="Accept"
          class="e4s-button--auto"
        />

        <ButtonGenericV2
          v-if="onlyAddToUserCart"
          @click="doOnlyAddToUserCart"
          text="Add to User Cart"
          class="e4s-button--auto"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  SetupContext,
  computed,
} from "@vue/composition-api";
import UserTypeAhead from "../../../admin/user/user-type-ahead.vue";
import {
  IUserClubComp,
  IUserProfile,
  IUserSummary,
} from "../../../admin/user/user-models";
import { IAthleteUser } from "../../../athlete/athlete-models";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
import { IBaseConcrete, IServerResponse } from "../../../common/common-models";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import FormGenericSectionHeader from "../../../common/ui/layoutV2/form/form-generic-section-header.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import { UserProfileData } from "../../../admin/user/user-data";
import { simpleClone } from "../../../common/common-service-utils";
import FieldSelectV2 from "../../../common/ui/layoutV2/fields/field-select-v2.vue";
import { UserService } from "../../../admin/user/user-service";
import FormGenericInputTextV2 from "../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FormGenericFieldGridV2 from "../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import { ENTITY_TYPE, IEntity } from "../../../config/config-app-models";
import { AddToUserCartOutput } from "./add-to-user-cart-models";
import { AthleteCompSchedData } from "../../athletecompsched-data";
import * as UserServiceV2 from "../../../admin/user/v2/user-service-v2";

export default defineComponent({
  name: "AddToUserCart",
  components: {
    FormGenericFieldGridV2,
    FormGenericInputTextV2,
    FieldSelectV2,
    FormGenericInputTemplateV2,
    FormGenericSectionHeader,
    LoadingSpinnerV2,
    ButtonGenericV2,
    UserTypeAhead,
  },
  props: {
    productId: {
      type: String,
      required: true,
    },
    onlyAddToUserCart: {
      type: Boolean,
      default: false,
    },
  },
  setup(
    props: { productId: string; onlyAddToUserCart: boolean },
    context: SetupContext
  ) {
    const userService = new UserService();

    const userSummary = ref<IBaseConcrete>({
      id: 0,
      name: "",
    });

    const userProfile = ref<IUserProfile>(userService.factoryUserProfile());

    const isLoading = ref(false);
    const validationMessage = ref("");

    const userEntityArea = ref<IEntity>(factoryEntity("County"));

    const userEntityClub = ref<IEntity>(factoryEntity("Club"));

    function factoryEntity(entityType: ENTITY_TYPE): IEntity {
      return {
        name: "",
        entityLevel: entityType === "County" ? 2 : 1,
        id: 0,
        entityName: entityType,
        clubType: entityType === "Club" ? "C" : "",
      };
    }

    /**
     *
     * @param user
     */
    function onUserSelected(user: IUserSummary | IAthleteUser) {
      userSummary.value = {
        id: user.id,
        name: user.email,
      };

      isLoading.value = true;
      const userProfileData: UserProfileData = new UserProfileData();
      userProfileData
        .getUserProfileById(user.id)
        .then((response: IServerResponse<IUserProfile>) => {
          if (response.errNo > 0) {
            messageDispatchHelper(
              response.error,
              USER_MESSAGE_LEVEL.ERROR.toString()
            );
            return;
          }
          userProfile.value = simpleClone(response.data);
          return;
        })
        .catch((error) => {
          messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
          return {};
        })
        .finally(() => {
          isLoading.value = false;
        });
    }

    function onAreaSelected(area: IEntity) {
      userEntityClub.value = factoryEntity("Club");
      userEntityArea.value = simpleClone(area);
    }

    function onClubSelected(club: unknown) {
      // club looks like this.
      // {
      //   "id": 10,
      //   "clubName": "Aberdare Valley A.C.",
      //   "region": "South Wales",
      //   "country": "Wales",
      //   "areaId": 49
      // }

      userEntityArea.value = factoryEntity("County");

      userEntityClub.value = {
        id: (club as IEntity).id,
        name: (club as any).clubName,
        entityLevel: 1,
        entityName: "Club",
        clubType: "C",
      };
    }

    function onClubCompSelected(clubComp: unknown) {
      // Reset other selections
      userEntityArea.value = factoryEntity("Club");
      // userEntityClub.value = factoryEntity("Club");

      // Cast the unknown clubComp to expected type
      const selectedClubComp = clubComp as IUserClubComp;

      userEntityClub.value = {
        id: selectedClubComp.clubCompId, // or would you pass the club id?
        name: selectedClubComp.club.name,
        entityLevel: 1,
        entityName: "Club",
        clubType: "C",
      };
    }

    /**
     *
     */
    function cancel() {
      context.emit("cancel");
    }

    /**
     *
     */
    function acceptUserAndEntity() {
      // context.emit("submit", userSummary.value);

      validationMessage.value = "";

      if (userProfile.value.user.id === 0) {
        validationMessage.value = "Please select a user.";
        return;
      }

      const output: AddToUserCartOutput = {
        userProfile: userProfile.value,
        userEntity: userEntityClub.value,
      };
      context.emit("input", simpleClone(output));
    }

    /**
     *
     */
    function doOnlyAddToUserCart() {
      // context.emit("submit", userSummary.value);

      validationMessage.value = "";

      if (userProfile.value.user.id === 0) {
        validationMessage.value = "Please select a user.";
        return;
      }

      isLoading.value = true;
      new AthleteCompSchedData()
        .addEntryToUsersCart(Number(props.productId), userSummary.value.id)
        .then((response) => {
          if (response.errNo > 0) {
            messageDispatchHelper(
              response.error,
              USER_MESSAGE_LEVEL.ERROR.toString()
            );
            return;
          }
          context.emit("addedToUserCart");
        })
        .catch((error) => {
          messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
          return {};
        })
        .finally(() => {
          isLoading.value = false;
        });
    }

    const getUserProfileCompClubs = computed<IUserClubComp[]>(() => {
      return UserServiceV2.getUserProfileCompClubs(userProfile.value);
    });

    return {
      userSummary,
      userProfile,
      isLoading,
      validationMessage,
      userEntityArea,
      userEntityClub,
      getUserProfileCompClubs,

      onUserSelected,
      onClubSelected,
      onAreaSelected,
      onClubCompSelected,

      cancel,
      acceptUserAndEntity,
      doOnlyAddToUserCart,
    };
  },
});
</script>
