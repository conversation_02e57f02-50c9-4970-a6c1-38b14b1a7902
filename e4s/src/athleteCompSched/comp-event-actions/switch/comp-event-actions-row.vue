<template>
    <div class="event-card">
        <div class="row">
            <div class="col s6 m4 l4">
                <div
                    class="event-card-checkbox"
                    @click="userSelecting"
                    v-html="getCheckBox(compEvent)"
                    v-model="compEvent.userSelected"
                ></div>
                <span class="event-card-date" v-text="getEventDate"></span>
                <span class="event-card-date-sep">@</span>
                <span class="event-card-time" v-text="getEventTime"></span>
            </div>

            <div class="col s6 m4 l4">
                <span v-text="getCompEventName"></span>
                <i v-if="athleteCompSchedService.isTeamEvent(compEvent)" class="tiny material-icons">group</i>
            </div>

            <div class="col s6 m4 l4" v-if="compEvent.ruleMessage">
                <span v-text="compEvent.ruleMessage"></span>
            </div>
        </div>
    </div>
</template>


<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {Prop} from "vue-property-decorator";
import {IAthleteCompSchedRuleEvent} from "../../athletecompsched-models"
import { AthleteCompSchedService } from "../../athletecompsched-service";
import {messageDispatchHelper} from "../../../user-message/user-message-store"
import {USER_MESSAGE_LEVEL} from "../../../user-message/user-message-models"

const athleteCompSchedService: AthleteCompSchedService = new AthleteCompSchedService();

@Component({
    name: "comp-event-actions-row",
    components: {
    },
    computed: {
    }
})
export default class CompEventActionsRow extends Vue {

    @Prop({
        default: () => {
            return athleteCompSchedService.factoryAthleteCompSchedRuleEvent();
        }
    })
    public readonly compEvent!: IAthleteCompSchedRuleEvent;

    @Prop({
        default: false
    })
    public isDisabled!: boolean;

    @Prop({
        default: false
    })
    public readonly hasBuilderPermissionForComp!: boolean;

    public athleteCompSchedService: AthleteCompSchedService = new AthleteCompSchedService();

    public getCheckBox(compEvent: IAthleteCompSchedRuleEvent) {
        if (compEvent.userSelected) {
            return "<input type='checkbox' checked='checked' /><span></span>";
        }
        // if (this.hasBuilderPermissionForComp) {
        //     return "<input type='checkbox' /><span></span>";
        // }
        // if (compEvent.userEventAction) {
        //     return "<input type='checkbox' disabled='disabled' /><span></span>";
        // }
        // if (compEvent.ruleIsDisabledBy) {
        //     return "<input type='checkbox' disabled='disabled' /><span></span>";
        // }
        // if (!compEvent.ruleIsDisabledBy && !compEvent.userSelected) {
        //     return "<input type='checkbox' /><span></span>";
        // }

        if (this.isDisabled) {
            return "<input type='checkbox' disabled='disabled' /><span></span>";
        }

        return "<input type='checkbox' /><span></span>";
    }

    public get getDisabledMessage(): string {
        if (this.compEvent.userSelected) {
            return "Already selected";
        }
        if (this.compEvent.ruleIsDisabledBy) {
            return this.compEvent.ruleMessage;
        }
        return "";
    }

    public get getEntryCountDisplay() {
        return this.compEvent.entrycnt + (this.compEvent.maxathletes > 0 ? "/" + this.compEvent.maxathletes : "");
    }

    public get getEventDate() {
        return this.athleteCompSchedService.getEventDate(this.compEvent);
    }

    public get getEventTime() {
        return this.athleteCompSchedService.getEventTime(this.compEvent);
    }

    public get getCompEventName() {
        const hasUpScaling = this.compEvent.ceoptions.ageGroups.length > 0;
        return this.compEvent.eventGroup + (
            hasUpScaling && this.compEvent.ageGroup && this.compEvent.ageGroup.shortName ?
                ": " +  this.compEvent.ageGroup.shortName :
                ""
        );
    }

    public userSelecting() {
        console.log("CompEventActionsRow.userSelecting()...");
        if (this.isDisabled) {
            console.log("CompEventActionsRow.userSelecting()...disabled");
            return;
        }
        console.log("CompEventActionsRow.userSelecting()...enabled");
        if (this.compEvent.userEventAction) {
            console.log("CompEventActionsRow.userSelecting()...userEventAction");
            messageDispatchHelper("Event Switch: this is the same event that is being switched, " +
                "select another event.", USER_MESSAGE_LEVEL.WARN.toString());
            return;
        }
        console.log("CompEventActionsRow.userSelecting()...$emit");
        const compEvent = R.clone(this.compEvent);
        compEvent.userSelected = !compEvent.userSelected;
        this.$emit("onEventSelected", compEvent);
    }

}
</script>

<style scoped>

.event-card {
    padding: 0.25em;
}

.event-selected {
    background-color: #e1eefd;
}

</style>
