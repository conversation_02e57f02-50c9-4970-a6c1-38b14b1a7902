<template>
    <div>
        <div class="row" v-if="isLoading">
            <div class="col s12 m12 l12">
                Loading...<LoadingSpinner></LoadingSpinner>
                <CloseIcon v-on:click="close" class="right"></CloseIcon>
            </div>
        </div>
        <CompEventActions
            v-if="isDataLoaded"
            :comp-event-actions-result="compEventActionsResult"
            v-on:close="close"
        ></CompEventActions>
    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {CompEventActionsController, ICompEventActionsResult} from "./comp-event-actions-controller"
import CompEventActions from "./comp-event-actions.vue"
import {mapState} from "vuex"
import {CONFIG_STORE_CONST, IConfigStoreState} from "../../config/config-store"
import {IConfigApp} from "../../config/config-app-models"
import {IAthlete} from "../../athlete/athlete-models"
import {IAthleteCompSchedRuleEvent} from "../athletecompsched-models"
import CloseIcon from "../../common/ui/close-icon.vue"
import {CompetitionData} from "../../competition/competition-data"
import {handleResponseMessages} from "../../common/handle-http-reponse"


@Component({
    name: "comp-event-actions-wrapper-schedule",
    components: {CloseIcon, CompEventActions},
    computed: {
        ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
            configApp: (state: IConfigStoreState) => state.configApp
        })
    }
})
export default class CompEventActionsWrapperSchedule extends Vue {
    public readonly configApp!: IConfigApp;

    @Prop({
        required: true
    })
    public readonly compId: number;

    @Prop({
        required: true
    })
    public readonly compEvent!: IAthleteCompSchedRuleEvent;

    @Prop({
        required: true
    })
    public readonly compEvents!: IAthleteCompSchedRuleEvent[];

    @Prop({
        required: true
    })
    public readonly athlete: IAthlete;

    public isLoading = true;
    public compEventActionsController: CompEventActionsController = new CompEventActionsController();
    public compEventActionsResult: ICompEventActionsResult = this.compEventActionsController.factoryCompEventActionsResult();

    public created() {
        this.loadData();
    }

    public get isDataLoaded(): boolean {
        return this.compEventActionsResult.competitionSummaryPublic.compId > 0;
    }

    public loadData() {
        this.isLoading = true;
        const compEventActionsResult: ICompEventActionsResult = this.compEventActionsController.factoryCompEventActionsResult();
        compEventActionsResult.compEvent = R.clone(this.compEvent);
        compEventActionsResult.compEvents = R.clone(this.compEvents);
        compEventActionsResult.athlete = R.clone(this.athlete);
        compEventActionsResult.userApplication = this.configApp.userInfo.user;

        const promComp = new CompetitionData().getCompById(this.compId);
        handleResponseMessages(promComp);
        promComp
            .then((response) => {
                if (response.errNo === 0) {
                    compEventActionsResult.competitionSummaryPublic = response.data;
                    this.compEventActionsResult = compEventActionsResult;
                    this.isLoading = false;
                }
            });
    }

    public close(requiresParentReloadOnClose: boolean) {
        this.$emit("close", requiresParentReloadOnClose);
    }

}
</script>
