import {IAthleteCompSchedRuleEvent} from "../../../athletecompsched-models"
import {ICompetitionSummaryPublic} from "../../../../competition/competition-models"
import {IUserSummary} from "../../../../admin/user/user-models"

export interface IAddCartEventState {
  showConfirm: boolean;
  userSummary: IUserSummary;
  isLoading: boolean;
}

export interface IAddCartEventInputParams {
  isAdmin: boolean;
  hasBuilderPermissionForComp: boolean;
  compEvent: IAthleteCompSchedRuleEvent;
  competitionSummaryPublic: ICompetitionSummaryPublic;
}

export interface IAddCartEventUserOutputPayload {
  compEvent: IAthleteCompSchedRuleEvent;
  reason: string;
}
