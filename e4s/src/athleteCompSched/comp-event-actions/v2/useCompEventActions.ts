import { computed, reactive } from "@vue/composition-api";
import { ICompEventActionsResult } from "../comp-event-actions-controller";
import {
  useConfigController,
  useConfigStore,
} from "../../../config/useConfigStore";
import * as CompEventActionsServiceV2 from "./comp-event-actions-service-v2";
import {
  getEventTitle,
  ICompEventActionsState, ICompEventActionsStatePerms,
  showSectionType,
} from "./comp-event-actions-service-v2"
import {eventDateTimeDisplay, simpleClone} from "../../../common/common-service-utils"
import { ISetPaidEventInputParams } from "./setpaid/comp-event-setpaid-models";
import { IAddCartEventInputParams } from "./addcart/comp-event-addcart-models";
import {PillV2StatusType} from "../../../common/ui/layoutV2/pills/pills-models-v2"

export function useCompEventActions() {
  const configStore = useConfigStore();
  const configController = useConfigController();

  const state = reactive<ICompEventActionsState>(
    CompEventActionsServiceV2.factoryCompEventActionsState()
  );

  function init(compEventActionsResult: ICompEventActionsResult, permsOverride?: ICompEventActionsStatePerms) {
    Object.assign(
      state,
      CompEventActionsServiceV2.init(compEventActionsResult, configStore, permsOverride)
    );
  }

  const access = computed( () => {
    if(state.perms.isAdmin) {
      return "Admin";
    }
    if(state.perms.hasBuilderPermission) {
      return "Builder";
    }
    if(state.perms.belongsToUser) {
      return "Owner";
    }
    return "NA";
  })

  const eventTitle = computed(() => {
    // return (
    //   state.compEventActionsResult.compEvent.eventGroup +
    //   " " +
    //   eventDateTimeDisplay(state.compEventActionsResult.compEvent.startdate)
    // );
    return getEventTitle(state.compEventActionsResult.compEvent);
  });

  const getPillType = computed<PillV2StatusType>(() => {
    if (state.eventFlags.isPaidFor) {
      return "success";
    }
    return "error";
  });

  const getPillMessage = computed(() => {
    return (
      access.value + " : " +
      (state.compFlags.isEntryOpen ? "Open" : "Closed") + " / " +
      (state.eventFlags.isPaidFor
        ? "Paid"
        : state.compEventActionsResult.compEvent.entryId > 0
        ? "In Cart"
        : "")
    );
  });

  function setShowSection(showSection: showSectionType) {
    state.showSection = showSection;
    if (showSection === "CANCEL") {
      showCancelSection();
    }
  }

  function showCancelSection() {
    state.showSection = "CANCEL";
    state.cancelEvent.input = {
      isAdmin: state.perms.isAdmin,
      hasBuilderPermissionForComp: state.perms.hasBuilderPermission,
      compEvent: simpleClone(state.compEventActionsResult.compEvent),
      competitionSummaryPublic: simpleClone(
        state.compEventActionsResult.competitionSummaryPublic
      ),
    };
  }

  const getSetPaidInput = computed<ISetPaidEventInputParams>(() => {
    return {
      isAdmin: state.perms.isAdmin,
      hasBuilderPermissionForComp: state.perms.hasBuilderPermission,
      compEvent: simpleClone(state.compEventActionsResult.compEvent),
      competitionSummaryPublic: simpleClone(
        state.compEventActionsResult.competitionSummaryPublic
      ),
    };
  });

  const getAddCartInput = computed<IAddCartEventInputParams>(() => {
    return {
      isAdmin: state.perms.isAdmin,
      hasBuilderPermissionForComp: state.perms.hasBuilderPermission,
      compEvent: simpleClone(state.compEventActionsResult.compEvent),
      competitionSummaryPublic: simpleClone(
        state.compEventActionsResult.competitionSummaryPublic
      ),
    };
  });

  const getOrderTime = computed(() => {
    const dateOrdered = state.compEventActionsResult.compEvent.order.dateOrdered;
    if (dateOrdered === "") {
      return "NA";
    }
    return eventDateTimeDisplay(dateOrdered);
  });

  return {
    state,
    configController,
    configStore,
    init,
    eventTitle,
    setShowSection,
    getSetPaidInput,
    getAddCartInput,
    getPillType,
    getPillMessage,
    getOrderTime
  };
}
