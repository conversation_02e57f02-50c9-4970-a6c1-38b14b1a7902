<template>
  <div>
    <FormGenericFieldGridV2>
      <template slot="content">
        <div></div>
      </template>
    </FormGenericFieldGridV2>

    <p>Please enter a reason why cancelling (required).</p>
    <InputRestrictLength
      slot="content"
      :use-text-area="true"
      :max-length="600"
      text-area-class="browser-default e4s-input-field e4s-input-field--primary ask-organiser--text-area"
      v-on:isMaxLength="isMaxLength = $event"
      v-on:onChanged="state.reason = $event"
    >
    </InputRestrictLength>
    <FormGenericInputErrorMessageV2 :error-message="errorMessages.reason" />

    <ButtonGroupSectionV2>
      <ButtonGenericV2
        slot="buttons-left"
        button-type="tertiary"
        v-on:click="cancel"
        text="Cancel"
      />

      <div slot="buttons-right">
        <ButtonGenericV2
          button-type="primary"
          v-on:click="askForConfirmation"
          text="Submit"
          v-if="!state.showConfirm"
        />
        <ButtonGenericV2
          button-type="primary"
          class="e4s-button--destructive"
          v-on:click="submit"
          text="Confirm"
          v-if="state.showConfirm"
        />
      </div>

    </ButtonGroupSectionV2>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  reactive,
  SetupContext,
} from "@vue/composition-api";
import InputRestrictLength from "../../../../common/ui/field/input-restrict-length/input-restrict-length.vue";
import FormGenericInputErrorMessageV2 from "../../../../common/ui/layoutV2/form/form-generic-input-error-message-v2.vue";
import FormGenericSectionTitleV2 from "../../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import {
  ICancelEventUserInputParams,
  ICancelEventUserOutputPayload,
  ICancelEventUserState
} from "./cancel-event-models-v2"
import FormGenericFieldGridV2 from "../../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import ButtonGroupSectionV2 from "../../../../common/ui/layoutV2/buttons/button-group-section-v2.vue"

export default defineComponent({
  name: "cancel-event-user-v2",
  components: {
    ButtonGroupSectionV2,
    ButtonGenericV2,
    FormGenericFieldGridV2,
    FormGenericSectionTitleV2,
    FormGenericInputErrorMessageV2,
    InputRestrictLength,
  },
  props: {
    cancelEventInputParams: {
      type: Object as PropType<ICancelEventUserInputParams>,
      required: true,
    },
  },
  setup(
    props: { cancelEventInputParams: ICancelEventUserInputParams },
    context: SetupContext
  ) {
    const errorMessages = reactive({
      reason: "",
    });

    const state = reactive<ICancelEventUserState>({
      showConfirm: false,
      reason: ""
    });

    /*
    watch(
      () => props.athlete,
      (newValue: IAthlete) => {
        athleteForm.init(newValue);
      },
      {
        immediate: true,
      }
    );
     */

    function doValidation(): boolean {
      errorMessages.reason = "";

      if (state.reason.length === 0) {
        errorMessages.reason = "Please enter a reason."
        return false;
      }
      return true;
    }

    function askForConfirmation() {

      if (!doValidation()) {
        return
      }

      state.showConfirm = true;
    }

    function cancel() {
      context.emit("cancel");
    }

    function submit() {

      if (!doValidation()) {
        return
      }

      const cancelEventOutputPayload: ICancelEventUserOutputPayload = {
        compEvent: props.cancelEventInputParams.compEvent,
        reason: state.reason
      }

      context.emit("submit", cancelEventOutputPayload);
    }

    return { state, errorMessages, askForConfirmation, cancel,submit };
  },
});
</script>
