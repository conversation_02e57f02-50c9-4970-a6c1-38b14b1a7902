import { ICompEventActionsResult } from "../comp-event-actions-controller";
import { IConfigStoreState } from "../../../config/config-store";

export const mockCompEventActionsResult: ICompEventActionsResult = {
  competitionSummaryPublic: {
    active: true,
    IndoorOutdoor: "",
    link: "",
    yearFactor: 0,
    areaid: 0,
    options: {
      sockets: true,
      useTeamBibs: true,
      scoreboard: { image: "/resources/esaa_blank.png" },
      seqEventNo: false,
      disabled: false,
      stripeMandatory: false,
      resultsAvailable: true,
      paymentCode: 123,
      homeInfo: "",
      shortCode: "",
      priority: { required: true, code: "e4s", dateTime: "", message: "" },
      bibNos: 1,
      bibSort1: "surname",
      bibSort2: "firstname",
      bibSort3: "dob",
      heatOrder: "s",
      stadium: "",
      adjustEventNo: 0,
      pfTargetDirectory: "",
      athleteType: "A",
      tickets: { enabled: false },
      contact: {
        email: "<EMAIL>",
        id: 30,
        tel: "02476 344 429",
        userName: "Nuneaton Open Events",
        visible: true,
      },
      cardInfo: { enabled: true, availableFrom: "" },
      subscription: {
        enabled: true,
        timeCloses: "2022-11-28T19:00:00+00:00",
        organiserMessage: "org waiting message",
        e4sMessage: "",
        refunded: "",
        process: false,
        processRefundTime: "2022-12-12T19:00:00+01:00",
      },
      checkIn: {
        enabled: true,
        checkInDateTimeOpens: "",
        defaultFrom: 180,
        defaultTo: 60,
        qrCode: true,
        text: "Welcome to the Check-in service for our competition. To help us in the registration process, we would appreciate if you could let us know which events you are taking part in today. This will help reduce the number of athletes and the time you spend at the registration area picking up your bib number.\r\nWe have provided a number of methods to do this, please choose one of the methods below.",
        terms: "",
        useTerms: false,
        seedOnEntries: false,
        codes: { "2022-07-31": 339264, "2022-08-01": 128521 },
      },
      school: false,
      cheques: { allow: false, ends: "" },
      allowAdd: { unregistered: true, registered: true },
      timetable: "provisional",
      helpText: { schedule: "", teams: "", cart: "" },
      showTeamAthletes: true,
      singleAge: false,
      stopReport: false,
      showAthleteAgeInEntries: false,
      report: {
        summary: true,
        athletes: true,
        ttathletes: true,
        ttentries: true,
        individual_entries: true,
        teams: true,
        subscriptions: true,
        orders: true,
        events: true,
      },
      athleteSecurity: { areas: [], clubs: [], onlyClubsUpTo: "" },
      ui: {
        enterButtonText: "Enter",
        entryDefaultPanel: "SCHEDULE",
        ticketComp: 0,
        ticketCompButtonText: "Buy Tickets",
      },
      athleteQrData: false,
      disabledReason: "",
      laneCount: 8,
      compLimits: { athletes: 0, entries: 0, teams: 0 },
      cancelEvent: {
        hrsBeforeClose: 48,
        refund: { allow: true, type: "E4S_FEES" },
        credit: { allow: true },
      },
      autoEntries: {
        selectedTargetComp: { id: 0, name: "" },
        targetable: { allowedSources: [], enabled: false },
      },
      level: "",
      dates: ["2022-07-31", "2022-08-01"],
      categoryId: 3813,
    },
    teamid: 0,
    lastentrymod: "2022-08-23 15:30:07",
    information: "",
    r4s: 0,
    waitingrefunded: 0,
    areaname: "All",
    today: "2022-08-31",
    systemtime: "2022-08-31 13:45:20",
    compDate: "2022-08-30",
    daysToComp: -1,
    daysToClose: -5,
    location: {
      id: 20,
      name: "Pingles Stadium 2",
      address1: "The Pingles Stadium",
      address2: "",
      town: "Nuneaton",
      postcode: "CV11 4LX",
      county: "Warwickshire",
      map: "https://www.google.com/maps?q=CV11 4LX",
      directions: "",
      website: "https://www.thepinglesstadium.com/",
    },
    loccontact: "02476 344 429",
    logo: "/resources/default_logo.gif",
    ctcid: null,
    maxathletes: null,
    maxteams: null,
    maxmale: null,
    maxfemale: null,
    maxagegroup: null,
    uniqueevents: null,
    singleagegroup: null,
    ctc: {
      ctcid: null,
      maxathletes: null,
      maxteams: null,
      maxmale: null,
      maxfemale: null,
      maxagegroup: null,
      uniqueevents: null,
      singleagegroup: null,
    },
    compOrgId: 2,
    compName: "New Age Groups",
    entityid: null,
    opendate: "2022-05-19T00:00:00+01:00",
    closedate: "2022-08-26T00:00:00+01:00",
    club: "Test Organisation",
    dates: ["2022-07-31", "2022-08-01"],
    saleenddate: null,
    entries: {
      eventCount: 341,
      teamEventCount: 54,
      indivEventCount: 287,
      indiv: 107,
      waitingCount: 3,
      uniqueIndivAthletes: 33,
      team: 10,
      uniqueTeamAthletes: 6,
      teamAthletes: 8,
      athletes: 33,
    },
    access: "",
    permissions: { adminMenu: true, builder: true, check: true, report: true },
    reportId: "",
    reportAccess: true,
    status: {
      id: 0,
      compid: 313,
      description: "No Status",
      status: "NO_STATUS",
      invoicelink: "",
      reference: "",
      notes: "",
      value: 0,
      code: 9010,
      wfid: 0,
      previd: 0,
      prevdescription: "",
      nextdescription: "",
      nextid: 0,
    },
    organisers: [
      {
        permId: 163,
        orgId: 2,
        compId: 0,
        user: {
          id: 10,
          userEmail: "<EMAIL>",
          displayName: "Test User",
        },
        role: { id: 1, name: "finance" },
      },
      {
        permId: 178,
        orgId: 2,
        compId: 0,
        user: {
          id: 2,
          userEmail: "<EMAIL>",
          displayName: "E4S User2",
        },
        role: { id: 9, name: "seeding" },
      },
    ],
    e4sNotes: "",
    newsFlash: "",
    termsConditions: "<p>Some terms and conditions.</p>",
    emailText: "",
    compId: 313,
    compRules: [
      {
        id: 204,
        agid: 2,
        options: {
          maxCompEvents: 2,
          maxCompField: 0,
          maxCompTrack: 0,
          maxExcludedEvents: 0,
          displayMaxDayFields: false,
        },
      },
    ],
  },
  athlete: {
    id: 1,
    firstName: "Jessica",
    surName: "Day",
    aocode: "EA",
    URN: 3590678,
    pof10id: 750634,
    dob: "2004-09-03",
    gender: "F",
    classification: 0,
    email: "",
    type: "A",
    options: {
      noEntryReason: "",
      emergency: { name: "", tel: "", relationship: "" },
    },
    activeEndDate: "2022-12-31",
    image:
      "https://www.thepowerof10.info/athletes/profilepic.aspx?athleteid=750634",
    club: "Nuneaton Harriers",
    clubId: 1198,
    club2Id: 0,
    schoolId: 0,
    club2: "",
    school: "",
    events: [
      {
        compid: 30,
        compname: "Nuneaton Open 17th July 2019",
        compdate: "2019-07-17T00:00:00+01:00",
        eventid: 20,
        eventname: "Shotput",
        eventtime: "2019-07-17T18:45:00+01:00",
        orderno: 46331,
        price: 6.5,
        paid: 1,
        entryid: 8481,
        userid: 1,
        teamBibNo: "DAY",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
        },
        username: "e4sadmin",
        email: "<EMAIL>",
      },
      {
        compid: 58,
        compname: "Nuneaton Open 7th Sept 2019",
        compdate: "2019-09-07T00:00:00+01:00",
        eventid: 20,
        eventname: "Shotput",
        eventtime: "2019-09-07T13:15:00+01:00",
        orderno: 0,
        price: 0,
        paid: 1,
        entryid: 16062,
        userid: 1,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          fee: true,
          reason: "Jessica",
        },
        username: "e4sadmin",
        email: "<EMAIL>",
      },
      {
        compid: 58,
        compname: "Nuneaton Open 7th Sept 2019",
        compdate: "2019-09-07T00:00:00+01:00",
        eventid: 22,
        eventname: "Long Jump",
        eventtime: "2019-09-07T12:00:00+01:00",
        orderno: 0,
        price: 0,
        paid: 1,
        entryid: 16063,
        userid: 1,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          fee: true,
          reason: "Jessica",
        },
        username: "e4sadmin",
        email: "<EMAIL>",
      },
      {
        compid: 137,
        compname: "Midland Open Competition",
        compdate: "2020-09-20T00:00:00+01:00",
        eventid: 20,
        eventname: "Shotput",
        eventtime: "2020-09-20T11:30:00+01:00",
        orderno: 0,
        price: 0,
        paid: 1,
        entryid: 30637,
        userid: 1,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          fee: true,
          reason: "Cos",
        },
        username: "e4sadmin",
        email: "<EMAIL>",
      },
      {
        compid: 166,
        compname: "End of Season Open Competition",
        compdate: "2020-10-04T00:00:00+01:00",
        eventid: 20,
        eventname: "Shotput",
        eventtime: "2020-10-04T15:00:00+01:00",
        orderno: 0,
        price: 0,
        paid: 1,
        entryid: 31679,
        userid: 1,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
        },
        username: "e4sadmin",
        email: "<EMAIL>",
      },
      {
        compid: 120,
        compname: "test do not use",
        compdate: "2021-01-31T00:00:00+00:00",
        eventid: 3,
        eventname: "100m",
        eventtime: "2020-10-31T00:00:00+00:00",
        orderno: 0,
        price: 0,
        paid: 1,
        entryid: 34516,
        userid: 1,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          fee: true,
          reason: "test",
        },
        username: "e4sadmin",
        email: "<EMAIL>",
      },
      {
        compid: 209,
        compname: "LICC - Including Middlesex Schools 12th / 13th June",
        compdate: "2021-06-12T00:00:00+01:00",
        eventid: 18,
        eventname: "Discus",
        eventtime: "2021-06-12T11:30:00+01:00",
        orderno: 116103,
        price: 8,
        paid: 0,
        entryid: 52859,
        userid: -21410,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          reason: "",
          cancelUser: 22824,
        },
        username: "e4stest2",
        email: "<EMAIL>",
      },
      {
        compid: 209,
        compname: "LICC - Including Middlesex Schools 12th / 13th June",
        compdate: "2021-06-12T00:00:00+01:00",
        eventid: 3,
        eventname: "100m",
        eventtime: "2021-06-12T13:50:00+01:00",
        orderno: 116107,
        price: 8,
        paid: 0,
        entryid: 52861,
        userid: -21410,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          refunds: [
            {
              reason: "test refund",
              id: 2213,
              orderid: 116107,
              type: "refund",
            },
          ],
        },
        username: "e4stest2",
        email: "<EMAIL>",
      },
      {
        compid: 312,
        compname: "test age groups",
        compdate: "2022-05-31T00:00:00+01:00",
        eventid: 5,
        eventname: "200m",
        eventtime: "2022-05-31T00:00:00+01:00",
        orderno: 136251,
        price: 2,
        paid: 1,
        entryid: 69461,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 310,
        compname: "test emails",
        compdate: "2022-12-25T00:00:00+00:00",
        eventid: 553,
        eventname: "Howler",
        eventtime: "2022-12-25T00:01:00+00:00",
        orderno: 0,
        price: 0,
        paid: 1,
        entryid: 69462,
        userid: 14095,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          fee: true,
        },
        username: "paulthedayfamily-co-uk",
        email: "<EMAIL>",
      },
      {
        compid: 310,
        compname: "test emails",
        compdate: "2022-12-25T00:00:00+00:00",
        eventid: 20,
        eventname: "Shotput",
        eventtime: "2022-12-25T02:00:00+00:00",
        orderno: 136263,
        price: 2.5,
        paid: 1,
        entryid: 69470,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 310,
        compname: "test emails",
        compdate: "2022-12-25T00:00:00+00:00",
        eventid: 38,
        eventname: "Javelin",
        eventtime: "2022-12-25T00:01:00+00:00",
        orderno: 136277,
        price: 2.5,
        paid: 1,
        entryid: 69477,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 3,
        eventname: "100m",
        eventtime: "2022-07-31T10:00:00+01:00",
        orderno: 136291,
        price: 5,
        paid: 1,
        entryid: 69486,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 5,
        eventname: "200m",
        eventtime: "2022-07-31T09:00:00+01:00",
        orderno: 136331,
        price: 5,
        paid: 1,
        entryid: 69514,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 10,
        eventname: "400m",
        eventtime: "2022-07-31T10:00:00+01:00",
        orderno: 136336,
        price: 5,
        paid: 1,
        entryid: 69522,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 16,
        eventname: "800m",
        eventtime: "2022-07-31T12:00:00+01:00",
        orderno: 136374,
        price: 5,
        paid: 1,
        entryid: 69547,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 14,
        eventname: "1500m",
        eventtime: "2022-07-31T00:00:00+01:00",
        orderno: 136399,
        price: 5,
        paid: 1,
        entryid: 69559,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "pending",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 310,
        compname: "test emails",
        compdate: "2022-12-25T00:00:00+00:00",
        eventid: 14,
        eventname: "1500m",
        eventtime: "2022-12-25T00:00:00+00:00",
        orderno: 136399,
        price: 2.5,
        paid: 1,
        entryid: 69560,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "pending",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 310,
        compname: "test emails",
        compdate: "2022-12-25T00:00:00+00:00",
        eventid: 18,
        eventname: "Discus",
        eventtime: "2022-12-25T00:00:00+00:00",
        orderno: 136415,
        price: 6,
        paid: 1,
        entryid: 69574,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "pending",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 18,
        eventname: "Discus",
        eventtime: "2022-07-31T00:00:00+01:00",
        orderno: 136419,
        price: 5,
        paid: 1,
        entryid: 69576,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 28,
        eventname: "Hammer",
        eventtime: "2022-07-31T00:00:00+01:00",
        orderno: 136427,
        price: 5,
        paid: 1,
        entryid: 69582,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 2,
        eventname: "75m",
        eventtime: "2022-07-31T00:00:00+01:00",
        orderno: 136465,
        price: 5,
        paid: 1,
        entryid: 69597,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 96,
        eventname: "60m Hurdles",
        eventtime: "2022-07-31T00:00:00+01:00",
        orderno: 136470,
        price: 0,
        paid: 1,
        entryid: 69603,
        userid: 1,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "pending",
          switched: { fromEvent: "60m", toEvent: "60m Hurdles" },
          reason: "Moved to users basket by user 1",
          fee: true,
        },
        username: "e4sadmin",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 18,
        eventname: "Discus",
        eventtime: "2022-07-31T00:00:00+01:00",
        orderno: 136501,
        price: 5,
        paid: 1,
        entryid: 69622,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "pending",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 20,
        eventname: "Shotput",
        eventtime: "2022-07-31T00:00:00+01:00",
        orderno: 136510,
        price: 5,
        paid: 1,
        entryid: 69623,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 313,
        compname: "New Age Groups",
        compdate: "2022-08-30T00:00:00+01:00",
        eventid: 18,
        eventname: "Discus",
        eventtime: "2022-07-31T04:00:00+01:00",
        orderno: 136510,
        price: 5,
        paid: 1,
        entryid: 69629,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 328,
        compname: "Cloned 323 test unique",
        compdate: "2022-07-31T00:00:00+01:00",
        eventid: 14,
        eventname: "1500m",
        eventtime: "2022-07-31T00:00:00+01:00",
        orderno: 0,
        price: 5,
        paid: 0,
        entryid: 69720,
        userid: -19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          reason: "cartemptied",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 328,
        compname: "Cloned 323 test unique",
        compdate: "2022-07-31T00:00:00+01:00",
        eventid: 16,
        eventname: "800m",
        eventtime: "2022-07-31T00:00:00+01:00",
        orderno: 136705,
        price: 5,
        paid: 1,
        entryid: 69827,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
      {
        compid: 328,
        compname: "Cloned 323 test unique",
        compdate: "2022-07-31T00:00:00+01:00",
        eventid: 5,
        eventname: "200m",
        eventtime: "2022-07-31T00:00:00+01:00",
        orderno: 136707,
        price: 5,
        paid: 1,
        entryid: 69828,
        userid: 19828,
        teamBibNo: "",
        options: {
          allowAreas: false,
          otd: false,
          forceBasket: false,
          checkIn: { from: null, to: null },
          autoEntries: {
            targetEntry: { id: 0, paid: 0, orderId: 0 },
            targetEventGroup: { id: 0, name: "" },
          },
          lastStatus: "completed",
        },
        username: "nick-wall",
        email: "<EMAIL>",
      },
    ],
    userAthletes: [
      { id: 1, email: "<EMAIL>", userName: "e4sadmin" },
      { id: 3291, email: "<EMAIL>", userName: "john-reilly" },
      { id: 19828, email: "<EMAIL>", userName: "nick-wall" },
      { id: 27257, email: "<EMAIL>", userName: "dale" },
    ],
    pbInfo: [
      {
        eventid: 3,
        pb: 13.78,
        pbid: 19678,
        pbText: 13.78,
        pof10pb: 13.78,
        sb: 13.6,
        trackSb: false,
        eventName: "100m",
        options: { maxInHeat: 8, min: 9.6, max: 20, helpText: "", wind: "E" },
        min: 9.6,
        max: 20,
        uomInfo: {
          id: 1,
          type: "T",
          options: [{ pattern: "s.SS", text: "seconds", short: "secs" }],
        },
      },
      {
        eventid: 14,
        pb: 0,
        pbid: 0,
        pbText: "",
        pof10pb: 0,
        sb: 0,
        trackSb: false,
        eventName: "1500m",
        options: { maxInHeat: 16, min: 150, max: 420 },
        min: 150,
        max: 420,
        uomInfo: {
          id: 3,
          type: "T",
          options: [{ pattern: "m.ss.SS", text: "mins", short: "m" }],
        },
      },
      {
        eventid: 201,
        pb: 131,
        pbid: 59522,
        pbText: 2.11,
        pof10pb: 0,
        sb: 0,
        trackSb: false,
        eventName: "1500m SC",
        options: { maxInHeat: 16, min: 120, max: 600 },
        min: 120,
        max: 600,
        uomInfo: {
          id: 3,
          type: "T",
          options: [{ pattern: "m.ss.SS", text: "mins", short: "m" }],
        },
      },
      {
        eventid: 5,
        pb: 0,
        pbid: 0,
        pbText: "",
        pof10pb: 0,
        sb: 0,
        trackSb: false,
        eventName: "200m",
        options: { maxInHeat: 8, min: 20, max: 60, wind: "E" },
        min: 20,
        max: 60,
        uomInfo: {
          id: 1,
          type: "T",
          options: [{ pattern: "s.SS", text: "seconds", short: "secs" }],
        },
      },
      {
        eventid: 10,
        pb: 0,
        pbid: 0,
        pbText: "",
        pof10pb: 0,
        sb: 0,
        trackSb: false,
        eventName: "400m",
        options: { maxInHeat: 8, min: 40, max: 120 },
        min: 40,
        max: 120,
        uomInfo: {
          id: 11,
          type: "T",
          options: [
            { pattern: "m.ss.SS", text: "minutes", short: "m" },
            { pattern: "s.SS", text: "seconds", short: "s" },
          ],
        },
      },
      {
        eventid: 32,
        pb: 133.79,
        pbid: 19679,
        pbText: 133.79,
        pof10pb: 133.79,
        sb: 0,
        trackSb: false,
        eventName: "600m",
        options: { maxInHeat: 8, min: 60, max: 180 },
        min: 60,
        max: 180,
        uomInfo: {
          id: 3,
          type: "T",
          options: [{ pattern: "m.ss.SS", text: "mins", short: "m" }],
        },
      },
      {
        eventid: 157,
        pb: 8.8,
        pbid: 19676,
        pbText: 8.8,
        pof10pb: 8.8,
        sb: 0,
        trackSb: false,
        eventName: "60m",
        options: { maxInHeat: 8, min: 6, max: 14 },
        min: 6,
        max: 14,
        uomInfo: {
          id: 1,
          type: "T",
          options: [{ pattern: "s.SS", text: "seconds", short: "secs" }],
        },
      },
      {
        eventid: 96,
        pb: 9.89,
        pbid: 19680,
        pbText: 9.89,
        pof10pb: 9.89,
        sb: 0,
        trackSb: false,
        eventName: "60m Hurdles",
        options: { maxInHeat: 8, min: 6, max: 20, wind: "E" },
        min: 6,
        max: 20,
        uomInfo: {
          id: 1,
          type: "T",
          options: [{ pattern: "s.SS", text: "seconds", short: "secs" }],
        },
      },
      {
        eventid: 91,
        pb: 11.41,
        pbid: 19681,
        pbText: 11.41,
        pof10pb: 11.41,
        sb: 0,
        trackSb: false,
        eventName: "70m Hurdles",
        options: { wind: "E" },
        min: 0,
        max: 0,
        uomInfo: {
          id: 1,
          type: "T",
          options: [{ pattern: "s.SS", text: "seconds", short: "secs" }],
        },
      },
      {
        eventid: 2,
        pb: 11.43,
        pbid: 19677,
        pbText: 11.43,
        pof10pb: 11.43,
        sb: 0,
        trackSb: false,
        eventName: "75m",
        options: { maxInHeat: 8, min: 6.6, max: 20 },
        min: 6.6,
        max: 20,
        uomInfo: {
          id: 1,
          type: "T",
          options: [{ pattern: "s.SS", text: "seconds", short: "secs" }],
        },
      },
      {
        eventid: 89,
        pb: 11.94,
        pbid: 19682,
        pbText: 11.94,
        pof10pb: 11.94,
        sb: 0,
        trackSb: false,
        eventName: "75m Hurdles",
        options: { maxInHeat: 8, min: 9, max: 25, wind: "E" },
        min: 9,
        max: 25,
        uomInfo: {
          id: 1,
          type: "T",
          options: [{ pattern: "s.SS", text: "seconds", short: "secs" }],
        },
      },
      {
        eventid: 16,
        pb: 0,
        pbid: 0,
        pbText: "",
        pof10pb: 0,
        sb: 0,
        trackSb: false,
        eventName: "800m",
        options: { maxInHeat: 8, min: 80, max: 240 },
        min: 80,
        max: 240,
        uomInfo: {
          id: 3,
          type: "T",
          options: [{ pattern: "m.ss.SS", text: "mins", short: "m" }],
        },
      },
      {
        eventid: 18,
        pb: 17.7,
        pbid: 19686,
        pbText: 17.7,
        pof10pb: 24.2,
        sb: 24.2,
        trackSb: false,
        eventName: "Discus",
        options: { min: 1, max: 100 },
        min: 1,
        max: 100,
        uomInfo: {
          id: 5,
          type: "D",
          options: [{ pattern: 0.99, text: "metres", short: "mt" }],
        },
      },
      {
        eventid: 28,
        pb: 16.74,
        pbid: 71932,
        pbText: 16.74,
        pof10pb: 18.23,
        sb: 18.23,
        trackSb: false,
        eventName: "Hammer",
        options: { min: 0, max: 100 },
        min: 0,
        max: 0,
        uomInfo: {
          id: 5,
          type: "D",
          options: [{ pattern: 0.99, text: "metres", short: "mt" }],
        },
      },
      {
        eventid: 24,
        pb: 1.26,
        pbid: 19683,
        pbText: 1.26,
        pof10pb: 1.25,
        sb: 0,
        trackSb: false,
        eventName: "High Jump",
        options: { min: 0.05, max: 2.5, cardType: "H" },
        min: 0,
        max: 0,
        uomInfo: {
          id: 13,
          type: "H",
          options: [{ pattern: 0.99, text: "metres", short: "mt" }],
        },
      },
      {
        eventid: 553,
        pb: 0,
        pbid: 0,
        pbText: "",
        pof10pb: 0,
        sb: 0,
        trackSb: false,
        eventName: "Howler",
        options: { min: 1, max: "50.00.00" },
        min: 1,
        max: 50,
        uomInfo: {
          id: 6,
          type: "D",
          options: [{ pattern: 9.99, text: "metres", short: "mt" }],
        },
      },
      {
        eventid: 38,
        pb: 17.16,
        pbid: 19687,
        pbText: 17.16,
        pof10pb: 18.77,
        sb: 18.26,
        trackSb: false,
        eventName: "Javelin",
        options: { min: 1, max: 110, unique: [{ e: 40 }] },
        min: 1,
        max: 110,
        uomInfo: {
          id: 5,
          type: "D",
          options: [{ pattern: 0.99, text: "metres", short: "mt" }],
        },
      },
      {
        eventid: 22,
        pb: 4.64,
        pbid: 19684,
        pbText: 4.64,
        pof10pb: 4.64,
        sb: 4.33,
        trackSb: false,
        eventName: "Long Jump",
        options: {
          min: 1,
          max: 10,
          class: "0,11-13,20,35-47,61-64",
          wind: "A",
        },
        min: 1,
        max: 10,
        uomInfo: {
          id: 12,
          type: "D",
          options: [{ pattern: 0.99, text: "metres", short: "mt" }],
        },
      },
      {
        eventid: 30,
        pb: 2.5,
        pbid: 71823,
        pbText: 2.5,
        pof10pb: 0,
        sb: 0,
        trackSb: false,
        eventName: "Pole Vault",
        options: { min: 1, max: 7, cardType: "H" },
        min: 1,
        max: 7,
        uomInfo: {
          id: 13,
          type: "H",
          options: [{ pattern: 0.99, text: "metres", short: "mt" }],
        },
      },
      {
        eventid: 20,
        pb: 7.87,
        pbid: 71844,
        pbText: 7.87,
        pof10pb: 7.87,
        sb: 7.52,
        trackSb: false,
        eventName: "Shotput",
        options: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
        min: 1,
        max: 30,
        uomInfo: {
          id: 5,
          type: "D",
          options: [{ pattern: 0.99, text: "metres", short: "mt" }],
        },
      },
      {
        eventid: 26,
        pb: 8.74,
        pbid: 71799,
        pbText: 8.74,
        pof10pb: 8.94,
        sb: 8.94,
        trackSb: false,
        eventName: "Triple Jump",
        options: { min: 3, max: 25, wind: "A" },
        min: 3,
        max: 25,
        uomInfo: {
          id: 5,
          type: "D",
          options: [{ pattern: 0.99, text: "metres", short: "mt" }],
        },
      },
    ],
    clubInfo: {
      "1198": [
        {
          id: 6900,
          userName: "jared-wilson",
          displayName: "Jared Wilson",
          email: "<EMAIL>",
        },
      ],
    },
  },
  compEvent: {
    ceid: 32965,
    ageGroupId: 11,
    ageGroupName: "Under 20",
    description: "std",
    startdate: "2022-07-31T12:00:00+01:00",
    IsOpen: 1,
    tf: "T",
    uomType: "T",
    price: {
      id: 435,
      priceName: "",
      stdPrice: 5,
      curPrice: 5,
      curFee: 0.6,
      salePrice: 5,
      saleDate: "+00:00",
    },
    eventid: 16,
    multiid: 0,
    Name: "800m",
    eventGroup: "800m",
    egOptions:
      '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
    split: 0,
    maxgroup: 4973,
    maxathletes: 0,
    ceoptions: {
      min: 80,
      max: 240,
      helpText: "",
      registeredAthletes: true,
      unregistered: false,
      registered: true,
      isTeamEvent: false,
      wind: "",
      excludeFromCntRule: false,
      unique: [],
      eventTeam: {
        min: 0,
        max: 0,
        mustBeIndivEntered: false,
        minTargetAgeGroupCount: 0,
        maxOtherAgeGroupCount: 0,
        teamPositionLabel: "",
        maxEventTeams: 0,
        currCount: 0,
        singleClub: false,
        teamNameFormat:
          "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
        teamSubstituteLabel: "athlete",
        showForm: false,
        formType: "DEFAULT",
        price: "",
      },
      rowOptions: {
        autoExpandHelpText: false,
        showPB: true,
        showPrice: false,
        showEntryCount: true,
      },
      maxInHeat: 8,
      heatInfo: { useLanes: "A", heatDurationMins: 0 },
      xiText: "",
      xeText: "",
      xbText: "",
      xrText: "",
      warningMessage: "",
      mandatoryPB: false,
      trialInfo: "",
      reportInfo: "",
      ageGroups: [],
      singleAge: false,
      security: {},
      athleteSecurity: {},
      checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
      uniqueEventGroups: [],
      unregisteredAthletes: true,
    },
    eoptions: { maxInHeat: 8, min: 80, max: 240 },
    ageGroup: {
      id: 11,
      minAge: 17,
      minAtDay: 31,
      minAtMonth: 8,
      keyName: "Under 20",
      options: [{ aocode: "EA", default: true, base: 1 }],
      name: "Under 20",
      maxAge: 19,
      maxAtDay: 31,
      maxAtMonth: 12,
      maxAtYear: 0,
      minAtYear: 0,
      shortName: "Under 20",
    },
    entered: true,
    entryId: 69547,
    paid: 1,
    athleteid: 1,
    entryInfo: {
      unpaidCount: 0,
      paidCount: 8,
      totalCount: 8,
      entryPosition: 2,
      entryCreated: "2022-06-12T06:07:01+01:00",
      entryPositionDate: "2022-06-12T06:07:01+01:00",
      maxAthletes: 0,
    },
    entrycnt: 8,
    compName: "New Age Groups",
    user: {
      userId: 19828,
      userName: "nick wall",
      userEmail: "<EMAIL>",
    },
    order: {
      orderId: 136374,
      productId: 136372,
      e4sLineValue: 5,
      wcLineValue: 5,
      isRefund: false,
      refundValue: 5,
      isCredit: false,
      creditValue: 5,
      discountId: 0,
      dateOrdered: "2022-06-12",
    },
    teamBibNo: "",
    firstName: "Jessica",
    surName: "Day",
    club: "Nuneaton Harriers",
    uom: [{ pattern: "m.ss.SS", text: "mins", short: "m", uomType: "T" }],
    ruleIsDisabledBy: false,
    ruleMessage: "",
    userSelected: false,
    ruleType: "",
    post_date: "",
  },
  compEvents: [
    {
      ceid: 33145,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T00:00:00+01:00",
      IsOpen: 1,
      tf: "T",
      uomType: "T",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 2,
      multiid: 0,
      Name: "75m",
      eventGroup: "75m Females U20",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4991,
      maxathletes: 0,
      ceoptions: {
        min: 6.6,
        max: 20,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 8,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
        uniqueEventGroups: [],
        unregisteredAthletes: true,
      },
      eoptions: { maxInHeat: 8, min: 6.6, max: 20 },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: true,
      entryId: 69597,
      paid: 1,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 2,
        totalCount: 2,
        entryPosition: 1,
        entryCreated: "2022-06-14T07:41:22+01:00",
        entryPositionDate: "2022-06-14T07:41:22+01:00",
        maxAthletes: 0,
      },
      entrycnt: 2,
      compName: "New Age Groups",
      user: {
        userId: 19828,
        userName: "nick wall",
        userEmail: "<EMAIL>",
      },
      order: {
        orderId: 136465,
        productId: 136461,
        e4sLineValue: 5,
        wcLineValue: 5,
        isRefund: false,
        refundValue: 5,
        isCredit: false,
        creditValue: 5,
        discountId: 0,
        dateOrdered: "2022-06-14",
      },
      teamBibNo: "",
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: "s.SS", text: "seconds", short: "secs", uomType: "T" }],
      ruleIsDisabledBy: true,
      ruleMessage: "Already Entered",
      userSelected: true,
      ruleType: "EVENT",
      post_date: "",
      pb: 11.43,
    },
    {
      ceid: 33019,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T00:00:00+01:00",
      IsOpen: 1,
      tf: "T",
      uomType: "T",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 14,
      multiid: 0,
      Name: "1500m",
      eventGroup: "1500m",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false}}',
      split: 0,
      maxgroup: 4976,
      maxathletes: 0,
      ceoptions: {
        min: 150,
        max: 420,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 16,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false },
        uniqueEventGroups: [],
      },
      eoptions: { maxInHeat: 16, min: 150, max: 420 },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: true,
      entryId: 69559,
      paid: 1,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 13,
        totalCount: 13,
        entryPosition: 3,
        entryCreated: "2022-06-12T06:57:35+01:00",
        entryPositionDate: "2022-06-12T06:57:35+01:00",
        maxAthletes: 0,
      },
      entrycnt: 13,
      compName: "New Age Groups",
      user: {
        userId: 19828,
        userName: "nick wall",
        userEmail: "<EMAIL>",
      },
      order: {
        orderId: 136399,
        productId: 136389,
        e4sLineValue: 5,
        wcLineValue: 5,
        isRefund: false,
        refundValue: 5,
        isCredit: false,
        creditValue: 5,
        discountId: 0,
        dateOrdered: "2022-06-12",
      },
      teamBibNo: "",
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: "m.ss.SS", text: "mins", short: "m", uomType: "T" }],
      ruleIsDisabledBy: true,
      ruleMessage: "Already Entered",
      userSelected: true,
      ruleType: "EVENT",
      post_date: "",
    },
    {
      ceid: 33181,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T00:00:00+01:00",
      IsOpen: 1,
      tf: "T",
      uomType: "T",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 96,
      multiid: 0,
      Name: "60m Hurdles",
      eventGroup: "60m Hurdles",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4986,
      maxathletes: 0,
      ceoptions: {
        min: 6,
        max: 20,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "E",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 8,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
        uniqueEventGroups: [],
        unregisteredAthletes: true,
      },
      eoptions: { maxInHeat: 8, min: 6, max: 20, wind: "E" },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: true,
      entryId: 69603,
      paid: 1,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 4,
        totalCount: 4,
        entryPosition: 4,
        entryCreated: "2022-07-05T10:55:18+01:00",
        entryPositionDate: "2022-07-05T10:55:18+01:00",
        maxAthletes: 0,
      },
      entrycnt: 4,
      compName: "New Age Groups",
      user: {
        userId: 1,
        userName: "E4S Admin",
        userEmail: "<EMAIL>",
      },
      order: {
        orderId: 136470,
        productId: 136469,
        e4sLineValue: 0,
        wcLineValue: 5,
        isRefund: false,
        refundValue: 5,
        isCredit: false,
        creditValue: 5,
        discountId: 0,
        dateOrdered: "2022-06-14",
      },
      teamBibNo: "",
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: "s.SS", text: "seconds", short: "secs", uomType: "T" }],
      ruleIsDisabledBy: true,
      ruleMessage: "Already Entered",
      userSelected: true,
      ruleType: "EVENT",
      post_date: "",
      pb: 9.89,
    },
    {
      ceid: 33163,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T00:00:00+01:00",
      IsOpen: 1,
      tf: "T",
      uomType: "T",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 157,
      multiid: 0,
      Name: "60m",
      eventGroup: "60m",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4985,
      maxathletes: 0,
      ceoptions: {
        min: 6,
        max: 14,
        helpText: "This is help text for the 60m",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: true,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 8,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
        uniqueEventGroups: [],
        unregisteredAthletes: true,
      },
      eoptions: { maxInHeat: 8, min: 6, max: 14 },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: false,
      entryId: 0,
      paid: 0,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 5,
        totalCount: 5,
        entryPosition: 6,
        entryCreated: "",
        entryPositionDate: "",
        maxAthletes: 0,
      },
      entrycnt: 5,
      compName: "New Age Groups",
      user: { userId: 0, userName: "", userEmail: "" },
      order: {
        orderId: 0,
        productId: 0,
        e4sLineValue: 0,
        wcLineValue: 0,
        isRefund: false,
        refundValue: 0,
        isCredit: false,
        creditValue: 0,
        discountId: 0,
        dateOrdered: "",
      },
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: "s.SS", text: "seconds", short: "secs", uomType: "T" }],
      ruleIsDisabledBy: false,
      ruleMessage: "",
      userSelected: false,
      ruleType: "",
      post_date: "",
      pb: 8.8,
    },
    {
      ceid: 33037,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T00:00:00+01:00",
      IsOpen: 1,
      tf: "F",
      uomType: "D",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 18,
      multiid: 0,
      Name: "Discus",
      eventGroup: "Discus",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4977,
      maxathletes: 0,
      ceoptions: {
        min: 1,
        max: 100,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 0,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
        uniqueEventGroups: [],
        unregisteredAthletes: true,
      },
      eoptions: { min: 1, max: 100 },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: true,
      entryId: 69576,
      paid: 1,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 12,
        totalCount: 12,
        entryPosition: 3,
        entryCreated: "2022-06-12T07:46:03+01:00",
        entryPositionDate: "2022-06-12T07:46:03+01:00",
        maxAthletes: 0,
      },
      entrycnt: 12,
      compName: "New Age Groups",
      user: {
        userId: 19828,
        userName: "nick wall",
        userEmail: "<EMAIL>",
      },
      order: {
        orderId: 136419,
        productId: 136416,
        e4sLineValue: 5,
        wcLineValue: 5,
        isRefund: false,
        refundValue: 5,
        isCredit: false,
        creditValue: 5,
        discountId: 0,
        dateOrdered: "2022-06-12",
      },
      teamBibNo: "",
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: 0.99, text: "metres", short: "mt", uomType: "D" }],
      ruleIsDisabledBy: true,
      ruleMessage: "Already Entered",
      userSelected: true,
      ruleType: "EVENT",
      post_date: "",
      pb: 17.7,
    },
    {
      ceid: 33217,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T00:00:00+01:00",
      IsOpen: 1,
      tf: "F",
      uomType: "D",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 18,
      multiid: 0,
      Name: "Discus",
      eventGroup: "Discus 2",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4988,
      maxathletes: 0,
      ceoptions: {
        min: 1,
        max: 100,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 0,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false },
        uniqueEventGroups: [],
      },
      eoptions: { min: 1, max: 100 },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: true,
      entryId: 69622,
      paid: 1,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 6,
        totalCount: 6,
        entryPosition: 2,
        entryCreated: "2022-06-15T07:28:54+01:00",
        entryPositionDate: "2022-06-15T07:28:54+01:00",
        maxAthletes: 0,
      },
      entrycnt: 6,
      compName: "New Age Groups",
      user: {
        userId: 19828,
        userName: "nick wall",
        userEmail: "<EMAIL>",
      },
      order: {
        orderId: 136501,
        productId: 136500,
        e4sLineValue: 5,
        wcLineValue: 5,
        isRefund: false,
        refundValue: 5,
        isCredit: false,
        creditValue: 5,
        discountId: 0,
        dateOrdered: "2022-06-15",
      },
      teamBibNo: "",
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: 0.99, text: "metres", short: "mt", uomType: "D" }],
      ruleIsDisabledBy: true,
      ruleMessage: "Already Entered",
      userSelected: true,
      ruleType: "EVENT",
      post_date: "",
      pb: 17.7,
    },
    {
      ceid: 33199,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T00:00:00+01:00",
      IsOpen: 1,
      tf: "F",
      uomType: "D",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 20,
      multiid: 0,
      Name: "Shotput",
      eventGroup: "Shotput 2",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4987,
      maxathletes: 0,
      ceoptions: {
        min: 1,
        max: 30,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "",
        excludeFromCntRule: false,
        unique: [{ e: 36 }, { e: 48 }],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 0,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
        uniqueEventGroups: [],
        unregisteredAthletes: true,
      },
      eoptions: { min: 1, max: 30, unique: [{ e: 36 }, { e: 48 }] },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: true,
      entryId: 69623,
      paid: 1,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 7,
        totalCount: 7,
        entryPosition: 1,
        entryCreated: "2022-06-14T20:40:36+01:00",
        entryPositionDate: "2022-06-14T20:40:36+01:00",
        maxAthletes: 0,
      },
      entrycnt: 7,
      compName: "New Age Groups",
      user: {
        userId: 19828,
        userName: "nick wall",
        userEmail: "<EMAIL>",
      },
      order: {
        orderId: 136510,
        productId: 136502,
        e4sLineValue: 5,
        wcLineValue: 5,
        isRefund: false,
        refundValue: 5,
        isCredit: false,
        creditValue: 5,
        discountId: 0,
        dateOrdered: "2022-06-15",
      },
      teamBibNo: "",
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: 0.99, text: "metres", short: "mt", uomType: "D" }],
      ruleIsDisabledBy: true,
      ruleMessage: "Already Entered",
      userSelected: true,
      ruleType: "EVENT",
      post_date: "",
      pb: 7.87,
    },
    {
      ceid: 33091,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T00:00:00+01:00",
      IsOpen: 1,
      tf: "F",
      uomType: "D",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 28,
      multiid: 0,
      Name: "Hammer",
      eventGroup: "Hammer Group name",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4980,
      maxathletes: 0,
      ceoptions: {
        min: 0,
        max: 100,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 0,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false },
        uniqueEventGroups: [],
      },
      eoptions: { min: 0, max: 100 },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: true,
      entryId: 69582,
      paid: 1,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 8,
        totalCount: 8,
        entryPosition: 2,
        entryCreated: "2022-06-12T07:49:20+01:00",
        entryPositionDate: "2022-06-12T07:49:20+01:00",
        maxAthletes: 0,
      },
      entrycnt: 8,
      compName: "New Age Groups",
      user: {
        userId: 19828,
        userName: "nick wall",
        userEmail: "<EMAIL>",
      },
      order: {
        orderId: 136427,
        productId: 136423,
        e4sLineValue: 5,
        wcLineValue: 5,
        isRefund: false,
        refundValue: 5,
        isCredit: false,
        creditValue: 5,
        discountId: 0,
        dateOrdered: "2022-06-12",
      },
      teamBibNo: "",
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: 0.99, text: "metres", short: "mt", uomType: "D" }],
      ruleIsDisabledBy: true,
      ruleMessage: "Already Entered",
      userSelected: true,
      ruleType: "EVENT",
      post_date: "",
      pb: 16.74,
    },
    {
      ceid: 33289,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T01:00:00+01:00",
      IsOpen: 1,
      tf: "T",
      uomType: "T",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 3,
      multiid: 0,
      Name: "100m",
      eventGroup: "100m Finala",
      egOptions:
        '{"maxathletes":-1,"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4996,
      maxathletes: -1,
      ceoptions: {
        min: 9.6,
        max: 20,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "E",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 8,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
        uniqueEventGroups: [],
        unregisteredAthletes: true,
      },
      eoptions: { maxInHeat: 8, min: 9.6, max: 20, helpText: "", wind: "E" },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: false,
      entryId: 0,
      paid: 0,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 0,
        totalCount: 0,
        entryPosition: 1,
        entryCreated: "",
        entryPositionDate: "",
        maxAthletes: 0,
      },
      entrycnt: 0,
      compName: "New Age Groups",
      user: { userId: 0, userName: "", userEmail: "" },
      order: {
        orderId: 0,
        productId: 0,
        e4sLineValue: 0,
        wcLineValue: 0,
        isRefund: false,
        refundValue: 0,
        isCredit: false,
        creditValue: 0,
        discountId: 0,
        dateOrdered: "",
      },
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: "s.SS", text: "seconds", short: "secs", uomType: "T" }],
      ruleIsDisabledBy: true,
      ruleMessage: "Schedule Only",
      userSelected: false,
      ruleType: "EVENT",
      post_date: "",
      pb: 13.78,
    },
    {
      ceid: 33235,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T04:00:00+01:00",
      IsOpen: 1,
      tf: "F",
      uomType: "D",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 18,
      multiid: 0,
      Name: "Discus",
      eventGroup: "Discus 3",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4989,
      maxathletes: 0,
      ceoptions: {
        min: 1,
        max: 100,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 0,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
        uniqueEventGroups: [],
        unregisteredAthletes: true,
      },
      eoptions: { min: 1, max: 100 },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: true,
      entryId: 69629,
      paid: 1,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 3,
        totalCount: 3,
        entryPosition: 3,
        entryCreated: "2022-06-15T07:47:24+01:00",
        entryPositionDate: "2022-06-15T07:47:24+01:00",
        maxAthletes: 0,
      },
      entrycnt: 3,
      compName: "New Age Groups",
      user: {
        userId: 19828,
        userName: "nick wall",
        userEmail: "<EMAIL>",
      },
      order: {
        orderId: 136510,
        productId: 136509,
        e4sLineValue: 5,
        wcLineValue: 5,
        isRefund: false,
        refundValue: 5,
        isCredit: false,
        creditValue: 5,
        discountId: 0,
        dateOrdered: "2022-06-15",
      },
      teamBibNo: "",
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: 0.99, text: "metres", short: "mt", uomType: "D" }],
      ruleIsDisabledBy: true,
      ruleMessage: "Already Entered",
      userSelected: true,
      ruleType: "EVENT",
      post_date: "",
      pb: 17.7,
    },
    {
      ceid: 32861,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T09:00:00+01:00",
      IsOpen: 1,
      tf: "T",
      uomType: "T",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 5,
      multiid: 0,
      Name: "200m",
      eventGroup: "200mb",
      egOptions:
        '{"trialInfo":"ES 12.00s  NS 11.60s","seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4961,
      maxathletes: 0,
      ceoptions: {
        min: 20,
        max: 60,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "E",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 8,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
        uniqueEventGroups: [],
        unregisteredAthletes: true,
      },
      eoptions: { maxInHeat: 8, min: 20, max: 60, wind: "E" },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: true,
      entryId: 69514,
      paid: 1,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 12,
        totalCount: 12,
        entryPosition: 11,
        entryCreated: "2022-06-10T07:05:10+01:00",
        entryPositionDate: "2022-06-10T07:05:10+01:00",
        maxAthletes: 0,
      },
      entrycnt: 12,
      compName: "New Age Groups",
      user: {
        userId: 19828,
        userName: "nick wall",
        userEmail: "<EMAIL>",
      },
      order: {
        orderId: 136331,
        productId: 136330,
        e4sLineValue: 5,
        wcLineValue: 5,
        isRefund: false,
        refundValue: 5,
        isCredit: false,
        creditValue: 5,
        discountId: 0,
        dateOrdered: "2022-06-10",
      },
      teamBibNo: "",
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: "s.SS", text: "seconds", short: "secs", uomType: "T" }],
      ruleIsDisabledBy: true,
      ruleMessage: "Already Entered",
      userSelected: true,
      ruleType: "EVENT",
      post_date: "",
    },
    {
      ceid: 32877,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T10:00:00+01:00",
      IsOpen: 1,
      tf: "T",
      uomType: "T",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 3,
      multiid: 0,
      Name: "100m",
      eventGroup: "100mc",
      egOptions:
        '{"maxInHeat":7,"seed":{"laneCount":8,"qualifyToEg":{"eventNo":0},"seeded":false}}',
      split: 0,
      maxgroup: 4969,
      maxathletes: 0,
      ceoptions: {
        min: 9.6,
        max: 20,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "E",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 8,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
        uniqueEventGroups: [],
        unregisteredAthletes: true,
      },
      eoptions: { maxInHeat: 8, min: 9.6, max: 20, helpText: "", wind: "E" },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: true,
      entryId: 69486,
      paid: 1,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 7,
        totalCount: 7,
        entryPosition: 1,
        entryCreated: "2022-06-03T16:22:55+01:00",
        entryPositionDate: "2022-06-03T16:22:55+01:00",
        maxAthletes: 0,
      },
      entrycnt: 7,
      compName: "New Age Groups",
      user: {
        userId: 19828,
        userName: "nick wall",
        userEmail: "<EMAIL>",
      },
      order: {
        orderId: 136291,
        productId: 136290,
        e4sLineValue: 5,
        wcLineValue: 5,
        isRefund: false,
        refundValue: 5,
        isCredit: false,
        creditValue: 5,
        discountId: 0,
        dateOrdered: "2022-06-03",
      },
      teamBibNo: "",
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: "s.SS", text: "seconds", short: "secs", uomType: "T" }],
      ruleIsDisabledBy: true,
      ruleMessage: "Already Entered",
      userSelected: true,
      ruleType: "EVENT",
      post_date: "",
      pb: 13.78,
    },
    {
      ceid: 32947,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T10:00:00+01:00",
      IsOpen: 1,
      tf: "T",
      uomType: "T",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 10,
      multiid: 0,
      Name: "400m",
      eventGroup: "400m U20 women",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4993,
      maxathletes: 0,
      ceoptions: {
        min: 40,
        max: 120,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 8,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false, checkInMins: 60 },
        uniqueEventGroups: [],
        unregisteredAthletes: true,
      },
      eoptions: { maxInHeat: 8, min: 40, max: 120 },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: true,
      entryId: 69522,
      paid: 1,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 3,
        totalCount: 3,
        entryPosition: 2,
        entryCreated: "2022-06-10T10:39:40+01:00",
        entryPositionDate: "2022-06-10T10:39:40+01:00",
        maxAthletes: 0,
      },
      entrycnt: 3,
      compName: "New Age Groups",
      user: {
        userId: 19828,
        userName: "nick wall",
        userEmail: "<EMAIL>",
      },
      order: {
        orderId: 136336,
        productId: 136335,
        e4sLineValue: 5,
        wcLineValue: 5,
        isRefund: false,
        refundValue: 5,
        isCredit: false,
        creditValue: 5,
        discountId: 0,
        dateOrdered: "2022-06-10",
      },
      teamBibNo: "",
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [
        { pattern: "m.ss.SS", text: "minutes", short: "m", uomType: "T" },
        { pattern: "s.SS", text: "seconds", short: "s", uomType: "T" },
      ],
      ruleIsDisabledBy: true,
      ruleMessage: "Already Entered",
      userSelected: true,
      ruleType: "EVENT",
      post_date: "",
    },
    {
      ceid: 33271,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T10:00:00+01:00",
      IsOpen: 1,
      tf: "F",
      uomType: "D",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 26,
      multiid: 0,
      Name: "Triple Jump",
      eventGroup: "2000m",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":-1,"to":-1,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 4968,
      maxathletes: 0,
      ceoptions: {
        min: 3,
        max: 25,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "A",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 0,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: { from: -1, to: -1, seedOnEntries: false },
        uniqueEventGroups: [],
      },
      eoptions: { min: 3, max: 25, wind: "A" },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: false,
      entryId: 0,
      paid: 0,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 5,
        totalCount: 5,
        entryPosition: 6,
        entryCreated: "",
        entryPositionDate: "",
        maxAthletes: 0,
      },
      entrycnt: 5,
      compName: "New Age Groups",
      user: { userId: 0, userName: "", userEmail: "" },
      order: {
        orderId: 0,
        productId: 0,
        e4sLineValue: 0,
        wcLineValue: 0,
        isRefund: false,
        refundValue: 0,
        isCredit: false,
        creditValue: 0,
        discountId: 0,
        dateOrdered: "",
      },
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: 0.99, text: "metres", short: "mt", uomType: "D" }],
      ruleIsDisabledBy: false,
      ruleMessage: "",
      userSelected: false,
      ruleType: "",
      post_date: "",
      pb: 8.74,
    },
    {
      ceid: 35314,
      ageGroupId: 11,
      ageGroupName: "Under 20",
      description: "std",
      startdate: "2022-07-31T16:00:00+01:00",
      IsOpen: 1,
      tf: "F",
      uomType: "H",
      price: {
        id: 435,
        priceName: "",
        stdPrice: 5,
        curPrice: 5,
        curFee: 0.6,
        salePrice: 5,
        saleDate: "+00:00",
      },
      eventid: 24,
      multiid: 0,
      Name: "High Jump",
      eventGroup: "High Jump",
      egOptions:
        '{"seed":{"firstLane":0,"qualifyToEg":{"eventNo":0},"seeded":false},"checkIn":{"from":30000,"to":30001,"seedOnEntries":false,"checkInMins":60}}',
      split: 0,
      maxgroup: 5148,
      maxathletes: 0,
      ceoptions: {
        min: 0.05,
        max: 2.5,
        helpText: "",
        registeredAthletes: true,
        unregistered: false,
        registered: true,
        isTeamEvent: false,
        wind: "",
        excludeFromCntRule: false,
        unique: [],
        eventTeam: {
          min: 0,
          max: 0,
          mustBeIndivEntered: false,
          minTargetAgeGroupCount: 0,
          maxOtherAgeGroupCount: 0,
          teamPositionLabel: "",
          maxEventTeams: 0,
          currCount: 0,
          singleClub: false,
          teamNameFormat:
            "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
          teamSubstituteLabel: "athlete",
          showForm: false,
          formType: "DEFAULT",
          price: "",
        },
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: false,
          showEntryCount: true,
        },
        maxInHeat: 0,
        heatInfo: { useLanes: "A", heatDurationMins: 0 },
        xiText: "",
        xeText: "",
        xbText: "",
        xrText: "",
        warningMessage: "",
        mandatoryPB: false,
        trialInfo: "",
        reportInfo: "",
        ageGroups: [],
        singleAge: false,
        security: {},
        athleteSecurity: {},
        checkIn: {
          from: 30000,
          to: 30001,
          seedOnEntries: false,
          checkInMins: 60,
        },
        uniqueEventGroups: [],
        cardType: "H",
        unregisteredAthletes: true,
      },
      eoptions: { min: 0.05, max: 2.5, cardType: "H" },
      ageGroup: {
        id: 11,
        minAge: 17,
        minAtDay: 31,
        minAtMonth: 8,
        keyName: "Under 20",
        options: [{ aocode: "EA", default: true, base: 1 }],
        name: "Under 20",
        maxAge: 19,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
        shortName: "Under 20",
      },
      entered: false,
      entryId: 0,
      paid: 0,
      athleteid: 1,
      entryInfo: {
        unpaidCount: 0,
        paidCount: 1,
        totalCount: 1,
        entryPosition: 2,
        entryCreated: "",
        entryPositionDate: "",
        maxAthletes: 0,
      },
      entrycnt: 1,
      compName: "New Age Groups",
      user: { userId: 0, userName: "", userEmail: "" },
      order: {
        orderId: 0,
        productId: 0,
        e4sLineValue: 0,
        wcLineValue: 0,
        isRefund: false,
        refundValue: 0,
        isCredit: false,
        creditValue: 0,
        discountId: 0,
        dateOrdered: "",
      },
      firstName: "Jessica",
      surName: "Day",
      club: "Nuneaton Harriers",
      uom: [{ pattern: 0.99, text: "metres", short: "mt", uomType: "H" }],
      ruleIsDisabledBy: false,
      ruleMessage: "",
      userSelected: false,
      ruleType: "",
      post_date: "",
      pb: 1.26,
    },
  ],
  compRule: { id: 0, options: {} },
  userApplication: {
    id: 1,
    user_login: "E4SAdmin",
    user_nicename: "e4sadmin",
    user_email: "<EMAIL>",
    display_name: "E4S Admin",
    role: "E4SUSER",
    google_email: "",
    impersonating: false,
    e4sCredit: [0],
    permissions: [
      {
        id: 5,
        userid: 1,
        role: { id: 0, name: "All" },
        comp: { id: 0, name: "" },
        org: { id: 0, name: "All" },
        permLevels: [],
      },
    ],
  },
} as any as ICompEventActionsResult;

export const mockConfigStoreState: IConfigStoreState = {
  configApp: {
    id: 2,
    systemName: "UK Entry System",
    public: 0,
    theme: "gbr",
    logo: "/resources/e4s_logo.png",
    cardlogo: "/resources/default_logo.gif",
    currency: "£",
    support: "https://support.entry4sports.com",
    userguide: "",
    defaultao: {
      id: 2,
      code: "EA",
      description: "United Kingdom",
      prefix: "uk_",
      urnformat: "(^[0-9]{7}$)",
      urnmessage: "Please enter 7 digits",
      icon: "",
      name: "England Athletics",
      registrationLink: "https://englandathletics.org/",
    },
    vetDisplay: "vet",
    userDaysPast: 10,
    adminDaysPast: 30,
    payments: "entry",
    options: {
      allowAreas: false,
      allowClubs: true,
      minFee: 0.5,
      minCost: 5,
      informActive: "<EMAIL>",
      paymentCodeMandatory: false,
      stripeMandatory: false,
      useStripeConnect: true,
      waitingListDays: 4,
      homePage: {
        message: "DEV 12th Aug 21",
        defaultFilters: {
          fromDate: "",
          toDate: "",
          type: "ALL",
          freeTextSearch: "",
          event: { id: 0, name: "" },
          compOrg: { id: 0, name: "" },
          location: { id: 0, name: "" },
          organiser: { id: 0, name: "" },
        },
        closeDisplay: "closeOnly",
      },
      maintenance: false,
      cartMessage:
        "<br>Please Note : With the new Strong Customer Authentication regulation ( SCA ), you may be required to complete a two part payment validation at the checkout.",
      adminEmail: "entry4sports.com",
      nationalminCost: 12,
      nationaladdCost: 0,
      addCost: 0.35,
      percCharged: 5,
      message: "",
      homeMessage: "DEV 12th Aug 21",
      health: {
        paymentStatus: { lastRun: "2021-08-12T08:50:26+01:00", every: 86400 },
        healthMonitor: { lastRun: "2021-08-15T14:19:08+01:00", every: 360 },
        waitingList: { lastRun: "2021-08-15T18:00:48+01:00", every: 900 },
        entryCheck: { lastRun: "2021-08-15T18:23:43+01:00", every: 120 },
        orderCheck: { lastRun: "2021-08-15T18:23:45+01:00", every: 360 },
        emailCheck: { lastRun: "2022-05-30T18:03:51+01:00", every: 360 },
      },
      cartTimeLimit: 30,
    },
    env: "dev",
    features: { feederClone: true, checkIn: true },
    maintenance: false,
    aos: [
      {
        id: 1,
        code: "IRL",
        description: "Athletics Ireland",
        prefix: "ire_",
        urnformat: "(^[0-9]{6}$)",
        urnmessage: "Please enter 6 digits",
        icon: "",
        name: "Athletics Ireland",
        registrationLink: "https://eventmaster.ie",
      },
      {
        id: 2,
        code: "EA",
        description: "United Kingdom",
        prefix: "uk_",
        urnformat: "(^[0-9]{7}$)",
        urnmessage: "Please enter 7 digits",
        icon: "",
        name: "England Athletics",
        registrationLink: "https://englandathletics.org/",
      },
      {
        id: 3,
        code: "ANI",
        description: "Athletics Northern Ireland",
        prefix: "",
        urnformat: "(^[0-9]{6}$)",
        urnmessage: "Please enter 6 digits",
        icon: "",
        name: "Athletics NI",
        registrationLink: "https://opentrack.run",
      },
    ],
    userInfo: {
      e4s_pagessize: 10,
      e4s_schedInfoState: 1,
      e4s_user: 1,
      e4s_credit: 0,
      e4s_useraccess: 1,
      e4s_stripe_approved_date: "2020-01-01 12:00:00",
      e4s_stripe_connected_date: "2020-01-01 12:00:00",
      orgs: [{ id: 38, name: "National", locations: [] }],
      areas: [
        {
          areaid: 9,
          areaname: "Leinster",
          areashortname: "Len",
          areaparentid: 6,
          entityLevel: 3,
          entitylevel: 3,
          entityName: "Region",
        },
      ],
      clubs: [
        {
          id: 11013,
          externid: 32923,
          Clubname: "Paxcroft Road Runners ",
          shortcode: null,
          Region: "Wiltshire",
          Country: "England",
          areaid: 137,
          clubtype: "C",
          active: 0,
          options: null,
          entityName: "Club",
          entityLevel: 1,
        },
      ],
      user: {
        id: 1,
        user_login: "E4SAdmin",
        user_nicename: "e4sadmin",
        user_email: "<EMAIL>",
        display_name: "E4S Admin",
        role: "E4SUSER",
        google_email: "",
        impersonating: false,
        e4sCredit: [0],
        permissions: [
          {
            id: 5,
            userid: 1,
            role: { id: 0, name: "All" },
            comp: { id: 0, name: "" },
            org: { id: 0, name: "All" },
            permLevels: [],
          },
        ],
      },
      wp_role: "administrator",
      security: {
        permissions: [
          {
            id: 5,
            userid: 1,
            roleid: 0,
            orgid: 0,
            compid: 0,
            role: "all",
            orgname: "All",
          },
        ],
        permLevels: { "": [] },
      },
    },
    userId: 1,
    checkIn: {
      shortUrl: "http://e4scheck.in/{compid}",
      defaultText:
        "Welcome to the Check-in service for our competition. To help us in the registration process, we would appreciate if you could let us know which events you are taking part in today. This will help reduce the number of athletes and the time you spend at the registration area picking up your bib number.\r\nWe have provided a number of methods to do this, please choose one of the methods below.",
    },
    role: "E4SUSER",
    menus: {
      reports: [
        {
          title: "Rolling Week finance",
          link: "https://uk.entry4sports.com/wp-json/e4s/v5/reports/1",
          description: "A Rolling week report",
        },
        {
          title: "Competitions report",
          link: "https://uk.entry4sports.com/wp-json/e4s/v5/reports/2",
          description: "Full description of what Report 2 shows",
        },
        {
          title: "Another Finance Report",
          link: "https://uk.entry4sports.com/wp-json/e4s/v5/reports/3",
          description: "Full description of what Report 3 shows",
        },
        {
          title: "Some other finance report",
          link: "https://uk.entry4sports.com/wp-json/e4s/v5/reports/4",
          description: "Full description of what Report 4 shows",
        },
      ],
      admin: {
        orgs: [],
        builder: true,
        cheques: true,
        userSearch: true,
        userPerms: true,
      },
    },
    classifications: [
      {
        id: 1,
        class: 11,
        title: "Visual impairment",
        description:
          "These athletes have a very low visual acuity and/or no light perception",
      },
      {
        id: 2,
        class: 12,
        title: "Visual impairment",
        description:
          "Athletes with a T12/F12 sport class have a higher visual acuity than athletes competing in the T11/F11 sport class and/or a visual field of less than five degrees radius",
      },
      {
        id: 3,
        class: 13,
        title: "Visual impairment",
        description:
          "Athletes with a T13/F13 sport class have the least severe visual impairment eligible for IPC Athletics. They have the highest visual acuity and/or a visual field of less than 20 degrees radius",
      },
      {
        id: 4,
        class: 20,
        title: "Intellectual impairment",
        description:
          "Athletes in this class have an intellectual impairment that impacts on the activities of running (400m - marathon), jumping (long jump and triple jump) or throwing events (shot put). As indicated in table 1, there is one sport class for running and jumping events (T20) and one for field events (F20) and athletes must meet the sport-specific MDC for each of their respective events (running, jumping or throwing).",
      },
      {
        id: 5,
        class: 35,
        title: "Coordination impairments (hypertonia, ataxia and athetosis)",
        description:
          "Athletes are typically affected in all four limbs but more so in the legs than the arms. Running gait is moderately to severely impacted, with stride length typically shortened.",
      },
      {
        id: 6,
        class: 36,
        title: "Coordination impairments (hypertonia, ataxia and athetosis)",
        description:
          "These athletes demonstrate moderate athetosis, ataxia and sometimes hypertonia or a mixture of these which affects all four limbs. The arms are usually similarly or more affected than the legs. Involuntary movements are clearly evident throughout the trunk and/or in the limbs in all sport activities, either when the athlete is attempting to stand still (athetosis) or when attempting a specific movement (tremor).",
      },
      {
        id: 7,
        class: 37,
        title: "Coordination impairments (hypertonia, ataxia and athetosis)",
        description:
          "Athletes have moderate hypertonia, ataxia or athetosis in one half of the body. The other side of the body may be minimally affected but always demonstrates good functional ability in running. Arm action is asymmetrical. Some trunk asymmetry is usually evident.",
      },
      {
        id: 8,
        class: 38,
        title: "Coordination impairments (hypertonia, ataxia and athetosis)",
        description:
          "Athletes have clear evidence of hypertonia, ataxia and/or athetosis on physical assessment that will affect running. Co-ordination impairment is mild to moderate and can be in one to four limbs. Co-ordination and balance are typically mildly affected, and overall these athletes are able to run and jump freely.",
      },
      {
        id: 9,
        class: 40,
        title: "Short stature",
        description:
          "Athletes with short stature compete in sport class T40/F40 and T41/F41. There are two classes depending on the body height of the athlete and the proportionality of the upper limbs. Athletes in classes T40 or F40 have a shorter stature than T41 and F41.",
      },
      {
        id: 10,
        class: 41,
        title: "Short stature",
        description:
          "Athletes with short stature compete in sport class T40/F40 and T41/F41. There are two classes depending on the body height of the athlete and the proportionality of the upper limbs. Athletes in classes T40 or F40 have a shorter stature than T41 and F41.",
      },
      {
        id: 11,
        class: 42,
        title:
          "Lower limb affected by limb deficiency, leg length difference, impaired muscle power or impaired range of movement",
        description:
          "Athletes have one or more impairment types affecting hip and/or knee function in one or both limbs and with activity limitations in throws, jumps and running comparable to that of an athlete with at least a single through or above knee amputation. Athletes with impairment(s) roughly comparable to bilateral above knee amputations are also placed in this class",
      },
      {
        id: 12,
        class: 43,
        title:
          "Lower limb affected by limb deficiency, leg length difference, impaired muscle power or impaired range of movement",
        description:
          "Athletes have bilateral lower limb impairments where both limbs meet the MDC, and where functional loss is in the feet, ankles and/or lower legs. The activity limitation in athletics is roughly comparable to that found in an athlete with bilateral below-knee amputations.",
      },
      {
        id: 13,
        class: 44,
        title:
          "Lower limb affected by limb deficiency, leg length difference, impaired muscle power or impaired range of movement",
        description:
          "This class is for any athlete with a unilateral or a combination of lower limb impairment/s where the impairment in only one limb meets the MDC. Functional loss is seen in one foot, ankle and/or lower leg. The activity limitation in athletics is roughly comparable to that found in an athlete with one through ankle / below knee amputation.",
      },
      {
        id: 14,
        class: 45,
        title:
          "Upper limb/s affected by limb deficiency, impaired muscle power or impaired range of movement",
        description:
          "Athletes have impairments of both arms affecting the shoulder and/or elbow joints which are comparable to the activity limitations in running and jumping as experienced by an athlete with bilateral above elbow amputations",
      },
      {
        id: 15,
        class: 46,
        title:
          "Upper limb/s affected by limb deficiency, impaired muscle power or impaired range of movement",
        description:
          "Athletes have a unilateral upper limb impairment that affects the shoulder and/or elbow joint of one arm and which is comparable to the activity limitations in running and jumps roughly comparable to that found in an athlete with a unilateral above elbow amputation. Athletes who have impairments of both arms, affecting elbow and wrist and roughly comparable to the activity limitations experienced by an athlete with bilateral through wrist / below elbow amputations of both arms, or an athlete with one above elbow amputation and one below elbow amputation, will also be placed in this class.",
      },
      {
        id: 16,
        class: 32,
        title: "Coordination impairments (hypertonia, ataxia and athetosis)",
        description:
          "Athletes have moderate to severe co-ordination impairment affecting all four limbs and trunk, but usually with slightly more function on one side of the body or in the legs. Function is affected so that throwing and wheelchair propulsion is difficult. Trunk control is poor. Hand function is very poor with a limited static grip, severely reduced throwing motion and poor follow through and release.",
      },
      {
        id: 17,
        class: 33,
        title: "Coordination impairments (hypertonia, ataxia and athetosis)",
        description:
          "Athletes have moderate to severe co-ordination impairment of three to four limbs, but typically have almost full functional control in the least impaired arm. Forward propulsion of the wheelchair is impacted by significant asymmetry in arm action and/or very poor grasp and release in one hand and limited trunk movement.",
      },
      {
        id: 18,
        class: 34,
        title: "Coordination impairments (hypertonia, ataxia and athetosis)",
        description:
          "Athletes are generally affected in all four limbs but more in the lower limbs than the upper limbs. The arms and trunk demonstrate fair to good functional strength and near to able-bodied grasp, release and relatively symmetrical wheelchair propulsion.",
      },
      {
        id: 19,
        class: 51,
        title:
          "Limb deficiency, leg length difference, impaired muscle power or impaired range of movement",
        description:
          "Athletes usually have decreased shoulder muscle power and difficulty straightening the elbows for a pushing action required for wheelchair racing propulsion. There is no muscle power in the trunk. Wheelchair propulsion is achieved with a pulling action using the elbow flexor and wrist extensor muscles.",
      },
      {
        id: 20,
        class: 52,
        title:
          "Limb deficiency, leg length difference, impaired muscle power or impaired range of movement",
        description:
          "Athletes use their shoulder, elbow and wrist muscles for wheelchair propulsion. There is poor to full muscle power in the fingers with wasting of the intrinsic muscles of the hands. Muscle power in the trunk is typically absent.",
      },
      {
        id: 21,
        class: 53,
        title:
          "Limb deficiency, leg length difference, impaired muscle power or impaired range of movement",
        description:
          "Athletes typically have full function of the arms but no abdominal or lower spinal muscle activity (grade 0).",
      },
      {
        id: 22,
        class: 54,
        title:
          "Limb deficiency, leg length difference, impaired muscle power or impaired range of movement",
        description:
          "Athletes have full upper muscle power in the arms and some to full muscle power in the trunk. Athletes may have some function in the legs.",
      },
      {
        id: 23,
        class: 31,
        title: "Coordination impairments (hypertonia, ataxia and athetosis)",
        description:
          "Athletes have severe hypertonia or athetosis, with very poor functional range, and/or control of movement in all four limbs and the trunk. Hand function is very poor with a limited static grip, severely reduced throwing motion and poor follow through and release.",
      },
      {
        id: 24,
        class: 55,
        title:
          "Limb deficiency, leg length difference, impaired muscle power or impaired range of movement",
        description:
          "Athletes have full function of the arms and partial to full trunk muscle power. There is no movement in the lower limbs. Athletes with bilateral hip disarticulations are appropriately placed in this class.",
      },
      {
        id: 25,
        class: 56,
        title:
          "Limb deficiency, leg length difference, impaired muscle power or impaired range of movement",
        description:
          "Athletes have full arm and trunk muscle power. Pelvic stability is provided by some to full ability to press the knees together. Hip abductor and hip extensor muscles are typically absent. Equivalent activity limitations are seen in athletes with bilateral high above knee amputations. Athletes with some but non-functional muscle power in the lower limbs will also fit in this class.",
      },
      {
        id: 26,
        class: 57,
        title:
          "Limb deficiency, leg length difference, impaired muscle power or impaired range of movement",
        description:
          "Athletes who meet one or more of the MDC for impaired muscle power, limb deficiency, impaired passive range of movement and leg length difference, who do not fit any of the previously described profiles, fall into this class.",
      },
      {
        id: 27,
        class: 47,
        title:
          "Upper limb/s affected by limb deficiency, impaired muscle power or impaired range of movement",
        description:
          "Athletes with a unilateral upper limb impairment resulting in some loss of function at the shoulder, elbow and wrist and which impacts sprints primarily. The impact of the impairment is comparable to the activity limitations experienced by an athlete with a unilateral through wrist/ below elbow amputation.",
      },
      {
        id: 28,
        class: 1,
        title: "RR1",
        description:
          "Athletes with severe athetoid, spastic, ataxic dystonic or mixed quadriplegia are in this class.",
      },
      {
        id: 29,
        class: 2,
        title: "RR2",
        description:
          "Athletes with moderate involvement in the upper extremities and trunk with moderate to severe involvement of the lower extremities are in this class.",
      },
      {
        id: 30,
        class: 3,
        title: "RR3",
        description:
          "Athletes with mild to moderate involvement in one or both upper extremities, fair to good trunk balance and moderate involvement in the lower extremities are in this class.",
      },
      {
        id: 31,
        class: 61,
        title:
          "(Lower limb/s competing with prosthesis affected by limb deficiency and leg length difference)",
        description:
          "Athletes with bilateral through knee or above knee limb deficiency competing with prostheses where minimum impairment criteria for lower limb deficiency are met (see World Para Athletics Classification Rules and Regulations).",
      },
      {
        id: 32,
        class: 62,
        title:
          "(Lower limb/s competing with prosthesis affected by limb deficiency and leg length difference)",
        description:
          "Athletes with bilateral below knee limb deficiency competing with prostheses where minimum impairment criteria for lower limb deficiency are met (see World Para Athletics Classification Rules and Regulations).",
      },
      {
        id: 33,
        class: 63,
        title:
          "(Lower limb/s competing with prosthesis affected by limb deficiency and leg length difference)",
        description:
          "Athletes with single through knee or above knee limb deficiency competing with a prosthesis where minimum impairment criteria for lower limb deficiency are met (see World Para Athletics Classification Rules and Regulations).",
      },
      {
        id: 34,
        class: 64,
        title:
          "(Lower limb/s competing with prosthesis affected by limb deficiency and leg length difference)",
        description:
          "Athletes with unilateral below knee limb deficiency competing with a prosthesis where the minimum impairment criteria for lower limb deficiency and leg length discrepancy are met (see World Para Athletics Rules and Regulations).",
      },
    ],
    logout: "https://dev.entry4sports.co.uk/wp-json/e4s/v5/logout",
    help: [
      {
        id: 1,
        preload: true,
        key: "showentries",
        title: "Entry4Sports Home Page",
        data: "https://support.entry4sports.com/the-home-page",
        type: "U",
      },
      {
        id: 5,
        preload: true,
        key: "builder",
        title: "Competition Builder",
        data: "https://support.entry4sports.com/builder",
        type: "U",
      },
      {
        id: 11,
        preload: true,
        key: "athletes",
        title: "Athlete Help",
        data: "Testing<b>HTML</b> help for Nick<br>\r\nThis is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br>This is some <b>HTML</b> help for Nick<br> THIS IS THE END",
        type: "H",
      },
    ],
    messages: [
      {
        id: 131,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: "ghfdsahs",
        sendFrom: "E4S_Entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 17:03:52",
        dateCreated: "2022-05-30 17:03:38",
        body: "hdhdahffdh",
        dateSentISO: "2022-05-30T17:03:52+01:00",
        dateCreatedISO: "2022-05-30T17:03:38+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 125,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: "what",
        sendFrom: "E4S_Entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:35:27",
        dateCreated: "2022-05-30 16:35:15",
        body: "test",
        dateSentISO: "2022-05-30T16:35:27+01:00",
        dateCreatedISO: "2022-05-30T16:35:15+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 124,
        sendTo: "_COMP310_",
        error: "You must provide at least one recipient email address.",
        priority: 1,
        subject: "testtest",
        sendFrom: "E4S_Entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:30:37",
        dateCreated: "2022-05-30 16:26:56",
        body: "testtsts",
        dateSentISO: "2022-05-30T16:30:37+01:00",
        dateCreatedISO: "2022-05-30T16:26:56+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 123,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: 11,
        sendFrom: "E4S_Entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:33:32",
        dateCreated: "2022-05-30 16:24:47",
        body: 11,
        dateSentISO: "2022-05-30T16:33:32+01:00",
        dateCreatedISO: "2022-05-30T16:24:47+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 122,
        sendTo: "_COMP310_",
        error: "You must provide at least one recipient email address.",
        priority: 1,
        subject: 22,
        sendFrom: "E4S_Entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 14:29:27",
        dateCreated: "2022-05-30 14:29:27",
        body: 22,
        dateSentISO: "2022-05-30T14:29:27+01:00",
        dateCreatedISO: "2022-05-30T14:29:27+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 121,
        sendTo: "_COMP310_",
        error: "You must provide at least one recipient email address.",
        priority: 1,
        subject: 11,
        sendFrom: "E4S_Entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 14:27:09",
        dateCreated: "2022-05-30 14:27:09",
        body: 11,
        dateSentISO: "2022-05-30T14:27:09+01:00",
        dateCreatedISO: "2022-05-30T14:27:09+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 120,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: "tttee",
        sendFrom: "E4S_Entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:33:31",
        dateCreated: "2022-05-30 14:23:24",
        body: "tteee",
        dateSentISO: "2022-05-30T16:33:31+01:00",
        dateCreatedISO: "2022-05-30T14:23:24+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 119,
        sendTo: "_COMP310_",
        error: "You must provide at least one recipient email address.",
        priority: 1,
        subject: "test to athletes of 310",
        sendFrom: "UK_Entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 10:39:14",
        dateCreated: "2022-05-30 10:39:14",
        body: "test",
        dateSentISO: "2022-05-30T10:39:14+01:00",
        dateCreatedISO: "2022-05-30T10:39:14+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 106,
        sendTo: "_COMP310_",
        error: "You must provide at least one recipient email address.",
        priority: 1,
        subject: "l",
        sendFrom: "UK_Entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 09:37:18",
        dateCreated: "2022-05-30 09:37:14",
        body: "l",
        dateSentISO: "2022-05-30T09:37:18+01:00",
        dateCreatedISO: "2022-05-30T09:37:14+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 105,
        sendTo: "_COMP310_",
        error: "",
        priority: 1,
        subject: "k",
        sendFrom: "",
        type: "M",
        options: null,
        dateSent: "",
        dateCreated: "2022-05-30 09:35:01",
        body: "k",
        dateSentISO: "",
        dateCreatedISO: "2022-05-30T09:35:01+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 104,
        sendTo: "_COMP310_",
        error: "You must provide at least one recipient email address.",
        priority: 1,
        subject: "j",
        sendFrom: "UK_Entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 09:31:11",
        dateCreated: "2022-05-30 09:30:21",
        body: "j",
        dateSentISO: "2022-05-30T09:31:11+01:00",
        dateCreatedISO: "2022-05-30T09:30:21+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 103,
        sendTo: "_COMP310_",
        error: "",
        priority: 1,
        subject: "i",
        sendFrom: "UK_Entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 09:29:09",
        dateCreated: "2022-05-30 09:24:26",
        body: "i",
        dateSentISO: "2022-05-30T09:29:09+01:00",
        dateCreatedISO: "2022-05-30T09:24:26+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 102,
        sendTo: "_COMP310_",
        error: "",
        priority: 1,
        subject: "h",
        sendFrom: "",
        type: "M",
        options: null,
        dateSent: "",
        dateCreated: "2022-05-30 09:22:08",
        body: "h",
        dateSentISO: "",
        dateCreatedISO: "2022-05-30T09:22:08+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 101,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: "g",
        sendFrom: "entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:33:31",
        dateCreated: "2022-05-30 09:19:41",
        body: "g",
        dateSentISO: "2022-05-30T16:33:31+01:00",
        dateCreatedISO: "2022-05-30T09:19:41+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 100,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: "f",
        sendFrom: "entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:33:30",
        dateCreated: "2022-05-30 09:18:02",
        body: "f",
        dateSentISO: "2022-05-30T16:33:30+01:00",
        dateCreatedISO: "2022-05-30T09:18:02+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 99,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: "e",
        sendFrom: "entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:33:29",
        dateCreated: "2022-05-30 09:16:09",
        body: "e",
        dateSentISO: "2022-05-30T16:33:29+01:00",
        dateCreatedISO: "2022-05-30T09:16:09+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 98,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: "d",
        sendFrom: "entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:33:28",
        dateCreated: "2022-05-30 09:15:24",
        body: "d",
        dateSentISO: "2022-05-30T16:33:28+01:00",
        dateCreatedISO: "2022-05-30T09:15:24+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 97,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: "c",
        sendFrom: "entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:33:27",
        dateCreated: "2022-05-30 09:14:25",
        body: "c",
        dateSentISO: "2022-05-30T16:33:27+01:00",
        dateCreatedISO: "2022-05-30T09:14:25+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 96,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: "test b",
        sendFrom: "entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:33:26",
        dateCreated: "2022-05-30 09:08:29",
        body: "b",
        dateSentISO: "2022-05-30T16:33:26+01:00",
        dateCreatedISO: "2022-05-30T09:08:29+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 95,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: "test a",
        sendFrom: "entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:33:20",
        dateCreated: "2022-05-30 09:07:47",
        body: "a",
        dateSentISO: "2022-05-30T16:33:20+01:00",
        dateCreatedISO: "2022-05-30T09:07:47+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 94,
        sendTo: "_COMP310_",
        error: "",
        priority: 2,
        subject: "test 2",
        sendFrom: "entries",
        type: "E",
        options: null,
        dateSent: "2022-05-30 16:33:18",
        dateCreated: "2022-05-30 08:17:44",
        body: "for Jessica",
        dateSentISO: "2022-05-30T16:33:18+01:00",
        dateCreatedISO: "2022-05-30T08:17:44+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: null,
            id: null,
            email: null,
            name: null,
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
      {
        id: 93,
        sendTo: "_COMP310_",
        error: "",
        priority: 1,
        subject: "test to Nick",
        sendFrom: "",
        type: "M",
        options: null,
        dateSent: "",
        dateCreated: "2022-05-30 08:14:51",
        body: "Athlete Jessica Day",
        dateSentISO: "",
        dateCreatedISO: "2022-05-30T08:14:51+01:00",
        competition: { id: 310, name: "test emails" },
        user: {
          id: 1,
          name: "310:test emails",
          email: "<EMAIL>",
        },
        forUsers: [
          {
            muId: 48,
            id: 1,
            email: "<EMAIL>",
            name: "e4sadmin",
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "2022-05-30 08:17:22",
              dateReadISO: "2022-05-30T08:17:22+01:00",
            },
          },
        ],
      },
      {
        id: 74,
        sendTo: "<EMAIL>",
        error: "",
        priority: 1,
        subject: "E4S Request. dev.entry4sports.co.uk/279: Competition query",
        sendFrom: "<EMAIL>",
        type: "E",
        options: null,
        dateSent: "2022-05-05 11:20:55",
        dateCreated: "2022-05-05 11:20:55",
        body: "Dear E4S Support,You have received this message from E4SAdminvia the Entry4Sports Website.34r5ert...",
        dateSentISO: "2022-05-05T11:20:55+01:00",
        dateCreatedISO: "2022-05-05T11:20:55+01:00",
        competition: { id: 0, name: "" },
        user: { id: 1, name: "e4sadmin", email: "<EMAIL>" },
        forUsers: [
          {
            muId: 34,
            id: 1,
            email: "<EMAIL>",
            name: "e4sadmin",
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
          {
            muId: 35,
            id: 1,
            email: "<EMAIL>",
            name: "e4sadmin",
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "2022-05-05 12:20:55",
              dateReadISO: "2022-05-05T12:20:55+01:00",
            },
          },
        ],
      },
      {
        id: 63,
        sendTo: "<EMAIL>",
        error:
          "SMTP Error: The following recipients failed: <EMAIL>: Requested action aborted: local error in processing\r\n",
        priority: 1,
        subject: "E4S Request. dev.entry4sports.co.uk/279: Competition query",
        sendFrom: "<EMAIL>",
        type: "E",
        options: null,
        dateSent: "2022-04-27 14:38:45",
        dateCreated: "2022-04-27 14:38:45",
        body: "Dear E4S Support,You have received this <NAME_EMAIL> via the Entry4Sports Website.comp",
        dateSentISO: "2022-04-27T14:38:45+01:00",
        dateCreatedISO: "2022-04-27T14:38:45+01:00",
        competition: { id: 0, name: "" },
        user: { id: -1, name: null, email: null },
        forUsers: [
          {
            muId: 28,
            id: 1,
            email: "<EMAIL>",
            name: "e4sadmin",
            status: {
              dateDeleted: "",
              dateDeletedISO: "",
              dateRead: "",
              dateReadISO: "",
            },
          },
        ],
      },
    ],
  },
  configMeta: {
    locations: [
      { id: 103, name: "a location", postCode: "" },
      { id: 104, name: "a n other", postCode: "" },
      { id: 72, name: "AIT International Arena, Athlone", postCode: "" },
      { id: 83, name: "ALEXANDER STADIUM", postCode: "" },
      { id: 105, name: "another location", postCode: "" },
      { id: 1, name: "Athlone IT", postCode: "" },
      { id: 16, name: "Ballingarry", postCode: "" },
      { id: 24, name: "Ballyneale", postCode: "" },
      { id: 64, name: "Beaufort", postCode: "" },
      { id: 37, name: "Belfast/Templemore", postCode: "" },
      { id: 48, name: "BFTTA", postCode: "" },
      { id: 86, name: "Blackbridge Jubilee Track", postCode: "GL2 5AB" },
      {
        id: 26,
        name: "Blackheath and Bromley Harriers AC",
        postCode: "BR2 9EJ",
      },
      { id: 61, name: "Brands Hatch", postCode: "DA3 8NG" },
      { id: 29, name: "Castleisland", postCode: "" },
      { id: 42, name: "CIT Arena", postCode: "T12 P928" },
      { id: 25, name: "Clonmel", postCode: "" },
      { id: 62, name: "Conna", postCode: "" },
      { id: 8, name: "Connacht", postCode: "" },
      { id: 44, name: "Dangan Regional Sports Centre", postCode: "" },
      { id: 50, name: "Dartford", postCode: "DA1 1JP" },
      { id: 3, name: "Dundalk I.T", postCode: "A91 K584" },
      { id: 10, name: "Dundrum", postCode: "" },
      { id: 102, name: "Edmondscote Athletics Track", postCode: "CV32 6AD" },
      { id: 73, name: "Fethard", postCode: "" },
      { id: 53, name: "Galbally", postCode: null },
      { id: 28, name: "Galway", postCode: "H91 YC9W" },
      { id: 89, name: "Guernsey Athletics Track", postCode: "" },
      { id: 69, name: "Hillingdon Cycle Circuit", postCode: "UB4 0LP" },
      { id: 80, name: "Hornchurch stadium", postCode: "RM14 2LX" },
      { id: 76, name: "Irishtown Stadium", postCode: "" },
      {
        id: 19,
        name: "IT WILL TAKE PLACE ON 5th SEPTEMBER details to follow",
        postCode: "CV22 5LJ",
      },
      { id: 91, name: "Jarman Park", postCode: "HP2 4JS" },
      { id: 101, name: "Julie Rose Stadium", postCode: "TN24 9QX" },
      { id: 96, name: "K2 Crawley ", postCode: "RH11 9BQ" },
      { id: 12, name: "Kettering Town Harriers", postCode: "NN15 7EP" },
      { id: 90, name: "Lee Valley Athletics Centre", postCode: "N9 0AR" },
      { id: 49, name: "Lees Road", postCode: "V95 EHH5" },
      { id: 9, name: "Leinster", postCode: "Dublin15" },
      {
        id: 32,
        name: "Leixlip Amenities centre, Leixlip",
        postCode: "W23 R942",
      },
      { id: 56, name: "Limerick Racecourse", postCode: "V94 K858" },
      { id: 88, name: "Linford Christie Stadium", postCode: "W12 0AE" },
      { id: 51, name: "London Marathon Community Track", postCode: "E20 2ST" },
      { id: 18, name: "Loughborough", postCode: "LE11 3TU" },
      { id: 43, name: "Lourdes Stadium, Drogheda", postCode: "W23 R942" },
      { id: 97, name: "Manchester", postCode: "" },
      { id: 84, name: "MCAA", postCode: "" },
      { id: 23, name: "Medway Park", postCode: "ME7 1HF" },
      { id: 39, name: "Morton Stadium", postCode: "" },
      { id: 35, name: "Morton Stadium Santry", postCode: "" },
      { id: 85, name: "Moulton College", postCode: "NN3 7RR" },
      { id: 57, name: "Moyglass", postCode: "" },
      { id: 5, name: "Moyne", postCode: "" },
      { id: 40, name: "N.Ireland and Ulster", postCode: "F93 NV0T" },
      {
        id: 77,
        name: "National Cross Country Track-Sport Ireland Campus",
        postCode: "https://",
      },
      { id: 2, name: "National Indoor Arena", postCode: "" },
      { id: 71, name: "National Indoor Arena", postCode: "Dublin 1" },
      { id: 68, name: "National Sports Campus", postCode: "" },
      { id: 33, name: "Newham", postCode: "E13 8SD" },
      { id: 58, name: "Newport", postCode: "" },
      { id: 14, name: "Nottingham", postCode: "NG8 4PB" },
      { id: 70, name: "Papel Cross, Phoenix Park, Dublin ", postCode: "" },
      { id: 4, name: "Pheonix Park", postCode: "" },
      { id: 20, name: "Pingles Stadium 2", postCode: "CV11 4LX" },
      { id: 27, name: "Raheny", postCode: "" },
      { id: 93, name: "Rockingham Triangle Stadium", postCode: "NN17 2FB" },
      { id: 79, name: "Rugby", postCode: "CV22 5LJ" },
      { id: 98, name: "Saffron Lane Athletics Stadium", postCode: "LE2 7NL" },
      { id: 75, name: "Santry", postCode: "" },
      { id: 31, name: "Shoreline Leisure Greystones", postCode: "" },
      { id: 45, name: "Sligo Institute of Technology", postCode: "" },
      { id: 66, name: "St Annes Park", postCode: "" },
      { id: 95, name: "StoneX Stadium", postCode: "NW4 1RL" },
      { id: 92, name: "Stourport Sports Club", postCode: "DY13 8BQ" },
      { id: 55, name: "Sutcliffe Park Sports Centre", postCode: "SE9 5LW" },
      { id: 74, name: "Tallaght", postCode: "" },
      { id: 38, name: "TBC", postCode: "" },
      { id: 52, name: "Telford AC", postCode: "TF2 7AB" },
      { id: 30, name: "Templemore", postCode: "" },
      { id: 11, name: "Templemore A.C.", postCode: "" },
      { id: 87, name: "Thames Valley Athletics Centre", postCode: "SL4 6HN" },
      { id: 22, name: "Thames Valley Harriers", postCode: "W12 0AE" },
      { id: 60, name: "The Demesne", postCode: "" },
      { id: 100, name: "The Julie Rose Stadium", postCode: "TN24 9QX" },
      {
        id: 94,
        name: "The Sir Roger Bannister Running Track",
        postCode: "OX4 1EQ",
      },
      { id: 59, name: "The Turnpike", postCode: "" },
      { id: 63, name: "Thurles Crokes Athletic Club", postCode: "E41 N520" },
      { id: 17, name: "Tipperary A.C.", postCode: "" },
      { id: 15, name: "Tipton Harriers", postCode: "DY4 0BS" },
      { id: 99, name: "Tonbridge School Centre", postCode: "" },
      { id: 34, name: "Track Academy", postCode: "NW10 3QX" },
      { id: 41, name: "Tullamore AC Stadium", postCode: "R35 CK22" },
      { id: 106, name: "wefwfwfw", postCode: "" },
      { id: 65, name: "Wickstead Park", postCode: "NN15 6NJ" },
      { id: 82, name: "Woodford Green", postCode: "" },
      { id: 81, name: "Wyndley Leisure Centre", postCode: "B73 6EN" },
    ],
    organisers: [
      { id: 91, name: "Achilles Club" },
      { id: 100, name: "Achilles Schools" },
      { id: 72, name: "AIT Sport" },
      { id: 105, name: "Ashford Athletic Club" },
      { id: 84, name: "Berkshire County AA" },
      { id: 64, name: "BFTTA" },
      { id: 78, name: "Birchfield Harriers" },
      { id: 58, name: "Blackheath and Bromley Harriers AC" },
      { id: 67, name: "British Athletics" },
      { id: 70, name: "Cambridge Harriers" },
      { id: 52, name: "Charnwood" },
      { id: 63, name: "Connacht" },
      { id: 44, name: "Connacht Athletics" },
      { id: 94, name: "Corby Athletic Club" },
      { id: 76, name: "Cork County" },
      { id: 59, name: "County Galway" },
      { id: 65, name: "Dartford Harriers" },
      { id: 77, name: "Dublin" },
      { id: 1, name: "E4S Stripe" },
      { id: 106, name: "English Masters AA" },
      { id: 104, name: "English Schools Athletic Association" },
      { id: 66, name: "Galway" },
      { id: 93, name: "Gloucester AC" },
      { id: 83, name: "Gloucestershire County" },
      { id: 85, name: "Guernsey Athletic" },
      { id: 79, name: "Havering AC" },
      { id: 89, name: "Hertfordshire Schools" },
      { id: 71, name: "Kent Primary Schools" },
      { id: 47, name: "Kettering Town Harriers" },
      { id: 92, name: "Kidderminster and Stourport" },
      { id: 99, name: "Leicestershire & Rutland County" },
      { id: 40, name: "Leinster" },
      { id: 45, name: "Leinster Athletics" },
      { id: 88, name: "LICC" },
      { id: 74, name: "Loughton Athletic Club" },
      { id: 82, name: "MCAA" },
      { id: 57, name: "Medway and Maidstone AC" },
      { id: 90, name: "Midland Masters Athletic Club" },
      { id: 46, name: "Munster" },
      { id: 39, name: "Munster Athletics" },
      { id: 38, name: "National" },
      { id: 60, name: "Newham" },
      { id: 75, name: "North Leinster Schools" },
      { id: 73, name: "North West Indoor Games" },
      { id: 95, name: "Northamptonshire County" },
      { id: 49, name: "Notts AC" },
      { id: 54, name: "Nuneaton Opens" },
      { id: 80, name: "Royal Sutton Coldfield A.C." },
      { id: 53, name: "Rugby and Northampton AC" },
      { id: 102, name: "Runbase Events" },
      { id: 68, name: "Telford AC" },
      { id: 2, name: "Test Organisation" },
      { id: 56, name: "Thames Valley Harriers" },
      { id: 50, name: "Tipperary Athletics" },
      { id: 41, name: "Tipperary County" },
      { id: 51, name: "Tipton Harriers" },
      { id: 61, name: "Track Academy" },
      { id: 62, name: "Ulster" },
      { id: 98, name: "Warwickshire County" },
      { id: 97, name: "Warwickshire Schools" },
      { id: 81, name: "Woodford Green" },
      { id: 101, name: "Worcester AA" },
      { id: 103, name: "Worthing Harriers" },
    ],
    events: {
      "16": { id: 16, name: "Mile", tf: "T" },
      "18": { id: 18, name: "10 Mile ( Road )", tf: "R" },
      "20": { id: 20, name: "1000m", tf: "T" },
      "23": { id: 23, name: "100m Hurdles", tf: "T" },
      "27": { id: 27, name: "100m", tf: "T" },
      "31": { id: 31, name: "10km", tf: "T" },
      "34": { id: 34, name: "10 Mile", tf: "T" },
      "35": { id: 35, name: "1200m", tf: "T" },
      "39": { id: 39, name: "1500m", tf: "T" },
      "41": { id: 41, name: "150m", tf: "T" },
      "51": { id: 51, name: "200m", tf: "T" },
      "53": { id: 53, name: "250m Hurdles", tf: "T" },
      "54": { id: 54, name: "250m", tf: "T" },
      "61": { id: 61, name: "3000m", tf: "T" },
      "63": { id: 63, name: "300m Hurdles", tf: "T" },
      "64": { id: 64, name: "300m", tf: "T" },
      "95": { id: 95, name: "400m Hurdles", tf: "T" },
      "99": { id: 99, name: "400m", tf: "T" },
      "124": { id: 124, name: "5000m", tf: "T" },
      "126": { id: 126, name: "500m", tf: "T" },
      "132": { id: 132, name: "5k", tf: "T" },
      "133": { id: 133, name: "5m", tf: "T" },
      "136": { id: 136, name: "600m", tf: "T" },
      "137": { id: 137, name: "60m Hurdles", tf: "T" },
      "139": { id: 139, name: "60m", tf: "T" },
      "141": { id: 141, name: "750m xc", tf: "X" },
      "143": { id: 143, name: "75m Hurdles", tf: "T" },
      "144": { id: 144, name: "75m", tf: "T" },
      "147": { id: 147, name: "800m", tf: "T" },
      "148": { id: 148, name: "80m Hurdles", tf: "T" },
      "149": { id: 149, name: "80m", tf: "T" },
      "150": { id: 150, name: 80, tf: "T" },
      "199": { id: 199, name: "Discus", tf: "F" },
      "203": { id: 203, name: "Hammer", tf: "F" },
      "206": { id: 206, name: "High Jump", tf: "F" },
      "219": { id: 219, name: "Javelin", tf: "F" },
      "221": { id: 221, name: "Long Jump", tf: "F" },
      "229": { id: 229, name: "Pentathlon", tf: "M" },
      "230": { id: 230, name: "Pole Vault", tf: "F" },
      "231": { id: 231, name: "Quadrathlon", tf: "M" },
      "254": { id: 254, name: "Shotput", tf: "F" },
      "268": { id: 268, name: "Triple Jump", tf: "F" },
      "275": { id: 275, name: "Weight for Distance", tf: "F" },
      "512": { id: 512, name: "10000m", tf: "T" },
      "519": { id: 519, name: "110m Hurdles", tf: "T" },
      "529": { id: 529, name: "70m Hurdles", tf: "T" },
      "537": { id: 537, name: "Decathlon", tf: "M" },
      "539": { id: 539, name: "Heptathlon ", tf: "M" },
      "593": { id: 593, name: "4 Mile", tf: "T" },
      "597": { id: 597, name: "8k ( Road )", tf: "R" },
      "600": { id: 600, name: "Triathlon", tf: "M" },
      "605": { id: 605, name: "200m Hurdles", tf: "T" },
      "610": { id: 610, name: "Parkrun", tf: "X" },
      "611": { id: 611, name: "Octathon", tf: "M" },
      "612": { id: 612, name: "Marathon", tf: "R" },
      "613": { id: 613, name: "Half Marathon", tf: "R" },
      "614": { id: 614, name: "3 Mile", tf: "T" },
    },
  },
  il8nLanguage: "en",
  athleticOrgObj: {
    IRL: {
      id: 1,
      code: "IRL",
      description: "Athletics Ireland",
      prefix: "ire_",
      urnformat: "(^[0-9]{6}$)",
      urnmessage: "Please enter 6 digits",
      icon: "",
      name: "Athletics Ireland",
      registrationLink: "https://eventmaster.ie",
    },
    EA: {
      id: 2,
      code: "EA",
      description: "United Kingdom",
      prefix: "uk_",
      urnformat: "(^[0-9]{7}$)",
      urnmessage: "Please enter 7 digits",
      icon: "",
      name: "England Athletics",
      registrationLink: "https://englandathletics.org/",
    },
    ANI: {
      id: 3,
      code: "ANI",
      description: "Athletics Northern Ireland",
      prefix: "",
      urnformat: "(^[0-9]{6}$)",
      urnmessage: "Please enter 6 digits",
      icon: "",
      name: "Athletics NI",
      registrationLink: "https://opentrack.run",
    },
  },
  userEntity: { name: "", entityLevel: 0, id: 0, entityName: "", clubType: "" },
  ui: { version: "v1" },
} as any as IConfigStoreState;


