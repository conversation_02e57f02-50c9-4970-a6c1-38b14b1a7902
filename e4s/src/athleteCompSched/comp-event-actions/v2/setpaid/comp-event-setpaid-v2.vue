<template>
  <CardGenericV2 class="e4s-card--well e4s-gap--standard">
    <template slot="all">
      <LoadingSpinnerV2 v-if="state.isLoading"/>
      <FormGenericSectionTitleV2
        title-size="400"
        :section-title="eventTitle"
        :section-overview="
        'Toggle Event Paid Status of: ' +
        setPaidEventInputParams.compEvent.eventGroup +
        ' to ' +
        (setPaidEventInputParams.compEvent.paid ? 'NOT PAID' : 'PAID')
      "
        :show-cancel-button="false"
        v-on:cancel="cancel"
      />

      <p>Please enter a reason (required).</p>
      <InputRestrictLength
        slot="content"
        :use-text-area="true"
        :max-length="600"
        text-area-class="browser-default e4s-input-field e4s-input-field--primary ask-organiser--text-area"
        v-on:isMaxLength="isMaxLength = $event"
        v-on:onChanged="state.reason = $event"
      >
      </InputRestrictLength>
      <FormGenericInputErrorMessageV2 :error-message="errorMessages.reason" />

      <ButtonGroupSectionV2>
        <ButtonGenericV2
          slot="buttons-left"
          button-type="tertiary"
          v-on:click="cancel"
          text="Cancel"
        />

        <div slot="buttons-right">
          <ButtonGenericV2
            button-type="primary"
            v-on:click="askForConfirmation"
            text="Submit"
            v-if="!state.showConfirm"
          />
          <ButtonGenericV2
            button-type="primary"
            class="e4s-button--destructive"
            v-on:click="submit"
            text="Confirm"
            v-if="state.showConfirm"
          />
        </div>
      </ButtonGroupSectionV2>
    </template>
  </CardGenericV2>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  reactive,
  SetupContext,
} from "@vue/composition-api";
import FormGenericSectionTitleV2 from "../../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import {
  ISetPaidEventInputParams,
  ISetPaidEventState,
  ISetPaidEventUserOutputPayload,
} from "./comp-event-setpaid-models";
import FormGenericInputErrorMessageV2 from "../../../../common/ui/layoutV2/form/form-generic-input-error-message-v2.vue";
import InputRestrictLength from "../../../../common/ui/field/input-restrict-length/input-restrict-length.vue";
import ButtonGroupSectionV2 from "../../../../common/ui/layoutV2/buttons/button-group-section-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { AthleteCompSchedData } from "../../../athletecompsched-data";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import { IServerGenericResponse } from "../../../../common/common-models";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue"
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue"

export default defineComponent({
  name: "comp-event-setpaid-v2",
  components: {
    CardGenericV2,
    LoadingSpinnerV2,
    ButtonGenericV2,
    ButtonGroupSectionV2,
    InputRestrictLength,
    FormGenericInputErrorMessageV2,
    FormGenericSectionTitleV2,
  },
  props: {
    eventTitle: {
      type: String,
      required: true,
    },
    setPaidEventInputParams: {
      type: Object as PropType<ISetPaidEventInputParams>,
    },
  },
  setup(
    props: {
      eventTitle: string;
      setPaidEventInputParams: ISetPaidEventInputParams;
    },
    context: SetupContext
  ) {
    const state = reactive<ISetPaidEventState>({
      showConfirm: false,
      reason: "",
      isLoading: false,
    });

    const errorMessages = reactive({
      reason: "",
    });

    function doValidation(): boolean {
      errorMessages.reason = "";

      if (state.reason.length === 0) {
        errorMessages.reason = "Please enter a reason.";
        return false;
      }
      return true;
    }

    function askForConfirmation() {
      if (!doValidation()) {
        return;
      }

      state.showConfirm = true;
    }

    function cancel() {
      context.emit("cancel");
    }

    function submit() {
      if (!doValidation()) {
        return;
      }

      state.isLoading = true;

      const compEvent = props.setPaidEventInputParams.compEvent;
      const setPaidTo = compEvent.paid ? 0 : 1;
      const prom = new AthleteCompSchedData().setPaid(
        [compEvent.order.productId],
        setPaidTo,
        state.reason
      );
      handleResponseMessages(prom);
      prom
        .then((resp: IServerGenericResponse) => {
          if (resp.errNo === 0) {
            messageDispatchHelper("Saved.", USER_MESSAGE_LEVEL.INFO.toString());
            const setPaidEventUserOutputPayload: ISetPaidEventUserOutputPayload =
              {
                compEvent: props.setPaidEventInputParams.compEvent,
                reason: state.reason,
              };

            context.emit("submitted", setPaidEventUserOutputPayload);
          }
        })
        .finally(() => {
          state.isLoading = false;
        });
    }

    return { state, errorMessages, askForConfirmation, cancel, submit };
  },
});
</script>
