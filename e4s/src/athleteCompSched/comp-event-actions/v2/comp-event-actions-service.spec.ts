import * as CompEventActionsServiceV2 from "./comp-event-actions-service-v2";
import {
  mockCompEventActionsResult,
  mockConfigStoreState,
} from "./comp-event-actions-mock-data";
import { ICompEventActionsState } from "./comp-event-actions-service-v2";
import {
  convertToIsoDateTimeWithOffset,
  simpleClone,
} from "../../../common/common-service-utils";
// import {USER_ROLES} from "../../../config/config-app-models"
import { nickConfig } from "./config-store-nick-mock";
import {addDays, format} from "date-fns"
import {CompetitionService} from "../../../competition/competiton-service"

describe("CompEventActionsServiceV2", () => {
  test("init perms as E4SUSER", () => {
    const compEventActionsState: ICompEventActionsState =
      CompEventActionsServiceV2.init(
        mockCompEventActionsResult,
        mockConfigStoreState
      );

    expect(
      compEventActionsState.compEventActionsResult.competitionSummaryPublic
        .compId
    ).toBe(313);

    expect(mockConfigStoreState.configApp.role).toBe("E4SUSER");

    expect(compEventActionsState.perms.isAdmin).toBe(true);

    expect(compEventActionsState.perms.belongsToUser).toBe(false);

    expect(compEventActionsState.perms.hasBuilderPermission).toBe(true);
  });

  test("init perms as Nick", () => {
    const localConfigStoreState = simpleClone(nickConfig);

    // localConfigStoreState.configApp.role = "USER" as USER_ROLES;
    // localConfigStoreState.configApp.userInfo.security.permLevels.builder = [];
    // localConfigStoreState.configApp.userInfo.security.permissions = [];

    const compEventActionsState: ICompEventActionsState =
      CompEventActionsServiceV2.init(
        mockCompEventActionsResult,
        localConfigStoreState
      );

    expect(
      compEventActionsState.compEventActionsResult.competitionSummaryPublic
        .compId
    ).toBe(313);

    expect(localConfigStoreState.configApp.role).toBe("USER");

    expect(compEventActionsState.perms.isAdmin).toBe(false);

    // compEventActionsResult.compEvent.user.userId ===
    // configStoreState.configApp.userInfo.user.id

    //  nickwall entered this event...
    expect(
      compEventActionsState.compEventActionsResult.compEvent.user.userId
    ).toBe(19828);

    //  nickwall entered this event...
    expect(localConfigStoreState.configApp.userInfo.user.id).toBe(19828);

    expect(compEventActionsState.perms.belongsToUser).toBe(true);

    expect(compEventActionsState.perms.hasBuilderPermission).toBe(false);

    //  Event is in the past
    expect(
      compEventActionsState.compEventActionsResult.compEvent.startdate
    ).toBe("2022-07-31T12:00:00+01:00");

    expect(compEventActionsState.access.switch.result).toBe(false);

    expect(compEventActionsState.access.switch.message).toBe(
      "Entries now closed."
    );
  });

  test("init perms as Nick did not enter compevent entries closed", () => {
    const localConfigStoreState = simpleClone(nickConfig);
    const localCompEventActionsResult = simpleClone(mockCompEventActionsResult);

    localCompEventActionsResult.compEvent.user.userId = 12345;

    const compEventActionsState: ICompEventActionsState =
      CompEventActionsServiceV2.init(
        localCompEventActionsResult,
        localConfigStoreState
      );

    expect(
      compEventActionsState.compEventActionsResult.competitionSummaryPublic
        .compId
    ).toBe(313);

    expect(localConfigStoreState.configApp.role).toBe("USER");

    expect(compEventActionsState.perms.isAdmin).toBe(false);

    // compEventActionsResult.compEvent.user.userId ===
    // configStoreState.configApp.userInfo.user.id

    //  nickwall entered this event...
    expect(
      compEventActionsState.compEventActionsResult.compEvent.user.userId
    ).toBe(12345);

    //  nickwall entered this event...
    expect(localConfigStoreState.configApp.userInfo.user.id).toBe(19828);

    expect(compEventActionsState.perms.belongsToUser).toBe(false);

    expect(compEventActionsState.perms.hasBuilderPermission).toBe(false);

    //  Event is in the past
    expect(
      compEventActionsState.compEventActionsResult.compEvent.startdate
    ).toBe("2022-07-31T12:00:00+01:00");

    expect(compEventActionsState.access.switch.result).toBe(false);

    expect(compEventActionsState.access.switch.message).toBe(
      "Entries now closed."
    );
  });

  test("init perms as Nick did not enter compevent entries open", () => {
    const localConfigStoreState = simpleClone(nickConfig);
    const localCompEventActionsResult = simpleClone(mockCompEventActionsResult);

    //  push comp out 6 days...
    localCompEventActionsResult.competitionSummaryPublic.dates[0] =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 6));

    //  close it in 5...
    localCompEventActionsResult.competitionSummaryPublic.closedate =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 5));

    //  swicth the user
    localCompEventActionsResult.compEvent.user.userId = 12345;

    //  make event date match comp date
    localCompEventActionsResult.compEvent.startdate =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 6));

    const compEventActionsState: ICompEventActionsState =
      CompEventActionsServiceV2.init(
        localCompEventActionsResult,
        localConfigStoreState
      );

    expect(
      compEventActionsState.compEventActionsResult.competitionSummaryPublic
        .compId
    ).toBe(313);

    expect(localConfigStoreState.configApp.role).toBe("USER");

    expect(compEventActionsState.perms.isAdmin).toBe(false);

    // compEventActionsResult.compEvent.user.userId ===
    // configStoreState.configApp.userInfo.user.id

    //  nickwall entered this event...
    expect(
      compEventActionsState.compEventActionsResult.compEvent.user.userId
    ).toBe(12345);

    //  nickwall entered this event...
    expect(localConfigStoreState.configApp.userInfo.user.id).toBe(19828);

    expect(compEventActionsState.perms.belongsToUser).toBe(false);

    expect(compEventActionsState.perms.hasBuilderPermission).toBe(false);

    expect(compEventActionsState.access.switch.result).toBe(false);

    expect(compEventActionsState.access.switch.message).toBe(
      "Not assigned to you."
    );
  });

  test("CANCEL Nick entered compevent entries open no cancel limit", () => {
    const localConfigStoreState = simpleClone(nickConfig);
    const localCompEventActionsResult = simpleClone(mockCompEventActionsResult);

    //  push comp out 6 days...
    localCompEventActionsResult.competitionSummaryPublic.dates[0] =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 6));

    //  close it in 5...
    localCompEventActionsResult.competitionSummaryPublic.closedate =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 5));

    //  swicth the user
    // localCompEventActionsResult.compEvent.user.userId = 12345;

    //  make event date match comp date
    localCompEventActionsResult.compEvent.startdate =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 6));

    const compEventActionsState: ICompEventActionsState =
      CompEventActionsServiceV2.init(
        localCompEventActionsResult,
        localConfigStoreState
      );

    expect(
      compEventActionsState.compEventActionsResult.competitionSummaryPublic
        .compId
    ).toBe(313);

    expect(localConfigStoreState.configApp.role).toBe("USER");
    expect(compEventActionsState.perms.isAdmin).toBe(false);
    //  nickwall entered this event...
    expect(
      compEventActionsState.compEventActionsResult.compEvent.user.userId
    ).toBe(19828);

    //  nickwall entered this event...
    expect(localConfigStoreState.configApp.userInfo.user.id).toBe(19828);
    expect(compEventActionsState.perms.belongsToUser).toBe(true);
    expect(compEventActionsState.perms.hasBuilderPermission).toBe(false);
    expect(compEventActionsState.access.cancel.result).toBe(true);

    expect(compEventActionsState.access.switch.message).toBe("");
  });

  test("CANCEL Nick entered compevent entries open with cancel limit date", () => {
    const localConfigStoreState = simpleClone(nickConfig);
    const localCompEventActionsResult = simpleClone(mockCompEventActionsResult);

    //  push comp out 16 days...way before the cancel is not allowed
    localCompEventActionsResult.competitionSummaryPublic.dates[0] =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 16));

    //  close it in 15...
    localCompEventActionsResult.competitionSummaryPublic.closedate =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 15));

    expect(
      localCompEventActionsResult.competitionSummaryPublic.options.cancelEvent
        .hrsBeforeClose
    ).toBe(48);

    //  make event date match comp date
    localCompEventActionsResult.compEvent.startdate =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 6));

    const compEventActionsState: ICompEventActionsState =
      CompEventActionsServiceV2.init(
        localCompEventActionsResult,
        localConfigStoreState
      );

    expect(
      compEventActionsState.compEventActionsResult.competitionSummaryPublic
        .compId
    ).toBe(313);

    expect(localConfigStoreState.configApp.role).toBe("USER");
    expect(compEventActionsState.perms.isAdmin).toBe(false);
    //  nickwall entered this event...
    expect(
      compEventActionsState.compEventActionsResult.compEvent.user.userId
    ).toBe(19828);

    //  nickwall entered this event...
    expect(localConfigStoreState.configApp.userInfo.user.id).toBe(19828);
    expect(compEventActionsState.perms.belongsToUser).toBe(true);
    expect(compEventActionsState.perms.hasBuilderPermission).toBe(false);

    expect(compEventActionsState.access.cancel.result).toBe(true);

    expect(compEventActionsState.access.switch.message).toBe(
      ""
    );
  });

  test("CANCEL Nick entered compevent entries open and past cancel limit date", () => {
    const localConfigStoreState = simpleClone(nickConfig);
    const localCompEventActionsResult = simpleClone(mockCompEventActionsResult);

    //  push comp out 16 days...way before the cancel is not allowed
    localCompEventActionsResult.competitionSummaryPublic.dates[0] =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 2));

    //  close it in 15...
    localCompEventActionsResult.competitionSummaryPublic.closedate =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 1));

    expect(
      localCompEventActionsResult.competitionSummaryPublic.options.cancelEvent
        .hrsBeforeClose
    ).toBe(48);

    //  make event date match comp date
    localCompEventActionsResult.compEvent.startdate =
      convertToIsoDateTimeWithOffset(addDays(new Date(), 2));

    const compEventActionsState: ICompEventActionsState =
      CompEventActionsServiceV2.init(
        localCompEventActionsResult,
        localConfigStoreState
      );

    expect(
      compEventActionsState.compEventActionsResult.competitionSummaryPublic
        .compId
    ).toBe(313);

    expect(localConfigStoreState.configApp.role).toBe("USER");
    expect(compEventActionsState.perms.isAdmin).toBe(false);
    //  nickwall entered this event...
    expect(
      compEventActionsState.compEventActionsResult.compEvent.user.userId
    ).toBe(19828);

    //  nickwall entered this event...
    expect(localConfigStoreState.configApp.userInfo.user.id).toBe(19828);
    expect(compEventActionsState.perms.belongsToUser).toBe(true);
    expect(compEventActionsState.perms.hasBuilderPermission).toBe(false);

    expect(compEventActionsState.access.cancel.result).toBe(false);

    const cancelNotAvailableAfter = format(
      new CompetitionService().cancelsNotPermittedAfter(
        compEventActionsState.compEventActionsResult.competitionSummaryPublic
      ),
      "ddd Do MMM HH:mm"
    );
    const expectedMessage = "Cancel is not available past " + cancelNotAvailableAfter
    expect(compEventActionsState.access.cancel.message).toBe(
      expectedMessage
    );
  });

});
