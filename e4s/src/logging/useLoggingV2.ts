// export interface ILogging {
//   id: number;
//   message: string;
// }

export interface ILoggingState<Data> {
  events: Data[];
  counter: number;
}

const loggingStateGlobal: ILoggingState<any> = {
  events: [],
  counter: 0,
};

export function useLoggingV2<Data>(useGlobalState: boolean, addElementsToEnd = true) {
  let maxSize = 15;
  let addAtEnd = addElementsToEnd;

  const loggingStateInternal: ILoggingState<any> = {
    events: [],
    counter: 0,
  };

  const loggingState: ILoggingState<Data> = useGlobalState
    ? loggingStateGlobal
    : loggingStateInternal;

  // function addsimpleMessage(message: string) {
  //
  //   if (loggingState.events.length >= maxSize) {
  //     loggingState.events.shift();
  //   }
  //
  //   loggingState.events.push({
  //     id: loggingState.counter++,
  //     message,
  //   });
  // }

  function addMessage(logging: Data): void {
    if (loggingState.events.length >= maxSize) {
      addAtEnd ? loggingState.events.shift() : loggingState.events.pop();
    }

    loggingState.counter++;
    addAtEnd ? loggingState.events.push(logging) : loggingState.events.unshift(logging);
  }

  function setMaxSize(size: number) {
    maxSize = size;
  }

  function setAddElementsToEnd(toEnd: boolean) {
    addAtEnd = toEnd;
  }


  const state: ILoggingState<any> = loggingState;


  return {
    state,
    addMessage,
    setMaxSize,
    setAddElementsToEnd
  };
}
