import { reactive } from "@vue/composition-api";
import { ISocketConfig } from "../competition/results/results-file-import/photofinish/usePhotoFinish";
import { getSocketInstance, getSocketState } from "./socket-controller";
import { format } from "date-fns";

export interface ISocketControllerInput {
  startImmediately: boolean;
  onMessage: (message: MessageEvent) => void;
}

export function useSocketController(
  socketControllerInput: ISocketControllerInput
) {
  const socketConfig = reactive<ISocketConfig>({
    socketAlivePollingTimer: 0,
    SOCKET_ALIVE_POLL_EVERY_MS: 2000,
    isTryingReconnect: false,
    currentSocketState: "NA",
  });

  let socketInstance = getSocketInstance({
    message: socketControllerInput.onMessage,
  });

  if (socketControllerInput.startImmediately) {
    // socketInstance.e4sSocket.addEventListener(
    //   "message",
    //   socketControllerInput.onMessage
    // );
    checkSocketConnected();
  }

  function checkSocketConnected(): void {
    socketConfig.socketAlivePollingTimer = window.setTimeout(() => {
      // const currentStatus = this.status;
      if (socketInstance && socketInstance.e4sSocket) {
        const currentSocketState = socketInstance.e4sSocket.readyState;
        socketConfig.currentSocketState = getSocketState(currentSocketState);

        console.log(
          "socketAlivePollingTimer: " +
            socketConfig.socketAlivePollingTimer +
            " " +
            new Date() +
            " CleoSocketWrapperController.checkSocketConnected()...currentStatus: " +
            currentSocketState +
            ", Init at: " +
            format(socketInstance.startedTime, "HH:mm:ss") +
            ", isTryingReconnect: " +
            socketConfig.isTryingReconnect
        );

        if (currentSocketState === WebSocket.CLOSED) {
          if (!socketConfig.isTryingReconnect) {
            socketConfig.isTryingReconnect = true;

            socketInstance = getSocketInstance({
              message: socketControllerInput.onMessage,
            });
            // socketInstance.e4sSocket.addEventListener(
            //   "message",
            //   socketControllerInput.onMessage
            // );
            socketConfig.isTryingReconnect = false;
            //  Do not get out of this "if" else will trigger another checkSocketConnected()
            // this.checkSocketConnected();
            // return;
          }
        }
      }

      checkSocketConnected();
    }, socketConfig.SOCKET_ALIVE_POLL_EVERY_MS);
  }

  function destroy() {
    clearTimeout(socketConfig.socketAlivePollingTimer);
    socketInstance.e4sSocket.removeEventListener(
      "message",
      socketControllerInput.onMessage
    );
  }

  function start() {
    checkSocketConnected();
  }

  function stop() {
    destroy();
  }

  return {
    socketConfig,
    socketInstance,
    destroy,
    start,
    stop,
  };
}
