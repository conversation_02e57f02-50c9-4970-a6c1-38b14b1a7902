import {LaunchRouteKeyV2} from "../launch-routes-v2"
import {LaunchRouteKey} from "../../launch-routes"

export type ParentLinkType = "PARENT__MY_ACCOUNT" | "PARENT__MY_ADMIN" | "CART" | "LOGIN";

export type ParentLinkId = LaunchRouteKeyV2 | ParentLinkType;

export type ChildLinkId = LaunchRouteKeyV2 | LaunchRouteKey | "MY_ACCOUNT" | "MY_NAME" | "LOGOUT";

export interface ILinkV2 {
  linkId: ChildLinkId;
  linkDisplayName: string;
  path: string;
  disabled: boolean;
}

export interface ILinkParentV2 {
  linkId: ParentLinkId;
  linkDisplayName: string;
  path: string;
  showBadge: boolean;
  badgeText: string;
  children: ILinkV2[];
}
