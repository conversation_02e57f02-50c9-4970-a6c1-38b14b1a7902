window.addEventListener("resize", () => {
  if (window.innerWidth > 768) {
    closeMobileNav();
  }
});


export function toggleNav(target: HTMLElement) {
  switch (true) {
    case target.classList.contains("e4s-hamburger__menu-open"):
      // CloseMobileNav(target);
      closeMobileNav();
      break;
    case target.classList.contains("e4s-hamburger__menu-closed"):
      // OpenMobileNav(target);
      openMobileNav();
      break;
  }
}

export function closeMobileNav() {
  const hamburger = document.querySelector(".e4s-navigation__hamburger")!;
  hamburger.classList.remove("e4s-hamburger__menu-open");
  hamburger.classList.add("e4s-hamburger__menu-closed");
  hamburger.classList.remove("is-active");
  const mobileNavMenu = document.querySelector("#MobileNavMenu");
  if (mobileNavMenu) {
    mobileNavMenu.remove();
  }

  reloadScrollbars();
}

export function openMobileNav() {
  const hamburger = document.querySelector(".e4s-navigation__hamburger")!;

  hamburger.classList.remove("e4s-hamburger__menu-closed");
  hamburger.classList.add("e4s-hamburger__menu-open");
  hamburger.classList.add("is-active");


  // Create document fragment to hold dynamic created mobile nav.
  const mobileNavDocFrag = document.createDocumentFragment();

  // Create mobile nav wrapper.
  const mobileNavWrapper = document.createElement("div");
  mobileNavWrapper.setAttribute("id", "MobileNavMenu");
  mobileNavWrapper.classList.add(
    "e4s-flex-column",
    "e4s-flex-center",
    "e4s-navigation-bar__mobile-nav-links-wrapper"
  );



  /*
  switch (true) {
    case document
      .querySelector("nav")
      .classList.contains("e4s-navigation-bar--primary");
      mobileNavWrapper.classList.add("e4s-mobile-nav-bar--primary");
      break;
    case document
      .querySelector("nav")
      .classList.contains("e4s-navigation-bar--ia-primary");
      mobileNavWrapper.classList.add("e4s-mobile-nav-bar--ia-primary");
  }
  */

  /*
  const navElements = document.querySelector("nav");
  if (navElements) {
    const classList = navElements.classList;
    if (classList.contains("e4s-navigation-bar--primary")) {
      mobileNavWrapper.classList.add("e4s-mobile-nav-bar--primary");
    } else {
      mobileNavWrapper.classList.add("e4s-mobile-nav-bar--ia-primary");
    }
  }
  */

  const e4sNavBar = document.getElementById("e4s-nav-bar");
  if (e4sNavBar) {
    const classList = e4sNavBar.classList;
    if (classList.contains("e4s-navigation-bar--primary")) {
      mobileNavWrapper.classList.add("e4s-mobile-nav-bar--primary");
    } else {
      mobileNavWrapper.classList.add("e4s-mobile-nav-bar--ia-primary");
    }
  }

  /*
  const navClass: DOMTokenList = document.querySelector("nav")!.classList;
  switch (true) {
    case (navClass as DOMTokenList).contains("e4s-navigation-bar--primary"):
      mobileNavWrapper.classList.add("e4s-mobile-nav-bar--primary");
      break;
    case (navClass as DOMTokenList).contains("e4s-navigation-bar--ia-primary"):
      mobileNavWrapper.classList.add("e4s-mobile-nav-bar--ia-primary");
  }
   */

  const navigationLinks = document.querySelectorAll(
    ".e4s-navigation-link__container"
  );
  for (let i = 0, len = navigationLinks.length; i < len; i++) {
    mobileNavWrapper.appendChild(navigationLinks[i].cloneNode(true));
  }

  mobileNavDocFrag.appendChild(mobileNavWrapper);
  document.querySelector("body")!.appendChild(mobileNavDocFrag);

  unloadScrollbars();
}

export function unloadScrollbars() {
  document.documentElement.style.overflow = "hidden";
  (document.body.scroll as unknown) = "no";
}

export function reloadScrollbars() {
  document.documentElement.style.overflow = "auto";
  (document.body.scroll as unknown) = "yes";
}
