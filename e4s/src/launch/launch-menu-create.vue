<template>
  <ul>
    <li>
      <router-link :to="{ name: launchRoutesPaths.BUILDER, params: { id: 0 } }">
        <span>Competition Builder</span>
      </router-link>
    </li>
    <li>
      <a
        href="https://support.entry4sports.com/onlinehelp/builder"
        target="_blank"
      >
        <span>Competition Help</span>
      </a>
    </li>
  </ul>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { LAUNCH_ROUTES_PATHS } from "./launch-routes";
import { Prop } from "vue-property-decorator";

@Component({
  name: "launch-menu-create",
})
export default class LaunchMenuCreate extends Vue {
  @Prop({ default: false }) public readonly sideNavClose: boolean;
  public readonly launchRoutesPaths = LAUNCH_ROUTES_PATHS;
}
</script>
