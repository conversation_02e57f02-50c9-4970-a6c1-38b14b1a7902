<template>
  <div :disabled="isProcessingLoginRequest">
    <div class="col s12 m4 offset-m4">
      <div class="card">
        <div class="card-content">
          <div class="row">
            <span class="card-title">Login</span>
          </div>
          <div class="row">
            <div class="input-field col s12">
              <input
                id="username"
                v-model="userName"
                placeholder="Username"
                type="text"
              />
              <label for="username">Username</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12">
              <input
                id="password"
                :type="passwordType"
                v-model="password"
                placeholder="Password"
              />
              <label for="password">Password</label>
            </div>
          </div>
        </div>
        <div class="card-action login-button-container">
          <div class="col s11 m11">
            <button
              class="waves-effect waves-light light-blue darken-4 btn"
              v-on:click="doLogin"
            >
              Login
            </button>
          </div>
          <div class="col s1 m1">
            <span v-if="isProcessingLoginRequest">
              <div class="preloader-wrapper small active">
                <div class="spinner-layer spinner-red-only">
                  <div class="circle-clipper left">
                    <div class="circle"></div>
                  </div>
                  <div class="gap-patch">
                    <div class="circle"></div>
                  </div>
                  <div class="circle-clipper right">
                    <div class="circle"></div>
                  </div>
                </div>
              </div>
            </span>
          </div>

          <div v-html="tokenResponseFailure.message"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { mapState } from "vuex";
import { AUTH_STORE_CONST, IAuthStoreState } from "./auth-store";
import { ITokenFailureResponse, ITokenReponse } from "./auth-models";

@Component({
  name: "login",
  computed: {
    ...mapState(AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME, {
      isProcessingLoginRequest: (state: IAuthStoreState) =>
        state.isProcessingLoginRequest,
      isLoggedIn: (state: IAuthStoreState) => state.isLoggedIn,
      tokenReponse: (state: IAuthStoreState) => state.tokenReponse,
      tokenResponseFailure: (state: IAuthStoreState) =>
        state.tokenResponseFailure,
    }),
  },
})
export default class Login extends Vue {
  public readonly isProcessingLoginRequest: boolean;
  public readonly isLoggedIn: boolean;
  public readonly tokenReponse: ITokenReponse;
  public readonly tokenResponseFailure: ITokenFailureResponse;

  public userName: string = "";
  public password: string = "";
  public message: string;
  public passwordType: string = "password";

  public doLogin() {
    this.$store.dispatch(
      AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME +
        "/" +
        AUTH_STORE_CONST.AUTH_ACTIONS_LOGIN,
      {
        username: this.userName,
        password: this.password,
      }
    );
  }
}
</script>
