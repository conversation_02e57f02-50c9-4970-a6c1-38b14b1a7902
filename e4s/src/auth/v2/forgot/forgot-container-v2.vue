<template>
  <div class="e4s-flex-row e4s-full-width e4s-justify-flex-center">
    <ForgotFormV2 />
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import ForgotFormV2 from "./forgot-form-v2.vue";

export default defineComponent({
  name: "forgot-container-v2",
  components: { ForgotFormV2 },
  props: {},
  setup(props: any, context: SetupContext) {
    return {};
  },
});
</script>
