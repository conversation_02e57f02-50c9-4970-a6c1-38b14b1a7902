<template>
  <div class="e4s-card e4s-card--login">
    <h1 class="e4s-header--400 e4s-flex-start">Reset Password</h1>
    <div class="e4s-flex-row e4s-card--login__signup-container">
      <a :href="getLoginHref" class="e4s-hyperlink--100 e4s-hyperlink--primary">
        Back to Login
      </a>
    </div>

    <FormGenericInputTextV2
      form-label="Email"
      v-model="loginName"
      field-type="email"
      :is-disabled="true"
      :error-message="errs.email"
    />

    <FormGenericInputTemplateV2 form-label="Password">
      <template slot="field">
        <InputWithButton>
          <FieldTextV2
            v-model="password"
            class="e4s-full-width"
            slot="field"
            :field-type="passwordFieldType['1']"
          />
          <div slot="after" class="e4s-flex-row">
            <ButtonGenericV2
              button-type="tertiary"
              with-input="right"
              v-on:click="togglePasswordType('1')"
            >
              <HideMinor slot="button-content" />
            </ButtonGenericV2>
          </div>
        </InputWithButton>
        <p
          v-if="errs.password && errs.password.length > 0"
          class="e4s-body--200 e4s-body--error"
          v-text="errs.password"
        ></p>
      </template>
    </FormGenericInputTemplateV2>

    <p v-text="configStore.configApp.options.pwd.helpText"></p>

    <FormGenericInputTemplateV2 form-label="Re-enter password">
      <template slot="field">
        <InputWithButton>
          <FieldTextV2
            v-model="password2"
            class="e4s-full-width"
            slot="field"
            :field-type="passwordFieldType['2']"
          />
          <div slot="after" class="e4s-flex-row">
            <ButtonGenericV2
              button-type="tertiary"
              with-input="right"
              v-on:click="togglePasswordType('2')"
            >
              <HideMinor slot="button-content" />
            </ButtonGenericV2>
          </div>
        </InputWithButton>
        <p
          v-if="errs.password2 && errs.password2.length > 0"
          class="e4s-body--200 e4s-body--error"
          v-text="errs.password2"
        ></p>
      </template>
    </FormGenericInputTemplateV2>

    <p
      class="e4s-body--100 e4s-body--error"
      v-text="message"
      v-if="message.length > 0"
    ></p>

    <!--    errs === {{errs}}-->

    <div class="e4s-flex-row e4s-justify-flex-end">
      <ButtonGenericV2
        text="Reset"
        v-on:click="doReset"
        :disabled="isLoading"
      />
    </div>

    <LoadingSpinnerV2 v-if="isLoading" />
  </div>
</template>

<script lang="ts">
import {
  ref,
  computed,
  defineComponent,
  SetupContext,
} from "@vue/composition-api";
import {
  LAUNCH_ROUTES_PATHS_V2,
  LAUNCH_ROUTES_PATHS_V2_BASE,
} from "../../../launch/v2/launch-routes-v2";
import { LoginData } from "../../login-data";
import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import InputWithButton from "../../../common/ui/layoutV2/fields/InputWithButton.vue";
import HideMinor from "../../../common/ui/svg/HideMinor.vue";
import FieldTextV2, {
  FieldTextType,
} from "../../../common/ui/layoutV2/fields/field-text-v2.vue";
import FormGenericInputTextV2 from "../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import {
  useConfigController,
  useConfigStore,
} from "../../../config/useConfigStore";
import { IServerGenericResponse } from "../../../common/common-models";
import { LAUNCH_ROUTES_PATHS } from "../../../launch/launch-routes";

export default defineComponent({
  name: "reset-form-v2",
  components: {
    FormGenericInputTextV2,
    FieldTextV2,
    HideMinor,
    InputWithButton,
    FormGenericInputTemplateV2,
    ButtonGenericV2,
    LoadingSpinnerV2,
  },
  props: {
    loginName: {
      type: String,
      default: "",
    },
    resetKey: {
      type: String,
      default: "",
    },
  },
  setup(props: { loginName: string; resetKey: string }, context: SetupContext) {
    const configStore = useConfigStore();
    const configController = useConfigController();

    const userName = ref("");
    const email = ref("");
    const password = ref("");
    const password2 = ref("");

    const passwordFieldType = ref<{ "1": FieldTextType; "2": FieldTextType }>({
      "1": "password",
      "2": "password",
    });

    const message = ref("");
    const isLoading = ref(false);

    // const registrationSuccess = ref(false);

    // const validationService = new ValidationService();
    const errs = ref<Record<string, string>>({});

    const getLoginHref = computed(() => {
      return configController.getVersion.value === "v2"
        ? LAUNCH_ROUTES_PATHS_V2_BASE + "/" + LAUNCH_ROUTES_PATHS_V2.LOGIN_V2
        : LAUNCH_ROUTES_PATHS.LOGIN_V1;
    });

    function doReset() {
      validate();
      if (Object.keys(errs.value).length > 0) {
        return;
      }

      const loginData: LoginData = new LoginData();
      message.value = "";
      isLoading.value = true;
      loginData
        .resetPassword(props.loginName, props.resetKey, password.value)
        .then((response: IServerGenericResponse) => {
          if (response.errNo > 1) {
            message.value = response.error;
            return;
          }
          context.emit("onReset");
        })
        .catch((error) => {
          messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
          message.value = "Error occurred during registration.";
          return;
        })
        .finally(() => {
          isLoading.value = false;
        });
    }

    function doCancel() {
      context.emit("onCancel");
    }

    function validate() {
      console.log("un: " + userName.value + ", email: " + email.value);

      errs.value = {};

      // if (userName.value.replace(/ /g, "").length === 0) {
      //   errs.value["userName"] = "Required";
      // }
      //
      // if (!validationService.isEmailValid(email.value)) {
      //   errs.value["email"] = "Valid email required.";
      // }

      if (password.value === "") {
        errs.value["password"] = "Required.";
      }
      if (!password.value.match(configStore.configApp.options.pwd.regex)) {
        errs.value["password"] = "Not valid.";
      }

      if (password2.value === "") {
        errs.value["password2"] = "Required.";
      }
      if (password2.value !== password.value) {
        errs.value["password2"] = "Does not match original password..";
      }
    }

    function togglePasswordType(field: "1" | "2") {
      passwordFieldType.value[field] =
        passwordFieldType.value[field] === "password" ? "text" : "password";
    }

    return {
      getLoginHref,
      userName,
      email,
      password,
      password2,
      message,
      doReset,
      isLoading,
      doCancel,
      validate,
      configStore,
      passwordFieldType,
      togglePasswordType,
      errs,
    };
  },
});
</script>
