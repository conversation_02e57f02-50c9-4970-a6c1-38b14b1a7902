<template>
  <div>
    <div class="e4s-flex-row">
      <InputWithButton class="e4s-full-width">
        <FieldTextV2
          slot="field"
          class="e4s-square--right e4s-flex-grow"
          v-model="orgGridController.state.filterValues.orgName"
          place-holder="Enter search..."
          v-on:keyUpEnter="orgGridController.doSearch"
        />
        <button-generic-v2
          class="e4s-button--auto"
          with-input="right"
          v-on:click="orgGridController.doSearch"
          slot="after"
        />
      </InputWithButton>
    </div>

    <OrgGrid
      :value="orgGridController.state.orgs"
      :show-approve="false"
      :showDelete="false"
      :showEdit="configController.isAdmin.value"
      @onEdit="onEdit"
    />

    <LoadingSpinnerV2 v-if="orgGridController.state.isLoading" />
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import FormGenericInputTextV2 from "../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import OrgGrid from "../../onboarding/ui/OrgGrid.vue";
import InputWithButton from "../../../common/ui/layoutV2/fields/InputWithButton.vue";
import FieldTextV2 from "../../../common/ui/layoutV2/fields/field-text-v2.vue";
import { useOrgGridController } from "./useOrgGridController";
import { useConfigController } from "../../../config/useConfigStore";

export default defineComponent({
  name: "OrgSearchGrid",
  components: {
    FieldTextV2,
    InputWithButton,
    FormGenericInputTextV2,
    ButtonGenericV2,
    LoadingSpinnerV2,
    OrgGrid,
  },
  props: {
    useGlobalState: {
      type: Boolean,
      default: false,
    },
  },
  setup(props: { useGlobalState: boolean }, context: SetupContext) {
    const orgGridController = useOrgGridController({
      useGlobalState: props.useGlobalState,
    });

    const configController = useConfigController();

    function onEdit(org: any) {
      context.emit("onEdit", org);
    }

    return { orgGridController, onEdit, configController };
  },
});
</script>
