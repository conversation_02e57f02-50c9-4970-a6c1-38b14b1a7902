import { ResourceData } from "../common/resource/resource-service";
import { IOrg, IOrgWithLocations, OrganiserStatus } from "./org-models";
import https from "../common/https";
import { IServerResponse } from "../common/common-models";

export class OrgData extends ResourceData<IOrg> {
  constructor() {
    super("/v5/orgs");
  }

  /*
  public getMyOrgs(
    id: number = 0
  ): Promise<IServerResponse<Record<number, IOrgWithLocations>>> {
    return https.get<IOrg[]>(
      "/v5/myorgs/" + (id === 0 ? "" : id)
    ) as any as Promise<IServerResponse<Record<number, IOrgWithLocations>>>;
  }
  */

  public getMyOrgs(
    id: number = 0
  ): Promise<IServerResponse<IOrgWithLocations[]>> {
    return https.get("/v5/myorgs/" + (id === 0 ? "" : id)).then((response) => {
      //  TODO: Remove this when the API is fixed..only for testing.

      // reduce the object to an object with a single key by taking the first key
      // const data = Object.entries(response.data).reduce(
      //   (acc, [key, value]) => {
      //     acc[key] = value;
      //     return acc;
      //   }
      // );

      //  @ts-ignore
      // response.data = [response.data[0]];

      // console.log("getMyOrgs", response);

      return response;
    }) as any as Promise<IServerResponse<IOrgWithLocations[]>>;
  }

  public approveOrg(id: number): Promise<IServerResponse<IOrg>> {
    return https.post("/v5/orgs/approve/" + id) as any as Promise<
      IServerResponse<IOrg>
    >;
  }

  public getOrgsByStatus(
    orgStatus: OrganiserStatus
  ): Promise<IServerResponse<IOrg[]>> {
    return https.get<IOrg[]>("/v5/orgs/" + orgStatus) as any as Promise<
      IServerResponse<IOrg[]>
    >;
  }
}
