import * as TermsAndConditionsService from "./tandc-service";
import {ConfigService} from "../../config/config-service"
import {OrgService} from "../org-service"

const orgService = new OrgService();
const configService = new ConfigService();

test("hasUserAcceptedOrganiserTermsAndCond", () => {

    const org = orgService.factory();
    const userApplication = {
        ...configService.factoryUserApplication(),
        id: 5
    };
    expect(TermsAndConditionsService.hasUserAcceptedOrganiserTermsAndCond(org, userApplication)).toBe(false);

    org.options.tAndCUsers.push( {
        user: {
            id: 7,
            name: "<PERSON>"
        },
        time: "2021-03-10T05:45:00+00:00"
    })
    expect(TermsAndConditionsService.hasUserAcceptedOrganiserTermsAndCond(org, userApplication)).toBe(false);

    org.options.tAndCUsers.push( {
        user: {
            id: 5,
            name: "<PERSON>"
        },
        time: ""
    })
    expect(TermsAndConditionsService.hasUserAcceptedOrganiserTermsAndCond(org, userApplication)).toBe(false);

    org.options.tAndCUsers.push( {
        user: {
            id: 5,
            name: "Bob"
        },
        time: "2021-03-10T05:45:00+00:00"
    })
    expect(TermsAndConditionsService.hasUserAcceptedOrganiserTermsAndCond(org, userApplication)).toBe(true);


})
