<template functional>
    <table class="responsive-table">
        <thead>
        <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Accepted</th>
            <th></th>
        </tr>
        </thead>

        <tbody>
        <tr v-for="tAndCUser of props.tAndCUsers" :key="tAndCUser.id">
            <td><span v-text="tAndCUser.user.id"></span></td>
            <td><span v-text="tAndCUser.user.name"></span></td>
            <td><span v-text="$options.methods.getAgeGroupFull(tAndCUser)"></span></td>
            <td>
                <button :disabled="props.isLoading"
                        class="btn xxx-btn-small btn-flat red-text e4s-bold"
                        v-on:click.stop="remove(tAndCUser)">
                    <span>X</span>
                </button>
            </td>
        </tr>

        </tbody>
    </table>
</template>

<script lang="ts">
import {ITandCUser} from "../org-models"

export default {
    props: ["tAndCUsers", "isLoading"],
    methods: {
        getAcceptedTime: (tandCUser: ITandCUser) => {

        }
    }

}
</script>
