<template>
  <CommonTypeAhead
    slot="default"
    :default-object="valueInternal"
    :data-function="search"
    :get-object-description="getLabel"
    :show-cancel-button="false"
    :is-disabled="isDisabled"
    :use-spinner="false"
    v-on:selected="onChanged"
    v-on:reset="reset"
  >
    <div slot-scope="{ result }">
      <div v-text="getOptionDisplayValue(result)"></div>
    </div>
  </CommonTypeAhead>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  reactive,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { useConfigController } from "../../../config/useConfigStore";
import CommonTypeAhead from "../../../common/ui/type-ahead/common-type-ahead.vue";
import {
  IOnboardingSearchOrganisation,
  OnboardingApi,
} from "../api/onboarding-api";
import { IBaseConcrete } from "../../../common/common-models";

export default defineComponent({
  name: "OnboardingOrgPicker",
  components: { CommonTypeAhead },
  inheritAttrs: false,
  props: {
    value: {
      type: Object as PropType<IOnboardingSearchOrganisation>,
      default: () => {
        return {
          id: 0,
          name: "",
        };
      },
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  setup(
    props: {
      value: IOnboardingSearchOrganisation;
      isDisabled: boolean;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(props.value);
    const onboardingApi = new OnboardingApi();
    const configController = useConfigController();

    const state = reactive({
      searchResults: [] as IOnboardingSearchOrganisation[],
    });

    watch(
      () => props.value,
      (newValue: IOnboardingSearchOrganisation) => {
        if (newValue !== valueInternal.value) {
          // console.log("field-drop-down-v2 value changed", {
          //   new: newValue,
          //   internal: valueInternal.value,
          // });
          valueInternal.value = newValue;
        }
      },
      {
        immediate: true,
      }
    );

    // const debounceSearch = debounce((searchTerm: string, loading: any) => {
    //   searchForClub(searchTerm, loading);
    // }, 250);

    function search(searchTerm: string) {
      return onboardingApi.searchOrganisations(searchTerm);
    }

    function getLabel(
      onboardingSearchOrganisation: IOnboardingSearchOrganisation
    ) {
      return onboardingSearchOrganisation.name;
    }

    function onChanged(onboardingSearchOrganisation: IBaseConcrete) {
      valueInternal.value =
        onboardingSearchOrganisation as IOnboardingSearchOrganisation;
      context.emit("input", valueInternal.value);
    }

    function getOptionDisplayValue(
      onboardingSearchOrganisation: IBaseConcrete
    ) {
      return (
        onboardingSearchOrganisation.name +
        (configController.isAdmin.value
          ? " (" +
            onboardingSearchOrganisation.id +
            ", Type: " +
            onboardingSearchOrganisation.name +
            ")"
          : "")
      );
    }

    function reset() {
      valueInternal.value = {
        id: 0,
        name: "",
      };

      //  But if club type ahead is on form, reset the model.
      context.emit("input", valueInternal.value);
    }

    return {
      state,
      valueInternal,
      onChanged,
      search,
      configController,
      getLabel,
      reset,
      getOptionDisplayValue,
    };
  },
});
</script>

<style></style>
