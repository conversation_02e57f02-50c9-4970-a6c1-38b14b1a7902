import { computed, reactive, UnwrapRef } from "@vue/composition-api";
import * as OnboardingService from "./onboarding-service";
import {
  IOnboardingSearchOrganisation,
  OnboardingApi,
} from "../api/onboarding-api";
import { simpleClone } from "../../../common/common-service-utils";
import {
  IOnboardingFormInput,
  IOnboardingFormState,
  IOnboardingOrganisation,
} from "./onboarding-form-models";
import { IUserSummary } from "../../../admin/user/user-models";
import { UserProfileData } from "../../../admin/user/user-data";
import { IBaseConcrete } from "../../../common/common-models";

export function onboardingFormControllerFactory(
  stateInject: IOnboardingFormState | UnwrapRef<IOnboardingFormState>
) {
  const state: IOnboardingFormState | UnwrapRef<IOnboardingFormState> =
    stateInject ? stateInject : OnboardingService.factoryOnboardingFormState();

  init();

  function init() {
    state.user = simpleClone(state.onboardingFormInput.user);
    state.userOrgs = simpleClone(state.onboardingFormInput.userOrgs);
  }

  function setOnboardingFormInput(onboardingFormInput: IOnboardingFormInput) {
    state.onboardingFormInput = simpleClone(onboardingFormInput);
  }

  function addThisExistingOrganisation(
    existingOrganisation: IOnboardingSearchOrganisation
  ) {
    state.addThisExistingOrganisation = simpleClone(existingOrganisation);
    setDisableSwitchBetweenSearchAndNew();
  }

  function addThisExistingOrganisationReset() {
    state.addThisExistingOrganisation =
      OnboardingService.factoryOnboardingSearchOrganisation();
    setDisableSwitchBetweenSearchAndNew();
  }

  function setDisableSwitchBetweenSearchAndNew() {
    state.ui.disableSwitchBetweenSearchAndNew =
      state.addThisExistingOrganisation.id > 0;
  }

  function submitAddExistingOrganisation() {
    state.ui.isLoading = true;
    new OnboardingApi()
      .addExistingOrgToUser(state.user.id, state.addThisExistingOrganisation.id)
      .then((resp) => {
        console.log("submitAddExistingOrganisation", resp);
        state.addThisExistingOrganisation =
          OnboardingService.factoryOnboardingSearchOrganisation();
      })
      .then(() => {
        return setSelectedUserOrgs();
      })
      .finally(() => {
        state.ui.isLoading = false;
      });
  }

  function setSelectedUserOrgs(): Promise<void> {
    return getUserOrgs(state.user.id).then((resp) => {
      console.log(
        "state.onboardingFormInput.userOrgs",
        state.onboardingFormInput.userOrgs
      );
      state.userOrgs = resp;
    });
  }

  function getUserOrgs(userId: number): Promise<IBaseConcrete[]> {
    const userProfileData = new UserProfileData();
    return userProfileData
      .getUserProfileById(userId)
      .then((resp) => {
        return resp.data.user.permissions.map((perm) => {
          return perm.org;
        });
      })
      .catch((err) => {
        console.error(err);
        return [];
      });
  }

  function setUser(userSummary: IUserSummary): Promise<void> {
    if (userSummary.id < 1) {
      return Promise.resolve();
    }
    state.user = {
      id: userSummary.id,
      name: userSummary.displayName,
    };

    //  do we need to get System Admin confirmation?

    state.ui.isLoading = true;
    return setSelectedUserOrgs().finally(() => {
      state.ui.isLoading = false;
    });
  }

  function submitAddNewOrganisation(
    onboardingOrganisation: IOnboardingOrganisation
  ) {
    state.ui.isLoading = true;
    new OnboardingApi()
      .createNewOrg(onboardingOrganisation)
      .then((resp) => {
        console.log("submitAddNewOrganisation", resp);
        state.ui.showAddNewOrganisation = false;
      })
      .finally(() => {
        state.ui.isLoading = false;
      });
  }

  return {
    state,
    setOnboardingFormInput,
    addThisExistingOrganisation,
    addThisExistingOrganisationReset,
    submitAddExistingOrganisation,
    setUser,
    submitAddNewOrganisation,
  };
}

export function useOnboardingFormController(
  onboardingFormInput: IOnboardingFormInput
) {
  const state = reactive(OnboardingService.factoryOnboardingFormState());
  state.onboardingFormInput = onboardingFormInput;
  const controller = onboardingFormControllerFactory(state);

  const userOrgsDisplay = computed(() => {
    return controller.state.userOrgs
      .map((org) => {
        return org.name;
      })
      .sort()
      .join(", ");
  });

  return { controller, userOrgsDisplay };
}
