import { IConfigStoreState } from "../../../config/config-store";
import { IBaseConcrete } from "../../../common/common-models";
import { ValidationMap } from "../../../validation/validation-models";
import { E4sTheme } from "../../../config/config-app-models";
import { IOnboardingSearchOrganisation } from "../api/onboarding-api";
import {
  IOnboardingFormState,
  IOnboardingOrganisation,
  IUserOrganisationSimple,
} from "./onboarding-form-models";

export function factoryOnboardingFormState(): IOnboardingFormState {
  return {
    user: {
      id: 0,
      name: "",
    },
    userOrgs: [],
    onboardingFormInput: {
      theme: "def",
      isAdmin: false,
      user: {
        id: 0,
        name: "",
      },
      userOrgs: [],
    },
    addThisExistingOrganisation: factoryOnboardingSearchOrganisation(),
    editThisOrganisation: factoryOnboardingOrganisation(),
    ui: {
      showAddNewOrganisation: false,
      disableSwitchBetweenSearchAndNew: false,
      isLoading: false,
    },
  };
}

export function getOrganisationsFromConfig(
  configStoreState: IConfigStoreState
): IUserOrganisationSimple[] {
  return configStoreState.configApp.userInfo.orgs.map((org) => {
    return convertOrgToUserOrganisationSimple(org);
  });
}

export function convertOrgToUserOrganisationSimple(
  org: IBaseConcrete
): IUserOrganisationSimple {
  return {
    id: org.id,
    name: org.name,
  };
}

export function factoryOnboardingSearchOrganisation(): IOnboardingSearchOrganisation {
  return {
    id: 0,
    name: "",
  };
}

export function factoryOnboardingOrganisation(): IOnboardingOrganisation {
  return {
    id: 0,
    name: "",
    logo: "",
    stripeUser: {
      id: 0,
      name: "",
    },
  };
}

export function validateOnboardingOrganisation(
  organisation: IOnboardingOrganisation,
  theme: E4sTheme
): ValidationMap<IOnboardingOrganisation> {
  const errors: ValidationMap<IOnboardingOrganisation> = {};

  if (organisation.name == "") {
    errors.name = ["Name is required"];
  }

  if (theme === "irl") {
    if (organisation.stripeUser.id === 0) {
      errors.stripeUser = ["Stripe User is required"];
    }
  }

  return errors;
}

// export function doesUserNeedSystemAdminConfirmation(
//   userOrgs: IBaseConcrete[]
// ): boolean {
//   return userOrgs.length === 0;
// }
