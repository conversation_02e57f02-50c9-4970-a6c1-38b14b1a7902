import { convertOrgToUserOrganisationSimple } from "./onboarding-service";
import { IBaseConcrete } from "../../../common/common-models";

describe("onboarding-service", () => {
  test("convertOrgToOnboardingOrg", () => {
    const testOrgs: IBaseConcrete[] = [
      {
        id: 38,
        name: "National",
      },
    ];

    expect(convertOrgToUserOrganisationSimple(testOrgs[0]).id).toBe(38);
  });
});
