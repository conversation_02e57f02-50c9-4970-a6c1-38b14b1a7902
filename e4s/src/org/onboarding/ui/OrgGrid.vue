<template>
  <div>
    <div class="e4s-flex-column e4s-gap--standard">
      <div
        class="
          e4s-flex-row
          e4s-repeatable-grid--top
          org-grid--row
          e4s-gap--large
        "
        v-for="org in value"
        :key="org.id"
      >
        <div class="e4s-flex-row e4s-gap--large e4s-full-width">
          <div v-text="'(' + org.id + ')'" class="org-row--id"></div>
          <div v-text="org.name" class="org-row--name"></div>

          <div
            v-if="org.status !== 'Approved'"
            v-text="'Status: ' + org.status"
            class="e4s-flex-row--end e4s-info-text--error"
          ></div>
        </div>

        <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
          <ButtonGenericV2
            v-on:click="onApprove(org)"
            text="Approve"
            v-if="showApprove"
          />
          <ButtonGenericV2
            v-on:click="onDelete(org)"
            text="Delete"
            button-type="destructive"
            v-if="showDelete"
          />
          <ButtonGenericV2
            v-on:click="onEdit(org)"
            text="Edit"
            v-if="showEdit"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { IOrg } from "../../org-models";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";

export default defineComponent({
  name: "OrgGrid",
  components: { ButtonGenericV2 },
  props: {
    value: {
      type: Array as PropType<IOrg[]>,
      required: true,
    },
    showApprove: {
      type: Boolean,
      default: false,
    },
    showDelete: {
      type: Boolean,
      default: false,
    },
    showEdit: {
      type: Boolean,
      default: false,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
  },
  setup(
    props: {
      value: IOrg[];
      showApprove: boolean;
      showDelete: boolean;
      showEdit: boolean;
      isAdmin: boolean;
    },
    context: SetupContext
  ) {
    function onDelete(org: IOrg) {
      console.log("Deleting org", org);
      context.emit("onDelete", org);
    }

    function onEdit(org: IOrg) {
      console.log("Editing org", org);
      context.emit("onEdit", org);
    }

    function onApprove(org: IOrg) {
      console.log("Approving org", org);
      context.emit("onApprove", org);
    }

    return { onDelete, onEdit, onApprove };
  },
});
</script>

<style scoped>
.org-grid--row {
  padding-top: var(--e4s-gap--small);
}

.org-row--id {
  width: 45px;
}

.org-row--name {
  flex-grow: 1;
}
</style>
