<template>
  <div style="display: flex; justify-content: center">
    <TicketWrapperV2>
      <template slot="primary-content">
        <div
          v-text="ticketForm.competition.name"
          class="ticket-form-v2--primary-title"
        ></div>
        <div
          v-text="getCompDate + ' ' + ticketForm.competition.location.name"
        ></div>
        <div>
          <span v-text="ticketForm.product.name"></span>
          (<span v-text="ticketForm.orderId"></span>)
        </div>

        <!--        <div-->
        <!--          v-if="-->
        <!--            ticketForm.data.name.length > 0 || ticketForm.data.email.length > 0-->
        <!--          "-->
        <!--        >-->
        <!--          <div-->
        <!--            v-text="getCompDate + ' : ' + ticketForm.data.name"-->
        <!--            class="ticket-form-v2&#45;&#45;primary-title"-->
        <!--          ></div>-->
        <!--          <div v-text="ticketForm.data.email"></div>-->
        <!--          <div>-->
        <!--            <span v-text="ticketForm.product.name"></span>-->
        <!--            (<span v-text="ticketForm.orderId"></span>)-->
        <!--          </div>-->
        <!--        </div>-->
        <div v-html="qrHtml"></div>
      </template>
      <template slot="secondary-content">
        <div>
          <div
            v-text="
              ticketForm.competition.id + ': ' + ticketForm.competition.name
            "
            class="ticket-form-v2--secondary-title"
          ></div>
          <div
            v-text="getCompDate + ' ' + ticketForm.competition.location.name"
          ></div>
          <div>
            <span v-text="ticketForm.product.name"></span>
            (<span v-text="ticketForm.orderId"></span>)
          </div>
          <a href="#" v-if="!editMode" v-on:click.prevent="doEdit"
            >Edit Contact Info</a
          >
          <a href="#" v-if="editMode" v-on:click.prevent="cancel">Cancel</a>
          <div v-if="!editMode">
            <label class="active">Name</label>
            <div v-text="ticketForm.data.name"></div>
            <label class="active">Email</label>
            <div v-text="ticketForm.data.email"></div>
            <label class="active">Tel</label>
            <div v-text="ticketForm.data.telNo"></div>
            <label class="active">Address</label>
            <div v-text="ticketForm.data.address"></div>
            <template v-if="hasCarReg">
              <label class="active">Car Reg</label>
              <div v-text="ticketFormData.info"></div>
            </template>
          </div>

          <div v-if="editMode">
            <label class="active" :for="PREFIX + 'name'">
              Name
              <span
                v-if="validationErrors['data.name']"
                class="e4s-label-invalid"
                >Required</span
              >
            </label>
            <input
              :id="PREFIX + 'name'"
              :name="PREFIX + 'name'"
              type="text"
              class="e4s-input"
              v-model="ticketFormData.name"
              placeholder=""
            />

            <label class="active" :for="PREFIX + 'email'">
              Email
              <span
                v-if="validationErrors['data.email']"
                class="e4s-label-invalid"
                >Required</span
              >
            </label>
            <input
              :id="PREFIX + 'email'"
              :name="PREFIX + 'email'"
              type="text"
              class="e4s-input"
              v-model="ticketFormData.email"
              placeholder=""
            />

            <label class="active" :for="PREFIX + 'tel'">
              Tel
              <span
                v-if="validationErrors['data.telNo']"
                class="e4s-label-invalid"
                >Required</span
              >
            </label>
            <input
              :id="PREFIX + 'tel'"
              :name="PREFIX + 'tel'"
              type="text"
              class="e4s-input"
              v-model="ticketFormData.telNo"
              placeholder=""
            />

            <label class="active" :for="PREFIX + 'address'">
              Address
              <span
                v-if="validationErrors['data.address']"
                class="e4s-label-invalid"
                >Required</span
              >
            </label>
            <input
              :id="PREFIX + 'address'"
              :name="PREFIX + 'address'"
              type="text"
              class="e4s-input"
              v-model="ticketFormData.address"
              placeholder=""
            />

            <template v-if="hasCarReg">
              <label class="active" :for="PREFIX + 'address'"> Car Reg </label>
              <input
                :id="PREFIX + 'e4s_car_reg'"
                :name="PREFIX + 'e4s_car_reg'"
                type="text"
                class="e4s-input"
                v-model="ticketFormData.info"
                placeholder=""
              />
            </template>

            <button
              v-on:click="submitData"
              class="e4s-button e4s-button--green e4s-button--pad"
            >
              Submit
            </button>
          </div>
        </div>
      </template>
    </TicketWrapperV2>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { TicketService } from "../secondary/ticket/ticket-service";
import {
  ITicketForm,
  ITicketFormData,
} from "../secondary/ticket/ticket-models";
import TicketWrapper from "../test/test-composition.vue";
import TicketWrapperV2 from "./ticket-wrapper-v2.vue";
import { format, parse } from "date-fns";
import * as CommonServiceUtils from "../common/common-service-utils";
import { ValidationController } from "../validation/validation-controller";

const ticketService: TicketService = new TicketService();

export default defineComponent({
  name: "ticket-form-v2",
  components: { TicketWrapperV2, TicketWrapper },
  props: {
    ticketForm: {
      type: Object as PropType<ITicketForm>,
      default: () => {
        return ticketService.factoryTicketForm();
      },
    },
    qrHtml: {
      type: String,
      default: () => {
        return "";
      },
    },
  },
  setup(
    props: { ticketForm: ITicketForm; qrHtml: string },
    context: SetupContext
  ) {
    const PREFIX = Math.random().toString(36).substring(2);
    const ticketFormData = reactive<ITicketFormData>(
      ticketService.factoryTicketFormData()
    );
    const editMode = ref(false);
    const validationController: ValidationController =
      new ValidationController();

    const validationErrors = ref(validationController.errs);

    init(props.ticketForm);

    watch(
      () => props.ticketForm,
      (newValue: ITicketForm) => {
        // if (newValue.data) {
        //
        //   const data = newValue.data;
        //   if (ticketService.hasCarReg(newValue)) {
        //     data.info = ticketService.getCarReg(newValue)
        //   }
        //
        //   Object.assign(
        //     ticketFormData,
        //     CommonServiceUtils.simpleClone(data)
        //   );
        // }
        init(newValue);
      }
    );

    function init(ticketForm: ITicketForm) {
      if (ticketForm.data) {
        const data = ticketForm.data;
        if (ticketService.hasCarReg(ticketForm)) {
          data.info = ticketService.getCarReg(ticketForm);
        }

        Object.assign(ticketFormData, CommonServiceUtils.simpleClone(data));
      }
    }

    const getCompDate = computed(() => {
      if (props.ticketForm.competition.date.length === 0) {
        return "";
      }
      return format(parse(props.ticketForm.competition.date), "Do MMM YYYY");
    });

    const getIsDataRequired = computed(() => {
      return props.ticketForm.ticket.dataReq;
    });

    function validateFormData() {
      validationController.reset();
      validationController.setErrors(
        ticketService.validate(mergeTicketObjects())
      );
      validationErrors.value = validationController.errs;
    }

    function mergeTicketObjects(): ITicketForm {
      const ticketForm = CommonServiceUtils.simpleClone(props.ticketForm);
      ticketForm.data = ticketFormData;
      return ticketForm;
    }

    function submitData() {
      if (getIsDataRequired.value) {
        validateFormData();
        if (!validationController.isValid) {
          return;
        }
      }
      context.emit("submitData", mergeTicketObjects());
    }

    function cancel() {
      Object.assign(
        ticketFormData,
        CommonServiceUtils.simpleClone(props.ticketForm.data)
      );
      editMode.value = false;
    }

    const hasCarReg = computed(() => {
      return ticketService.hasCarReg(props.ticketForm);
    });

    function doEdit() {
      editMode.value = true;
      validateFormData();
    }

    return {
      PREFIX,
      getCompDate,
      ticketFormData,
      submitData,
      editMode,
      cancel,
      hasCarReg,
      validationController,
      validationErrors,
      doEdit,
    };
  },
});
</script>

<style>
.ticket-form-v2--primary-title {
  font-size: 1.25em;
}
.ticket-form-v2--secondary-title {
  font-size: 1.25em;
}
</style>
