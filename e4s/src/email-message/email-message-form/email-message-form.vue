<template>
    <div>

        <div class="row">
            <div class="col s12 m12 l12">
                <span v-text="getFrom" class="email-message-form--from"></span>
                <div class="right">
                    <CloseBack
                        link-text="Back to Inbox"
                        :show-icon="true"
                        v-on:close="closeMessage"
                    />
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col s12 m12 l12">
                <span v-text="getDateTimeOutput(e4sEmailMessageInternal.dateCreatedISO)" id="created"></span>
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div v-text="e4sEmailMessageInternal.subject" class="email-message-form--subject"></div>
            </div>
        </div>

        <SectionDividerLine></SectionDividerLine>

        <div class="row">
            <div class="col s12 m12 l12">
                <div v-html="e4sEmailMessageInternal.body" id="message-body"></div>
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div class="right">

<!--                    <div class="e4s-force-inline-block">-->
<!--                        <ButtonsCancelOkConfirm-->
<!--                            ok-text="Mark As Read"-->
<!--                            :show-cancel-only-when-confirming="true"-->
<!--                            v-on:ok="markMessage"-->
<!--                        ></ButtonsCancelOkConfirm>-->
<!--                    </div>-->
                    <div class="e4s-force-inline-block-x">
                        <ButtonsCancelOkConfirm
                            ok-text="Delete"
                            :show-cancel-only-when-confirming="true"
                            v-on:ok="deleteMessage"
                        ></ButtonsCancelOkConfirm>
                    </div>

<!--                    <button class="e4s-button e4s-button&#45;&#45;red e4s-button&#45;&#45;10-wide"-->
<!--                            v-on:click.stop="markMessage">-->
<!--                        <span>Mark As Read</span>-->
<!--                    </button>-->

<!--                    <button class="e4s-button e4s-button&#45;&#45;green e4s-button&#45;&#45;10-wide"-->
<!--                            v-on:click.stop="deleteMessage">-->
<!--                        <span>Delete</span>-->
<!--                    </button>-->
                </div>
            </div>
        </div>

    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {IE4sEmailMessageApp} from "../email-message-models";
import {EmailMessageService} from "../email-message-service";
import CloseBack from "../../common/ui/close-back.vue";
import {IsoDateTime} from "../../common/common-models";
import {CommonService} from "../../common/common-service";
import SectionDividerLine from "../../common/ui/section-divider-line.vue";
import ButtonsCancelOkConfirm from "../../common/ui/buttons/buttons-cancel-ok-confirm.vue";

const emailMessageService = new EmailMessageService();

@Component({
    name: "email-message-form",
    components: {
        ButtonsCancelOkConfirm,
        SectionDividerLine,
        CloseBack
    }
})
export default class EmailMessageForm extends Vue {
    @Prop({default: () => {
            return emailMessageService.factoryE4sEmailMessageApp();
        }
    })
    public readonly e4sEmailMessage: IE4sEmailMessageApp;

    @Watch("e4sEmailMessage", {
        immediate: true
    })
    public onMessageChanged(newValue: IE4sEmailMessageApp, oldValue: IE4sEmailMessageApp) {
        this.e4sEmailMessageInternal = R.clone(newValue);
    }

    public e4sEmailMessageInternal: IE4sEmailMessageApp = emailMessageService.factoryE4sEmailMessageApp();
    public commonService: CommonService = new CommonService();

    public getDateTimeOutput(dateTime: IsoDateTime): string {
        return dateTime.length === 0 ? "" : this.commonService.getE4sStandardHumanDateTimeOutPut(dateTime);
    }

    public closeMessage(e4sEmailMessage: IE4sEmailMessageApp) {
        this.$emit("closeMessage", R.clone(this.e4sEmailMessageInternal));
    }

    public get getFrom() {
        return emailMessageService.getFrom(this.e4sEmailMessageInternal)
    }

    public deleteMessage() {
        this.$emit("deleteMessage", R.clone(this.e4sEmailMessageInternal));
    }

    public markMessage() {
        this.$emit("markMessage", R.clone(this.e4sEmailMessageInternal));
    }
}
</script>

<style scoped>
.email-message-form--from {
    font-weight: 700;
}
.email-message-form--subject {
    font-size: 1.5em;
}
</style>
