import https from "../common/https";
import {IServerPagingResponseList, IServerResponse} from "../common/common-models";
import {
    IE4sEmailMessageApp, IE4sEmailMessageComp,
    IE4sEmailMessageCreate,
    IEmailMessagesParams
} from "./email-message-models";

export class EmailMessageData {
    /**
     * Returns ALL "app" messages.
     * @param payload
     */
    public getEmailMessagesAdmin(payload: IEmailMessagesParams) {
        return https.get( "/v5/message", {
            params: payload
        }) as Promise<IServerPagingResponseList<IE4sEmailMessageApp>>
    }

    /**
     * Returns all app messages for logged in user.
     * @param payload
     */
    public getEmailMessages(payload: IEmailMessagesParams) {
        return https.get( "/v5/message", {
            params: payload
        }) as Promise<IServerPagingResponseList<IE4sEmailMessageApp>>
    }

    public getEmailMessage(id: number) {
        return https.get( "/v5/message/" + id) as Promise<IServerResponse<IE4sEmailMessageApp>>
    }

    public markEmailMessageUnread(e4sEmailMessage: IE4sEmailMessageApp) {
        return https.get( "/v5/message/unread/" + e4sEmailMessage.id) as Promise<IServerResponse<IE4sEmailMessageApp>>
    }

    public softDeleteEmailMessage(e4sEmailMessage: IE4sEmailMessageApp | IE4sEmailMessageComp) {
        return https.delete( "/v5/message/delete/" + e4sEmailMessage.id) as Promise<IServerResponse<IE4sEmailMessageApp>>
    }

    public createMessage(e4sEmailMessageCreate: IE4sEmailMessageCreate) {
        return https.post("/v5/message", e4sEmailMessageCreate) as Promise<IServerResponse<IE4sEmailMessageCreate>>
    }
}

