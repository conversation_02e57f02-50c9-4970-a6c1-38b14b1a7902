<template>
    <div>

        <div class="row">
            <div class="col s12 m12 l12">
<!--                <i class='material-icons e4s-icon'>person_outline</i>-->
<!--                <span v-text="getFrom" class="email-message-form&#45;&#45;from"></span>-->
                <div class="right">
                    <CloseIcon v-on:close="closeMessage"></CloseIcon>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col s12 m6 l6">
                <label class="active"
                       for="subject-title">
                    To
                </label>
                <div v-text="getTo"></div>
            </div>

            <div class="col s12 m6 l6">
                <label class="active"
                       for="subject-title">
                    Type
                </label>
                <select
                    class="browser-default"
                    v-model="e4sEmailMessageCreateInternal.type"
                    v-if="!isSendingToAllUsers"
                >
                    <option value="E">Email</option>
                    <option value="M">Message</option>
                </select>
<!--                <span>Message</span>-->
            </div>
        </div>


        <div class="row">
            <div class="col s12 m12 l12">
                <label class="active"
                       for="subject-title">
                    Title\Subject
                </label>
                <input
                    id="subject-title"
                    name="subject-title"
                    class="e4s-input"
                    v-model="e4sEmailMessageCreateInternal.subject"
                    placeholder=""/>
            </div>
        </div>

<!--        <SectionDividerLine></SectionDividerLine>-->

        <div class="row">
            <div class="col s12 m12 l12">
                <HtmlEditor
                    :content="e4sEmailMessageCreateInternal.body"
                    v-on:onContentChange="onHtmlEdited"
                    v-on:onCancel="showHtmlEditor = false">
                    <div slot="buttons"></div>
                </HtmlEditor>
            </div>
        </div>

        <div class="e4s-section-padding-separator"></div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div class="right">

                    <div class="e4s-force-inline-block-x">
                        <ButtonsCancelOkConfirm
                            ok-text="Send"
                            :is-loading="httpResponseControllerMessage.isLoading"
                            :show-cancel-only-when-confirming="true"
                            v-on:ok="sendMessage"
                        ></ButtonsCancelOkConfirm>
                    </div>

                </div>
            </div>
        </div>

    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {IE4sEmailMessageCreate} from "../email-message-models";
import {EmailMessageService} from "../email-message-service";
import CloseIcon from "../../common/ui/close-icon.vue";
import {IsoDateTime} from "../../common/common-models";
import {CommonService} from "../../common/common-service";
import SectionDividerLine from "../../common/ui/section-divider-line.vue";
import ButtonsCancelOkConfirm from "../../common/ui/buttons/buttons-cancel-ok-confirm.vue";
import HtmlEditor from "../../common/ui/html-editor/html-editor.vue";
import {useHttpResponseController} from "../../common/handle-http-reponse";
import {EmailMessageData} from "../email-message-data";

const emailMessageService = new EmailMessageService();

@Component({
    name: "email-message-create-form",
    components: {
        HtmlEditor,
        ButtonsCancelOkConfirm,
        SectionDividerLine,
        CloseIcon
    }
})
export default class EmailMessageCreateForm extends Vue {
    @Prop({default: () => {
            return emailMessageService.factoryE4sEmailMessageApp();
        }
    })
    public readonly e4sEmailMessageCreate: IE4sEmailMessageCreate;

    public httpResponseControllerMessage = useHttpResponseController<IE4sEmailMessageCreate>(emailMessageService.factoryE4sEmailMessageCreate())

    @Watch("e4sEmailMessageCreate", {
        immediate: true
    })
    public onMessageChanged(newValue: IE4sEmailMessageCreate, oldValue: IE4sEmailMessageCreate) {
        this.e4sEmailMessageCreateInternal = R.clone(newValue);
    }

    public e4sEmailMessageCreateInternal: IE4sEmailMessageCreate = emailMessageService.factoryE4sEmailMessageCreate();
    public commonService: CommonService = new CommonService();

    public getDateTimeOutput(dateTime: IsoDateTime): string {
        return dateTime.length === 0 ? "" : this.commonService.getE4sStandardHumanDateTimeOutPut(dateTime);
    }

    public onHtmlEdited(content: string) {
        this.e4sEmailMessageCreateInternal.body = content;
    }

    public get getTo(): string {
        return emailMessageService.getHumanReadableSendTos(this.e4sEmailMessageCreateInternal.to).join(", ");
    }

    public get isSendingToAllUsers() {
        return emailMessageService.isSendingToAllUsers(this.e4sEmailMessageCreateInternal.to);
    }

    public closeMessage(e4sEmailMessage: IE4sEmailMessageCreate) {
        this.$emit("closeMessage", R.clone(this.e4sEmailMessageCreateInternal));
    }

    public sendMessage() {

        this.httpResponseControllerMessage.getData(
            new EmailMessageData().createMessage(this.e4sEmailMessageCreateInternal)
        ).then(() => {
            this.$emit("sentMessage", R.clone(this.e4sEmailMessageCreateInternal));
        });
    }
}
</script>

<style scoped>
.email-message-form--from {
    font-weight: 700;
}
.email-message-form--subject {
    font-size: 1.5em;
}
</style>
