<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <LoadingSpinnerV2
      v-if="
        athleteSectionController.state.isLoadingAthlete ||
        athleteSectionController.state.isLoading
      "
    />

    <!--    <div>-->
    <!--      athleteSectionController.state.ui.showSection:-->
    <!--      {{ athleteSectionController.state.ui.showSection }}-->
    <!--      <hr />-->
    <!--      entryCompSectionLink-->
    <!--      {{ entryCompSectionLink }}-->
    <!--    </div>-->

    <!--    | "ATHLETES" | "ATHLETE_EVENTS" | "ATHLETE_OPTIONS" | "ATHLETE_ENTRANTS";-->
    <div
      class="e4s-tab-links--section-links-bottom"
      id="athlete-section-links"
      v-if="false"
    >
      <SectionLinkSimple
        link-title="My Athletes"
        :is-active="
          athleteSectionController.state.ui.showSection === 'ATHLETES'
        "
        @selected="athleteSectionController.resetToAllAthletes()"
      />
      <SectionLinkSimple
        link-title="Schedule"
        v-if="
          ['ATHLETE_EVENTS', 'ATHLETE_OPTIONS'].indexOf(
            athleteSectionController.state.ui.showSection
          ) > -1
        "
        :is-active="
          athleteSectionController.state.ui.showSection === 'ATHLETE_EVENTS'
        "
        @selected="
          athleteSectionController.state.ui.showSection = 'ATHLETE_EVENTS'
        "
      />
      <SectionLinkSimple
        link-title="Event Options"
        v-if="
          athleteSectionController.state.ui.showSection === 'ATHLETE_OPTIONS'
        "
        :is-active="
          athleteSectionController.state.ui.showSection === 'ATHLETE_OPTIONS'
        "
      />
    </div>

    <!--Athlete Grid-->
    <CardGenericV2
      class="e4s-card--transparent"
      v-if="
        ['ATHLETES', 'ATHLETE_EVENTS'].indexOf(
          athleteSectionController.state.ui.showSection
        ) > -1
      "
    >
      <div slot="all" class="e4s-flex-column e4s-gap--standard">
        <!--        v-if="athleteSectionController.state.selectedAthlete.id === 0"-->
        <div
          v-if="athleteSectionController.state.ui.showSection === 'ATHLETES'"
          class="e4s-flex-column e4s-gap--standard"
        >
          <!--          As user will have already created an athlete, this section is redundant.-->
          <!--          <p v-if="athleteSectionController.state.athletes.length > 0">-->
          <!--            Select an athlete to retrieve their competition schedule.-->
          <!--            Alternatively, create an athlete via-->
          <!--            <a href="#" v-on:click.prevent="createAthlete"-->
          <!--              >Create Athlete(s).</a-->
          <!--            >-->
          <!--          </p>-->

          <template
            v-if="athleteSectionController.state.ui.showNoAthletesMessage"
          >
            <InfoSectionV2 info-type="warn">
              <p>
                You do not have any athletes associated to your account. Please
                create at least one athlete to continue.
              </p>
              <!--              <ButtonGenericV2 text="Create" v-on:click="createAthlete" />-->
            </InfoSectionV2>

            <!--            <div class="e4s-vertical-spacer"></div>-->
          </template>

          <!--SimpleFilter-->
          <div class="e4s-flex-row e4s-justify-flex-space-between">
            <div
              class="
                e4s-flex-row
                e4s-full-width
                e4s-justify-flex-space-between
                e4s-justify-flex-row-vert-center
              "
            >
              <div
                class="e4s-flex-row e4s-gap--small e4s-flex-center"
                v-if="athleteSectionController.state.ui.showQuickSearch"
              >
                <InputWithButton class="e4s-hide-mobile-x">
                  <ButtonGenericV2
                    text="Filters"
                    class="e4s-button--auto"
                    with-input="left"
                    button-type="primary"
                    :disabled="
                      !athleteSectionController.isFilterEntryAvailable.value
                    "
                    v-on:click="athleteSectionController.showSearchFilters"
                    slot="before"
                  >
                    <template slot="button-content">
                      <div
                        class="
                          e4s-flex-row
                          e4s-gap--tiny
                          e4s-justify-flex-row-vert-center
                        "
                      >
                        <span
                          style="color: var(--e4s-button--primary__text-color)"
                          >Filters</span
                        >
                        <PlayMinor :arrow-direction="'down'" fill="white" />
                      </div>
                    </template>
                  </ButtonGenericV2>

                  <FieldTextV2
                    slot="field"
                    place-holder="Search..."
                    :disabled="
                      !athleteSectionController.isFilterEntryAvailable.value
                    "
                    v-model="
                      athleteSectionController.state.athleteSearch.search
                    "
                    v-on:keyUpEnter="athleteSectionController.doQuickSearch"
                    class="
                      e4s-input-field--150
                      e4s-square--right e4s-square--left
                    "
                  />
                  <button-generic-v2
                    class="e4s-button--auto"
                    with-input="right"
                    :disabled="
                      !athleteSectionController.isFilterEntryAvailable.value
                    "
                    v-on:click="athleteSectionController.doQuickSearch()"
                    slot="after"
                  />
                </InputWithButton>
              </div>

              <ButtonGenericV2
                :disabled="!canUserCreateAthlete"
                class="e4s-button--100"
                button-type="primary"
                text="Add Athlete"
                v-on:click="createAthlete"
              />
            </div>
          </div>
          <!--/SimpleFilter-->
        </div>

        <!--        -->
        <template
          v-if="isMobileDevice && athleteSectionController.state.ui.showSearch"
        >
          <AthleteFilterV2
            class="e4s-card e4s-card--generic"
            :athlete-search="athleteSectionController.state.athleteSearch"
            :age-group-info="athleteSectionController.state.ageGroupInfo"
            v-on:search="athleteSectionController.doFilterSearch"
            v-on:cancel="athleteSectionController.hideAdvancedSearchFilters"
          />
          <div class="e4s-vertical-spacer--large"></div>
        </template>

        <template
          v-if="athleteSectionController.state.ui.showSection === 'ATHLETES'"
        >
          <div class="e4s-flex-column e4s-gap--standard">
            <InfoSectionV2
              v-if="athleteSectionController.state.ui.showClubCompInfo"
              info-type="error"
            >
              <ClubCompInfo
                :club-comp-info="
                  athleteSectionController.state.competitionSummaryPublic
                    .clubCompInfo
                "
              />
            </InfoSectionV2>

            <!--          <div class="e4s-flex-column">-->
            <!--            globalAthleteSearch:-->
            <!--            {{ athleteSectionController.globalAthleteSearch }}-->
            <!--          </div>-->

            <!--          <div class="e4s-flex-column">-->
            <!--            athleteSearch: {{ athleteSectionController.state.athleteSearch }}-->
            <!--          </div>-->

            <AthleteGridV2
              v-if="!athleteSectionController.state.isLoading"
              :athlete-section-state="athleteSectionController.state"
              v-on:onSelected="onAthleteSelected"
              v-on:onPageClicked="athleteSectionController.setPageAndSearch"
              v-on:viewAthlete="viewAthlete"
            />
          </div>
        </template>

        <!--Athlete Info of selected athlete to show above grid-->
        <!--        v-if="athleteSectionController.state.selectedAthlete.id > 0"-->

        <div
          v-if="
            athleteSectionController.state.ui.showSection === 'ATHLETE_EVENTS'
          "
          class="e4s-flex-column e4s-gap--standard"
        >
          <!--          <div class="e4s-flex-row">-->
          <!--            <button-generic-v2-->
          <!--              text="Back to athletes"-->
          <!--              class="e4s-flex-row&#45;&#45;end e4s-button&#45;&#45;auto"-->
          <!--              @click="athleteSectionController.resetToAllAthletes()"-->
          <!--            />-->
          <!--          </div>-->

          <div class="e4s-flex-row e4s-gap--large">
            <!--only show this section if on a device larger than mobile-->
            <!--Using v-if to prevent components from rendering/calculating on mobile-->
            <CardGenericV2 v-if="!isMobileDevice">
              <div slot="content">
                <HeaderSectionThickLine
                  title="Athletes"
                  :line-thickness-px="2"
                  style="margin-bottom: var(--e4s-gap--standard)"
                  v-if="!isMobileDevice"
                />

                <!--SimpleFilter-->
                <div class="e4s-flex-row e4s-justify-flex-space-between">
                  <div
                    class="
                      e4s-flex-row
                      e4s-full-width
                      e4s-justify-flex-space-between
                      e4s-justify-flex-row-vert-center
                    "
                  >
                    <div
                      class="e4s-flex-row e4s-gap--small e4s-flex-center"
                      v-if="athleteSectionController.state.ui.showQuickSearch"
                    >
                      <InputWithButton class="e4s-hide-mobile-x">
                        <ButtonGenericV2
                          class="e4s-button--auto"
                          with-input="left"
                          button-type="primary"
                          :disabled="
                            !athleteSectionController.isFilterEntryAvailable
                              .value
                          "
                          v-on:click="
                            athleteSectionController.showSearchFilters
                          "
                          slot="before"
                        >
                          <template slot="button-content">
                            <div
                              class="
                                e4s-flex-row
                                e4s-gap--tiny
                                e4s-justify-flex-row-vert-center
                              "
                            >
                              <span
                                style="
                                  color: var(--e4s-button--primary__text-color);
                                "
                                v-text="
                                  athleteSectionController.getFilterButtonText
                                    .value
                                "
                              ></span>
                              <PlayMinor
                                :arrow-direction="'down'"
                                fill="white"
                              />
                            </div>
                          </template>
                        </ButtonGenericV2>

                        <FieldTextV2
                          slot="field"
                          place-holder="Search..."
                          :disabled="
                            !athleteSectionController.isFilterEntryAvailable
                              .value
                          "
                          v-model="
                            athleteSectionController.state.athleteSearch.search
                          "
                          v-on:keyUpEnter="
                            athleteSectionController.doQuickSearch
                          "
                          class="
                            e4s-input-field--150
                            e4s-square--right e4s-square--left
                          "
                        />
                        <button-generic-v2
                          class="e4s-button--auto"
                          with-input="right"
                          :disabled="
                            !athleteSectionController.isFilterEntryAvailable
                              .value
                          "
                          v-on:click="athleteSectionController.doQuickSearch()"
                          slot="after"
                        />
                      </InputWithButton>
                    </div>

                    <ButtonGenericV2
                      :disabled="!canUserCreateAthlete"
                      class="e4s-button--100"
                      button-type="primary"
                      text="Add Athlete"
                      v-on:click="createAthlete"
                    />
                  </div>
                </div>
                <!--/SimpleFilter-->

                <InfoSectionV2
                  v-if="athleteSectionController.state.ui.showClubCompInfo"
                  info-type="error"
                >
                  <ClubCompInfo
                    :club-comp-info="
                      athleteSectionController.state.competitionSummaryPublic
                        .clubCompInfo
                    "
                  />
                </InfoSectionV2>

                <!--                athleteSectionController.state.athleteSearch: {{-->
                <!--                  athleteSectionController.state.athleteSearch-->
                <!--                }}-->

                <AthleteFilterV2
                  v-if="athleteSectionController.state.ui.showSearch"
                  class="e4s-card e4s-card--generic"
                  :athlete-search="athleteSectionController.state.athleteSearch"
                  :age-group-info="athleteSectionController.state.ageGroupInfo"
                  v-on:search="athleteSectionController.doFilterSearch"
                  v-on:cancel="
                    athleteSectionController.hideAdvancedSearchFilters
                  "
                />

                <AthleteGridV2
                  v-if="!athleteSectionController.state.isLoading"
                  :athlete-section-state="athleteSectionController.state"
                  v-on:onSelected="onAthleteSelected"
                  v-on:onPageClicked="athleteSectionController.setPageAndSearch"
                  v-on:viewAthlete="viewAthlete"
                />
              </div>
            </CardGenericV2>
            <!--/only show this section if on a device larger than mobile-->
            <!--/Using v-if to prevent components from rendering/calculating on mobile-->

            <CardGenericV2
              :class="isMobileDevice ? 'e4s-card--transparent' : ''"
            >
              <div slot="content">
                <HeaderSectionThickLine
                  title="Athlete Schedule"
                  :line-thickness-px="2"
                  v-if="!isMobileDevice"
                  style="margin-bottom: var(--e4s-gap--standard)"
                />

                <AthleteScheduleSection
                  v-if="
                    athleteSectionController.state.ui.showSection ===
                    'ATHLETE_EVENTS'
                  "
                  :athlete-schedule-section-input="
                    athleteSectionController.getAthleteScheduleSectionInput
                      .value
                  "
                  @doOptions="doOptions"
                  @showEntrants="athleteSectionController.showEntrants"
                  @close="athleteScheduleClosed"
                  @contactOrganiser="contactOrganiser"
                  @onEventSelected="onEventSelected"
                />
              </div>
            </CardGenericV2>
          </div>
        </div>

        <!--/Athlete Info of selected athlete to show above grid-->
      </div>
    </CardGenericV2>
    <!--/Athlete Grid-->

    <div
      class="e4s-flex-column e4s-gap--standard"
      v-if="athleteSectionController.state.ui.showSection === 'ATHLETE_OPTIONS'"
    >
      <!--      </CardGenericV2>-->
      <EntriesOptionsAthleteScheduleV2
        slot="all"
        :entries-options-athlete-schedule-input="
          getEntriesOptionsAthleteScheduleInput
        "
        :is-admin="configController.isAdmin.value"
        v-on:cancel="closeOptions"
        v-on:submitted="closeOptions"
      />
    </div>

    <CardGenericV2
      v-if="
        athleteSectionController.state.ui.showSection === 'ATHLETE_ENTRANTS'
      "
    >
      <div slot="all">
        <ResultEntriesSimple
          :athlete-comp-sched-rule-event="
            athleteSectionController.state.athleteCompSchedRuleEvent
          "
          :athlete-entries="athleteSectionController.state.athleteEntries"
          v-on:onCancel="
            athleteSectionController.state.ui.showSection = 'ATHLETE_EVENTS'
          "
        />
      </div>
    </CardGenericV2>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { ICompetitionSummaryPublic } from "../../../competition/competition-models";
import { useAthleteSectionController } from "./useAthleteSectionController";
import FormGenericSectionTitleV2 from "../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import CardGenericV2 from "../../../common/ui/layoutV2/card-generic-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import AthleteGridV2 from "../../../athlete/v2/AthleteGridV2.vue";
import InfoSectionV2 from "../../../common/ui/layoutV2/info-section-v2.vue";
import { IAthleteSummary } from "../../../athlete/athlete-models";
import ButtonGroupSectionV2 from "../../../common/ui/layoutV2/buttons/button-group-section-v2.vue";
import AthleteScheduleSection from "../athlete-schedule/athlete-schedule-section.vue";
import { simpleClone } from "../../../common/common-service-utils";
import FormGenericInputTextV2 from "../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import AthleteFilterV2 from "../../../athlete/v2/filter/AthleteFilterV2.vue";
import { EntryCompSectionLink } from "../entry-models-v2";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import PowerOfTenLinkV2 from "../../../common/ui/layoutV2/PowerOfTenLinkV2.vue";
import AthleteGridRowContentV2 from "../../../athlete/v2/AthleteGridRowContentV2.vue";
import EntriesOptionsAthleteScheduleV2, {
  IEntriesOptionsAthleteScheduleInput,
} from "../athlete-schedule/entries-options-athlete-schedule-v2.vue";
import {
  AthleteScheduleStateOptionsOutput,
  IEmitAthleteScheduleEventSelected,
} from "../athlete-schedule/athlete-schedule-models";
import { AthleteCompSchedService } from "../../../athleteCompSched/athletecompsched-service";
import { useConfigController } from "../../../config/useConfigStore";
import ButtonGenericBackV2 from "../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import ResultEntriesSimple from "../../../competition/results/public/schedule/v2/result-entries-simple-v2.vue";
import InputWithButton from "../../../common/ui/layoutV2/fields/InputWithButton.vue";
import FieldTextV2 from "../../../common/ui/layoutV2/fields/field-text-v2.vue";
import ClubCompInfo from "../schools/ClubCompInfo.vue";
import PlayMinor from "../../../common/ui/svg/PlayMinor.vue";
import SectionLinkSimple from "../../../common/ui/layoutV2/tabs/section-link-simple.vue";
import { useIsMobile } from "../../../common/composables/useIsMobile";
import HeaderSectionThickLine from "../../../common/ui/layoutV2/form/HeaderSectionThickLine.vue";

const athleteCompSchedService = new AthleteCompSchedService();

// import {LAUNCH_ROUTES_PATHS_V2_BASE} from "../../../launch/v2/launch-routes-v2";

export default defineComponent({
  name: "athlete-section",
  components: {
    HeaderSectionThickLine,
    SectionLinkSimple,
    PlayMinor,
    ClubCompInfo,
    FieldTextV2,
    InputWithButton,
    ResultEntriesSimple,
    ButtonGenericBackV2,
    EntriesOptionsAthleteScheduleV2,
    AthleteGridRowContentV2,
    PowerOfTenLinkV2,
    LoadingSpinnerV2,
    AthleteFilterV2,
    FormGenericInputTextV2,
    AthleteScheduleSection,
    ButtonGroupSectionV2,
    InfoSectionV2,
    AthleteGridV2,
    ButtonGenericV2,
    CardGenericV2,
    FormGenericSectionTitleV2,
  },
  props: {
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
    entryCompSectionLink: {
      type: Object as PropType<EntryCompSectionLink>,
      required: true,
    },
    athleteId: {
      type: Number,
      default: 0,
    },
  },
  setup(
    props: {
      competitionSummaryPublic: ICompetitionSummaryPublic;
      entryCompSectionLink: EntryCompSectionLink;
      athleteId: number;
    },
    context: SetupContext
  ) {
    const athleteSectionController = useAthleteSectionController();
    const configController = useConfigController();
    // athleteSectionController.getAthletes();

    // Use the mobile detection composable
    const { isMobileDevice } = useIsMobile();

    athleteSectionController.state.ui.showSection = "ATHLETES";

    if (isMobileDevice.value) {
      athleteSectionController.state.ui.showSection = "ATHLETES";
    } else {
      athleteSectionController.state.ui.showSection = "ATHLETE_EVENTS";
    }

    watch(
      () => props.competitionSummaryPublic,
      (newValue: ICompetitionSummaryPublic) => {
        athleteSectionController.state.competitionSummaryPublic =
          simpleClone(newValue);
        if (newValue.compId > 0) {
          athleteSectionController.getAthletes(props.athleteId);
        }
      },
      {
        immediate: true,
      }
    );

    watch(
      () => props.entryCompSectionLink,
      (newValue: EntryCompSectionLink) => {
        if (
          newValue.uniqueDesc === "INDIVIDUAL" ||
          newValue.uniqueDesc === "ATHLETES_GRID"
        ) {
          athleteSectionController.showAllAthletes();
          athleteSectionController.getAthletes();
          // athleteSectionController.onAthleteSelected(
          //   athleteSectionController.state.selectedAthlete
          // );
          // ...but, if PC show the split screen.
          if (!isMobileDevice.value) {
            athleteSectionController.state.ui.showSection = "ATHLETE_EVENTS";
          }
        }
        if (newValue.uniqueDesc === "EVENTS") {
          athleteSectionController.state.ui.showSection = "ATHLETE_EVENTS";
        }
      }
      // {
      //   immediate: true,
      // }
    );

    watch(
      () => isMobileDevice.value,
      (newValue: boolean) => {
        // going to mobile
        if (newValue) {
          athleteSectionController.state.ui.showSection = "ATHLETES";
        } else {
          athleteSectionController.state.ui.showSection = "ATHLETE_EVENTS";
        }
      }
    );

    function onAthleteSelected(athleteSummary: IAthleteSummary) {
      athleteSectionController.onAthleteSelected(athleteSummary).then(() => {
        // context.emit(
        //   "onAthleteSelected",
        //   athleteSectionController.state.selectedAthlete
        // );
      });
      context.emit("onAthleteSelected", athleteSummary);
    }

    function createAthlete(pageNumber: number) {
      context.emit("onCreateAthlete", pageNumber);
    }

    const getUrn = computed(() => {
      if (athleteSectionController.state.selectedAthlete.URN) {
        return athleteSectionController.state.selectedAthlete.URN;
      }
      return "";
    });

    function doOptions(
      athleteScheduleStateOptionsOutput: AthleteScheduleStateOptionsOutput
    ) {
      athleteSectionController.doOptions(athleteScheduleStateOptionsOutput);
      context.emit("doOptions", athleteScheduleStateOptionsOutput);
    }

    function closeOptions() {
      athleteSectionController.state.ui.showSection = "ATHLETE_EVENTS";
      context.emit("closeOptions");
    }

    const getEntriesOptionsAthleteScheduleInput =
      computed<IEntriesOptionsAthleteScheduleInput>(() => {
        const output =
          athleteSectionController.state.athleteScheduleStateOptionsOutput;

        const input: IEntriesOptionsAthleteScheduleInput = {
          competitionSummaryPublic:
            athleteSectionController.state.competitionSummaryPublic,
          athlete: athleteSectionController.state.selectedAthlete,
          pbMap: {},
          selectedAthleteCompSchedRuleEvent:
            athleteCompSchedService.factoryAthleteCompSchedRuleEvent(),
          athleteCompSchedRuleEvents: [],
          athleteCompSchedResponse:
            athleteCompSchedService.factoryAthleteCompSchedResponse(),
        };

        if (output) {
          input.pbMap = output.pbMap;
          input.selectedAthleteCompSchedRuleEvent =
            output.selectedAthleteCompSchedRuleEvent;
          input.athleteCompSchedRuleEvents = output.athleteCompSchedRuleEvents;
          input.athleteCompSchedResponse = output.athleteCompSchedResponse;
        }

        return input;
      });

    function viewAthlete(athleteSummary: IAthleteSummary) {
      console.log(">>> section");
      context.emit("viewAthlete", athleteSummary);
    }

    const canUserCreateAthlete = computed(() => {
      const isClubCountyRep = configController.hasUserEntities.value;

      if (configController.isAdmin.value) {
        return true;
      }

      if (
        configController.getStore.value.configApp.theme === "irl" &&
        isClubCountyRep
      ) {
        return false;
      }

      return true;
    });

    function contactOrganiser(
      competitionSummaryPublic: ICompetitionSummaryPublic
    ) {
      context.emit("contactOrganiser", competitionSummaryPublic);
    }

    function onEventSelected(
      emitPayload: IEmitAthleteScheduleEventSelected,
      forceContinue?: boolean
    ) {
      console.warn("AthleteSection.onEventSelected emit", emitPayload);
      context.emit("onEventSelected", emitPayload, forceContinue);
    }

    function athleteScheduleClosed() {
      athleteSectionController.resetToAllAthletes();
      context.emit("athleteScheduleClosed");
    }

    return {
      athleteSectionController,
      configController,
      getUrn,
      getEntriesOptionsAthleteScheduleInput,
      canUserCreateAthlete,
      isMobileDevice,

      athleteScheduleClosed,
      contactOrganiser,
      onAthleteSelected,
      createAthlete,
      viewAthlete,
      doOptions,
      closeOptions,
      onEventSelected,
    };
  },
});
</script>
