import { EventGroupIdString } from "../../../common/common-models";

export interface IClubCompInfo {
  configData: IClubCompInfoConfigData;
  entryData: IClubCompInfoEntryData;
  clubs: IClubCompInfoClub[];
}

export interface IClubCompInfoClub {
  clubId: number; //  the legit id of the entity.
  clubName: string;
  clubCompId: number; //  a summary of club stuff, this is the id to get to it...NOT the club Id.
  categoryId: number; //  ?
  categoryName: "A" | "B" | "C" | string; //  ?
}

export interface IClubCompInfoConfigData {
  clubId: number;
  clubName: string;
  clubCompId: number; //  The id of the club comp table
  categoryId: number;
  categoryName: "C" | string;
  maxEntries: number;
  maxRelays: number;
  maxMixedRelays: number;
  bibNos: (string | number)[];
  maxPerEvent: number;
}

export interface IClubCompInfoSummary {
  entryCnt: number;
  teamCnt: number;
}

export interface IClubCompInfoEntryData extends IClubCompInfoSummary {
  entries: Record<EventGroupIdString, IClubCompInfoEntryIndiv>;
  teams: Record<EventGroupIdString, IClubCompInfoEntryTeam>;
}

export interface IClubCompInfoEntryBase {
  egCount: number;
  eventAgeGroup: string;
  eventGroup: string;
}

export interface IClubCompInfoEntryIndiv extends IClubCompInfoEntryBase {
  athletes: IClubCompInfoEntryIndivAthlete[];
}

export interface IClubCompInfoEntryIndivAthlete {
  entryId: number;
  entryName: string;
  teamBibNo: string;
}

export interface IClubCompInfoEntryTeam extends IClubCompInfoEntryBase {
  teams: string[];
}
