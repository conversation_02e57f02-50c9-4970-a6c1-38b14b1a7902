import { IClubCompInfo } from "./clubCompInfo-models";

export const clubCompInfoMock: IClubCompInfo = {
  configData: {
    clubId: 10598,
    clubName: "Warwickshire",
    categoryId: 3,
    categoryName: "C",
    maxEntries: 32,
    maxRelays: 4,
    bibNos: [57, 58, "58a"],
    maxPerEvent: 3,
    maxMixedRelays: 2,
  },
  entryData: {
    entries: {
      "5285": {
        athletes: [
          {
            entryName: "Oluwatolase ABBAS",
            teamBibNo: "TBC",
          },
          {
            entryName: "Ava 0`MEARA",
            teamBibNo: "TBC",
          },
        ],
        eventGroup: "100m Inters Girls",
        eventAgeGroup: "Inters",
        egCount: 2,
      },
      "5286": {
        athletes: [
          {
            entryName: "James DAY",
            teamBibNo: "TBC",
          },
          {
            entryName: "Luke Bridges",
            teamBibNo: "TBC",
          },
          {
            entryName: "<PERSON>",
            teamBibNo: "TBC",
          },
        ],
        eventGroup: "100m Inters Men",
        eventAgeGroup: "Inters",
        egCount: 3,
      },
      "5287": {
        athletes: [
          {
            entryName: "<PERSON> Hornby",
            teamBibNo: "TBC",
          },
          {
            entryName: "Lily Jones",
            teamBibNo: "TBC",
          },
          {
            entryName: "Chloe Jordan",
            teamBibNo: "TBC",
          },
        ],
        eventGroup: "100m Juniors Girls",
        eventAgeGroup: "Juniors",
        egCount: 3,
      },
      "5288": {
        athletes: [
          {
            entryName: "James Annis",
            teamBibNo: "TBC",
          },
          {
            entryName: "Elliott Deakin",
            teamBibNo: "TBC",
          },
          {
            entryName: "Thomas Dean",
            teamBibNo: "TBC",
          },
        ],
        eventGroup: "100m Juniors Boys",
        eventAgeGroup: "Juniors",
        egCount: 3,
      },
      "5289": {
        athletes: [
          {
            entryName: "Jessica DAY",
            teamBibNo: "TBC",
          },
          {
            entryName: "Natasza Bielecka",
            teamBibNo: "TBC",
          },
          {
            entryName: "Megan Day",
            teamBibNo: "TBC",
          },
        ],
        eventGroup: "100m Seniors Women",
        eventAgeGroup: "Seniors",
        egCount: 3,
      },
      "5290": {
        athletes: [
          {
            entryName: "Callum ABBOTT",
            teamBibNo: "TBC",
          },
          {
            entryName: "Issah ABDULKARIM",
            teamBibNo: "TBC",
          },
        ],
        eventGroup: "100m Seniors Men",
        eventAgeGroup: "Seniors",
        egCount: 2,
      },
    },
    entryCnt: 16,
    teams: {
      "5293": {
        teams: ["4 x 100 Male Juniors A"],
        eventGroup: "Team Event",
        eventAgeGroup: "Juniors",
        egCount: 1,
      },
      "5294": {
        teams: ["Warwckshire Relay  Under 20 A"],
        eventGroup: "100m Relay",
        eventAgeGroup: "Juniors",
        egCount: 1,
      },
    },
    teamCnt: 2,
  },
} as any as IClubCompInfo;
