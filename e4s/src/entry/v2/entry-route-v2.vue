<template>
  <div>
    <LoadingSpinnerV2 v-if="isLoading" />
    <EntryV2
      :competition-summary-public="competitionSummaryPublic"
      :athlete-id="athleteId"
      v-if="isReady"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext, ref } from "@vue/composition-api";
import { useRoute } from "../../router/migrateRouterVue3";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";
import { CompetitionData } from "../../competition/competition-data";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import { CompetitionService } from "../../competition/competiton-service";
import EntryV2 from "./entry-v2.vue";

export default defineComponent({
  name: "entry-route-v2",
  components: { EntryV2, LoadingSpinnerV2 },
  props: {},
  setup(props: any, context: SetupContext) {
    const route = useRoute();

    const isLoading = ref(false);
    const isReady = ref(false);

    const competitionService = new CompetitionService();

    const competitionSummaryPublic = ref(
      competitionService.factorySummaryPublic()
    );

    let athleteId: number = 0;

    if (route.query.athleteid) {
      if (isNaN(Number(route.query.athleteid))) {
        athleteId = 0
      } else {
        athleteId = parseInt((route.query.athleteid) as string, 0)
      }


      // athleteId = isNaN(Number(route.query.athleteid))
      //   ? 0
      //   : parseInt(route.query.athleteid!, 0);
    }


    const compId = isNaN(Number(route.params.id))
      ? 0
      : parseInt(route.params.id, 0);
    if (compId > 0) {
      isLoading.value = true;
      const prom = new CompetitionData().getCompById(compId);
      handleResponseMessages(prom);
      prom
        .then((resp) => {
          if (resp.errNo === 0) {
            competitionSummaryPublic.value = resp.data;
            isReady.value = true;
          }
        })
        .finally(() => {
          isLoading.value = false;
        });
    }

    return {
      athleteId,
      competitionSummaryPublic,
      isLoading,
      isReady,
    };
  },
});
</script>
