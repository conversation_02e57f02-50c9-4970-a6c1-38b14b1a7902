import { computed, reactive } from "@vue/composition-api";
import { ICompetitionSummaryPublic } from "../../competition/competition-models";
import { CompetitionService } from "../../competition/competiton-service";
import { CompetitionData } from "../../competition/competition-data";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import * as CommonServiceUtils from "../../common/common-service-utils";
import { simpleClone } from "../../common/common-service-utils";
import { IAthleteSummary } from "../../athlete/athlete-models";
import { format, parse } from "date-fns";
import * as EntryServiceV2 from "./entry-service-v2";
import {
  EntryCompSectionLink,
  EntryStateSection,
  IEntryState,
} from "./entry-models-v2";
import { IDoOptionsAthleteSchedule } from "./athlete-schedule/athlete-schedule-models";
import { useRouter } from "../../router/migrateRouterVue3";
import { RawLocation } from "vue-router";
import { CurrentCompSelection } from "../../common/ui/layoutV2/tabs/section-links-models";
import { useAthleteState } from "../../athlete/v2/form/useAthleteState";
import { LAUNCH_ROUTES_PATHS_V2_BASE } from "../../launch/v2/launch-routes-v2";
import { useBreadCrumbController } from "../../common/ui/breadcrumb/useBreadCrumb";
import { useCompetitionSummaryPublicController } from "../../competition/v2/useCompetitionSummaryPublicController";

const competitionService = new CompetitionService();

/**
 * State is global
 */
const state = reactive<IEntryState>(EntryServiceV2.factoryEntryState());

export function useEntryNavigationHelper() {
  // const breadCrumbController = useBreadCrumbController({
  //   useGlobalState: true,
  //   compId: state.competitionSummaryPublic.id,
  // });

  function onLinkSelected(sectionLink: EntryCompSectionLink) {
    console.log("useEntryNavigationHelper.onLinkSelected()", sectionLink);
    setShowSection("COMPETITION");

    // if (sectionLink.uniqueDesc === "INDIVIDUAL") {
    // }
    state.ui.compSectionLinkSelected = simpleClone(sectionLink);
    setSectionLinks();
  }

  function setShowSection(section: EntryStateSection) {
    state.ui.sectionsHistory.push(state.ui.showSection);
    if (section === "CONTACT_ORGANISER") {
      state.ui.currentCompSelection = "CONTACT";
    }
    state.ui.showSection = section;
  }

  function setSectionLinks() {
    const sectionLinks: EntryCompSectionLink[] = [];

    //  TODO what the hell is this rubbish!?!?!?!?

    // const compSectionKeys: keyof = Object.keys(state.ui.compSectionLinkMap);

    /*
    if (state.ui.compSectionLinkSelected.uniqueDesc === "") {
      state.ui.compSectionLinkSelected = {
        iconId: "1",
        uniqueDesc: "ATHLETES",
        title: "Athletes",
      };

      //  set up defaults.
      //  TODO
      if (true) {
        // if (state.competitionSummaryPublic.options.showIndivAthletes) {
        sectionLinks.push(state.ui.compSectionLinkMap.ATHLETES);
      }
    }
    */

    console.log("useEntry.setSectionLinks()", state.ui.compSectionLinkSelected);

    if (state.ui.compSectionLinkSelected.uniqueDesc === "INDIVIDUAL") {
      sectionLinks.push(state.ui.compSectionLinkMap.INDIVIDUAL);
      // sectionLinks.push(state.ui.compSectionLinkMap.EVENTS);
      if (state.competitionSummaryPublic.eventTypes.teamEvents) {
        sectionLinks.push(state.ui.compSectionLinkMap.TEAMS);
      }
    }

    if (state.ui.compSectionLinkSelected.uniqueDesc === "EVENTS") {
      sectionLinks.push(state.ui.compSectionLinkMap.INDIVIDUAL);
      sectionLinks.push(state.ui.compSectionLinkMap.EVENTS);
      if (state.competitionSummaryPublic.eventTypes.teamEvents) {
        sectionLinks.push(state.ui.compSectionLinkMap.TEAMS);
      }
    }

    if (state.ui.compSectionLinkSelected.uniqueDesc === "OPTIONS") {
      sectionLinks.push(state.ui.compSectionLinkMap.INDIVIDUAL);
      sectionLinks.push(state.ui.compSectionLinkMap.EVENTS);
      sectionLinks.push(state.ui.compSectionLinkMap.OPTIONS);
      if (state.competitionSummaryPublic.eventTypes.teamEvents) {
        sectionLinks.push(state.ui.compSectionLinkMap.TEAMS);
      }
    }

    if (state.ui.compSectionLinkSelected.uniqueDesc === "TEAMS") {
      if (state.competitionSummaryPublic.eventTypes.indivEvents) {
        sectionLinks.push(state.ui.compSectionLinkMap.INDIVIDUAL);
      }
      sectionLinks.push(state.ui.compSectionLinkMap.TEAMS);
    }

    // if (state.competitionSummaryPublic.options.showTeamAthletes) {
    //   sectionLinks.push(state.ui.compSectionLinkMap.TEAMS);
    // }

    state.ui.compSectionLinks = sectionLinks;
  }

  return {
    state,
    onLinkSelected,
    setShowSection,
    setSectionLinks,
  };
}

export function useEntry() {
  const router = useRouter();

  const athleteState = useAthleteState();
  const entryNavigationHelper = useEntryNavigationHelper();
  const breadCrumbController = useBreadCrumbController({
    useGlobalState: true,
    compId: state.competitionSummaryPublic.id,
  });
  const competitionSummaryPublicController =
    useCompetitionSummaryPublicController(state.competitionSummaryPublic);

  breadCrumbController.reset();

  function init(competitionSummaryPublic: ICompetitionSummaryPublic) {
    Object.assign(state, EntryServiceV2.factoryEntryState());
    setCompetition(competitionSummaryPublic);
    competitionSummaryPublicController.init(competitionSummaryPublic);
  }

  /**
   * Fetches competition data based on the provided competition ID.
   *
   * @param {number} compId - The unique identifier of the competition to retrieve.
   * @return {Promise<void>} A promise that resolves when the competition data is fetched and processed.
   */
  function getCompetition(compId: number): Promise<void> {
    state.isLoading = true;
    const prom = new CompetitionData().getCompById(compId);
    handleResponseMessages(prom);
    return prom
      .then((resp) => {
        if (resp.errNo === 0) {
          state.competitionSummaryPublic = resp.data;
        }
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  /**
   * Reloads the competition data by fetching it again from the server.
   *
   * @return {Promise<void>} A promise that resolves when the competition data is reloaded.
   */
  function reloadCompetition() {
    console.warn("useEntry.reloadCompetition()");
    return getCompetition(state.competitionSummaryPublic.compId);
  }

  function setCompetition(competitionSummaryPublic: ICompetitionSummaryPublic) {
    state.competitionSummaryPublic = CommonServiceUtils.simpleClone(
      competitionSummaryPublic
    );

    setSectionLinks();

    //  Do wwe need to show T&Cs or priority.
    const isPriorityRequired = competitionService.isPriorityRequired(
      state.competitionSummaryPublic
    );
    const hasTermsAndConditions =
      state.competitionSummaryPublic.termsConditions.length > 0;

    if (isPriorityRequired || hasTermsAndConditions) {
      setShowSection("ENTRY_CONDITIONS");
      return;
    }

    //  TODO debug
    setShowSection("COMPETITION");
  }

  function entryConditionsProceed() {
    setShowSection("COMPETITION");
  }

  function entryConditionsCancel() {
    // console.log(
    //   "entry.entryConditionsCancel() what should we do, leave user on page, let them browse back to home or send them there automatically?"
    // );
    const location: RawLocation = {
      path: "/v2",
    };
    router.push(location);
  }

  const getEntriesCloseText = computed(() => {
    return (
      "Entries close on " +
      format(
        parse(state.competitionSummaryPublic.closedate),
        "Do MMM YYYY HH:mm"
      )
    );
  });

  const getCompDate = computed(() => {
    return format(
      parse(state.competitionSummaryPublic.closedate),
      "Do MMM YYYY"
    );
  });

  function onAthleteSelected(athleteSummary: IAthleteSummary) {
    state.selectedAthlete = simpleClone(athleteSummary);
    breadCrumbController.setLastBreadCrumb({
      name: "COMP_ATHLETE",
      title: "Athlete",
    });
    onLinkSelected(state.ui.compSectionLinkMap.EVENTS);
  }

  function creatAthlete() {
    setShowSection("CREATE_ATHLETE");
  }

  function athleteCreated() {
    //  Need athletes to refresh in athlete section, how do you trigger
    //  that efficiently.  Doing this accomplishes...but...mheh.
    state.competitionSummaryPublic = simpleClone(
      state.competitionSummaryPublic
    );
    setShowSection("COMPETITION");
  }

  function setShowSection(section: EntryStateSection) {
    // state.ui.sectionsHistory.push(state.ui.showSection);
    // if (section === "CONTACT_ORGANISER") {
    //   state.ui.currentCompSelection = "CONTACT";
    // }
    // state.ui.showSection = section;
    entryNavigationHelper.setShowSection(section);

    if (section === "COMPETITION") {
      //  If has just Indiv or both, default to indiv.
      if (state.competitionSummaryPublic.eventTypes.indivEvents) {
        breadCrumbController.setLastBreadCrumb({
          name: "COMP_ATHLETES",
        });
        return;
      }

      if (state.competitionSummaryPublic.eventTypes.teamEvents) {
        breadCrumbController.setLastBreadCrumb({
          name: "COMP_TEAMS",
        });
        //  Only hasd teams.
        if (!state.competitionSummaryPublic.eventTypes.indivEvents) {
          state.ui.compSectionLinkSelected.uniqueDesc = "TEAMS";
        }
      }
    }
  }

  function showCompAgain() {
    setShowSection("COMPETITION");
    state.ui.currentCompSelection = "ENTER";
  }

  function setShowLastSection() {
    //  Where user used to be before opening...
    const lastSectionVisited =
      state.ui.sectionsHistory[state.ui.sectionsHistory.length - 1];
    //  ...this section....
    state.ui.sectionsHistory.push(state.ui.showSection);

    state.ui.showSection = lastSectionVisited;
  }

  function onLinkSelected(sectionLink: EntryCompSectionLink) {
    entryNavigationHelper.onLinkSelected(sectionLink);
    console.log(
      "useEntry.onLinkSelected(): " + sectionLink.iconId,
      sectionLink
    );

    if (sectionLink.uniqueDesc === "INDIVIDUAL") {
      breadCrumbController.setLastBreadCrumb({
        name: "COMP_ATHLETES",
      });

      // need to reload competition to get
      // reloadCompetition();
    }

    if (sectionLink.uniqueDesc === "TEAMS") {
      breadCrumbController.setLastBreadCrumb({
        name: "COMP_TEAMS",
      });
    }
  }

  function onShowSectionSelected(showSection: EntryStateSection) {
    state.ui.compSectionLinkSelected = {
      iconId: "UNKNOWN",
      uniqueDesc: "UNKNOWN",
      title: "",
    } as any as EntryCompSectionLink;
    setShowSection(showSection);
    setSectionLinks();
  }

  function setSectionLinks() {
    entryNavigationHelper.setSectionLinks();
  }

  function doOptions(doOptionsAthleteSchedule: IDoOptionsAthleteSchedule) {
    // state.doOptionsAthleteSchedule = simpleClone(doOptionsAthleteSchedule);
    // setShowSection("ENTRY_OPTIONS_ATHLETE_SCHEDULE");

    onLinkSelected(state.ui.compSectionLinkMap.OPTIONS);
  }

  function closeOptions() {
    onLinkSelected(state.ui.compSectionLinkMap.EVENTS);
  }

  function compLinkSelected(currentCompSelection: CurrentCompSelection) {
    if (currentCompSelection === "HOME") {
      router.push({
        name: LAUNCH_ROUTES_PATHS_V2_BASE,
      });
      // router.push({
      //   name: LAUNCH_ROUTES_PATHS_V2_BASE,
      // });
      window.location.href = window.location.origin + "/#/v2";
      return;
    }
    state.ui.currentCompSelection = currentCompSelection;

    if (currentCompSelection === "COMP") {
      state.ui.showSection = "COMPETITION_MORE_INFO";
    }
    if (currentCompSelection === "ENTER") {
      state.ui.showSection = "COMPETITION";
    }
    if (currentCompSelection === "CONTACT") {
      state.ui.showSection = "CONTACT_ORGANISER";
    }
  }

  // const getMoveToSectionTop = computed( () => {
  //   const currentCompSelection = state.ui.currentCompSelection;
  //   const showSection = state.ui.showSection;
  //
  //   if
  //
  //
  //   if (currentCompSelection === "COMP") {
  //     state.ui.showSection = "COMPETITION_MORE_INFO";
  //   }
  //   if (currentCompSelection === "ENTER") {
  //     state.ui.showSection = "COMPETITION";
  //   }
  //   if (currentCompSelection === "CONTACT") {
  //     state.ui.showSection = "CONTACT_ORGANISER";
  //   }
  // })

  const isIndividualTabActive = computed(() => {
    if (state.ui.currentCompSelection !== "ENTER") {
      return false;
    }

    if (
      state.ui.compSectionLinkSelected.uniqueDesc ===
      state.ui.compSectionLinkMap.TEAMS.uniqueDesc
    ) {
      return false;
    }

    const isIndivualSpecificallyActive =
      state.ui.compSectionLinkSelected.uniqueDesc ===
      state.ui.compSectionLinkMap.INDIVIDUAL.uniqueDesc;

    if (isIndivualSpecificallyActive) {
      return true;
    }

    return (
      ["ATHLETES_GRID", "EVENTS", "OPTIONS"].indexOf(
        state.ui.compSectionLinkSelected.uniqueDesc
      ) > -1
    );
  });

  const isAthletesTabSelected = computed(() => {
    const isIndivSelected =
      state.ui.compSectionLinkSelected.uniqueDesc ===
      state.ui.compSectionLinkMap.INDIVIDUAL.uniqueDesc;

    const isAthletesGridSelected =
      state.ui.compSectionLinkSelected.uniqueDesc ===
      state.ui.compSectionLinkMap.ATHLETES_GRID.uniqueDesc;

    const isViewAthleteProfileSelected =
      state.ui.showSection === "VIEW_ATHLETE";

    return (
      (isIndivSelected || isAthletesGridSelected) &&
      !isViewAthleteProfileSelected
    );
  });

  const shouldShowIndivTeamsTabs = computed(() => {
    return EntryServiceV2.shouldShowIndivTeamsTabs(state);
  });

  function viewAthlete(athleteSummary: IAthleteSummary) {
    athleteState.getAthlete(athleteSummary.id);
    setShowSection("VIEW_ATHLETE");
  }

  const canShowMainSection = computed(() => {
    const dontShowMainSectionIfCurrentSectionIs: EntryStateSection[] = [
      "ENTRY_CONDITIONS",
      "CREATE_ATHLETE",
      "VIEW_ATHLETE",
      "COMPETITION_MORE_INFO",
      "CONTACT_ORGANISER",
    ];
    return (
      dontShowMainSectionIfCurrentSectionIs.indexOf(state.ui.showSection) === -1
    );
  });

  return {
    state,

    athleteState,
    shouldShowIndivTeamsTabs,
    breadCrumbController,
    canShowMainSection,
    competitionSummaryPublicController,
    isIndividualTabActive,
    isAthletesTabSelected,
    getEntriesCloseText,
    getCompDate,

    getCompetition,
    setCompetition,
    creatAthlete,
    onShowSectionSelected,
    compLinkSelected,

    init,
    setShowSection,
    setShowLastSection,
    showCompAgain,
    entryConditionsProceed,
    entryConditionsCancel,
    onAthleteSelected,
    athleteCreated,
    onLinkSelected,
    doOptions,
    closeOptions,
    viewAthlete,
    reloadCompetition,
  };
}
