import { CompetitionService } from "../../competition/competiton-service";
import { AthleteService } from "../../athlete/athlete-service";
import { IEntryState } from "./entry-models-v2";
// import { ISectionLink } from "../../common/ui/layoutV2/tabs/section-links-models";

const competitionService = new CompetitionService();
const athleteService = new AthleteService();

export function factoryEntryState(): IEntryState {
  return {
    isLoading: false,
    ui: {
      showGlobalMask: false,
      showSection: "COMPETITION",
      sectionsHistory: [],
      athlete: {
        showSection: "ATHLETES",
        showSearch: false,
        showCreate: true,
        showPaging: false,
        showNoAthletesMessage: false,
        showQuickSearch: true,
        showClubCompInfo: false,
        filterKeysEnabled: {},
        isFilterEnabled: false,
      },
      compSectionLinkMap: {
        INDIVIDUAL: {
          iconId: "1",
          title: "Athlete Entries",
          uniqueDesc: "INDIVIDUAL",
        },
        ATHLETES_GRID: {
          iconId: "1a",
          title: "Athletes",
          uniqueDesc: "ATHLETES_GRID",
        },
        EVENTS: {
          iconId: "1a",
          title: "Events",
          uniqueDesc: "EVENTS",
        },
        OPTIONS: {
          iconId: "1b",
          title: "Options",
          uniqueDesc: "OPTIONS",
        },
        VIEW_ATHLETE: {
          iconId: "1b",
          title: "Athlete",
          uniqueDesc: "VIEW_ATHLETE",
        },
        TEAMS: {
          iconId: "2",
          title: "Team Entries",
          uniqueDesc: "TEAMS",
        },
        SHOP: {
          iconId: "3",
          title: "Shop",
          uniqueDesc: "SHOP",
        },
        YOUR_ENTRIES: {
          iconId: "4",
          title: "Your Entries",
          uniqueDesc: "YOUR_ENTRIES",
        },
      },
      compSectionLinks: [],
      compSectionLinkSelected: {
        iconId: "1",
        title: "INDIVIDUAL",
        uniqueDesc: "INDIVIDUAL",
      },
      currentCompSelection: "ENTER",
      debugEnabled: false,
    },
    competitionSummaryPublic: competitionService.factorySummaryPublic(),
    selectedAthlete: athleteService.factoryAthleteSummary(),
    doOptionsAthleteSchedule: null,
  };
}

// export function factorySectionLinks(): ISectionLink[] {
//   return [
//     {
//       index: 1,
//       title: "Athletes",
//       uniqueDesc: "ATHLETES"
//     },
//     {
//       index: 2,
//       title: "Events",
//       uniqueDesc: "EVENTS"
//     }
//   ];
// }

export function shouldShowIndivTeamsTabs(entryState: IEntryState): boolean {
  const eventTypes = entryState.competitionSummaryPublic.eventTypes;
  return (
    entryState.ui.currentCompSelection === "ENTER" &&
    eventTypes.indivEvents &&
    eventTypes.teamEvents
  );
}
