<template>
  <div>
    <LoadingSpinnerV2 v-if="isLoadingSchedule" />
    <EntityEntries
      :r4s-comp-schedule="r4sCompScheduleState"
      :club-comp-info="clubCompInfo"
      v-if="isReady"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { ScoreboardData } from "../../../competition/scoreboard/scoreboard-data";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import { IR4sCompSchedule } from "../../../competition/scoreboard/rs4/rs4-scoreboard-models";
import { Rs4Service } from "../../../competition/scoreboard/rs4/rs4-service";
import EntityEntries from "./EntityEntries.vue";
import { IClubCompInfo } from "../schools/clubCompInfo-models";
import * as CompetitonServiceV2 from "../../../competition/v2/competiton-service-v2";
import { CompetitionData } from "../../../competition/competition-data";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import { useStore } from "../../../app.store";
import { ENTRY_STORE_CONST } from "../../entry-store";

export default defineComponent({
  name: "entity-entries-loader",
  components: { LoadingSpinnerV2, EntityEntries },
  props: {
    compId: {
      type: Number,
      default: 0,
    },
    clubCompInfo: {
      type: Object as PropType<IClubCompInfo>,
      default: () => {
        return CompetitonServiceV2.factoryClubCompInfo();
      },
    },
  },
  setup(props: { compId: number }, context: SetupContext) {
    const rs4Service: Rs4Service = new Rs4Service();
    const isLoadingSchedule = ref(false);
    const isReady = ref(false);
    // const clubCompInfoInternal = ref(CompetitonServiceV2.factoryClubCompInfo());

    const store = useStore();

    const r4sCompScheduleState = ref<IR4sCompSchedule>(
      rs4Service.factoryR4sCompSchedule()
    );

    function getScheduleData(): Promise<void> {
      isLoadingSchedule.value = true;
      const prom = new ScoreboardData().getCompSchedule(props.compId, false);
      handleResponseMessages(prom);
      return prom
        .then((resp) => {
          if (resp.errNo === 0) {
            r4sCompScheduleState.value = resp.data;

            /*
            const promGetComp = new CompetitionData().getCompById(props.compId);
            handleResponseMessages(promGetComp);
            return promGetComp.then((resp) => {
              // clubCompInfoInternal.value = resp.data.clubCompInfo;
              store.commit(
                ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
                  "/" +
                  ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_CLUB_COMP_INFO,
                resp.data.clubCompInfo
              );


              isReady.value = true;
            });
            */

            const promGetClubCompInfo = new CompetitionData().getClubCompInfo(props.compId);
            handleResponseMessages(promGetClubCompInfo);
            return promGetClubCompInfo.then( (resp) => {
              console.log("xxxxxxxxxxxxxxx", resp.meta)
              store.commit(
                ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
                "/" +
                ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_CLUB_COMP_INFO,
                resp.meta!.clubCompInfo
              );
              isReady.value = true;
            })


          }
          return;
        })
        .finally(() => {
          isLoadingSchedule.value = false;
        });

    }

    watch(
      () => props.compId,
      (newValue: number) => {
        if (r4sCompScheduleState.value.compId === props.compId) {
          return;
        }
        getScheduleData();
      },
      {
        immediate: true,
      }
    );

    return {
      r4sCompScheduleState,
      isLoadingSchedule,
      isReady,
    };
  },
});
</script>
