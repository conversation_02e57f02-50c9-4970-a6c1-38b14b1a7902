<template>
  <div class="e4s-flex-row e4s-gap--standard e4s-flex-center">
    <select
      v-model="clubCompInfoEntryIndivAthleteInternal.teamBibNo"
      v-on:change="onEdited"
      class="browser-default e4s-input-field e4s-input-field--primary"
      style="width: 75px"
    >
      <option v-for="bibNo in bibNos" :value="bibNo" v-text="bibNo"></option>
    </select>

    <div v-text="clubCompInfoEntryIndivAthleteInternal.entryName"></div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { IClubCompInfoEntryIndivAthlete } from "../schools/clubCompInfo-models";
import { simpleClone } from "../../../common/common-service-utils";

export default defineComponent({
  name: "entity-entries-event-edit-athlete",
  components: {},
  props: {
    clubCompInfoEntryIndivAthlete: {
      type: Object as PropType<IClubCompInfoEntryIndivAthlete>,
      required: true,
    },
    bibNos: {
      type: Array as PropType<(number | string)[]>,
      default: () => {
        return [];
      },
    },
  },
  setup(
    props: {
      clubCompInfoEntryIndivAthlete: IClubCompInfoEntryIndivAthlete;
      bibNos: (number | string)[];
    },
    context: SetupContext
  ) {
    const clubCompInfoEntryIndivAthleteInternal = ref(
      simpleClone(props.clubCompInfoEntryIndivAthlete)
    );

    watch(
      () => props.clubCompInfoEntryIndivAthlete,
      (
        newValue: IClubCompInfoEntryIndivAthlete,
        oldValue: IClubCompInfoEntryIndivAthlete
      ) => {
        if (newValue.entryId !== oldValue.entryId) {
          clubCompInfoEntryIndivAthleteInternal.value = simpleClone(newValue);
        }
      }
    );

    function onEdited() {
      context.emit(
        "onEdited",
        simpleClone(clubCompInfoEntryIndivAthleteInternal.value)
      );
    }

    return { clubCompInfoEntryIndivAthleteInternal, onEdited };
  },
});
</script>

<style>
.entity-entries--has-entries {
  background-color: var(--blue-50);
}

.entity-entries--has-entries-full {
  background-color: var(--blue-100);
}

.entity-entries--has-entries-title {
  font-weight: bold;
}
</style>
