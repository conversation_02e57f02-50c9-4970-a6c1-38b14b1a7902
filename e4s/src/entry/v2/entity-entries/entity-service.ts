import {
  IR4sCompSchedule,
  IR4sScheduledEvent,
  IScheduleTableRow,
} from "../../../competition/scoreboard/rs4/rs4-scoreboard-models";
import { Rs4Service } from "../../../competition/scoreboard/rs4/rs4-service";
import { sortArray } from "../../../common/common-service-utils";
import { IsoDate } from "../../../common/common-models";
import { IScheduleTableRowState } from "../schools/clubCompInfoService";
import { IClubCompInfoEntryIndivAthlete } from "../schools/clubCompInfo-models";

const rs4Service: Rs4Service = new Rs4Service();

// interface IScheduleTableRowWithEntries {
//   scheduledEvent: {
//     entries: AthleteEntry[];
//   };
// }

// interface AthleteEntry {
//   entryId: number;
//   entryName: string;
//   teamBibNo: string;
// }

export function createScheduleTableRows(
  r4sCompSchedule: IR4sCompSchedule
): IScheduleTableRow[] {
  let scheduleRows: IScheduleTableRow[] = r4sCompSchedule.schedule.map(
    (scheduledEvent) => {
      return rs4Service.mapScheduleEventToScheduleTableRow(scheduledEvent);
    }
  );

  scheduleRows = sortArray("startTime", scheduleRows);
  return scheduleRows;
}

export interface IScheduleFilterParams {
  simpleFilterValue: string;
  onlyWithEntries: boolean;
  scheduleDateDisplay: IsoDate | "ALL" | "";
}

// IScheduleTableRowState
export function filterScheduleTableRows(
  scheduleTableRows: IScheduleTableRow[],
  scheduleFilterParams: IScheduleFilterParams
): IScheduleTableRow[] {
  const preds: ((scheduleTableRow: IScheduleTableRow) => boolean)[] = [];
  const simpleFilter =
    scheduleFilterParams.simpleFilterValue.length > 0
      ? scheduleFilterParams.simpleFilterValue.toLowerCase()
      : "";

  // const searchType: "AND" | "OR" = "AND";

  const simpleFilterPred = (scheduleTableRow: IScheduleTableRow) => {
    return scheduleTableRow.eventName.toLowerCase().indexOf(simpleFilter) > -1;
  };

  // const sonlyEntriesPred = (scheduleTableRow: IScheduleTableRow) => {
  //   return scheduleTableRow.entries > 0;
  // };

  const datePred = (scheduleTableRow: IScheduleTableRow) => {
    if (
      scheduleFilterParams.scheduleDateDisplay === "ALL" ||
      scheduleFilterParams.scheduleDateDisplay.length === 0
    ) {
      return true;
    }

    return (
      scheduleFilterParams.scheduleDateDisplay ===
      scheduleTableRow.startTime.split("T")[0]
    );
  };

  /*
  const athleteEntryPred = (scheduleTableRow: IScheduleTableRow) => {
    const scheduleTableRowWithEntries: IScheduleTableRowWithEntries =
      scheduleTableRow as any as IScheduleTableRowWithEntries;

    if (!scheduleTableRowWithEntries.scheduledEvent.entries) {
      return false;
    }

    const entries = scheduleTableRowWithEntries.scheduledEvent.entries;

    if (entries.length === 0) {
      return false;
    }

    //  does the entryNAme match on one of the entries.
    return entries.some((athleteEntry: AthleteEntry) => {
      const entryName = athleteEntry.entryName.toLowerCase();
      const teamBibNo = athleteEntry.teamBibNo.toLowerCase();

      console.log(
        "athleteEntry entryName: " +
          entryName +
          " in " +
          simpleFilter +
          " =  " +
          (entryName.indexOf(simpleFilter) > -1)
      );

      return (
        entryName.indexOf(simpleFilter) > -1 ||
        teamBibNo.toLowerCase().indexOf(simpleFilter) > -1
      );
    });
  };
  */

  if (scheduleFilterParams.simpleFilterValue.length > 0) {
    preds.push(simpleFilterPred);
    // preds.push(athleteEntryPred);
  }

  if (scheduleFilterParams.scheduleDateDisplay.length > 0) {
    preds.push(datePred);
  }

  // const searchType: "AND" | "OR" = "OR";

  // const onlyWithEntriesMatch = scheduleFilterParams.onlyWithEntries ? hasEntries : true;
  // const hasEntries = scheduleTableRow.entries > 0;
  return scheduleTableRows.filter((scheduleTableRow) => {
    if (preds.length === 0) {
      return true;
    }

    // if (searchType === "OR") {
    //   return preds.some((pred) => {
    //     return pred(scheduleTableRow);
    //   });
    // }

    // we are doing an AND
    return preds.reduce<boolean>((accum, pred) => {
      const res = pred(scheduleTableRow);
      if (!res) {
        accum = false;
      }
      return accum;
    }, true);
  });
}

/**
 * Filters an array of schedule table row states based on specified filter criteria.
 *
 * @param {IScheduleTableRowState[]} data - An array of objects representing the state of each schedule table row.
 * @param {IScheduleFilterParams} scheduleFilterParams - The filter parameters used to determine if a schedule row should be included in the results.
 * @return {IScheduleTableRowState[]} - A filtered array of schedule table row states that meet the specified filter criteria.
 */
export function filterScheduleTableRowStates(
  data: IScheduleTableRowState[],
  scheduleFilterParams: IScheduleFilterParams,
  searchType: "AND" | "OR" = "AND"
): IScheduleTableRowState[] {
  const preds: ((scheduleTableRow: IScheduleTableRowState) => boolean)[] = [];
  const simpleFilter =
    scheduleFilterParams.simpleFilterValue.length > 0
      ? scheduleFilterParams.simpleFilterValue.toLowerCase()
      : "";

  /**
   * A predicate function to check if a schedule table row matches a specified event description filter.
   *
   * @param scheduleTableRowState
   */
  const eventDescriptionPred = (
    scheduleTableRowState: IScheduleTableRowState
  ) => {
    return (
      scheduleTableRowState.eventDescription
        .toLowerCase()
        .indexOf(simpleFilter) > -1
    );
  };

  /**
   * A predicate function to check if a schedule table row matches a specified date filter.
   *
   * @param scheduleTableRowState
   */
  const datePred = (scheduleTableRowState: IScheduleTableRowState) => {
    // null | IR4sScheduledEvent | IR4sPayloadTrack;
    const scheduledEvent: IR4sScheduledEvent = scheduleTableRowState
      .scheduleTableRow.scheduledEvent as IR4sScheduledEvent;

    const date = scheduledEvent.startDate.split("T")[0];
    if (
      scheduleFilterParams.scheduleDateDisplay === "ALL" ||
      scheduleFilterParams.scheduleDateDisplay.length === 0
    ) {
      return true;
    }

    return scheduleFilterParams.scheduleDateDisplay === date;
  };

  /**
   * A predicate function to check if a schedule table row has any entries.
   *
   * @param scheduleTableRowState
   */
  const hasAnyEntriesPred = (scheduleTableRowState: IScheduleTableRowState) => {
    return hasAnyEntries(scheduleTableRowState);
  };

  /**
   * A predicate function to check if a schedule table row matches a specified athlete entry filter.
   *
   * @param scheduleTableRow
   */
  const athleteEntryPred = (scheduleTableRowState: IScheduleTableRowState) => {
    //
    if (!scheduleTableRowState.entries) {
      return false;
    }

    // if zero
    if (scheduleTableRowState.entries.length === 0) {
      return false;
    }

    const entries = scheduleTableRowState.entries;

    //  does the entryName match on one of the entries.
    return entries.some(
      (athleteEntry: string | IClubCompInfoEntryIndivAthlete) => {
        let entryName = "";
        let teamBibNo = "";

        if (typeof athleteEntry === "string") {
          entryName = athleteEntry.toLowerCase();
        } else {
          entryName = athleteEntry.entryName.toLowerCase();
          teamBibNo = athleteEntry.teamBibNo.toString().toLowerCase();
        }

        const entryNameMatch = entryName.indexOf(simpleFilter) > -1;
        const teamBibNoMatch = teamBibNo.indexOf(simpleFilter) > -1;

        console.log(
          "athleteEntry entryName: " +
            entryName +
            " in " +
            simpleFilter +
            " =  " +
            entryNameMatch
        );

        return entryNameMatch || teamBibNoMatch;
      }
    );
  };

  const simpleFilterPred = (scheduleTableRowState: IScheduleTableRowState) => {
    return (
      eventDescriptionPred(scheduleTableRowState) ||
      athleteEntryPred(scheduleTableRowState)
    );
  };

  // now add any required preds
  if (scheduleFilterParams.simpleFilterValue.length > 0) {
    preds.push(simpleFilterPred);
  }

  if (scheduleFilterParams.scheduleDateDisplay.length > 0) {
    preds.push(datePred);
  }

  if (scheduleFilterParams.onlyWithEntries) {
    preds.push(hasAnyEntriesPred);
  }

  /**
   * Filters the schedule table row states based on the specified filter predicates.
   *
   * @param scheduleTableRowState
   */
  return data.filter((scheduleTableRowState: IScheduleTableRowState) => {
    if (preds.length === 0) {
      return true;
    }

    // if (searchType === "OR") {
    //   return preds.some((pred) => {
    //     return pred(scheduleTableRow);
    //   });
    // }

    // const resEventDescription = eventDescriptionPred(scheduleTableRowState);
    // const resDate = datePred(scheduleTableRowState);
    // const resHasAnyEntries = hasAnyEntriesPred(scheduleTableRowState);
    // const resAthleteEntry = athleteEntryPred(scheduleTableRowState);

    // if (searchType === "OR") {
    //   return (
    //     resEventDescription || resDate || resHasAnyEntries || resAthleteEntry
    //   );
    // }

    // return (
    //   (resEventDescription || resAthleteEntry) && resDate && resHasAnyEntries
    // );

    return preds.reduce<boolean>((accum, pred) => {
      const res = pred(scheduleTableRowState);
      if (!res) {
        accum = false;
      }
      return accum;
    }, true);
  });
}

export function hasAnyEntries(
  scheduleTableRowState: IScheduleTableRowState
): boolean {
  // return scheduleTableRow.entries > 0;
  return scheduleTableRowState.entries.length > 0;
}
