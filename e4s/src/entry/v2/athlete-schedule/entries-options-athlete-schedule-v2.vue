<template>
  <!--  :athlete-schedule-state="entriesOptionsAthleteScheduleInput"-->
  <CompEventActionsV2
    v-if="compEventActionsResult.competitionSummaryPublic.compId > 0"
    :entries-options-athlete-schedule-input="entriesOptionsAthleteScheduleInput"
    :comp-event-actions-result="compEventActionsResult"
    v-on:submitted="submitted"
    v-on:cancel="cancel"
  />
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";

import {
  CompEventActionsController,
  ICompEventActionsResult,
} from "../../../athleteCompSched/comp-event-actions/comp-event-actions-controller";
import CompEventActionsV2 from "../../../athleteCompSched/comp-event-actions/v2/comp-event-actions-v2.vue";
import { useConfigStore } from "../../../config/useConfigStore";
import { simpleClone } from "../../../common/common-service-utils";
import { AthleteCompSchedService } from "../../../athleteCompSched/athletecompsched-service";
import { ICompetitionSummaryPublic } from "../../../competition/competition-models";
import { IAthlete, IAthletePb } from "../../../athlete/athlete-models";
import {
  IAthleteCompSchedResponse,
  IAthleteCompSchedRuleEvent,
} from "../../../athleteCompSched/athletecompsched-models";

const athleteCompSchedService = new AthleteCompSchedService();

export interface IEntriesOptionsAthleteScheduleInput {
  competitionSummaryPublic: ICompetitionSummaryPublic;
  athlete: IAthlete;
  pbMap: Record<number, IAthletePb>;
  selectedAthleteCompSchedRuleEvent: IAthleteCompSchedRuleEvent;
  athleteCompSchedRuleEvents: IAthleteCompSchedRuleEvent[];
  athleteCompSchedResponse: IAthleteCompSchedResponse;
}

/**
 * This component's only job is to get all the data to pass into <CompEventActionsV2/>
 */
export default defineComponent({
  name: "entries-options-athlete-schedule-v2",
  components: {
    CompEventActionsV2,
  },
  props: {
    entriesOptionsAthleteScheduleInput: {
      type: Object as PropType<IEntriesOptionsAthleteScheduleInput>,
      default: function () {
        return athleteCompSchedService.factoryAthleteCompSchedResponse();
      },
    },
    isAdmin: {
      type: Boolean,
      default: function () {
        return false;
      },
    },
  },
  setup(
    props: {
      entriesOptionsAthleteScheduleInput: IEntriesOptionsAthleteScheduleInput;
      isAdmin: boolean;
    },
    context: SetupContext
  ) {
    const configStore = useConfigStore();

    const compEventActionsController = new CompEventActionsController();

    const compEventActionsResult = ref<ICompEventActionsResult>(
      compEventActionsController.factoryCompEventActionsResult()
    );

    const isLoading = ref(false);

    watch(
      () => props.entriesOptionsAthleteScheduleInput,
      (newValue: IEntriesOptionsAthleteScheduleInput) => {
        init(newValue);
      }
    );

    init(props.entriesOptionsAthleteScheduleInput);

    function init(
      entriesOptionsAthleteScheduleInput: IEntriesOptionsAthleteScheduleInput
    ) {
      const compEventActionsResultInit: ICompEventActionsResult = {
        ...compEventActionsController.factoryCompEventActionsResult(),
      };
      compEventActionsResultInit.competitionSummaryPublic =
        entriesOptionsAthleteScheduleInput.competitionSummaryPublic;
      compEventActionsResultInit.athlete =
        entriesOptionsAthleteScheduleInput.athlete;
      compEventActionsResultInit.compEvent =
        entriesOptionsAthleteScheduleInput.selectedAthleteCompSchedRuleEvent;
      compEventActionsResultInit.compEvents =
        entriesOptionsAthleteScheduleInput.athleteCompSchedRuleEvents;
      compEventActionsResultInit.userApplication =
        configStore.configApp.userInfo.user;

      compEventActionsResult.value = simpleClone(compEventActionsResultInit);
    }

    function cancel() {
      context.emit("cancel");
    }

    function submitted() {
      context.emit("submitted");
    }

    return { cancel, isLoading, compEventActionsResult, submitted };
  },
});
</script>
