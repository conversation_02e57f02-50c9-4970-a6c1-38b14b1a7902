<template>
    <div>
        <ul id="tabs" class="tabs">
            <li class="tab col s2 q1">
                <a href="#" v-on:click.prevent="onMobileTabClick(sections.LOCATION)"
                   class="e4s-breadcrumb"
                   :class="getActiveClass(sections.LOCATION)">
                    Info
                </a>
            </li>
            <li class="tab col s2 q2">
                <a href="#" v-on:click.prevent="onMobileTabClick(sections.SHOW_ENTRIES)"
                   class="e4s-breadcrumb"
                   :class="getActiveClass(sections.SHOW_ENTRIES)">
                    All Entries
                </a>
            </li>
            <li class="tab col s2 q3">
                <a href="#" v-on:click.prevent="onMobileTabClick(sections.FLYER)"
                   class="e4s-breadcrumb"
                   :class="getActiveClass(sections.FLYER)">
                    Flyer
                </a>
            </li>
            <li class="tab col s2 q4">
                <a href="#" v-on:click.prevent="onMobileTabClick(sections.SCHEDULE)"
                   class="e4s-breadcrumb"
                   :class="getActiveClass(sections.SCHEDULE)">
                    Schedule
                </a>
            </li>
            <li class="indicator" style="width: 100%;">

            </li>
        </ul>

        <div>
            <div class="col s12">
                <location v-if="canLoadTab(sections.LOCATION)"
                              v-show="displaySection === sections.LOCATION"
                              v-bind:competition="competition"
                              class="entry-open-tab-content">
                </location>
            </div>
            <div class="col s12">
                <show-entries v-if="canLoadTab(sections.SHOW_ENTRIES)"
                              v-show="displaySection === sections.SHOW_ENTRIES"
                              v-bind:competition="competition"
                              class="entry-open-tab-content">
                </show-entries>
            </div>
            <div class="col s12">
                <flyer v-if="canLoadTab(sections.FLYER)"
                       v-show="displaySection === sections.FLYER"
                       v-bind:competition="competition"
                       class="entry-open-tab-content">
                </flyer>
            </div>
            <div class="col s12">
                <schedule v-if="canLoadTab(sections.SCHEDULE)"
                          v-show="displaySection === sections.SCHEDULE"
                          v-bind:competition="competition"
                          class="entry-open-tab-content">
                </schedule>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import { Prop } from "vue-property-decorator";
    import { ICompetitionInfo } from "../competition/competition-models";
    import { CompetitionService } from "../competition/competiton-service";
    import Flyer from "../flyer/flyer.vue";
    import Location from "../location/location.vue";
    import ShowEntries from "../showentries/showentries.vue";
    import Schedule from "../schedule/schedule.vue";

    const competitionService: CompetitionService = new CompetitionService();

    @Component({
        name: "entry-info",
        components: {
            "location": Location,
            "flyer": Flyer,
            "show-entries": ShowEntries,
            "schedule": Schedule
        }
    })
    export default class EntryInfo extends Vue {
        @Prop({
            default: () => {
                return competitionService.factoryCompetitionInfo();
            }
        }) public readonly competition: ICompetitionInfo;

        public sections = {
            LOCATION: "LOCATION",
            SHOW_ENTRIES: "SHOW_ENTRIES",
            FLYER: "FLYER",
            SCHEDULE: "SCHEDULE"
        };

        public displaySection: string = this.sections.LOCATION;

        public hasSectionOpened = {
            [this.sections.LOCATION]: true,
            [this.sections.SHOW_ENTRIES]: false,
            [this.sections.FLYER]: false,
            [this.sections.SCHEDULE]: false
        };

        public isActiveTab(sectionId: string) {
            return sectionId === this.displaySection;
        }

        public getActiveClass(sectionId: string): string {
            if (this.isActiveTab(sectionId)) {
                return "breadcrumb-active";
            }
            return "";
        }

        public onMobileTabClick(sectionId: string) {
            this.displaySection = sectionId;
            this.hasSectionOpened[sectionId] = true;
        }

        public canLoadTab(sectionId: string) {
            return this.hasSectionOpened[sectionId];
        }

    }
</script>

<style scoped>

    .e4s-nav-mob {
        height: 3rem;
        padding: 1rem;
    }

    .e4s-nav-mob a {
        height: 3rem;
        padding: 0.5rem;
    }

    .e4s-breadcrumb {
        font-size: 14px;
        /*color: black;*/
        padding-top: 1rem !important;
    }

    .breadcrumb-active {
        /*color: red;*/
        font-size: 14px;
        font-weight: bold;
    }

</style>
