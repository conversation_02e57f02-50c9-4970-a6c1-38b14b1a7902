<template>
  <div>
    <div class="row">
      <div class="col s12">
        <div class="mobile--comp-name">
          <span v-text="getCompNameHeader"></span>
        </div>

        <!--                <span v-if="isTeamsLoading">isTeamsLoading</span>-->
        <!--                <span v-if="eventTeamHeadersLoading">eventTeamHeadersLoading</span>-->
        <!--                id: <span v-text="selectedCompetition.id"></span>-->
        <div class="right">
          <loading-spinner v-if="getIsDataInitiallyLoading"></loading-spinner>
        </div>
      </div>
    </div>

    <div v-show="!getIsDataInitiallyLoading">
      <div class="row">
        <div class="col s12 m12 l12">
          <div class="e4s-nav-mob">
            <div>
              <a
                href="#"
                v-on:click.prevent="onMobileTabClick(entryTabs.COMP)"
                class="e4s-breadcrumb"
                :class="getActiveClass(entryTabs.COMP)"
              >
                Meet
              </a>
              <a
                href="#"
                v-on:click.prevent="onMobileTabClick(entryTabs.TEAM_EVENTS)"
                v-show="showEventTeams"
                v-if="showThisSection('TEAMS')"
                class="e4s-breadcrumb"
                :class="getActiveClass(entryTabs.TEAM_EVENTS)"
              >
                Teams
              </a>
              <a
                href="#"
                v-on:click.prevent="onMobileTabClick(entryTabs.ATHLETES)"
                v-show="showAthletes"
                v-if="showThisSection('ATHLETES')"
                class="e4s-breadcrumb"
                :class="getActiveClass(entryTabs.ATHLETES)"
              >
                Athletes
              </a>
              <a
                href="#"
                v-on:click.prevent="onMobileTabClick(entryTabs.SCHEDULE)"
                v-show="showAthletes"
                v-if="showThisSection('SCHEDULE')"
                class="e4s-breadcrumb"
                :class="getActiveClass(entryTabs.SCHEDULE)"
              >
                Events
              </a>
              <a
                href="#"
                v-on:click.prevent="onMobileTabClick(entryTabs.YOUR_ENTRIES)"
                v-show="showYourEntries"
                class="e4s-breadcrumb"
                :class="getActiveClass(entryTabs.YOUR_ENTRIES)"
              >
                Entries
              </a>

              <a
                href="#"
                v-if="showThisSection('SHOP')"
                v-on:click.prevent="onMobileTabClick(entryTabs.SHOP)"
                class="e4s-breadcrumb"
                :class="getActiveClass(entryTabs.SHOP)"
              >
                Shop
              </a>
              <!--                        <a href="#" v-on:click.prevent="onMobileTabClick(entryTabs.CART)"-->
              <!--                           class="e4s-breadcrumb"-->
              <!--                           :class="getActiveClass(entryTabs.CART)">-->
              <!--                            Cart-->
              <!--                        </a>-->
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div class="entry-mobile">
            <div v-show="mobileTabNumber === entryTabs.COMP">
              <sched-info v-bind:schedInfo="getSchedInfoComp">
                <div slot="extra">
                  <ButtonGenericBackV2
                    text="Contact Organiser"
                    @click="showContactOrganiser = true"
                  />
                  <!--                  <button-->
                  <!--                    class="btn waves-effect waves green"-->
                  <!--                    v-on:click="showContactOrganiser = true"-->
                  <!--                  >-->
                  <!--                    Contact Organiser-->
                  <!--                  </button>-->
                </div>
              </sched-info>

              <div class="e4s-section-padding-separator"></div>

              <!--                        <div class="row">-->
              <!--                            <div class="col s10">-->
              <!--                                <div v-text="getCompName" class="mobile&#45;&#45;comp-name"></div>-->
              <!--                            </div>-->
              <!--                            <div class="col s2">-->
              <!--                                <loading-spinner v-if="isLoadingClubs"></loading-spinner>-->
              <!--                            </div>-->
              <!--                        </div>-->

              <!--This bit is going away-->
              <div class="row" v-show="false">
                <div class="e4s-mobile-drop-down-wrapper">
                  <div class="col s10">
                    <div v-text="getCompName" class="mobile--comp-name"></div>
                  </div>
                  <div class="col s2">
                    <loading-spinner v-if="isLoadingClubs"></loading-spinner>
                  </div>
                </div>
              </div>

              <div class="row" v-show="false">
                <div class="e4s-mobile-drop-down-wrapper">
                  <div class="col s10">
                    <club-dropdown
                      class="
                        e4s-force-inline-block
                        e4s-select
                        e4s-select-full
                        e4s-select-mobile
                      "
                      :autoSelectId="autoSelect.compOrgId"
                      :clubs="compOrgs"
                      :is-loading="isLoadingClubs"
                      v-on:ON_SELECT="onSelectedClub"
                    >
                    </club-dropdown>
                  </div>
                  <div class="col s2">
                    <loading-spinner v-if="isLoadingClubs"></loading-spinner>
                  </div>
                </div>
              </div>
              <div class="row" v-show="false">
                <div class="e4s-mobile-drop-down-wrapper">
                  <div class="col s10">
                    <competition-dropdown
                      class="
                        e4s-force-inline-block
                        e4s-select
                        e4s-select-full
                        e4s-select-mobile
                        right
                      "
                      :class="isLoadingCompetitions ? 'disabled' : ''"
                      :autoSelectId="autoSelect.compId"
                      :id="compOrgId"
                      :competitions="competitions"
                      :isLoading="competitionsLoading"
                      v-on:onSelected="onSelectedCompetition"
                    >
                    </competition-dropdown>
                  </div>
                  <div class="col s2">
                    <loading-spinner
                      v-if="isLoadingCompetitions"
                    ></loading-spinner>
                  </div>
                </div>
              </div>
              <!--/This bit is going away-->

              <div v-if="showAthleteSelectionMessage">
                <div class="e4s-section-padding-separator"></div>
                <CompRestricted
                  :athlete-security="
                    selectedCompetition.options.athleteSecurity
                  "
                ></CompRestricted>
              </div>

              <div v-if="selectedCompetition && selectedCompetition.id > 0">
                <div class="e4s-section-padding-separator"></div>

                <div class="row">
                  <div class="col s12">
                    <span class="e4s-bold">Date:</span>
                    <span class="right" v-text="getStartDate"></span>
                  </div>
                </div>

                <div class="e4s-section-padding-separator"></div>

                <div class="row">
                  <div class="col s12">
                    <span class="e4s-bold">Entries close:</span>
                    <span class="right" v-text="getEntryCloseDate"></span>
                  </div>
                </div>
              </div>

              <div class="row" v-if="showEventTeams">
                <div class="e4s-section-padding-separator"></div>

                <div class="col s12">
                  <span class="e4s-bold">Team selection:</span>
                  <!--<span> available</span>-->
                  <a
                    class="right"
                    href="#"
                    v-on:click.prevent="onMobileTabClick(entryTabs.TEAM_EVENTS)"
                  >
                    Select
                  </a>
                </div>
              </div>

              <div class="row" v-if="showAthletes">
                <div class="e4s-section-padding-separator"></div>

                <div class="col s12">
                  <span class="e4s-bold">Athlete selection:</span>
                  <!--<span> available</span>-->
                  <a
                    class="right"
                    href="#"
                    v-on:click.prevent="onMobileTabClick(entryTabs.ATHLETES)"
                  >
                    Select
                  </a>
                </div>
              </div>
            </div>

            <event-teams-panel
              v-show="mobileTabNumber === entryTabs.TEAM_EVENTS"
              class="e4s-section-wrapper-mobile"
            >
            </event-teams-panel>

            <!-- <team-select
                                v-show="mobileTabNumber===entryTabs.TEAMS"
                                v-bind:competition="competition">
                        </team-select> -->

            <div v-show="mobileTabNumber === entryTabs.ATHLETES">
              <AthleteGrid
                class="e4s-section-wrapper-mobile"
                v-bind:competition="competition"
                v-bind:selectedTeam="selectedTeam"
                v-bind:athletesVisible="athletesExpanded"
                v-bind:triggerRefresh="triggerRefreshAthlete"
                v-on:onSelected="onSelectedAthleteMobile"
              >
              </AthleteGrid>
            </div>

            <div v-show="mobileTabNumber === entryTabs.SCHEDULE">
              <!--                        <div class="row" v-if="anySecondaryAthleteItems">-->
              <!--                            <div class="col s12 m12 l12">-->
              <!--                                <span class="e4s-bold">More Shop items will be available when an eligible athlete is selected.</span>-->
              <!--                            </div>-->
              <!--                        </div>-->

              <!--                            <athlete-comp-sched-grid-->
              <!--                                class="e4s-section-wrapper-mobile"-->
              <!--                                v-bind:competition="competition"-->
              <!--                                v-bind:athlete="athlete"-->
              <!--                                v-on:openShop="openShop"-->
              <!--                            >-->
              <!--                            </athlete-comp-sched-grid>-->

              <EventsCardGrid
                class="e4s-section-wrapper-mobile"
                v-on:openShop="openShop"
              ></EventsCardGrid>
            </div>

            <div v-show="mobileTabNumber === entryTabs.SHOP">
              <SecondaryCustFormGrid
                :secondary-ref-obj="getSecondaryRefObj"
              ></SecondaryCustFormGrid>
            </div>

            <div v-if="mobileTabNumber === entryTabs.YOUR_ENTRIES">
              <EntityEntriesLoader
                :comp-id="selectedCompetition.id"
                :club-comp-info="clubCompInfo"
              />
            </div>

            <cart
              class="e4s-section-wrapper-mobile"
              v-show="mobileTabNumber === entryTabs.CART"
              v-bind:athlete="athlete"
              v-bind:competition="competition"
              v-bind:eventsSelected="eventsSelected"
            >
            </cart>
          </div>
        </div>
      </div>
    </div>

    <AskOrganiserModal
      :show-contact-organiser="showContactOrganiser"
      :selected-competition="selectedCompetition"
      v-on:onClose="showContactOrganiser = false"
    >
    </AskOrganiserModal>

    <ModalV2
      v-if="showAthleteGenericEntityPicker"
      :is-full-screen="true"
      :always-show-header-blank="true"
    >
      <div slot="body" style="padding: var(--e4s-gap--standard)">
        <GenericClubEntityPicker
          :builder-competition="selectedCompetition"
          :athlete="athlete"
          @input="saveGenericEntityPickerMobile"
          @onCancel="hideGenericEntityPicker"
        />
      </div>
    </ModalV2>

    <LoadingSpinnerV2
      v-if="isSelectedAthleteLoading"
      loading-message="Loading selected athlete..."
    />
  </div>
</template>

<script lang="ts">
import Component from "vue-class-component";
import { Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import AthleteGrid from "../athlete/athlete-grid.vue";
import { IAthleteSummary } from "../athlete/athlete-models";
import AgeInfo from "../athletecompsched/ageinfo.vue";
import AthleteCompSchedGrid from "../athletecompsched/athletecompsched.vue";
import EventTeamsPanel from "../athletecompsched/comp-event-teams/event-teams-panel.vue";
import OrderSummary from "../athletecompsched/order-summary.vue";
import ShowCompSched from "../athletecompsched/showcompsched.vue";
import {
  ATH_COMP_SCHED_STORE_CONST,
  IAthCompSchedStoreState,
} from "../athletecompsched/store/athletecompsched-store";
import Cart from "../cart/cart.vue";
import ClubDropDown from "../club/club-dropdown.vue";
import { CLUB_STORE_CONST, IClubStoreState } from "../club/club-store";
import LoadingSpinner from "../common/ui/loading-spinner.vue";
import CompetitionDropDown from "../competition/competition-dropdown.vue";
import Flyer from "../flyer/flyer.vue";
import Location from "../location/location.vue";
import Schedule from "../schedule/schedule.vue";
import ShowEntries from "../showentries/showentries.vue";
import TeamEntryModalMat from "../team/entry/team-entry-modal-mat.vue";
import TeamSelect from "../team/select/team-select.vue";
import { ITeam } from "../team/team-models";
import { ITeamStoreState, TEAM_STORE_CONST } from "../team/team-store";
import UserMessagesTable from "../user-message/user-message-table.vue";
import { ENTRY_STORE_CONST, IEntryStoreState } from "./entry-store";
import {
  COMP_EVENT_TEAMS_STORE_CONST,
  ICompEventTeamsStoreState,
} from "../athletecompsched/comp-event-teams/comp-event-store";
import { IEventTeamHeader } from "../athletecompsched/comp-event-teams/event-teams-models";
import { ICompetitionInfo } from "../competition/competition-models";
import {
  IAthleteCompSchedRuleEvent,
  ISchedInfo,
} from "../athletecompsched/athletecompsched-models";
import SchedInfo from "../athletecompsched/sched-info/sched-info.vue";
import { IClub } from "../club/club-models";
import EntryBase from "./entry-base";
import { IConfigApp } from "../config/config-app-models";
import AskOrganiserModal from "../competition/askorganiser/ask-organiser-modal.vue";
import CompRestricted from "../competition/restricted/comp-restricted.vue";
import SecondaryCustFormGrid from "../secondary/cust/secondary-cust-form-grid.vue";
import { ISecondaryRefObj } from "../secondary/secondary-models";
import { SecondaryService } from "../secondary/secondary-service";
import { SECONDARY_CUST_STORE_CONST } from "../secondary/cust/secondary-cust-store";
import EventsCardGrid from "../athletecompsched/events-card-grid.vue";
import { hasClubCompInfoCompetition } from "./v2/schools/clubCompInfoService";
import { IClubCompInfo } from "./v2/schools/clubCompInfo-models";
import EntityEntriesLoader from "./v2/entity-entries/EntityEntriesLoader.vue";
import { ENTRY_SECTION } from "../builder/builder-models";
import InfoSectionV2 from "../common/ui/layoutV2/info-section-v2.vue";
import GenericClubEntityPicker from "./v2/GenericClubEntityPicker.vue";
import ModalV2 from "../common/ui/layoutV2/modal/modal-v2.vue";
import { VUE_MQ_SIZES } from "../index";
import LoadingSpinnerV2 from "../common/ui/loading-spinner-v2.vue";
import ButtonGenericBackV2 from "../common/ui/layoutV2/buttons/button-generic-back-v2.vue";

enum ENTRY_TABS {
  COMP = "COMP",
  TEAMS = "TEAMS",
  TEAM_EVENTS = "TEAM_EVENTS",
  ATHLETES = "ATHLETES",
  SCHEDULE = "SCHEDULE",
  SHOP = "SHOP",
  CART = "CART",
  YOUR_ENTRIES = "YOUR_ENTRIES",
}

@Component({
  name: "entry-mobile",
  components: {
    ButtonGenericBackV2,
    LoadingSpinnerV2,
    ModalV2,
    GenericClubEntityPicker,
    InfoSectionV2,
    EntityEntriesLoader,
    EventsCardGrid,
    SecondaryCustFormGrid,
    CompRestricted,
    AskOrganiserModal,
    "club-dropdown": ClubDropDown,
    "competition-dropdown": CompetitionDropDown,
    AthleteGrid,
    "athlete-comp-sched-grid": AthleteCompSchedGrid,
    "user-messages-table": UserMessagesTable,
    cart: Cart,
    ageinfo: AgeInfo,
    location: Location,
    flyer: Flyer,
    "show-comp-sched": ShowCompSched,
    "show-entries": ShowEntries,
    ordersummary: OrderSummary,
    "team-select": TeamSelect,
    "team-entry-modal-mat": TeamEntryModalMat,
    "loading-spinner": LoadingSpinner,
    schedule: Schedule,
    "event-teams-panel": EventTeamsPanel,
    "sched-info": SchedInfo,
  },
  computed: {
    VUE_MQ_SIZES() {
      return VUE_MQ_SIZES;
    },
    ...mapState(CLUB_STORE_CONST.CLUB_STORE_CONST_MODULE_NAME, {
      compOrgs: (state: IClubStoreState) => state.clubDropDown.clubs,
      isLoadingClubs: (state: IClubStoreState) =>
        state.clubDropDown.clubsLoading,
    }),
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      competitions: (state: IEntryStoreState) => state.entryForm.competitions,
      competitionsLoading: (state: IEntryStoreState) =>
        state.entryForm.competitionsLoading,
      athlete: (state: IEntryStoreState) => state.entryForm.selectedAthlete,
      selectedClub: (state: IEntryStoreState) => state.entryForm.club,
      selectedCompetition: (state: IEntryStoreState) =>
        state.entryForm.selectedCompetition,
      athleteMultiPb: (state: IEntryStoreState) =>
        state.entryForm.athleteMultiPb,
      athleteMultiPbModalShow: (state: IEntryStoreState) =>
        state.entryForm.athleteMultiPbModalShow,
      selectedAthleteLoading: (state: IEntryStoreState) =>
        state.entryForm.selectedAthleteLoading,
      triggerRefreshAthlete: (state: IEntryStoreState) =>
        state.entryForm.triggerRefreshAthlete,
      autoSelect: (state: IEntryStoreState) => state.entryForm.autoSelect,
      clubCompInfo: (state: IEntryStoreState) => state.entryForm.clubCompInfo,
      isSelectedAthleteLoading: (state: IEntryStoreState) =>
        state.entryForm.selectedAthleteLoading,
    }),
    ...mapState(ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME, {
      eventsSelected: (state: IAthCompSchedStoreState) => state.eventsSelected,
      eventsProcessed: (state: IAthCompSchedStoreState) =>
        state.eventsProcessed,
      ageInfo: (state: IAthCompSchedStoreState) =>
        state.eventsServerResponse.ageInfo,
    }),
    ...mapState(TEAM_STORE_CONST.TEAM_STORE_CONST_MODULE_NAME, {
      selectedTeam: (state: ITeamStoreState) => state.selectedTeam,
      isTeamsLoading: (state: ITeamStoreState) => state.isLoading,
      createTeamShowModal: (state: ITeamStoreState) =>
        state.createTeamShowModal,
    }),
    ...mapState(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME,
      {
        eventTeamHeaders: (state: ICompEventTeamsStoreState) =>
          state.eventTeamHeaders,
        eventTeamHeadersLoading: (state: ICompEventTeamsStoreState) =>
          state.eventTeamHeadersLoading,
      }
    ),
    ...mapGetters({
      getAgeInfo:
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_GETTERS_AGE_INFO,
      getOrderSummary:
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_GETTERS_ORDER_SUMMARY_TOTALS,
      isAthleteFilterOn:
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_GETTERS_IS_ATHLETE_SEARCH_FILTER_ON,
      isSelectedCompetitionTeam:
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_GETTERS_SELECTED_COMP_IS_TEAM,
      showAddAthleteButton:
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_GETTERS_SHOW_ADD_ATHLETE,
    }),
    ...mapGetters({
      anySecondaryAthleteItems:
        SECONDARY_CUST_STORE_CONST.SECONDARY_CUST_CONST_MODULE_NAME +
        "/" +
        SECONDARY_CUST_STORE_CONST.SECONDARY_CUST_GETTER_ANY_ATHLETE_ITEMS,
    }),
  },
})
export default class EntryMobile extends EntryBase {
  public readonly selectedClub!: IClub;
  public readonly isLoadingClubs!: boolean;
  public readonly configApp!: IConfigApp;
  public readonly selectedCompetition!: ICompetitionInfo;
  public readonly isTeamsLoading!: boolean;
  public readonly eventsSelected!: IAthleteCompSchedRuleEvent[];
  public readonly eventsProcessed!: IAthleteCompSchedRuleEvent[];
  public readonly compOrgs!: IClub[];
  public readonly anySecondaryAthleteItems!: boolean;
  public readonly eventTeamHeadersLoading!: boolean;
  public readonly athlete!: IAthleteSummary;
  public readonly clubCompInfo: IClubCompInfo;
  public readonly showAthleteGenericEntityPicker: boolean;
  public readonly isSelectedAthleteLoading!: boolean;

  public compOrgId: number = 0;
  public selectedTeam: ITeam;

  public userConfirmedBrowseAway: boolean = false;
  public showRequiresConfirmBrowseAway: boolean = false;
  public competitionInfoExpanded = false;
  public scheduleExpanded = false;
  public summaryExpanded = false;
  public teamsExpanded = false;
  public athletesExpanded = false;
  public entryTabs = ENTRY_TABS;
  public mobileTabNumber: string = "";
  public setTabToSchedule: string = "";

  // public isLoadingClubs: boolean; //  don't set, see mappers
  //  don't set, see mappers
  public isLoadingCompetitions: boolean = false;
  public eventTeamHeaders: IEventTeamHeader[];

  public maxRestrictedClubsDisplay: number = 2;
  public showAllRestrictedClubs = false;
  public showContactOrganiser: boolean = false;

  public created() {
    this.mobileTabNumber = this.entryTabs.COMP;
  }

  public mounted() {
    this.resetStore({ resetTeams: true, resetEvents: true });

    this.$store.dispatch(
      CLUB_STORE_CONST.CLUB_STORE_CONST_MODULE_NAME +
        "/" +
        CLUB_STORE_CONST.CLUB_STORE_ACTIONS_GET_COMP_ORGS,
      {
        public: false,
        orgId: this.compOrgId,
      }
    );

    // this.$store.dispatch(
    //     ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
    //     ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_GET_CART
    // );
  }

  @Watch("athlete")
  public onAthleteSelectedChanged(newValue: IAthleteSummary) {
    this.resetStore({
      resetTeams: false,
      resetEvents: true,
    });
    if (newValue && newValue.id > 0) {
      if (
        this.competitionService.getEntryDefaultPanel(
          this.selectedCompetition.options
        ) === "SHOP"
      ) {
        this.mobileTabNumber = this.entryTabs.SHOP;
        return;
      }
      this.setTabToSchedule = new Date().toISOString();
    }
  }

  @Watch("eventsSelected")
  public onEventsSelectedChanged() {
    this.userConfirmedBrowseAway = false;
    this.showRequiresConfirmBrowseAway = false;
  }

  public get getIsDataInitiallyLoading() {
    if (this.athlete && this.athlete.id > 0) {
      return false;
    }

    return (
      !this.selectedCompetition ||
      !this.selectedCompetition.id ||
      this.selectedCompetition.id === 0 ||
      this.isLoadingClubs ||
      this.eventTeamHeadersLoading ||
      this.isTeamsLoading
    );
  }

  @Watch("getIsDataInitiallyLoading")
  public onGetIsDataLoadingChanged(newValue: boolean, oldValue: boolean) {
    if (
      this.selectedCompetition &&
      this.selectedCompetition.id &&
      this.selectedCompetition.id > 0
    ) {
      if (
        this.competitionService.getEntryDefaultPanel(
          this.selectedCompetition.options
        ) === "SHOP"
      ) {
        this.mobileTabNumber = this.entryTabs.SHOP;
        return;
      }

      if (
        this.eventTeamHeaders.length === 0 &&
        !(this.athlete && this.athlete.id > 0)
      ) {
        this.mobileTabNumber = this.entryTabs.ATHLETES;
      }
    }
  }

  public get getCompNameHeader() {
    if (
      !this.selectedClub ||
      !this.selectedCompetition ||
      this.selectedClub.id === 0 ||
      this.selectedCompetition.id === 0
    ) {
      return "";
    }
    return (
      this.selectedClub.club +
      " - " +
      (this.selectedCompetition.name ? this.selectedCompetition.name : "") +
      ": " +
      this.startDate(this.selectedCompetition)
    );
  }

  public get getCompName() {
    if (
      !this.selectedClub ||
      !this.selectedCompetition ||
      this.selectedClub.id === 0 ||
      this.selectedCompetition.id === 0
    ) {
      return "";
    }
    return (
      this.selectedClub.club +
      " - " +
      (this.selectedCompetition.name ? this.selectedCompetition.name : "")
    );
  }

  public onSelectedClub(club: IClub) {
    this.compOrgId = club.clubid;
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_ENTRYFORM_SET_CLUB,
      club
    );

    this.$store.dispatch(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_ACTIONS_GET_COMPETITIONS_BY_ID,
      {
        orgId: this.compOrgId,
        compId: this.compId,
      }
    );
  }

  public onMobileTabClick(tabId: string) {
    this.mobileTabNumber = tabId;
  }

  public get getAthleteName() {
    return this.athlete && this.athlete.firstName
      ? this.athlete.firstName + " " + this.athlete.surName
      : "";
  }

  public get showDropDownSpinner() {
    return this.isLoadingClubs || this.isTeamsLoading;
  }

  public competitionLoading(isLoading: boolean) {
    this.isLoadingCompetitions = isLoading;
  }

  public get isAthleteSelected() {
    return this.athlete && this.athlete.id > 0;
  }

  public get isTeamSelected() {
    return this.selectedTeam && this.selectedTeam.teamName;
  }

  public get getSelectedTeamName() {
    return this.selectedTeam && this.selectedTeam.teamName
      ? this.selectedTeam.teamName
      : "";
  }

  public isActiveTab(tabId: string) {
    return tabId === this.mobileTabNumber;
  }

  public getActiveClass(tabId: string): string {
    if (this.isActiveTab(tabId)) {
      return "breadcrumb-active";
    }
    return "";
  }

  public get showEventTeams() {
    return this.eventTeamHeaders.length > 0;
  }

  public get showAthletes() {
    return this.selectedCompetition && this.selectedCompetition.indivEvents;
  }

  public get showYourEntries() {
    return hasClubCompInfoCompetition(
      this.selectedCompetition.clubCompInfo as IClubCompInfo
    );
  }

  public get getSchedInfoComp(): ISchedInfo {
    const schedInfo: ISchedInfo = {
      title: "Meeting",
    } as ISchedInfo;
    schedInfo.schedInfoDetails = [];
    schedInfo.autoExpand = true;
    schedInfo.shortDescription =
      "Select organiser and competition you wish to enter.";
    schedInfo.showLinks = false;

    return schedInfo;
  }

  public get getEntryCloseDate() {
    if (this.selectedCompetition && this.selectedCompetition.id > 0) {
      return this.entryCloseDate(this.selectedCompetition);
    }
    return "";
  }

  public get getStartDate() {
    if (this.selectedCompetition && this.selectedCompetition.id > 0) {
      return this.startDate(this.selectedCompetition);
    }
    return "";
  }

  public onSelectedAthleteMobile(athleteLookup: IAthleteSummary) {
    this.onMobileTabClick(this.entryTabs.SCHEDULE);
    this.onSelectedAthlete(athleteLookup);
  }

  public get showAthleteSelectionMessage() {
    if (
      this.selectedCompetition &&
      this.selectedCompetition.options &&
      this.selectedCompetition.options.athleteSecurity &&
      this.selectedCompetition.options.athleteSecurity.clubs
    ) {
      return this.selectedCompetition.options.athleteSecurity.clubs.length > 0;
    }
    return false;
  }

  public get getSecondaryRefObj(): ISecondaryRefObj {
    const secondaryRefObj = new SecondaryService().factorySecondaryRefObj();
    if (
      this.selectedCompetition &&
      this.selectedCompetition.id &&
      this.selectedCompetition.id > 0
    ) {
      secondaryRefObj.objId = this.selectedCompetition.id;
      secondaryRefObj.objType = "COMP";
      secondaryRefObj.compId = this.selectedCompetition.id;
      secondaryRefObj.objName = this.selectedCompetition.name;
    }
    return secondaryRefObj;
  }

  public get getShowAthleteItemsAvailable() {
    return this.anySecondaryAthleteItems && this.eventsProcessed.length > 0;
  }

  public openShop() {
    this.onMobileTabClick(this.entryTabs.SHOP);
  }

  public showThisSection(sectionName: ENTRY_SECTION): boolean {
    const selectedCompetition = this.selectedCompetition;
    if (!(selectedCompetition && selectedCompetition.id > 0)) {
      return true;
    }
    if (!this.selectedCompetition.options.ui.sectionsToHide) {
      //  A legacy comp and this is not set in the config.
      return true;
    }
    const hideSection =
      this.selectedCompetition.options.ui.sectionsToHide[sectionName];
    return !hideSection;
  }
}
</script>

<style scoped>
.e4s-nav-mob {
  /*height: 3rem;*/
  padding: 5px 0;
}

.e4s-nav-mob a {
  /*height: 3rem;*/
  padding: 0.5rem;
}

.e4s-breadcrumb {
  font-size: 14px;
  /*color: black;*/
  padding-top: 1rem !important;
}

.breadcrumb-active {
  /*color: red;*/
  font-size: 14px;
  font-weight: bold;
}

.mobile--comp-name {
  font-size: 1.5em;
}
</style>
