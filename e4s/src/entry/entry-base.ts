import { format, parse } from "date-fns";
import Vue from "vue";
import Component from "vue-class-component";
import { Watch } from "vue-property-decorator";
import { mapState } from "vuex";
import { IAthleteSummary } from "../athlete/athlete-models";
import {
  COMP_EVENT_TEAMS_STORE_CONST,
  ICompEventTeamsStoreState,
} from "../athletecompsched/comp-event-teams/comp-event-store";
import { IEventTeamHeader } from "../athletecompsched/comp-event-teams/event-teams-models";
import { ATH_COMP_SCHED_STORE_CONST } from "../athletecompsched/store/athletecompsched-store";
import { IClub } from "../club/club-models";
import { ICompetitionInfo } from "../competition/competition-models";
import { CompetitionService } from "../competition/competiton-service";
import { TEAM_STORE_CONST } from "../team/team-store";
import { ENTRY_STORE_CONST, IEntryStoreState } from "./entry-store";
import EventSwitchModal from "../athletecompsched/event-switch-modal.vue";
import { CONFIG } from "../common/config";
import { BUILDER_STORE_CONST } from "../builder/builder-store-constants";
import { IBuilderStoreState } from "../builder/builder-store";
import { IBuilderCompetition } from "../builder/builder-models";
import { IBaseConcrete } from "../common/common-models";
import { WindowController } from "../common/window-controller";

// You can declare a mixin as the same style as components.
@Component({
  name: "entry-base",
  components: {
    "event-switch-modal": EventSwitchModal,
  },
  computed: {
    ...mapState(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME,
      {
        eventTeamHeaders: (state: ICompEventTeamsStoreState) =>
          state.eventTeamHeaders,
      }
    ),
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      athlete: (state: IEntryStoreState) => state.entryForm.selectedAthlete,
      showAthleteGenericEntityPicker: (state: IEntryStoreState) =>
        state.entryForm.showAthleteGenericEntityPicker,
    }),
    ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
      builderCompetition: (state: IBuilderStoreState) =>
        state.builderCompetition,
    }),
  },
})
export default class EntryBase extends Vue {
  public eventTeamHeaders: IEventTeamHeader[];
  public athlete: IAthleteSummary;
  public readonly builderCompetition: IBuilderCompetition;
  public readonly showAthleteGenericEntityPicker: boolean;

  public competition: ICompetitionInfo = {} as ICompetitionInfo;
  public competitionEntryCloseUserString: string = "";
  public compOrgId: number = 0;
  public compId: number = 0;

  public club: IClub = {} as IClub;
  public competitionSelected: boolean = false;
  public displayFlyer: boolean = false;
  public competitionService = new CompetitionService();
  public windowController = new WindowController();

  public created() {
    const routeQuery = this.$route.query;
    if (routeQuery.comporgid) {
      this.compOrgId = Number(routeQuery.comporgid);
    }
    if (routeQuery.compid) {
      this.compId = Number(routeQuery.compid);
    }
  }

  @Watch("club")
  public onClubSelectedChanged() {
    this.competition = {} as ICompetitionInfo;
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_SELECTED_ATHLETE,
      {}
    );
    //  this.athlete = {} as IAthlete;
    this.resetStore({ resetTeams: true, resetEvents: true });
  }

  @Watch("competition")
  public onCompetitionSelectedChanged(newValue: ICompetitionInfo) {
    //  this.athlete = {} as IAthlete;
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_SELECTED_ATHLETE,
      {}
    );
    this.displayFlyer =
      this.competitionService.displayCompetitionFlyer(newValue);
    this.resetStore({ resetTeams: true, resetEvents: true });

    // this.hideShowsections(newValue);
  }

  public onSelectedClub(club: IClub) {
    this.compOrgId = club.clubid;
    this.club = club;
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_ENTRYFORM_SET_CLUB,
      club
    );
    this.$store.commit(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME +
        "/" +
        COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_MUTATIONS_SET_TEAM_HEADERS,
      []
    );
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_AUTO_SELECT_COMP_ORG,
      club.clubid
    );
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_AUTO_SELECT_COMP,
      0
    );
  }

  public onSelectedCompetition(competitionLookup: ICompetitionInfo) {
    this.competitionSelected = true;
    this.competition = competitionLookup;
    this.competitionEntryCloseUserString = competitionLookup.entriesClose
      ? "Entries close on " + this.entryCloseDate(competitionLookup)
      : "";

    this.$store.commit(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME +
        "/" +
        COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_MUTATIONS_SET_TEAM_HEADERS,
      []
    );
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_ENTRYFORM_SET_COMPETITION,
      competitionLookup
    );

    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_AUTO_SELECT_COMP,
      competitionLookup.id
    );
  }

  public get getEventTeamHeadersMessage() {
    return (
      " - " +
      (this.eventTeamHeaders ? this.eventTeamHeaders.length : "None") +
      " found."
    );
  }

  public get showTeamEvents() {
    return true;
    // return this.eventTeamHeaders && this.eventTeamHeaders.length > 0;
  }

  public onSelectedAthlete(athleteLookup: IAthleteSummary) {
    if (this.athlete.id !== athleteLookup.id) {
      this.$store.dispatch(
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
          "/" +
          ENTRY_STORE_CONST.ENTRY_STORE_ACTIONS_GET_SELECTED_ATHLETE,
        { id: athleteLookup.id }
      );
      this.$store.commit(
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
          "/" +
          ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_MUTATIONS_SWITCH_EVENT_CANCEL
      );
      this.$store.commit(
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
          "/" +
          ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_RESET_SECONDARY_CLAIM
      );
    }
  }

  public entryCloseDate(competitionLookup: ICompetitionInfo): string {
    return format(parse(competitionLookup.entriesClose), "Do MMM YYYY HH:mm");
  }

  public startDate(competitionLookup: ICompetitionInfo): string {
    if (
      competitionLookup &&
      competitionLookup.date &&
      competitionLookup.date.length > 0
    ) {
      return format(parse(competitionLookup.date), "Do MMM YYYY");
    }
    return "";
  }

  public resetStore(opts: { resetTeams: boolean; resetEvents: boolean }) {
    if (opts.resetEvents) {
      this.$store.commit(
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
          "/" +
          ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_EVENTS_SERVER_RESP,
        []
      );
      this.$store.commit(
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
          "/" +
          ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_MUTATIONS_PROCESS_EVENTS,
        []
      );
    }

    if (opts.resetTeams) {
      this.$store.commit(
        TEAM_STORE_CONST.TEAM_STORE_CONST_MODULE_NAME +
          "/" +
          TEAM_STORE_CONST.TEAM_STORE_MUTATIONS_SET_TEAMS,
        []
      );
      this.$store.commit(
        TEAM_STORE_CONST.TEAM_STORE_CONST_MODULE_NAME +
          "/" +
          TEAM_STORE_CONST.TEAM_STORE_MUTATIONS_SET_SELECTED_TEAM,
        {}
      );
    }
  }

  public get showCompDropDown() {
    return !(this.compOrgId.toString() === "0");
  }

  public openWooBasket() {
    window.location.href = CONFIG.WP_BASKET;
  }

  public hideGenericEntityPicker() {
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SHOW_ATHLETE_GENERIC_ENTITY_PICKER,
      false
    );
  }

  public saveGenericEntityPicker(payload: {
    athlete: IAthleteSummary;
    club: IBaseConcrete;
  }) {
    this.saveGenericEntity({ ...payload, isMobile: false });
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_ATHLETE_TRIGGER_REFRESH
    );
  }

  public saveGenericEntityPickerMobile(payload: {
    athlete: IAthleteSummary;
    club: IBaseConcrete;
  }) {
    this.saveGenericEntity({ ...payload, isMobile: true });
  }

  public saveGenericEntity(payload: {
    athlete: IAthleteSummary;
    club: IBaseConcrete;
    isMobile: boolean;
  }) {
    this.$store.dispatch(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SUBMIT_ATHLETE_GENERIC_ENTITY,
      payload
    );
    this.hideGenericEntityPicker();
  }
}
