<template>
    <div>
        <!--Top Section-->
        <div class="col s12 m12">
            <ul id="topCollapsible" class="collapsible">
                <li id="topCollapsibleItem" class="">
                    <div id="competitionContainer"
                         class="collapsible-header white-text competition-collapsible-header"
                         :class="'e4s-primary-color-bg-' + configApp.theme">

                        <div class="row e4s-collapsible-row">

                            <div class="col s10 m10 l10">
                                <span v-text="getCompName"></span>

                                <div class="e4s-force-inline-block comp-close-date">
                                    <span v-text="getEntryCloseUserString"></span>
                                </div>
                                <EntryBuilderLink v-if="hasBuilderPermissionForComp" :comp-id="competitionSummaryPublic.compId"></EntryBuilderLink>

                            </div>

                            <div class="col s10 m10 l3" v-if="false">
                                <div class="e4s-force-inline-block">
                                    <i class="material-icons e4s-force-inline-block">location_on</i>
                                </div>

                                <span v-text="competitionSummaryPublic.compName"></span>
                                <EntryBuilderLink v-if="hasBuilderPermissionForComp" :comp-id="competitionSummaryPublic.compId"></EntryBuilderLink>

                                <div>

                                    <div class="section hide-on-large-only">
                                        <i class="material-icons">location_on</i>

                                        <EntryBuilderLink v-if="hasBuilderPermissionForComp" :comp-id="competitionSummaryPublic.compId"></EntryBuilderLink>

                                        <div class="e4s-force-inline-block comp-close-date">
                                            <span v-text="getEntryCloseUserString"></span>
                                        </div>

                                    </div>
                                </div>

                            </div>

                            <div class="col s2 m2 l2 right-align">
                                <div class="right">
                                    <a v-if="!competitionInfoExpanded"
                                       v-on:click.prevent="openCompetitionInfo(true)"
                                       class="collapsible-toggle-custom e4s-expander"
                                       href="#">
                                        <i class="material-icons">add</i>
                                    </a>
                                    <a v-if="competitionInfoExpanded"
                                       v-on:click.prevent="openCompetitionInfo(false)"
                                       class="collapsible-toggle-custom e4s-expander"
                                       href="#">
                                        <i class="material-icons">remove</i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="competitionInfoExpanded">
                        <div class="competition-info">
                            <EntryInfo :competition="competitionSummaryPublic"></EntryInfo>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <!--/Top Section-->

        <!--Athlete Section-->
        <div class="col s12 m5">
            <ul id="leftCollapsible" class="collapsible athlete-team-section">
<!--                <li class="active" id="teamContainer" v-show="isSelectedCompetitionTeam">-->
<!--                    <div id="athleteHeader" class="collapsible-header white-text" :class="'e4s-primary-color-bg-' + configApp.theme">-->
<!--                        <div class="pull-left">-->
<!--                            <div>-->
<!--                                <i class="material-icons">group</i>&nbsp;Teams&nbsp;&nbsp;-->
<!--                                <span v-if="selectedTeam.teamName" v-html="selectedTeam.teamName"></span>-->
<!--                                <loading-spinner v-show="isTeamsLoading"></loading-spinner>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="pull-right">-->
<!--                            <div class="col s10 m10">-->
<!--                                <a-->
<!--                                    v-on:click="openTeamsModal()" class="btn waves-effect waves btn-secondary header-action scale-transition scale-out"-->
<!--                                    v-bind:class="{ 'scale-in': isSelectedCompetitionTeam, 'scale-out': !isSelectedCompetitionTeam }" href="#">-->
<!--                                    <i class="material-icons left">add</i>-->
<!--                                    Add-->
<!--                                </a>-->
<!--                            </div>-->
<!--                            <div class="col s2 m2">-->
<!--                                <a v-if="!teamsExpanded"-->
<!--                                   @click.prevent="openTeams()"-->
<!--                                   class="collapsible-toggle-custom e4s-expander" href="#">-->
<!--                                    <i class="material-icons">add</i>-->
<!--                                </a>-->
<!--                                <a v-if="teamsExpanded"-->
<!--                                   @click.prevent="closeTeams()"-->
<!--                                   class="collapsible-toggle-custom e4s-expander" href="#">-->
<!--                                    <i class="material-icons">remove</i>-->
<!--                                </a>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="collapsible-body team-body">-->
<!--                        <team-select v-bind:competition="competition"></team-select>-->
<!--                    </div>-->
<!--                </li>-->

                <li v-show="true">
                    <div class="collapsible-header white-text"
                         :class="'e4s-primary-color-bg-' + configApp.theme"
                         v-on:click.prevent="eventTeamExpanded ? openEventTeamPanel(false) : openEventTeamPanel(true)">
                        <div class="row e4s-collapsible-row">

                            <div class="col s10 m10 l10">
                                <div>
                                    <i class="material-icons">group</i>
                                    Team Events<span v-text="getEventTeamHeadersMessage"></span>
                                </div>
                            </div>

                            <div class="col s2 m2 l2 right-align">
                                <a v-if="!eventTeamExpanded"
                                   class="collapsible-toggle-custom e4s-expander"
                                   href="#">
                                    <i class="material-icons">add</i>
                                </a>
                                <a v-if="eventTeamExpanded"
                                   class="collapsible-toggle-custom e4s-expander"
                                   href="#">
                                    <i class="material-icons">remove</i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div v-show="eventTeamExpanded">
                        <EventTeamsPanel  class="e4s-section-wrapper"></EventTeamsPanel>
                    </div>
                </li>


                <li id="athleteContainer" v-show="!isHideAtheletes">
                    <div class="collapsible-header white-text"
                         :class="'e4s-primary-color-bg-' + configApp.theme"
                         v-on:click.prevent="athletesExpanded ? closeAthletes() : openAthletes()">
                        <div class="row e4s-collapsible-row">

                            <div class="col s10 m10 l10">
                                <div>
                                    <i class="material-icons">directions_run</i>
                                    Athletes
                                </div>
                            </div>

                            <div class="col s2 m2 l2 right-align">
                                <a v-if="!athletesExpanded"
                                   class="collapsible-toggle-custom e4s-expander"
                                   href="#">
                                    <i class="material-icons">add</i>
                                </a>
                                <a v-if="athletesExpanded"
                                   class="collapsible-toggle-custom e4s-expander"
                                   href="#">
                                    <i class="material-icons">remove</i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div v-show="athletesExpanded">
                        <AthleteGrid
                            class="e4s-section-wrapper"
                            :competition="competition"
                            :selectedTeam="selectedTeam"
                            :athletesVisible="athletesExpanded"
                            :triggerRefresh="triggerRefreshAthlete"
                            v-on:onSelected="onSelectedAthlete"
                        ></AthleteGrid>
                    </div>
                </li>
            </ul>
        </div>
        <!--/Athlete Section-->

    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {CompetitionService} from "../../competition/competiton-service"
import {ICompetitionSummaryPublic} from "../../competition/competition-models"
import {mapState} from "vuex"
import {CONFIG_STORE_CONST, IConfigStoreState} from "../../config/config-store"
import {IConfigApp} from "../../config/config-app-models"
import {ConfigService} from "../../config/config-service"
import EntryBuilderLink from "../entry-builder-link.vue"
import EntryInfo from "../entry-info.vue"
import {IEventTeamHeader} from "../../athletecompsched/comp-event-teams/event-teams-models"
import EventTeamsPanel from "../../athletecompsched/comp-event-teams/event-teams-panel.vue"
import AthleteGrid from "../../athlete/athlete-grid.vue"

const competitionService = new CompetitionService();
const configService: ConfigService = new ConfigService();

@Component({
    name: "entry-pc",
    components: {AthleteGrid, EventTeamsPanel, EntryInfo, EntryBuilderLink},
    computed: {
        ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
            configApp: (state: IConfigStoreState) => state.configApp
        })
    }
})
export default class EntryPc extends Vue {
    public readonly configApp!: IConfigApp;

    @Prop({
        required: true
    })
    public readonly competitionSummaryPublic!: ICompetitionSummaryPublic;

    public competitionService = competitionService;
    public configService = configService;

    // legacy
    public competitionInfoExpanded = false;
    public eventTeamExpanded: boolean = false;
    public athletesExpanded = false;
    public isHideAtheletes: boolean = false;
    public teamsExpanded = false;

    public eventTeamHeaders: IEventTeamHeader[];
    // /legacy

    public get getCompName() {
        return this.competitionService.getCompFullName(this.competitionSummaryPublic);
    }

    public get getEntryCloseUserString() {
        return this.competitionService.getClosingDate(this.competitionSummaryPublic);
    }

    public get hasBuilderPermissionForComp(): boolean {
        return configService.hasBuilderPermissionForComp(
            this.configApp.userInfo,
            this.competitionSummaryPublic.compOrgId,
            this.competitionSummaryPublic.compId
        );
    }



    // <legacy crap>
    public openCompetitionInfo(open: boolean) {
        this.competitionInfoExpanded = open;
    }

    public openEventTeamPanel(openPanel: boolean) {
        this.eventTeamExpanded = openPanel;
        this.athletesExpanded = !openPanel;
    }

    public get getEventTeamHeadersMessage() {
        return " - " + (this.eventTeamHeaders ? this.eventTeamHeaders.length : "None") + " found.";
    }

    public hideAthletes(hideIt: boolean) {
        this.isHideAtheletes = hideIt;
    }

    public openAthletes() {
        this.athletesExpanded = true;
        this.teamsExpanded = false;
        this.competitionInfoExpanded = false;
        this.eventTeamExpanded = false;
    }

    public closeAthletes() {
        this.athletesExpanded = false;
        this.teamsExpanded = false;
        this.competitionInfoExpanded = false;
        this.eventTeamExpanded = false;
    }

    // </>legacy crap>
}
</script>
