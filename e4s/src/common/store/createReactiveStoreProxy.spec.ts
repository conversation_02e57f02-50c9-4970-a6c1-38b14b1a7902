import {useCreateReactiveStoreProxy} from "./useCreateReactiveStoreProxy"

interface ITestData {
  name: string;
  age: number;
  _privateInfo: {
    ssn: string;
  };
}

describe("useCreateReactiveStore", () => {
  test("token", () => {
    const data: ITestData = {
      name: "<PERSON>",
      age: 25,
      _privateInfo: { ssn: "***********" },
    };
    const myStore = useCreateReactiveStoreProxy<ITestData>(data);

    myStore.addObserver((changes: unknown) => {
      console.log("Data changed:", changes);
    });

    myStore.data.age = 30; // Notifies the observer

    expect(myStore.data.age).toBe(30);
  });
});
