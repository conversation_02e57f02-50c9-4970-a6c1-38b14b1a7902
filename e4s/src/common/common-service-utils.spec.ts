import * as CommonServiceUtils from "./common-service-utils";
import { pluckUniqueData } from "./common-service-utils-mock";

describe("common", () => {
  test("isNumeric", () => {
    expect(CommonServiceUtils.isNumeric("1")).toBe(true);
    expect(CommonServiceUtils.isNumeric("x")).toBe(false);
    expect(CommonServiceUtils.isNumeric("1.01")).toBe(true);
    expect(CommonServiceUtils.isNumeric("1.01x")).toBe(false);
    expect(CommonServiceUtils.isNumeric("")).toBe(false);
  });

  test("isNumbersOnlySeparatorsAllowed", () => {
    expect(CommonServiceUtils.hasAtLeastOneCharacter("1")).toBe(false);
    expect(CommonServiceUtils.hasAtLeastOneCharacter("1x")).toBe(true);
    expect(CommonServiceUtils.hasAtLeastOneCharacter("1.2")).toBe(false);
    expect(CommonServiceUtils.hasAtLeastOneCharacter("1.2.12")).toBe(false);
    expect(CommonServiceUtils.hasAtLeastOneCharacter("1.2:12")).toBe(false);
  });

  test("pluckUnique", () => {
    expect(CommonServiceUtils.unique(pluckUniqueData).length).toBe(2);
  });

  test("simpleClone vs Spread", () => {
    const user = {
      name: "Bob",
      age: 34,
      address: {
        line1: "the street",
        line2: "some town",
      },
      stuff: [
        {
          name: "a",
        },
        {
          name: "b",
        },
      ],
    };

    const user2 = { ...user };
    expect(user.name).toBe(user2.name);

    user2.name = "Bobx";
    expect(user.name).toBe("Bob");
    expect(user2.name).toBe("Bobx");

    //  but since {...user} is hallow copy...
    user2.stuff[0] = {
      name: "c",
    };

    //  as expected.
    expect(user2.stuff[0].name).toBe("c");

    //  but this is not what you expect...becaus eit's shallow copy.
    expect(user.stuff[0].name).toBe("c");

    // ********************************
    // now lets repeat using simpleClone
    // ********************************
    const user3 = {
      name: "Bob",
      age: 34,
      address: {
        line1: "the street",
        line2: "some town",
      },
      stuff: [
        {
          name: "x",
        },
        {
          name: "y",
        },
      ],
    };

    const user4 = CommonServiceUtils.simpleClone(user3);
    expect(user3.name).toBe(user4.name);

    user4.name = "Bobx";
    expect(user3.name).toBe("Bob");
    expect(user4.name).toBe("Bobx");

    expect(user3.stuff[0].name).toBe("x");
    expect(user4.stuff[0].name).toBe("x");

    //  but since {...user} is hallow copy...
    user4.stuff[0] = {
      name: "e",
    };

    //  as expected.
    expect(user4.stuff[0].name).toBe("e");

    //  but this is not what you expect...becaus eit's shallow copy.
    expect(user3.stuff[0].name).toBe("x");
  });

  test("isValidHttpUrl", () => {
    expect(CommonServiceUtils.isValidHttpUrl("")).toBe(false);
    expect(CommonServiceUtils.isValidHttpUrl("/some-image.png")).toBe(false);
    expect(CommonServiceUtils.isValidHttpUrl("/blah/some-image.png")).toBe(
      false
    );

    //  This one is a bit weird...but it's valid.
    expect(CommonServiceUtils.isValidHttpUrl("http:/blah/some-image.png")).toBe(
      true
    );

    expect(
      CommonServiceUtils.isValidHttpUrl("https://www.blah.com/some-image.png")
    ).toBe(true);
  });

  test("isNil", () => {
    expect(CommonServiceUtils.isNil("")).toBe(true);
    expect(CommonServiceUtils.isNil(undefined)).toBe(true);
    expect(CommonServiceUtils.isNil(null)).toBe(true);
    expect(CommonServiceUtils.isNil([])).toBe(true);

    expect(CommonServiceUtils.isNil(["1"])).toBe(false);
    expect(CommonServiceUtils.isNil("1")).toBe(false);
    expect(CommonServiceUtils.isNil(1)).toBe(false);
  });

  test("getFractionalPartAsWholeNumber", () => {
    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12)).toBe(0);
    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.0)).toBe(0);

    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.2)).toBe(20);
    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.02)).toBe(2);
    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.002)).toBe(0);

    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.8)).toBe(80);
    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.08)).toBe(8);
    //  Rounds up.
    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.008)).toBe(1);

    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.4)).toBe(40);
    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.04)).toBe(4);
    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.004)).toBe(0);

    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.5)).toBe(50);
    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.05)).toBe(5);
    //  Rounds up.
    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.005)).toBe(1);

    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.005, 3)).toBe(
      5
    );

    expect(CommonServiceUtils.getFractionalPartAsWholeNumber(12.003, 1)).toBe(
      0
    );
  });

  test("convertSecondsToHMS", () => {
    let res;
    res = CommonServiceUtils.convertSecondsToHMS(0);
    expect(res.hours).toBe(0);
    expect(res.minutes).toBe(0);
    expect(res.seconds).toBe(0);
    expect(res.hundredths).toBe(0);

    res = CommonServiceUtils.convertSecondsToHMS(12.2);
    expect(res.hours).toBe(0);
    expect(res.minutes).toBe(0);
    expect(res.seconds).toBe(12);
    expect(res.hundredths).toBe(20);

    res = CommonServiceUtils.convertSecondsToHMS(12.02);
    expect(res.hours).toBe(0);
    expect(res.minutes).toBe(0);
    expect(res.seconds).toBe(12);
    expect(res.hundredths).toBe(2);

    res = CommonServiceUtils.convertSecondsToHMS(66.02);
    expect(res.hours).toBe(0);
    expect(res.minutes).toBe(1);
    expect(res.seconds).toBe(6);
    expect(res.hundredths).toBe(2);
  });

  /**
   * write a load of tests for convertedSecondsToHMSToSeconds()
   */
  test("convertSecondsToHMSToSeconds", () => {
    let res;

    res = CommonServiceUtils.convertedSecondsToHMSToSeconds({
      hours: 0,
      minutes: 0,
      seconds: 0,
      hundredths: 0,
    });
    expect(res).toBe(0);

    res = CommonServiceUtils.convertedSecondsToHMSToSeconds({
      hours: 0,
      minutes: 0,
      seconds: 12,
      hundredths: 20,
    });
    expect(res).toBe(12.2);

    res = CommonServiceUtils.convertedSecondsToHMSToSeconds({
      hours: 0,
      minutes: 0,
      seconds: 12,
      hundredths: 2,
    });
    expect(res).toBe(12.02);

    res = CommonServiceUtils.convertedSecondsToHMSToSeconds({
      hours: 0,
      minutes: 1,
      seconds: 6,
      hundredths: 2,
    });

    expect(res).toBe(66.02);

    //  1:01.06.02
    res = CommonServiceUtils.convertedSecondsToHMSToSeconds({
      hours: 1,
      minutes: 1,
      seconds: 6,
      hundredths: 2,
    });

    expect(res).toBe(3666.02);
  });

  test("getNumberWithLeadingZeros", () => {
    expect(CommonServiceUtils.getNumberWithLeadingZeros(1, 1)).toBe("1");

    expect(CommonServiceUtils.getNumberWithLeadingZeros(1, 2)).toBe("01");

    expect(CommonServiceUtils.getNumberWithLeadingZeros(1, 3)).toBe("001");
    expect(CommonServiceUtils.getNumberWithLeadingZeros(12, 3)).toBe("012");
    expect(CommonServiceUtils.getNumberWithLeadingZeros(123, 3)).toBe("123");
  });

  test("convertObjectToUrlParams", () => {
    expect(
      CommonServiceUtils.convertObjectToUrlParams({
        name: "Bob Smith",
        age: 33,
        height: "6'2",
      })
    ).toBe("name=Bob%20Smith&age=33&height=6'2");

    expect(
      CommonServiceUtils.convertObjectToUrlParams({
        process: false,
        startClip: 0.5,
        duration: 0.5,
        reverse: false,
        output: "E4S_VIDEO_MP4",
        speed: 6.0,
        text: "",
        text2: "",
      })
    ).toBe(
      "process=false&startClip=0.5&duration=0.5&reverse=false&output=E4S_VIDEO_MP4&speed=6&text=&text2="
    );
  });

  test("eventTimeDisplay", () => {
    //  Well this is rubbish...date at time of writing test is: 2021-03-03.
    //  If you format(parse("2024-03-31T00:00:00+01:00")) 31st Mar IS in DST...
    //  ...but date-fns is not taking that into account and using time as of now!!!!
    expect(CommonServiceUtils.eventTimeDisplay("2024-03-31T00:00:00")).toBe(
      "TBC"
    );
    expect(
      CommonServiceUtils.eventTimeDisplay("2024-03-31T00:00:00+01:00")
    ).toBe("TBC");
  });

  test("eventDateDisplay", () => {
    expect(CommonServiceUtils.eventDateDisplay("2024-03-31T00:00:00")).toBe(
      "31st Mar 2024"
    );
    expect(
      CommonServiceUtils.eventDateDisplay("2024-03-31T00:00:00+01:00")
    ).toBe("30th Mar 2024");

    expect(
      CommonServiceUtils.eventDateDisplay(
        "2024-03-31T00:00:00+01:00",
        new Date(2023, 2, 3)
      )
    ).toBe("30th Mar 2024");
  });

  test("getAgeInYearsMonthsDays", () => {
    expect(
      CommonServiceUtils.getAgeInYearsMonthsDays(
        "1970-03-28T00:00:00",
        "2024-03-28T00:00:00"
      )
    ).toBe("54 years, 0 months and 1 days");

    expect(
      CommonServiceUtils.getAgeInYearsMonthsDays(
        "2008-10-17T00:00:00",
        "2024-05-11T00:00:00"
      )
    ).toBe("15 years, 6 months and 25 days");
  });

  test("eventDateDisplayCard", () => {
    expect(
      CommonServiceUtils.eventDateDisplayCard(
        "2024-05-11T09:00:00",
        new Date(2024, 4, 11)
      )
    ).toBe("Sat 11th May");
  });

  test("extractNumericChars", () => {
    expect(CommonServiceUtils.extractNumericChars("2024-05-11T09:00:00")).toBe(
      "20240511090000"
    );

    expect(CommonServiceUtils.extractNumericChars("Under 11")).toBe("11");
    expect(CommonServiceUtils.extractNumericChars("U11 G")).toBe("11");
  });

  test("intersection", () => {
    let res;

    res = CommonServiceUtils.intersection([], []);
    expect(res.length).toBe(0);

    res = CommonServiceUtils.intersection(["a"], ["b"]);
    expect(res.length).toBe(0);

    res = CommonServiceUtils.intersection(["a"], ["b", "a"]);
    expect(res.length).toBe(1);

    res = CommonServiceUtils.intersection(["a"], ["b", "a", "a"]);
    expect(res.length).toBe(1);

    res = CommonServiceUtils.intersection(["a", "a"], ["b", "a", "a"]);
    expect(res.length).toBe(1);

    res = CommonServiceUtils.intersection(["a", "a"], ["b", "a", "a", "c"]);
    expect(res.length).toBe(1);

    res = CommonServiceUtils.intersection(["a", "", "a"], ["b", "a", "a", "c"]);
    expect(res.length).toBe(1);

    res = CommonServiceUtils.intersection(
      ["a", "c", "a"],
      ["b", "a", "a", "c"]
    );
    expect(res.length).toBe(2);
  });

  /*
  test("eventDateDisplayCard", () => {
    // const xml = `
    // <root>
    //   <name>John</name>
    //   <age>25</age>
    //   <city>New York</city>
    // </root>`;
    const xml =
      "<root><tag>tag content</tag><tag2>another content</tag2></root>";
    // const res = CommonServiceUtils.convertXmlToJson(xml);
    const res = CommonServiceUtils.xmlToJson(xml);

    expect(res).toBe("John");
  });
  */

  test("merge", () => {
    let res = CommonServiceUtils.mergeObjects({ name: "bob" }, { name: "jim" });

    expect(res.name).toBe("jim");

    res = CommonServiceUtils.mergeObjects(
      { name: "bob", age: 25 },
      { name: "jim" }
    );

    expect(res.name).toBe("jim");
    expect((res as any).age).toBe(25);

    res = CommonServiceUtils.mergeObjects(
      { name: "bob", age: 25 },
      {
        name: "jim",
        age: 30,
        address: { line1: "The house", line2: "The Street" },
      }
    );

    expect((res as any).age).toBe(30);
    expect((res as any).address.line1).toBe("The house");
    expect((res as any).address.line1).toBe("The house");
  });

  test("validateUKPhoneNumber", () => {
    // Valid UK phone numbers
    expect(CommonServiceUtils.validateUKPhoneNumber("+447911123456")).toBe(
      true
    );
    expect(CommonServiceUtils.validateUKPhoneNumber("07911123456")).toBe(true);
    expect(CommonServiceUtils.validateUKPhoneNumber("+441234567890")).toBe(
      true
    );
    expect(CommonServiceUtils.validateUKPhoneNumber("01234567890")).toBe(true);

    // Invalid UK phone numbers
    expect(CommonServiceUtils.validateUKPhoneNumber("123456")).toBe(false);
    expect(CommonServiceUtils.validateUKPhoneNumber("+44791112345")).toBe(
      false
    );
    expect(CommonServiceUtils.validateUKPhoneNumber("0791112345")).toBe(false);
    expect(CommonServiceUtils.validateUKPhoneNumber("+44123456789")).toBe(
      false
    );
    expect(CommonServiceUtils.validateUKPhoneNumber("0123456789")).toBe(false);
    expect(CommonServiceUtils.validateUKPhoneNumber("not a phone number")).toBe(
      false
    );
  });

  test("validateUKPhoneNumber", () => {
    // Valid UK phone numbers
    expect(CommonServiceUtils.stripHtmlTags("a test")).toBe("a test");
    expect(CommonServiceUtils.stripHtmlTags("<p>a test</p>")).toBe("a test");
    expect(
      CommonServiceUtils.stripHtmlTags("<p>a test</p> <span>ddd</span>")
    ).toBe("a test ddd");
    expect(
      CommonServiceUtils.stripHtmlTags("<p>a test <span>blah</span>")
    ).toBe("a test blah");
  });

  test("isArray", () => {
    expect(CommonServiceUtils.isArray([])).toBe(true);
    expect(CommonServiceUtils.isArray({})).toBe(false);
    expect(CommonServiceUtils.isArray("")).toBe(false);
    expect(CommonServiceUtils.isArray(1)).toBe(false);
    expect(CommonServiceUtils.isArray(true)).toBe(false);
    expect(CommonServiceUtils.isArray(null)).toBe(false);
    expect(CommonServiceUtils.isArray(undefined)).toBe(false);
  });

  test("socials validation", () => {
    // test TikTok handle validation
    expect(CommonServiceUtils.validateTikTokHandle("")).toBe(false);
    expect(CommonServiceUtils.validateTikTokHandle("123")).toBe(false);
    expect(CommonServiceUtils.validateTikTokHandle("1234567890123456")).toBe(
      false
    );
    expect(CommonServiceUtils.validateTikTokHandle("@mrg.oat1234")).toBe(false);

    // some true ones
    expect(CommonServiceUtils.validateTikTokHandle("@mrgoat")).toBe(true);
    expect(CommonServiceUtils.validateTikTokHandle("@mrgoat1")).toBe(true);
    expect(CommonServiceUtils.validateTikTokHandle("@mrgoat123")).toBe(true);
  });
});
