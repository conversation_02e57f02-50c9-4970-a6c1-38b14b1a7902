<template>
    <div>
        <div v-for="option in classifications" :key="option.id">
            <class-ification  class="e4s-card" :classification="option" @onSelect="onSelect"></class-ification>
            <div class="e4s-card-standard-sep"></div>
        </div>
    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {Watch} from "vue-property-decorator";
    import {IClassification} from "../../../config/config-app-models";
    import {IConfigStoreState, CONFIG_STORE_CONST} from "../../../config/config-store";
    import {mapState} from "vuex";
    import Classification from "./classification.vue";


    @Component({
        name: "classification-select",
        computed: {
            ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
                classificationsState: (state: IConfigStoreState) => state.configApp.classifications
            })
        },
        components: {
            "class-ification": Classification
        }
    })
    export default class ClassificationSelect extends Vue {
        public classificationsState: IClassification[];
        public classifications: IClassification[] = [];
        public classification: IClassification = {
            id: 0,
            class: 0,
            description: "",
            title: ""
        };

        public created() {
            this.init(this.classificationsState);
        }

        @Watch("classificationsState")
        public onClassificationsStateChanged(newValue: IClassification[]) {
            // this.classifications = newValue.sort((a: IClassification, b: IClassification): number => {
            //     return a.class - b.class;
            // });
            this.init(newValue);
        }

        public init(classifications: IClassification[]) {
            classifications = R.clone(classifications);

            classifications = classifications.sort((a: IClassification, b: IClassification): number => {
                return a.class - b.class;
            });

            this.classifications = classifications;
        }

        public getLabel(classification: IClassification): string {
            return classification.class + " " + (classification.title.length > 10 ? classification.title.slice(0, 20) + "..." : classification.title);
            // return classification.title;
        }

        public onSelect(classification: IClassification) {
            this.classification = classification;
            this.onChange();
        }

        public onChange() {
            this.$emit("onChange", R.clone(this.classification));
        }
    }
</script>
