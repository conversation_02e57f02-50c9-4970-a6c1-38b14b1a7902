import {FormController} from "./form-controller";


describe("form-controller", () => {
    test("processChanges", () => {

        const sourceObject = {
            name: "bob",
            age: 34
        };
        const changedObject = {
            name: "bob",
            age: 34
        };
        const formController: FormController = new FormController(sourceObject, changedObject);

        //  Objects the same, no diffs.
        formController.processChanges();
        let res = formController.isFieldDirty("age");
        expect(res).toBe(false);
        expect(formController.isDirty).toBe(false);

        changedObject.age = 35;
        formController.processChanges();
        res = formController.isFieldDirty("age");
        expect(res).toBe(true);
        expect(formController.isDirty).toBe(true);

        changedObject.age = 34;
        formController.processChanges();
        res = formController.isFieldDirty("age");
        expect(res).toBe(false);
    });

    test("forcePropDiff", () => {

        const sourceObject = {
            name: "bob",
            age: 34
        };
        let changedObject;
        changedObject = {
            name: "bob",
            age: 34
        };
        const formController: FormController = new FormController(sourceObject, changedObject);

        formController.processChanges();
        expect(formController.isDirty).toBe(false);

        formController.forcePropDiff("name");

        let res;
        res = formController.isFieldDirty("name");
        expect(res).toBe(true);
    });

    test("resetField", () => {

        const sourceObject = {
            name: "bob",
            age: 34
        };
        let changedObject;
        changedObject = {
            name: "bob",
            age: 34
        };
        const formController: FormController = new FormController(sourceObject, changedObject);

        let res;

        changedObject.age = 35;
        formController.processChanges();
        res = formController.isFieldDirty("age");
        expect(res).toBe(true);
        expect(formController.isDirty).toBe(true);

        let editObject;
        editObject = formController.resetField( "age");
        // expect(editObject).toBe(false);
        res = formController.isFieldDirty("age");
        expect(res).toBe(false);
        expect(editObject.age).toBe(34);

        changedObject = {
            name: "bob",
            age: 34
        };
        formController.forcePropDiff( "name");
        res = formController.isFieldDirty("name");
        expect(res).toBe(true);

        let objForce;
        objForce = formController.getDirtyForceFields;
        expect(objForce.name).toBe("bob");

        editObject = formController.resetField( "name");
        res = formController.isFieldDirty("name");
        expect(formController.isDirty).toBe(false);

        objForce = formController.getDirtyForceFields;
        expect(objForce.name).toBe(undefined);
    });

    test("getDirtyFields", () => {

        const sourceObject = {
            name: "bob",
            age: 34
        };
        let changedObject;
        changedObject = {
            name: "bob",
            age: 33,
            family: {
                son: {
                    name: "Alex",
                    age: 12
                }
            }
        };
        const formController: FormController = new FormController(sourceObject, changedObject);

        formController.processChanges();
        expect(formController.isDirty).toBe(true);

        expect(formController.getDirtyFields.age).toBe(33);
        expect(formController.getDirtyFields.family.son.age).toBe(12);
    });
});
