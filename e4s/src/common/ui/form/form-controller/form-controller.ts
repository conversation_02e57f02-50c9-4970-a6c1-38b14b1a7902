import * as R from "ramda";
import {CommonService} from "../../../common-service";
import {debounce} from "../../../debounce";

const commonService: CommonService = new CommonService();

export class FormController {
    public liveObject: any;
    public debounceProcessChanges: any;
    public objDiffs: any = {};

    private objectDiffsForce: any = {};
    private sourceObject: any = {};

    constructor(sourceObject: any, liveObject: any) {
        this.setSources(sourceObject, liveObject);

        this.debounceProcessChanges =  debounce(() => {
            this.processChanges();
        }, 150);
    }

    /**
     *
     * @param sourceObject      The original Object
     * @param liveObject        The object being "edited"
     */
    public setSources(sourceObject: any, liveObject: any) {
        // this.sourceObject = Object.assign({}, sourceObject);
        this.sourceObject = R.clone(sourceObject);
        //  N.B.  do  NOT clone this object.
        this.liveObject = liveObject;
        // this.objDiffs = {};
        // this.objectDiffsForce = {};
        this.processChanges();
    }

    public setSourcesAnProcessDebounce(sourceObject: any, liveObject: any) {
        this.sourceObject = R.clone(sourceObject);
        this.liveObject = liveObject;
        this.debounceProcessChanges();
    }

    public processChangesDebounce() {
        this.debounceProcessChanges();
    }

    public processChanges() {
        let objectDiffs = commonService.differenceBetweenTwoObjects(this.sourceObject, this.liveObject);
        if (Object.keys(objectDiffs).length === 0 ) {
            objectDiffs = {};
        }
        objectDiffs = R.mergeDeepRight(objectDiffs, this.objectDiffsForce);
        this.objDiffs = objectDiffs;

        if (this.isDirty) {
            this.runThisProcessWhenObjectsAreDifferent();
        }
    }

    public runThisProcessWhenObjectsAreDifferent(): any {
        console.log("FormController.runThisProcessWhenObjectsAreDifferent() to use this, set this function");
    }

    public isFieldDirty(propPath: string): boolean {
        return commonService.objectHasPropPath(this.objDiffs, propPath);
    }

    public get isDirty(): boolean {
        return Object.keys(this.objDiffs).length > 0;
    }

    public forcePropDiff(propPath: string): void {
    // public forcePropDiff(objectBeingEdited: any, propPath: string): void {
        if (!commonService.objectHasPropPath(this.liveObject, propPath)) {
            return;
        }
        const valueAtPath = commonService.valueAtPath(this.liveObject, propPath);
        const objectDiffsForce = commonService.createObjectPath(this.objectDiffsForce, propPath, valueAtPath);
        this.objectDiffsForce = objectDiffsForce;
        this.processChanges();
    }

    public resetField(propPath: string): any {
        if (!commonService.objectHasPropPath(this.sourceObject, propPath)) {
            return;
        }
        const sourcePropPathValue = commonService.valueAtPath(this.sourceObject, propPath);
        const objectBeingEditedClone = commonService.createObjectPath(this.liveObject, propPath, sourcePropPathValue);
        // this.liveObject = Object.assign({}, objectBeingEditedClone);
        this.liveObject = R.clone(objectBeingEditedClone);
        this.objectDiffsForce = commonService.deleteAtPath(this.objectDiffsForce, propPath);
        this.processChanges();
        return this.liveObject;
    }

    public get getDirtyFields(): any {
        return this.objDiffs;
    }

    public get getDirtyForceFields(): any {
        return this.objectDiffsForce;
    }

    public getObjectDiffs(): any {
        return this.objDiffs;
    }

    public getSourceObject(): any {
        return R.clone(this.sourceObject);
    }

    public getLiveObject(): any {
        return R.clone(this.liveObject);
    }
}
