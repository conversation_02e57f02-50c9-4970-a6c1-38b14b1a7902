<template>
  <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <path fill="#5c5f62" d="M8,0v2.4c3.09,0,5.6,2.51,5.6,5.6H16C16,3.58,12.42,0,8,0z"/>
  </svg>
</template>

<script lang="ts">
import { defineComponent } from "@vue/composition-api";

export default defineComponent({
  name: "loading-spinner-minor",
  props: {
    height: {
      type: String,
      default: () => {
        return "20px";
      }
    }
  }
});
</script>
