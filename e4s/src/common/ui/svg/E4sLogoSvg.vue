<template>
  <svg id="E4S_Logo-x" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 180 80" :style="'height:' + height +';'">
    <g id="LogoGroup-x">
      <g id="EntryGroup-x" fill="#1f427d">
        <path
          id="E"
          d="M36.52,37.4h-14.69c-2.27,0-4.25-.52-5.95-1.55-1.84-1.12-2.88-2.9-3.14-5.34-.25-2.44,.19-5.13,1.31-8.06,1.12-2.9,2.7-5.53,4.76-7.88,2.25-2.56,4.72-4.42,7.41-5.6,1.48-.63,2.83-1.08,4.07-1.34,1.23-.26,2.58-.39,4.05-.39h13.62l-2.06,5.34h-12.58c-1.12,0-2.12,.15-3,.45-.88,.3-1.84,.84-2.88,1.62-1.05,.78-1.85,1.53-2.42,2.26-.57,.73-1.06,1.64-1.48,2.74h19.82l-2.06,5.34H21.47c-.38,.98-.58,1.83-.6,2.56-.02,.73,.16,1.49,.54,2.26,.38,.78,.86,1.36,1.44,1.77,.59,.4,1.38,.6,2.39,.6h13.28l-2.01,5.21Z"
        />
        <path
          id="n"
          d="M59.7,37.4h-5.99l5.66-14.69c.14-.37,.19-.71,.13-1.01-.06-.3-.19-.6-.39-.88-.39-.49-.95-.73-1.7-.73h-5.86l-6.67,17.32h-5.99l8.59-22.32h11.46c2.04,0,3.47,.12,4.3,.35,1.3,.34,2.16,1.11,2.57,2.28,.45,1.38,.35,2.89-.28,4.52l-5.84,15.17Z"
        />
        <path
          id="t"
          d="M84.16,19.74h-5.82l-3.9,10.12c-.35,.92-.33,1.68,.07,2.28,.49,.6,1.32,.9,2.5,.9h1.77l-1.68,4.35h-5.56c-1.67,0-2.86-.6-3.57-1.81-.41-.72-.55-1.51-.42-2.37,.13-.86,.42-1.88,.88-3.06l4.01-10.43h-3.79l1.79-4.65h3.79l3.02-7.84h5.9l-3.02,7.84h5.82l-1.79,4.65Z"
        />
        <path
          id="r"
          d="M99.36,19.91h-6.68l-6.74,17.49h-5.95l8.59-22.32h5.95l-1.19,3.1c.61-.78,1.25-1.41,1.9-1.9,.65-.49,1.4-.86,2.26-1.12,.39-.12,.79-.22,1.22-.3,.42-.09,.83-.13,1.24-.13,.49,0,.93,.06,1.32,.17l-1.92,5Z"
        />
        <path
          id="y"
          d="M124.95,15.09l-9,23.27c-2.74,7.08-6.37,6.98-11.82,6.98H40.48l2.12-5.15h59.81c.98-.04,6.55,.45,7.44-1.87l.33-.86h-6.89c-1.32,0-2.38-.15-3.19-.45-.8-.3-1.47-.88-2-1.74-.95-1.49-1-3.35-.14-5.56l5.62-14.61h6.29l-5.55,14.3c-.22,.57-.35,1.01-.39,1.29-.04,.29,.04,.6,.24,.95,.2,.34,.49,.58,.87,.71,.39,.13,.89,.19,1.53,.19h5.5l6.72-17.45h6.16Z"
        />
      </g>
      <g id="_4Group">
        <g id="Calendar" fill="#f7b008">
          <path
            fill-rule="evenodd"
            d="M153.92,2.45h11.13l-.92,2.38h-11.13l.92-2.38Zm-13.88,0h8.24l-.92,2.38h-5.86l-14.77,38.36h34.12l14.77-38.36h-5.86l.92-2.38h8.24l-16.6,43.12h-38.88L140.04,2.45Z"
          />
          <path
            d="M154,29.04l5.16-13.4-18.09,13.4h12.93Zm9.17,5.17h-4.27l-2.02,5.26h-6.89l2.02-5.26h-20.68l2.37-6.16,25.76-18.74h9.03l-7.53,19.56h4.27l-2.06,5.34Z"
          />
        </g>
        <path
          id="Holder"
          fill-rule="evenodd"
          fill="#1f427d"
          d="M168.8,0h0c.86,0,1.3,.71,.97,1.57l-1.59,4.13c-.33,.87-1.31,1.57-2.18,1.57h0c-.86,0-1.3-.71-.97-1.57l1.59-4.13c.33-.87,1.31-1.57,2.18-1.57m-16.76,0h0c.87,0,1.3,.71,.97,1.57l-1.59,4.13c-.33,.87-1.31,1.57-2.18,1.57h0c-.86,0-1.3-.71-.97-1.57l1.59-4.13c.33-.87,1.31-1.57,2.18-1.57Z"
        />
      </g>
      <g id="SportsGroup" fill="#ba0000">
        <path
          id="S"
          d="M34.25,60.99c-1.02,2.64-2.75,4.87-5.2,6.68-2.45,1.81-5.02,2.71-7.72,2.71H0l2.09-5.43H19.67c1.18,0,2.27-.27,3.29-.83,1.16-.63,1.96-1.5,2.38-2.6,.99-2.57-.32-3.86-3.94-3.86H12.52c-2.56,0-4.49-.79-5.81-2.37-1.32-1.58-1.5-3.59-.56-6.03,1.04-2.7,2.82-4.89,5.35-6.57,2.53-1.68,5.33-2.52,8.4-2.52h20.38l-1.99,5.17H20.32c-.8,0-1.75,.34-2.82,1.01-1.08,.67-1.79,1.47-2.15,2.39-.4,1.03-.34,1.9,.18,2.61,.52,.7,1.34,1.06,2.46,1.06h9.78c3.04,0,5.13,.78,6.27,2.35,1.13,1.57,1.2,3.64,.21,6.23h0Z"
        />
        <path
          id="p"
          d="M54.92,59.01c.72-1.87,.71-3.4-.02-4.59-.73-1.19-1.99-1.79-3.77-1.79h-4.22l-5.01,13.01h4.65c1.72,0,3.36-.61,4.91-1.83,1.55-1.22,2.7-2.82,3.47-4.8m6.25-.35c-1.37,3.56-3.39,6.4-6.06,8.51-2.67,2.11-5.51,3.17-8.52,3.17h-6.51l-3.14,8.14h-6.03l11.73-30.46h11.98c3.39,0,5.64,1.01,6.77,3.02,1.07,1.92,.99,4.47-.22,7.63Z"
        />
        <path
          id="o"
          d="M82.73,59.05c.7-1.81,.71-3.32,.05-4.55-.66-1.22-1.97-1.83-3.93-1.83-1.78,0-3.49,.62-5.14,1.87-1.64,1.25-2.83,2.82-3.56,4.72-.84,2.18-.92,3.92-.24,5.21,.68,1.29,1.95,1.94,3.82,1.94,2.13,0,3.96-.67,5.52-2.02,1.42-1.24,2.58-3.02,3.48-5.34m6.66,.04c-1.49,3.88-3.87,6.85-7.14,8.92-3,1.9-6.45,2.84-10.36,2.84s-6.61-.86-8.1-2.58c-1.74-1.98-1.8-5.07-.18-9.26,1.35-3.5,3.76-6.26,7.24-8.27,3.11-1.81,6.43-2.71,9.96-2.71,3.88,0,6.61,.97,8.21,2.91,1.59,1.94,1.72,4.66,.37,8.16Z"
        />
        <path
          id="r-2"
          d="M107.87,52.85h-6.68l-6.74,17.49h-5.95l8.59-22.32h5.95l-1.19,3.1c.61-.78,1.25-1.41,1.9-1.9,.65-.49,1.4-.86,2.26-1.12,.39-.12,.79-.22,1.22-.3,.42-.09,.83-.13,1.24-.13,.49,0,.93,.06,1.32,.17l-1.92,5Z"
        />
        <path
          id="t-2"
          d="M124.35,52.68h-5.82l-3.9,10.12c-.35,.92-.33,1.68,.07,2.28,.49,.6,1.32,.9,2.5,.9h1.77l-1.68,4.35h-5.56c-1.67,0-2.86-.6-3.57-1.81-.41-.72-.55-1.51-.42-2.37,.13-.86,.42-1.88,.88-3.06l4.01-10.43h-3.79l1.79-4.65h3.79l3.02-7.84h5.9l-3.02,7.84h5.82l-1.79,4.65Z"
        />
        <path
          id="s"
          d="M144.09,63.53c-.73,1.9-1.97,3.52-3.71,4.87-1.74,1.35-3.54,2.02-5.41,2.02h-14.78l1.56-4.05h12.15c.86,0,1.67-.22,2.42-.65,.75-.43,1.29-1.05,1.6-1.85,.72-1.87-.19-2.8-2.71-2.8h-6.2c-1.32,0-2.36-.63-3.11-1.9-.75-1.26-.84-2.63-.28-4.09,.8-2.07,1.9-3.73,3.3-5,1.62-1.44,3.46-2.15,5.53-2.15h14.22l-1.63,4.22h-12.67c-.49,0-1,.24-1.54,.71-.54,.47-.93,1.01-1.16,1.62-.25,.66-.24,1.23,.06,1.7,.29,.47,.77,.71,1.43,.71h6.68c2.1,0,3.52,.62,4.25,1.87,.74,1.25,.74,2.84,0,4.76"
        />
      </g>
    </g>
  </svg>
</template>

<script lang="ts">
import { defineComponent } from "@vue/composition-api";

export default defineComponent({
  name: "E4s-Logo-svg",
  props: {
    height: {
      type: String,
      default: () => {
        return "50px";
      },
    },
  },
});
</script>
