<template>
    <ul class="collapsible">
        <li class="active">
            <div :class="getHeaderCss"
                 v-on:click.prevent="onSectionClicked">
                <div class="row e4s-collapsible-row">

                    <div class="col s10 m10 l10">
                        <slot name="section-header">
                            <div>
                                <i class="material-icons e4s-collapse-section--icon" v-text="iconName"></i>
                                <span v-text="headerMessage"></span>
                            </div>
                        </slot>
                    </div>

                    <div class="col s2 m2 l2 right-align">
                        <slot name="section-header-right">
                            <a v-if="!isExpandedInternal"
                               class="collapsible-toggle-custom e4s-expander"
                               href="#">
                                <i class="material-icons e4s-collapse-section--icon">add</i>
                            </a>
                            <a v-if="isExpandedInternal"
                               class="collapsible-toggle-custom e4s-expander"
                               href="#">
                                <i class="material-icons e4s-collapse-section--icon">remove</i>
                            </a>
                        </slot>
                    </div>

                </div>
            </div>

            <div v-show="isExpandedInternal" class="e4s-collapsible-body">
                <slot name="section-content">
                    * * * This is where you place your section content. * * *
                </slot>
            </div>
        </li>
    </ul>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {mapState} from "vuex";
    import {CONFIG_STORE_CONST, IConfigStoreState} from "../../../config/config-store";
    import {IConfigApp} from "../../../config/config-app-models"

    @Component({
        name: "collapse-section",
        computed: {
            ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
                configApp: (state: IConfigStoreState) => state.configApp
            })
        }
    })
    export default class CollapseSection extends Vue {
        configApp: IConfigApp;

        @Prop({
            default: false
        }) public readonly isExpanded: boolean;
        @Prop({default: ""}) public readonly iconName: string;
        @Prop({default: ""}) public readonly headerMessage: string;
        @Prop({default: true}) public readonly allowExpandCollapse: boolean;
        @Prop({default: ""}) public readonly headerCss: string;


        public isExpandedInternal: boolean = false;

        public created() {
            this.isExpandedInternal = this.isExpanded;
        }

        @Watch("isExpanded")
        public isExpandedChanged(newValue: boolean) {
            this.isExpandedInternal = newValue;
        }

        public get getHeaderCss() {
        // class="collapsible-header white-text"
        // :class="'e4s-primary-color-bg-' + configApp.theme"
            const standard = "collapsible-header white-text e4s-primary-color-bg-" + this.configApp.theme;
            const cssPassedIn = this.headerCss && this.headerCss.length > 0;
            return cssPassedIn ? this.headerCss : standard;
        }

        public onSectionClicked() {
            if (this.allowExpandCollapse) {
                this.isExpandedInternal = !this.isExpandedInternal;
            }
            this.isExpandedInternal ? this.$emit("onExpanded") :  this.$emit("onCollapsed")
            this.$emit("isSectionBodyVisible", this.isExpandedInternal);
        }
    }
</script>

<style scoped>
    .e4s-collapsible-body {
        padding: 1rem;
    }
    .e4s-collapsible-row {
      height: 30px;
    }
    .e4s-collapse-section--icon {
      height: 15px;
      line-height: 15px;
      vertical-align: middle;
    }
</style>
