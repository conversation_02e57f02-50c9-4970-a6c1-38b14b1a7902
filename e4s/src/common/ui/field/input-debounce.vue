<template>
  <!--  <input v-model="selectedValue" v-on:keyup="debounceOnChange" :placeholder="placeholder"/>-->
  <input
    type="text"
    v-model="selectedValue"
    class="browser-default e4s-input-field e4s-input-field--primary"
    :class="isDisabled ? 'e4s-input-field--disabled' : ''"
    :disabled="isDisabled"
    :placeholder="placeholder"
    v-on:keyup="debounceOnChange"
  />
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { debounce } from "../../debounce";
import { Prop, Watch } from "vue-property-decorator";
import { IBase } from "../../common-models";

@Component({
  name: "input-debounce",
})
export default class InputDebounce extends Vue {
  @Prop({ default: "" })
  public readonly value!: string;

  @Prop({ default: "" })
  public readonly defaultValue!: string;

  @Prop({
    default: () => {
      return {
        id: "",
        description: "",
      };
    },
  })
  public readonly defaultObjectHack!: IBase;

  @Prop({ default: "" })
  public readonly placeholder!: string;

  @Prop({ default: false })
  public readonly isDisabled: boolean;

  @Prop({ default: 250 })
  public readonly debounceRate: number;

  public selectedValue = "";
  public debounceOnChange: any;

  @Watch("value")
  public onValueChanged(newValue: string) {
    if (newValue !== this.selectedValue) {
      this.selectedValue = newValue;
    }
  }

  @Watch("defaultValue")
  public onDefaultValueChanged(newValue: string) {
    // if (newValue.length > 0) {
    if (newValue !== this.selectedValue) {
      this.selectedValue = newValue;
    }
    // }
  }

  @Watch("defaultObjectHack")
  public onDefaultObjectHackChanged(newValue: IBase, oldValue: IBase) {
    if (newValue !== oldValue) {
      this.selectedValue = "";
    }
  }

  public created(): void {
    if (this.defaultValue.length > 0) {
      if (this.defaultValue !== this.selectedValue) {
        this.selectedValue = this.defaultValue;
      }
    }

    this.debounceOnChange = debounce(() => {
      console.log(
        "InputDebounce.debounceOnChange()...selectedValue: " +
          this.selectedValue
      );
      this.onChange();
    }, this.debounceRate);
  }

  public onChange(): void {
    console.log(
      "InputDebounce.onChange()...emit: 'input', selectedValue: " +
        this.selectedValue
    );
    this.$emit("input", this.selectedValue);
    this.$emit("onChange", this.selectedValue);
  }
}
</script>
