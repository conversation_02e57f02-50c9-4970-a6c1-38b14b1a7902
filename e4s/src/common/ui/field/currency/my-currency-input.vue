<template>
    <input :disabled="isDisabled"
           type="number"
           v-model.number="valueInternal"
           v-on:keyup="onChange"
           @focus="onFocus"
           @blur="onBlur"/>
</template>

<script lang="ts">
    import Vue from "vue";
    import {Prop, Watch} from "vue-property-decorator";
    import Component from "vue-class-component";

    @Component({
        name: "my-currency-input",
    })
    export default class MyCurrencyInput extends Vue {
        @Prop({
            default: 0
        }) public readonly value: number;
        @Prop({
            default: 2
        }) public readonly decimalPlaces: number;

        public isInputActive: boolean = false;
        public isDisabled: boolean = false;
        public valueInternal: number = 0;
        public isValid: boolean = true;

        public created() {
            if (!isNaN(this.value)) {
                this.valueInternal = this.value;
            }
        }

        @Watch("value")
        public onValueChanged(newValue: number, oldValue: number) {
            if (newValue !== oldValue) {
                this.valueInternal = this.calcValueToDisplay(newValue);
            }
        }

        public onFocus() {
            this.isInputActive = true;
        }

        public onBlur() {
            this.isInputActive = false;
            this.onChange();
        }

        public calcValueToDisplay(newValue: number): number {
            if (this.isInputActive) {
                return newValue;
            }
            const value = parseFloat(newValue.toString());
            this.isValid = !isNaN(value);
            if (isNaN(value)) {
                return newValue;
            }
            return Number(value.toFixed(this.decimalPlaces));
        }

        public onChange() {
            if (!this.isInputActive) {
                const newValue = this.calcValueToDisplay(this.valueInternal);
                this.valueInternal = newValue;
            }
            this.$emit("input", this.valueInternal);
            this.$emit("isValid", this.isValid);
        }
    }
</script>
