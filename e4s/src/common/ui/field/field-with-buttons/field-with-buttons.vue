<template>
    <div>
        <input
            v-model="inputValue"
            class="e4s-input e4s-input-auto-width e4s-force-inline-block"
            :style="getGroupNameStyle"
            v-on:keyup="onValueChanged"
        />
        <div class="e4s-force-inline-block">

            <slot name="buttonPrimary">
                <button
                    class="e4s-button e4s-button--green e4s-button--medium e4s-button--fat"
                    v-on:click="clickButtonPrimary"
                    style="margin-bottom: 5px"
                    :disabled="isLoading"
                >
                    <span v-text="buttonPrimaryText"></span>
                </button>
            </slot>

            <slot name="buttonSecondary">
                <button
                    class="e4s-button e4s-button--red e4s-button--medium e4s-button--fat"
                    v-on:click="clickButtonSecondary"
                    style="margin-bottom: 5px"
                    :disabled="isLoading"
                >
                    <span v-text="buttonSecondaryText"></span>
                </button>
            </slot>

        </div>
    </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";

@Component({
    name: "field-with-buttons",
    components: {
    }
})
export default class FieldWithButtons extends Vue {
    @Prop({default: ""})
    public readonly value: string;

    @Prop({default: "Save"})
    public readonly buttonPrimaryText: string;

    @Prop({default: "Cancel"})
    public readonly buttonSecondaryText: string;

    @Prop({default: false})
    public readonly isLoading: boolean;

    public inputValue = "";

    @Watch("value", {immediate: true})
    public onDefaultValueChanged(newValue: string, oldValue: string) {
        this.inputValue = newValue;
    }

    public get getGroupNameStyle(): Record<string, any> {
        return {};
    }

    public onValueChanged() {
        this.$emit("input", this.inputValue);
    }

    public clickButtonPrimary() {
        this.$emit("primary", this.inputValue);
    }

    public clickButtonSecondary() {
        this.inputValue = "";
        this.$emit("secondary", this.inputValue);
    }
}
</script>
