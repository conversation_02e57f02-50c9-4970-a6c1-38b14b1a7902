<template>
  <div class="input-field" :class="classColWidth">
    <i v-if="showPrefixIcon" class="material-icons prefix">{{
      iconClassName
    }}</i>
    <!--userInput{{userInput}}-->
    <!--@change="onSelection"-->
    <label class="active e4s-label" :for="FIELD_SUFFIX">
      <span
        :class="isLoading ? 'auto-complete-is-loading-label' : ''"
        v-text="fieldLabel"
      >
      </span>
      <span v-show="isLoading">Loading...</span>
      <span
        v-show="getShowUserMessage"
        :class="getUserMessageClass"
        v-text="userMessage.message"
      ></span>
      <span v-show="getShowNoResults" :class="getNoResultsClass"
        >No results found for '<span v-text="userInput"></span>'</span
      >
      <!--            isDirty: <span v-text="isDirty"></span>-->
      <!--            isObj: <span v-text="getHasObjectBeenSelected"></span>-->
      <!--            getUserNeedsToSelect: <span v-text="getUserNeedsToSelect"></span>-->
      <span v-show="getUserNeedsToSelect" :class="getUserNotSelectedClass">
        Please select from <span v-text="getDataLength"></span> result<span
          v-if="getDataLength > 1"
          >s</span
        >
        found
      </span>
    </label>

    <input
      v-model="userInput"
      autocomplete="off"
      type="text"
      :placeholder="getPlaceholder"
      ref="autoComplete"
      :id="FIELD_SUFFIX"
      v-on:focus="onUserFocus"
      v-on:keyup="onUserInputChanged"
      v-on:blur="onUserBlur"
      class="autocomplete e4s-input-mat"
    />

    <!--<div>userInput>>>{{userInput}}</div>-->
    <!--        <div>data>>>{{data}}</div>-->
    <!--        <div>dropDownData>>>{{dropDownData}}</div>-->
    <!--<div>custom>>>{{custom}}</div>&ndash;&gt;-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  IUserMessage,
  USER_MESSAGE_LEVEL,
} from "../../../user-message/user-message-models";
import { ICustom, IAutoCompleteValue } from "./auto-complete-mat-models";
import { IObjectKey } from "../../common-models";

@Component({
  name: "auto-complete-mat",
})
export default class AutoCompleteMat extends Vue {
  @Prop({ default: "" }) public userInputPreload: string; //  Not used at present
  @Prop({ default: "" }) public fieldLabel: string; //  Label
  @Prop({ default: () => [] }) public data: any[]; //  Default data
  @Prop({ default: null }) public labelProp: string; //  default prop from object to display in drop down
  @Prop({ default: null }) public custom: ICustom; //  @See interface above
  @Prop({ default: "" }) public classColWidth: string;
  @Prop({ default: "" }) public iconClassName: string;
  @Prop({ default: 250 }) public debounceSearch: number;
  @Prop({ default: false }) public isLoading: boolean;
  // @Prop({default: false}) public allowValuesNotInList: boolean;
  @Prop({ default: "" }) public placeholder: string;
  @Prop({
    default: () => {
      return { level: USER_MESSAGE_LEVEL.INFO, message: "" } as IUserMessage;
    },
  })
  public userMessage: IUserMessage;

  public FIELD_SUFFIX = Math.random().toString(36).substring(10);
  public userInput: string = "";
  public dropDownData: IObjectKey = {};
  public dropDownDataShadow: IObjectKey = {};
  public instance: any;
  public debounceEmitUserInputId: number;
  public debounceEmitSelectedId: number;
  public showNoResults: boolean = false;
  public suppressAutoSearch: boolean = false;
  public onAutoCompleteSelectedObj: any = null;
  public isDirty: boolean = false;

  private EMIT_ACTIONS = {
    SELECTION_MADE: "autoSelectionMade",
    SEARCH_TERM_CHANGED: "searchTermChanged",
  };

  public created() {
    this.setData(this.data);
  }

  public mounted() {
    this.instance = (window as any).M.Autocomplete.init(
      this.$refs.autoComplete,
      {
        onAutocomplete: (val: any) => {
          this.onAutoCompleteSelectedObj =
            this.getOutputObjectObjectfromKey(val);

          this.userInput = val;

          this.$emit(
            this.EMIT_ACTIONS.SELECTION_MADE,
            this.getOutputObjectObjectfromKey(val)
          );
        },
        sortFunction: () => {
          return false;
        },
      }
    );
    (window as any).document
      .getElementById(this.FIELD_SUFFIX)
      .setAttribute("autocomplete", "off");

    const dropDown = this.instance.dropdown;
    dropDown.options.onCloseStart = (val: any) => {
      // console.log("AutoCompleteMat.mounted()  dropDown.onCloseStart() ");
    };

    dropDown.options.onCloseEnd = (val: any) => {
      if (
        Object.keys(this.dropDownData).length > 0 &&
        R.isNil(this.onAutoCompleteSelectedObj)
      ) {
        // console.log("AutoCompleteMat.mounted()  dropDown.onCloseEnd() ...user not selected!");
      }
    };

    this.onUserInputPreloadChanged(this.userInputPreload, "");
  }

  public get getHasObjectBeenSelected() {
    return this.onAutoCompleteSelectedObj ? true : false;
  }

  @Watch("userInputPreload")
  public onUserInputPreloadChanged(newValue: string, oldValue: string) {
    if (newValue !== oldValue) {
      this.suppressAutoSearch = true;
      this.userInput = newValue;
      this.dropDownData = {};
      this.instance.updateData([]);
      // this.suppressAutoSearch = false;
    }
  }

  @Watch("data")
  public onDatachanged(data: any[]) {
    this.setData(data);
  }

  @Watch("isLoading")
  public onIsLoadingChanged(isLoading: boolean) {
    if (isLoading) {
      this.resetAndClose();
    }
  }

  public setData(data: any[]): void {
    if (this.userInput.length === 0) {
      this.showNoResults = false;
      this.resetAndClose();
      return;
    }
    if (data && data.length === 0 && this.userInput.length > 0) {
      this.showNoResults = true;
      this.resetAndClose();
      return;
    }

    if (data && data.length > 0) {
      this.showNoResults = false;
      const dropDownData = this.getDataObject(data, false);
      this.dropDownDataShadow = this.getDataObject(data, true);
      this.dropDownData = dropDownData;
      this.instance.updateData(dropDownData);
      try {
        this.instance.open();
      } catch (e) {
        // console.log("AutoCompleteMat.setData() c error", e);
      }
    }
  }

  public resetAndClose() {
    this.dropDownDataShadow = {};
    this.dropDownData = {};

    try {
      this.instance.updateData([]);
      this.instance.close();
    } catch (e) {
      // console.log("AutoCompleteMat.setData() b error", e);
    }
  }

  public onUserInputChanged() {
    clearTimeout(this.debounceEmitUserInputId);
    this.debounceEmitUserInputId = window.setTimeout(() => {
      this.isDirty = true;
      if (this.userInput.length === 0) {
        this.showNoResults = false;
        this.dropDownDataShadow = {};
        this.dropDownData = {};
        this.instance.updateData([]);
      }
      this.$emit(this.EMIT_ACTIONS.SEARCH_TERM_CHANGED, this.userInput);
    }, this.debounceSearch);
  }

  public getObjectKeyValue(obj: any): string {
    if (!obj) {
      return "NOT_FOUND";
    }
    if (this.custom && this.custom.dropDownLabelFunc) {
      return this.custom.dropDownLabelFunc(obj);
    } else {
      return obj[this.labelProp];
    }
  }

  public getDataObject = (events: any[], addObject: boolean): any => {
    const dataObj = events.reduce((accum: any, obj: any) => {
      const keyUserEventUnique: string = this.getObjectKeyValue(obj);
      if (!accum[keyUserEventUnique]) {
        //  TODO totally annoying, how store object and not have the image get loaded???
        //   @see https://materializecss.com/autocomplete.html
        accum[keyUserEventUnique] = addObject ? obj : null;
      }
      return accum;
    }, {});

    return dataObj;
  };

  public onUserBlur() {
    const userInput = this.userInput;
    if (userInput.length > 0) {
      if (this.onAutoCompleteSelectedObj) {
        // console.log("AutoCompleteMat.onUserBlur() onAutoCompleteSelectedObj is selected", this.onAutoCompleteSelectedObj);
      } else {
        if (this.data.length > 0) {
          // userInput = this.custom.dropDownLabelFunc(this.data[0]);
          //
          // const obj = this.getOutputObjectObjectfromKey(userInput);
          // this.onAutoCompleteSelectedObj = obj;
          //
          // this.suppressAutoSearch = true;
          // userInput = this.custom.dropDownLabelFunc(obj.value);
          // this.userInput = userInput;
          //
          // window.setTimeout( () => {
          //     this.$emit(this.EMIT_ACTIONS.SELECTION_MADE, obj);
          // }, 100);
        } else {
          // console.log("AutoCompleteMat.onUserBlur() onAutoCompleteSelectedObj is NOT selected, no data, so clear.");
        }
      }
    }
  }

  public onUserFocus() {
    this.$emit("onUserFocus");
  }

  public get getPlaceholder(): string {
    // return  "Enter " + this.placeholder + " name or registration # here...";
    return this.placeholder;
  }

  public get getShowUserMessage(): boolean {
    return !this.isLoading && this.userMessage.message.length > 0;
  }

  public get getUserMessageClass(): string {
    return "user-message-warn";
  }

  public get getNoResultsClass(): string {
    return "no-results";
  }

  public get getUserNotSelectedClass(): string {
    return "user-not-selected-result";
  }

  public get getShowNoResults() {
    return !this.isLoading && this.showNoResults;
  }

  public get showPrefixIcon() {
    return this.iconClassName.length > 0;
  }

  public getOutputObjectObjectfromKey(key: string): IAutoCompleteValue {
    const userSelectedObject = this.dropDownDataShadow[key];
    const userInput: string = this.userInput;
    return {
      userInput,
      keyValue: this.getObjectKeyValue(userSelectedObject),
      value: userSelectedObject,
    } as IAutoCompleteValue;
  }

  public get getDataLength(): number {
    return Object.keys(this.dropDownData).length;
  }

  public get getUserNeedsToSelect() {
    return (
      this.getDataLength &&
      this.isDirty === true &&
      R.isNil(this.onAutoCompleteSelectedObj)
    );
  }

  public beforeDestroy() {
    this.instance.destroy();
  }
}
</script>

<style scoped>
.user-message-info {
  /*color: red;*/
}
.user-message-warn {
  color: #f9bf2e;
}

.no-results {
  color: red;
}

.user-not-selected-result {
  color: red;
}

.row .col .input-field {
  margin-bottom: 0.5rem !important;
}

.row .col .input-field .e4s-input-mat {
  height: 2rem !important;
  background-color: white;
  margin-bottom: 0rem !important;
  /*color: red;*/
}

.e4s-label {
  color: black;
}
</style>
