<template>
    <input type="text"
           :id="FIELD_SUFFIX"
           autocomplete="off"
           v-model="userInput"
           class="autocomplete"
           :placeholder="placeHolder"
           v-on:keyup="onUserInputChanged"
           v-on:keydown="onKeyDown">
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {IObjectKey} from "../../common-models";
    import * as R from "ramda";
    import {InputRestrictController} from "../field/input-restrict-length/input-restrict-controller";

    @Component({
        name: "auto-complete-mat-simple"
    })
    export default class AutoCompleteMatSimple extends Vue {

        @Prop({default: ""}) public readonly defaultUserInput: string;
        @Prop({default: () => []}) public readonly data: any[];                     //  Default data
        @Prop({default: null}) public readonly labelProp: string;                   //  default prop from object to display in drop down
        @Prop({default: "Enter search term"}) public readonly placeHolder: string;
        @Prop({
            default: 100
        }) public readonly maxLength: number;

        public instance: any;
        public dropDownData: IObjectKey = {};
        public userInput: string = "";
        public FIELD_SUFFIX = Math.random().toString(36).substring(2);
        public inputRestrictController: InputRestrictController = new InputRestrictController();

        public created() {
            // this.userInput = this.defaultUserInput;
            this.inputRestrictController.setMaxLength(this.maxLength);
            this.inputRestrictController.setInputValue(this.defaultUserInput);
            this.userInput = this.inputRestrictController.getInputValue();

            this.setData(this.data);
        }

        public mounted() {
            const elems = document.getElementById(this.FIELD_SUFFIX);

            const options = {
                data: R.clone(this.dropDownData),
                onAutocomplete: (val: string) => {
                    this.onSelected(val);
                }
            };
            this.instance = (window as any).M.Autocomplete.init(elems, options);
        }

        @Watch("defaultUserInput")
        public onDefaultUserInputChanged(newValue: string) {
            this.userInput = newValue;
        }

        @Watch("data")
        public onDatachanged(newValue: any[]) {
            this.setData(newValue);
            if (this.instance && this.instance.updateData) {
                this.instance.updateData(R.clone(this.dropDownData));
            }
        }

        public setData(data: any[]): void {
            if (data && data.length > 0) {
                const dropDownData = this.getDataObject(data, false);
                this.dropDownData = dropDownData;
            }
        }

        public getDataObject = (events: any[], addObject: boolean): any => {
            const dataObj = events.reduce((accum: any, obj: any) => {
                const keyUserEventUnique: string = this.getObjectKeyValue(obj);
                if (!accum[keyUserEventUnique]) {
                    //  TODO totally annoying, how store object and not have the image get loaded???
                    //   @see https://materializecss.com/autocomplete.html
                    accum[keyUserEventUnique] = addObject ? obj : null;
                }
                return accum;
            }, {});

            return dataObj;
        }

        public getObjectKeyValue(obj: any): string {
            if (!obj) {
                return "NOT_FOUND";
            }
            return obj[this.labelProp];
        }

        public onUserInputChanged() {
            this.inputRestrictController.setInputValue(this.userInput);
            this.$emit("onUserInputChanged", this.userInput);
        }

        public onSelected(value: string) {
            const data = this.data.reduce((accum, obj) => {
                if (obj[this.labelProp] === value) {
                    accum = R.clone(obj);
                }
                return accum;
            }, {id: 0});
            this.$emit("onSelected", data);
        }

        public onKeyDown(evt: any) {
            this.inputRestrictController.onKeyDown(evt);
        }

        public beforeDestroy() {
            this.instance.destroy();
        }
    }
</script>
