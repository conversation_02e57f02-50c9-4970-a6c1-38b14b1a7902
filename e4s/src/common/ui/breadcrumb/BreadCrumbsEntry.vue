<template>
  <div class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center">
    <BreadCrumbFromConfig
      :bread-crumb="breadCrumbController.state.breadCrumbsMap.HOME"
      @selected="breadCrumbController.goHome()"
    />
    <BreadCrumbFromConfig
      :bread-crumb="breadCrumbController.state.breadCrumbsMap.COMP_HOME"
      v-if="breadCrumbController.state.breadCrumbsMap.COMP_HOME.isVisible"
      @selected="breadCrumbController.goCompHome()"
    />
    <BreadCrumbFromConfig
      :bread-crumb="breadCrumbController.state.breadCrumbsMap.COMP_ATHLETES"
      v-if="breadCrumbController.state.breadCrumbsMap.COMP_ATHLETES.isVisible"
      @selected="breadCrumbController.goToAthletes()"
    />
    <BreadCrumbFromConfig
      :bread-crumb="breadCrumbController.state.breadCrumbsMap.COMP_ATHLETE"
      v-if="breadCrumbController.state.breadCrumbsMap.COMP_ATHLETE.isVisible"
    />
    <BreadCrumbFromConfig
      :bread-crumb="
        breadCrumbController.state.breadCrumbsMap.COMP_ATHLETE_SCHEDULE
      "
      v-if="
        breadCrumbController.state.breadCrumbsMap.COMP_ATHLETE_SCHEDULE
          .isVisible
      "
    />
    <BreadCrumbFromConfig
      :bread-crumb="breadCrumbController.state.breadCrumbsMap.COMP_TEAMS"
      v-if="breadCrumbController.state.breadCrumbsMap.COMP_TEAMS.isVisible"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext, onUpdated } from "@vue/composition-api";
import { useBreadCrumbController } from "./useBreadCrumb";
import BreadCrumbFromConfig from "./BreadCrumbFromConfig.vue";

export default defineComponent({
  name: "BreadCrumbsEntry",
  components: { BreadCrumbFromConfig },
  props: {
    compId: {
      type: Number,
      required: true,
    },
  },
  setup(props: { compId: number }, context: SetupContext) {
    const breadCrumbController = useBreadCrumbController({
      useGlobalState: true,
      compId: props.compId,
    });

    onUpdated(() => {
      console.log("BreadCrumb " + "...updated! " + new Date().toISOString());
    });

    return { breadCrumbController };
  },
});
</script>

<style scoped></style>
