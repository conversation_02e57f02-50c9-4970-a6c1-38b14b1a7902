<template>
  <div class="e4s-card e4s-card--generic accordion-v3--wrapper">
    <div
      class="
        e4s-flex-row
        accordion-v3--header
        e4s-justify-flex-row-vert-center e4s-full-width
      "
      v-on:click="onRowClick"
    >
      <div
        class="
          e4s-flex-row
          e4s-gap--standard
          e4s-justify-flex-row-vert-center e4s-full-width
        "
      >
        <!--        <PrimaryLink-->
        <!--          :link-text="isExpandedInternal ? '-' : '+'"-->
        <!--          class="accordion-v3&#45;&#45;button"sss-->
        <!--        />-->
        <PlayMinor
          v-if="showArrow"
          :arrow-direction="isExpandedInternal ? 'down' : 'right'"
          fill="black"
          v-on:click.prevent="onArrowClick"
        />
        <slot name="summary">
          <div v-text="title" class="e4s-input--label"></div>
        </slot>
      </div>
    </div>

    <div v-if="isExpandedInternal">
      <hr class="dat-e4s-hr-only" />
      <div class="accordion-v3--content-wrapper">
        <slot name="content"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import PrimaryLink from "../href/PrimaryLink.vue";
import PlayMinor from "../../svg/PlayMinor.vue";

export default defineComponent({
  name: "AccordionV3",
  components: { PlayMinor, PrimaryLink },
  props: {
    isExpanded: {
      type: Boolean,
      default: false,
    },
    suppressRowClick: {
      type: Boolean,
      default: false,
    },
    showArrow: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: "",
    },
  },
  setup(
    props: {
      isExpanded: boolean;
      suppressRowClick: boolean;
      showArrow: boolean;
      title: string;
    },
    context: SetupContext
  ) {
    const isExpandedInternal = ref(props.isExpanded);

    watch(
      () => props.isExpanded,
      (newValue) => {
        isExpandedInternal.value = newValue;
      }
    );

    function onRowClick() {
      if (!props.suppressRowClick) {
        isExpandedInternal.value = !isExpandedInternal.value;
        context.emit("onIsExpanded", isExpandedInternal.value);
      }
    }

    function onArrowClick() {
      isExpandedInternal.value = !isExpandedInternal.value;
      context.emit("onIsExpanded", isExpandedInternal.value);
    }

    return { isExpandedInternal, onRowClick, onArrowClick };
  },
});
</script>

<style scoped>
.accordion-v3--wrapper {
  padding: 0;
}

.accordion-v3--header {
  cursor: pointer;
  height: 40px;
  padding: 0 var(--e4s-gap--standard);
}

.accordion-v3--button {
  font-size: 35px;
}

.accordion-v3--button:hover,
.accordion-v3--button:focus {
  text-decoration: none;
}

.accordion-v3--content-wrapper {
  padding: var(--e4s-gap--standard);
}

details summary::before {
  content: "+";
  color: red;
  /* you can style it however you want, use background-image for example */
  margin-right: 8px;
  font-size: 28px;
  /*height: 28px;*/
  /*margin-top: -14px;*/
}

/* By using [open] we can define different styles when the disclosure widget is open */
details[open] summary::before {
  content: "-";
  color: red;
  margin-right: 8px;
  font-size: 28px;
  /*height: 28px;*/
  /*margin-top: -14px;*/
}
</style>
