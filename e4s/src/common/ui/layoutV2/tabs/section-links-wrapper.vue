<template>
  <section class="e4s-tab-links--section-links-wrapper">
    <div class="e4s-flex-row e4s-tab-links--section-links-container">
      <slot> Put Links Here </slot>

      <slot name="right" class="e4s-flex-row--end"> </slot>
    </div>
  </section>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";

export default defineComponent({
  name: "section-links-wrapper",
  components: {},
  props: {},
  setup(props: {}, context: SetupContext) {
    return {};
  },
});
</script>
