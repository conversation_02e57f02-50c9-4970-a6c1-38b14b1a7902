<template>
  <div class="e4s-flex-column e4s-input--container">
    <span class="dat-e4s-input--info-label">
      <label class="e4s-input--label" v-text="formLabel"></label>
      <div
        v-if="showHelp"
        class="
          e4s-flex-column e4s-flex-center e4s-justify-flex-center
          dat-e4s-input--info-icon-container
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 12 12"
        >
          <path
            d="M12,6A6,6,0,1,1,6,0a6,6,0,0,1,6,6ZM4.928,4.525h-.99A1.84,1.84,0,0,1,6,2.625c1.048,0,2,.548,2,1.68a1.831,1.831,0,0,1-.933,1.543c-.553.419-.758.576-.758,1.115v.266H5.338l-.005-.347a1.708,1.708,0,0,1,.876-1.49c.443-.333.724-.552.724-1.028a.871.871,0,0,0-.986-.876A.957.957,0,0,0,4.928,4.525Zm.938,4.832a.7.7,0,1,1,0-1.4.7.7,0,1,1,0,1.4Z"
          />
        </svg>
      </div>
    </span>
<!--    id="qazwsx"-->
    <div class="e4s-flex-row e4s-flex-center">
      <select
        name="state"
        v-on:change="onChanged"
        v-model="valueInternal"
        class="browser-default e4s-input-field e4s-input-field--primary"
        :class="errorMessage.length > 0 ? 'e4s-input-field--error' : ''"
      >
        <option v-for="obj in dataArray" :value="obj" :key="obj.id" :disabled="obj.disabled">
          <slot :obj="obj"><span v-text="obj.name"></span></slot>
        </option>
      </select>
      <slot name="button">
<!--        <ButtonGenericV2 text="Go"/>-->
      </slot>
    </div>

    <p
      v-if="errorMessage.length > 0"
      class="e4s-body--200 e4s-body--error"
      v-text="errorMessage"
    ></p>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import ButtonGenericV2 from "../buttons/button-generic-v2.vue"
import {IBaseConcreteDropDown} from "./field-models"




/**
 * Use this as follows, obv' pass whatever native events needed:
 <FieldDropDownV2
    form-label="some drop down"
    :data-array="[{id:1, name: 'one'}, {id:2, name: 'two'}]"
    v-model="state.organiser"
 >
    <template v-slot="{ obj }">
      <span v-text="obj.id + ' - ' + obj.name"></span>
    </template>
 </FieldDropDownV2>
 */
export default defineComponent({
  name: "field-drop-down-v2",
  components: {ButtonGenericV2},
  inheritAttrs: false,
  props: {
    formLabel: {
      type: String,
      default: () => {
        return "";
      },
    },
    placeHolder: {
      type: String,
      default: () => {
        return "";
      },
    },
    value: {
      type: Object as PropType<IBaseConcreteDropDown>,
      default: () => {
        return {
          id: 0,
          name: ""
        };
      },
    },
    dataArray: {
      type: Array as PropType<IBaseConcreteDropDown[]>,
      default: () => {
        return [];
      },
    },
    errorMessage: {
      type: String,
      default: () => {
        return "";
      },
    },
    showHelp: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  setup(
    props: {
      value: IBaseConcreteDropDown;
      formLabel: string;
      placeHolder: string;
      errorMessage: string;
      showHelp: boolean;
      dataArray: IBaseConcreteDropDown[];
    },
    context: SetupContext
  ) {
    const valueInternal = ref(props.value);

    watch(
      () => props.value,
      (newValue: IBaseConcreteDropDown) => {
        if (newValue !== valueInternal.value) {
          console.log("field-drop-down-v2 value changed", {
            new: newValue,
            internal: valueInternal.value
          })
          valueInternal.value = newValue;
        }
      },
      {
        immediate: true,
      }
    );

    function onChanged() {
      context.emit("input", valueInternal.value);
    }

    return { valueInternal, onChanged };
  },
});
</script>
