<template>
  <div class="e4s-flex-column e4s-input--container">
    <span class="dat-e4s-input--info-label">
      <label
        class="e4s-input--label"
        :class="formLabelClass"
        v-text="formLabel"
      ></label>
      <div
        v-if="showHelp"
        class="
          e4s-flex-column e4s-flex-center e4s-justify-flex-center
          dat-e4s-input--info-icon-container
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 12 12"
        >
          <path
            d="M12,6A6,6,0,1,1,6,0a6,6,0,0,1,6,6ZM4.928,4.525h-.99A1.84,1.84,0,0,1,6,2.625c1.048,0,2,.548,2,1.68a1.831,1.831,0,0,1-.933,1.543c-.553.419-.758.576-.758,1.115v.266H5.338l-.005-.347a1.708,1.708,0,0,1,.876-1.49c.443-.333.724-.552.724-1.028a.871.871,0,0,0-.986-.876A.957.957,0,0,0,4.928,4.525Zm.938,4.832a.7.7,0,1,1,0-1.4.7.7,0,1,1,0,1.4Z"
          />
        </svg>
      </div>
    </span>
    <!--    <DateEntry v-model="valueInternal" :allow-null-reset="true"/>-->
    <DateInputDropDownV2
      :value="valueInternal"
      v-on:input="onDateChanged"
      :allow-null-reset="true"
      :is-disabled="isDisabled"
      :restrict-to-years="restrictToYears"
    />
    <p
      v-if="errorMessage && errorMessage.length > 0"
      class="e4s-body--200 e4s-body--error"
      v-text="errorMessage"
    ></p>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  SetupContext,
  watch,
  onMounted,
  PropType,
} from "@vue/composition-api";
import DateInputDropDownV2 from "./date-input-drop-down-v2.vue";

export default defineComponent({
  name: "field-date-drop-down-v2",
  components: { DateInputDropDownV2 },
  inheritAttrs: false,
  props: {
    formLabel: {
      type: String,
      default: () => {
        return "";
      },
    },
    formLabelClass: {
      type: String,
      default: () => {
        return "";
      },
    },
    value: {
      type: String,
      default: () => {
        return "";
      },
    },
    errorMessage: {
      type: String,
      default: () => {
        return "";
      },
    },
    showHelp: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    onlyDate: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    onlyTime: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    displayFormat: {
      type: String,
      default: () => {
        //  Only provide one if you want to override the inbuilt "date" and "time" formats
        return "";
      },
    },
    placeHolder: {
      type: String,
      default: () => {
        return "";
      },
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    restrictToYears: {
      type: Array as PropType<number[]>,
      default: () => {
        return [];
      },
    },
  },
  setup(
    props: {
      value: string;
      formLabel: string;
      errorMessage: string;
      showHelp: boolean;
      onlyDate: boolean;
      onlyTime: boolean;
      displayFormat: string;
      placeHolder: string;
      isDisabled: boolean;
      restrictToYears: number[];
    },
    context: SetupContext
  ) {
    const valueInternal = ref(props.value);

    onMounted(() => {
      // document
      //   .getElementById(idForInput + "-input")!
      //   .classList.add(
      //     "browser-default",
      //     "e4s-input-field",
      //     "e4s-input-field--primary"
      //   );
    });

    watch(
      () => props.value,
      (newValue: string) => {
        if (newValue !== valueInternal.value) {
          valueInternal.value = newValue;
        }
      },
      {
        immediate: true,
      }
    );

    function onDateChanged(iso: string) {
      valueInternal.value = iso;
      onChanged();
    }

    function onChanged() {
      context.emit("input", valueInternal.value);
    }

    return {
      valueInternal,
      onChanged,
      onDateChanged,
    };
  },
});
</script>
