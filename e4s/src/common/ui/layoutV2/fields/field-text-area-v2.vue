<template>
  <textarea
    v-model="valueInternal"
    class="
      browser-default
      e4s-input-field e4s-input-field--primary
      e4s-textarea-v2
    "
    :class="cssClasses"
    :placeholder="placeHolder"
    :disabled="isDisabled"
    :tabindex="tabIndex"
    @click="clickHandler"
    @change="submit"
    @keyup="keyUpHandler"
  ></textarea>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { useFieldController } from "./useFieldController";
import {
  isNullOrUndefined,
  stripHtmlTags,
} from "../../../common-service-utils";

export default defineComponent({
  name: "field-text-area-v2",
  components: {},
  inheritAttrs: false,
  props: {
    value: {
      type: String,
      default: () => {
        return "";
      },
    },
    placeHolder: {
      type: String,
      default: () => {
        return "";
      },
    },
    errorMessage: {
      type: String,
      default: () => {
        return "";
      },
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    tabIndex: {
      type: Number,
      default: () => {
        return 0;
      },
    },
    useIsDirty: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    allowHtmlTags: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  setup(
    props: {
      value: string;
      placeHolder: string;
      errorMessage: string;
      isDisabled: boolean;
      tabIndex: number;
      useIsDirty: boolean;
      allowHtmlTags: boolean;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(props.value);

    const fieldController = useFieldController();
    fieldController.init(props.value);

    watch(
      () => props.value,
      (newValue: string) => {
        if (isNullOrUndefined(newValue)) {
          return;
        }

        //  because underlying model is screwed up
        if (typeof (newValue as unknown as number) == "number") {
          newValue = newValue.toString();
        }

        // remove new lines and tabs
        newValue = sanitizeValue(newValue);

        if (newValue !== valueInternal.value) {
          valueInternal.value = newValue;
          // sourceValueInternal.value = newValue;
          fieldController.init(newValue);
        }
      },
      {
        immediate: true,
      }
    );

    function clickHandler() {
      console.log("clickHandler");
      context.emit("onClick", valueInternal.value);
    }

    function sanitizeValue(value: string) {
      let newValue = value;
      if (!props.allowHtmlTags) {
        return stripHtmlTags(newValue);
      }

      // remove new lines and tabs and replace with space
      newValue = newValue.replace(/(\r\n|\n|\r|\t)/gm, " ");

      return newValue;
    }

    function submit() {
      console.log("field-text-v2.submit() value: " + valueInternal.value);

      let newValue = valueInternal.value;

      newValue = sanitizeValue(newValue);

      context.emit("input", newValue);
      context.emit("change", newValue);

      fieldController.updateCurrent(newValue);
    }

    function keyUpHandler(args: KeyboardEvent) {
      console.log("keyUpHandler", args);

      if (args.key === "Enter") {
        //   TODO I know this is wrong!  I just need to get this working.
        //   TODO At some point sort this as per the Vue.js docs.
        // https://stackoverflow.com/questions/56224091/what-are-inheritattrs-false-and-attrs-used-for-in-vue
        context.emit("keyUpEnter", valueInternal.value);
      }

      context.emit("keyUp", valueInternal.value);
    }

    const cssClasses = computed(() => {
      const css: string[] = [];

      if (props.errorMessage.length > 0) {
        css.push("e4s-input-field--error");
        return css;
      }
      if (props.isDisabled) {
        css.push("e4s-input-field--disabled");
        return css;
      }

      if (props.useIsDirty && fieldController.state.isDirty) {
        css.push("e4s-input-field--dirty");
      }

      return css;
    });

    return {
      valueInternal,
      submit,
      keyUpHandler,
      clickHandler,
      fieldController,
      cssClasses,
    };
  },
});
</script>

<style>
.e4s-textarea-v2 {
  height: 75px;
  padding: 5px;
  overflow-x: hidden;
  overflow-y: auto;
  resize: vertical;
  min-height: 70px;
  max-height: 150px;
}
</style>
