<template>
  <div
    class="e4s-flex-row e4s-flex-nowrap e4s-input--container e4s-gap--standard"
  >
    <label>
      <input
        type="radio"
        class="browser-default e4s-input-field e4s-input-field--primary"
        :value="GENDER.FEMALE"
        v-model="genderInternal"
        v-on:change="onValueChanged"
        :disabled="isDisabled"
      />
      <span class="e4s-radio--span">Female</span>
    </label>
    <label>
      <input
        type="radio"
        class="browser-default e4s-input-field e4s-input-field--primary"
        :value="GENDER.MALE"
        v-model="genderInternal"
        v-on:change="onValueChanged"
        :disabled="isDisabled"
      />
      <span class="e4s-radio--span">Male</span>
    </label>
    <label v-if="showAll">
      <input
        type="radio"
        class="browser-default e4s-input-field e4s-input-field--primary"
        :value="''"
        v-model="genderInternal"
        v-on:change="onValueChanged"
        :disabled="isDisabled"
      />
      <span class="e4s-radio--span">All</span>
    </label>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { GenderType } from "../../../../common-models";
import { GENDER } from "../../../../common-models";

export default defineComponent({
  name: "gender-radio-v2",
  components: {},
  props: {
    value: {
      type: String as PropType<GenderType>,
      default: "",
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
    errorMessage: {
      type: String,
      default: "",
    },
    showAll: {
      type: Boolean,
      default: false,
    },
  },
  setup(
    props: { value: GenderType; isDisabled: boolean; errorMessage: string },
    context: SetupContext
  ) {
    const genderInternal = ref("");

    watch(
      () => props.value,
      (newValue: GenderType) => {
        genderInternal.value = newValue;
      },
      {
        immediate: true,
      }
    );

    function onValueChanged() {
      context.emit("input", genderInternal.value);
    }

    return { genderInternal, GENDER, onValueChanged };
  },
});
</script>
