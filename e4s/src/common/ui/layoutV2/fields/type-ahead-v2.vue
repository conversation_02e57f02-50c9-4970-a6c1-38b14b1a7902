<template>
  <v-select
    :filterable="false"
    :options="state.searchClubResults"
    label="clubname"
    :value="valueInternal"
    @search="searchForClub"
    @input="onChanged"
  >
    <template v-slot:option="option">
      {{ option.id + ": " + option.clubname }}
    </template>
  </v-select>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  reactive,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { ClubDataCrud } from "../../../../club/crud/club-data-crud";
import { handleResponseMessages } from "../../../handle-http-reponse";
import { IClubCrud } from "../../../../club/club-models";

export default defineComponent({
  name: "type-ahead-v2",
  components: {},
  inheritAttrs: false,
  props: {
    value: {
      type: Object as PropType<IClubCrud>,
      required: true,
    },
    debugConsole: {
      type: String,
      default: "",
    },
  },
  setup(
    props: {
      value: IClubCrud;
      debugConsole: string;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(props.value);

    const state = reactive({
      clubSearchTerm: "",
      searchClubResults: [] as IClubCrud[],
    });

    watch(
      () => props.value,
      (newValue: IClubCrud) => {
        if (newValue !== valueInternal.value) {
          if (props.debugConsole.length > 0) {
            console.log("field-drop-down-v2 value changed", {
              new: newValue,
              internal: valueInternal.value,
            });
          }

          valueInternal.value = newValue;
        }
      },
      {
        immediate: true,
      }
    );

    function searchForClub(searchTerm: string, loading: any) {
      state.searchClubResults = [];
      const clubDataCrud: ClubDataCrud = new ClubDataCrud();
      if (searchTerm.length === 0) {
        return;
      }
      loading(true);
      const isSchool = false;
      const prom = clubDataCrud.searchClubs(0, searchTerm, isSchool);
      handleResponseMessages(prom);
      prom
        .then((resp) => {
          if (resp.errNo === 0) {
            state.searchClubResults = resp.data;
          }
        })
        .finally(() => {
          loading(false);
        });
    }

    function onChanged(selectedValue: IClubCrud) {
      valueInternal.value = selectedValue;
      context.emit("input", valueInternal.value);
    }

    return { state, valueInternal, onChanged, searchForClub };
  },
});
</script>

<style></style>
