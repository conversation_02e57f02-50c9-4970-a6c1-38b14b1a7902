<template>
  <div class="e4s-card e4s-card--generic" :class="'dat-info--' + infoType">
    <slot><p v-text="simpleMessage"></p></slot>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "@vue/composition-api";

export type InfoSectionType = "success" | "info" | "warn" | "error" | "neutral";

export default defineComponent({
  name: "info-section-v2",
  components: {},
  props: {
    infoType: {
      type: String as PropType<InfoSectionType>,
      default: () => {
        return "info";
      },
    },
    simpleMessage: {
      type: String,
      default: () => {
        return "";
      },
    },
  },
});
</script>
