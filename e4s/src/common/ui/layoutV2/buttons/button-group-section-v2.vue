<template>
  <!--  If only 1 button group, you can use justify-content: center/flex-start/flex-end;-->
  <div
    class="
      e4s-flex-row
      full-width
      align-e4s-flex-center
      e4s-justify-flex-space-between
      e4s-card-competition__more-information-container
    "
  >
    <slot name="buttons">
      <div class="e4s-flex-row" style="gap: 8px">
        <slot name="buttons-left">
          <div style="order: 1">
            <button class="e4s-button--tertiary">
              <span class="e4s-body--100">Test Tertiary</span>
            </button>
          </div>
        </slot>
      </div>

      <div class="e4s-flex-row" style="gap: 8px">
        <slot name="buttons-right">
          <div style="order: 2">
            <button class="e4s-button--primary">
              <span class="e4s-body--100">Test Primary</span>
            </button>
          </div>
        </slot>
      </div>
    </slot>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "@vue/composition-api";

export default defineComponent({
  name: "button-group-section-v2",
});
</script>

<style></style>
