<template>
  <div class="e4s-flex-row e4s-gap--standard">
    <ButtonGenericV2
      :text="okText"
      :disabled="getIsDisableButtons"
      v-on:click="askForConf"
      v-if="!askConf"
    />

    <ButtonGenericV2
      :style="'order: ' + (cancelFirst ? 1 : 3)"
      :text="cancelText"
      button-type="tertiary"
      v-if="displayCancel"
      v-on:click="cancel"
    />

    <ButtonGenericV2
      :style="'order: ' + 2"
      :text="confirmText"
      :disabled="getIsDisableButtons"
      v-on:click="confirmed"
      v-if="askConf"
    />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import ButtonGenericV2 from "./button-generic-v2.vue";

export default defineComponent({
  name: "buttongroup-ok-confirm-v2",
  components: { ButtonGenericV2 },
  props: {
    isDisabled: {
      type: Boolean,
      default: false,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    cancelText: {
      type: String,
      default: "Cancel",
    },
    cancelFirst: {
      type: Boolean,
      default: true,
    },
    okText: {
      type: String,
      default: "OK",
    },
    confirmText: {
      type: String,
      default: "Confirm",
    },
    showCancelOnlyWhenConfirming: {
      type: Boolean,
      default: false,
    },
  },
  setup(
    props: {
      isDisabled: boolean;
      isLoading: boolean;
      cancelText: string;
      cancelFirst: boolean;
      okText: string;
      confirmText: string;
      showCancelOnlyWhenConfirming: boolean;
    },
    context: SetupContext
  ) {
    const askConf = ref(false);

    watch(
      () => props.isLoading,
      (newValue: boolean, oldValue: boolean) => {
        if (!newValue && oldValue) {
          askConf.value = false;
        }
      }
    );

    const getIsDisableButtons = computed(() => {
      return props.isLoading || props.isDisabled;
    });

    function askForConf() {
      askConf.value = true;
      context.emit("askForConf");
    }

    const displayCancel = computed(() => {
      if (!props.showCancelOnlyWhenConfirming) {
        return true;
      }
      return askConf.value;
    });

    function confirmed() {
      context.emit("ok");
    }

    function cancel() {
      askConf.value = false;
      context.emit("cancel");
    }

    return {
      askConf,
      getIsDisableButtons,
      displayCancel,
      askForConf,
      confirmed,
      cancel,
    };
  },
});
</script>
