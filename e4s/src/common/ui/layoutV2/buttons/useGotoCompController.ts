import { LAUNCH_ROUTES_PATHS } from "../../../../launch/launch-routes";
import { LAUNCH_ROUTES_PATHS_V2 } from "../../../../launch/v2/launch-routes-v2";
import { RawLocation } from "vue-router";
import { ICompetitionSummaryPublic } from "../../../../competition/competition-models";
import { useRouter } from "../../../../router/migrateRouterVue3";
import { useAuthStore } from "../../../../auth/useAuthStore";

export function useGotoCompController() {
  const router = useRouter();
  const authStore = useAuthStore();

  function process(competitionSummaryPublic: ICompetitionSummaryPublic) {
    if (competitionSummaryPublic.options.ui.entryDefaultPanel === "SHOP_ONLY") {
      router.push({
        path:
          "/" +
          LAUNCH_ROUTES_PATHS.SHOP +
          "/" +
          competitionSummaryPublic.compId,
      });
      return;
    }

    const compUrl =
      "/v2/" +
      LAUNCH_ROUTES_PATHS_V2.ENTRY_V2 +
      "/" +
      competitionSummaryPublic.compId;

    let location: RawLocation;
    if (!authStore.isLoggedIn) {
      location = {
        path: "/v2/" + LAUNCH_ROUTES_PATHS_V2.LOGIN_V2,
        query: {
          redirectFrom: compUrl,
        },
      };
    } else {
      location = {
        path: compUrl,
      };
    }

    router.push(location);
  }

  return {
    process,
  };
}
