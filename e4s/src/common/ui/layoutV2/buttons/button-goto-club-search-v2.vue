<template>
  <ButtonGenericV2
    class="e4s-button--100"
    :text="buttonText"
    v-on:click="goToClubSearch"
  />
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import { RawLocation } from "vue-router";
import { useRouter } from "../../../../router/migrateRouterVue3";
import ButtonGenericV2 from "./button-generic-v2.vue";
import { useAuthStore } from "../../../../auth/useAuthStore";
import { LAUNCH_ROUTES_PATHS_V2 } from "../../../../launch/v2/launch-routes-v2";

export default defineComponent({
  name: "ButtonGotoClubSearchV2",
  components: { ButtonGenericV2 },
  props: {
    buttonText: {
      type: String,
      default: () => {
        return "Search";
      },
    },
  },
  setup(
    props: {
      buttonText: string;
    },
    context: SetupContext
  ) {
    let authStore = useAuthStore();
    const router = useRouter();

    function goToClubSearch() {
      const compUrl = "/v2/" + LAUNCH_ROUTES_PATHS_V2.CLUB_SEARCH_V2;

      let location: RawLocation;
      if (!authStore.isLoggedIn) {
        location = {
          path: "/v2/" + LAUNCH_ROUTES_PATHS_V2.LOGIN_V2,
          query: {
            redirectFrom: compUrl,
          },
        };
      } else {
        location = {
          path: compUrl,
        };
      }

      router.push(location);
    }
    return {
      goToClubSearch,
    };
  },
});
</script>
