<template>
  <div
    class="
      e4s-flex-row e4s-flex-nowrap e4s-full-width e4s-justify-flex-center
      e4s-gap--standard
    "
    style="align-items: center"
  >
    <div
      class="e4s-flex-column e4s-justify-flex-center e4s-pagination--wrapper"
    >
      <div
        title="Go to first page"
        v-on:click="onFirstPageClicked"
        class="
          e4s-pagination--container
          e4s-flex-row e4s-flex-nowrap e4s-justify-flex-center
        "
      >
        <div class="e4s-pagination--icon-container e4s-justify-flex-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="12"
            viewBox="0 0 14 12"
          >
            <g transform="translate(-838 -1359)">
              <path
                d="M12,16a1,1,0,0,1-.707-.293l-5-5a1,1,0,0,1,0-1.414l5-5a1,1,0,1,1,1.414,1.414L8.414,10l4.293,4.293A1,1,0,0,1,12,16Z"
                transform="translate(832 1355)"
              />
              <path
                d="M12,16a1,1,0,0,1-.707-.293l-5-5a1,1,0,0,1,0-1.414l5-5a1,1,0,1,1,1.414,1.414L8.414,10l4.293,4.293A1,1,0,0,1,12,16Z"
                transform="translate(839 1355)"
              />
            </g>
          </svg>
        </div>
      </div>
    </div>

    <div
      title="Go to previous page"
      v-on:click="onPreviousPageClicked"
      class="
        e4s-pagination--container
        e4s-flex-row e4s-flex-nowrap e4s-justify-flex-center
      "
    >
      <div class="e4s-pagination--icon-container e4s-justify-flex-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="7"
          height="12"
          viewBox="0 0 7 12"
        >
          <path
            d="M12,16a1,1,0,0,1-.707-.293l-5-5a1,1,0,0,1,0-1.414l5-5a1,1,0,1,1,1.414,1.414L8.414,10l4.293,4.293A1,1,0,0,1,12,16Z"
            transform="translate(-6 -4)"
          />
        </svg>
      </div>
    </div>

    <div class="e4s-flex-row e4s-flex-nowrap e4s-justify-flex-center">
      <select
        class="browser-default"
        v-model="currentPageNumber"
        v-on:change="onPageClicked"
      >
        <option
          v-for="opt in pageNumbers"
          :value="opt"
          v-text="opt"
          :key="opt"
        ></option>
        <!--        <option>1</option>-->
        <!--        <option>2</option>-->
        <!--        <option>3</option>-->
      </select>
    </div>

    <div
      title="Go to next page"
      v-on:click="onNextPageClicked"
      class="
        e4s-pagination--container
        e4s-flex-row e4s-flex-nowrap e4s-justify-flex-center
      "
    >
      <div class="e4s-pagination--icon-container e4s-justify-flex-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="7"
          height="12"
          viewBox="0 0 7 12"
        >
          <path
            d="M8,16a1,1,0,0,1-.707-1.707L11.586,10,7.293,5.707A1,1,0,1,1,8.707,4.293l5,5a1,1,0,0,1,0,1.414l-5,5A1,1,0,0,1,8,16Z"
            transform="translate(-6.999 -4)"
          />
        </svg>
      </div>
    </div>

    <div
      title="Go to last page"
      v-on:click="onLastPageClicked"
      class="
        e4s-pagination--container
        e4s-flex-row e4s-flex-nowrap e4s-justify-flex-center
      "
    >
      <div class="e4s-pagination--icon-container e4s-justify-flex-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="14"
          height="12"
          viewBox="0 0 14 12"
        >
          <g transform="translate(-864.001 -1359)">
            <path
              d="M8,16a1,1,0,0,1-.707-1.707L11.586,10,7.293,5.707A1,1,0,1,1,8.707,4.293l5,5a1,1,0,0,1,0,1.414l-5,5A1,1,0,0,1,8,16Z"
              transform="translate(857.002 1355)"
            />
            <path
              d="M8,16a1,1,0,0,1-.707-1.707L11.586,10,7.293,5.707A1,1,0,1,1,8.707,4.293l5,5a1,1,0,0,1,0,1.414l-5,5A1,1,0,0,1,8,16Z"
              transform="translate(864.002 1355)"
            />
          </g>
        </svg>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  SetupContext,
  PropType,
  computed,
  watch,
} from "@vue/composition-api";
import { IPaging } from "../../../common-models";

export default defineComponent({
  name: "paging-v2",
  components: {},
  props: {
    paging: {
      type: Object as PropType<IPaging>,
      default: () => {
        const defaultPaging: IPaging = {
          page: 1,
          pageSize: 20,
          totalCount: 0,
        };
        return defaultPaging;
      },
    },
  },
  setup(props: { paging: IPaging }, context: SetupContext) {
    const currentPageNumber = ref(1);

    init();

    function init() {
      currentPageNumber.value = props.paging.page;
    }

    watch(
      () => props.paging,
      (newValue: IPaging) => {
        currentPageNumber.value = newValue.page;
      }
    );

    const totalPages = computed(() => {
      if (props.paging.totalCount === 0) {
        return 0;
      }
      return Math.ceil(props.paging.totalCount / props.paging.pageSize);
    });

    const pageNumbers = computed(() => {
      //  If there are 100+ pages, what's the point in a drop down...or any
      //  input type thing, what the user really needs to do is "search"

      // const totalPages = Math.ceil(props.paging.totalCount / props.paging.pageSize);
      const pages = [...Array(totalPages.value)].map(function (item, index) {
        return index + 1;
      });

      return pages.slice(0, 20);
    });

    function onPageClicked() {
      context.emit("onPageClicked", currentPageNumber.value);
    }

    function onFirstPageClicked() {
      currentPageNumber.value = 1;
      onPageClicked();
    }

    function onLastPageClicked() {
      currentPageNumber.value = totalPages.value;
      onPageClicked();
    }

    function onPreviousPageClicked() {
      if (currentPageNumber.value !== 1) {
        currentPageNumber.value--;
        onPageClicked();
      }
    }

    function onNextPageClicked() {
      if (currentPageNumber.value !== totalPages.value) {
        currentPageNumber.value++;
        onPageClicked();
      }
    }

    return {
      currentPageNumber,
      onPageClicked,
      onFirstPageClicked,
      onLastPageClicked,
      onPreviousPageClicked,
      onNextPageClicked,
      totalPages,
      pageNumbers,
    };
  },
});
</script>

<style>
.e4s-pagination--wrapper {
  /*align-items: center;*/
  /*height: 20px;*/
  /*width: 20px;*/
  gap: 8px;
  height: 36px;
}

.e4s-pagination--container {
  /*gap: 8px;*/
  /*height: 100%  NICK took this off;*/
  padding: var(--e4s-pagination--container__padding);
  background: var(--e4s-button--tertiary__background);
  border: var(--e4s-button--tertiary__border);
  border-color: var(--e4s-button--tertiary__border-color);
  border-radius: var(--e4s-button__border-radius);
  cursor: pointer;
  transition: all 0.36s;
}

.e4s-pagination--container:hover,
.e4s-pagination--container:focus {
  background: var(--e4s-button--tertiary__hover-background);
  border-color: var(--e4s-button--tertiary__hover-border-color);
}

.e4s-pagination--container:active {
  background: var(--e4s-button--tertiary__active-background);
  border-color: var(--e4s-button--tertiary__active-border-color);
}

.e4s-pagination--icon-container {
  display: flex;
  flex-direction: row;
  /*gap: 4px;*/
  height: 20px;
  width: 20px;
  align-items: center;
}

.e4s-pagination--wrapper select {
  cursor: pointer;
}

.e4s-pagination--icon-container svg {
  fill: var(--e4s-button--tertiary__text-color);
}
</style>
