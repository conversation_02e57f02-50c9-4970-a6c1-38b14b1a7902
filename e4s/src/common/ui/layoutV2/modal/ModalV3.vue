<template>
  <div class="qazwsxxedc">
    <div class="modal-v2--overlay"></div>
    <div class="modal-v2--wrapper">
      <div
        class="modal-v2--container"
        :class="isFullScreen ? 'modal-v2--container-fullscreen' : ''"
      >
        <!--        <LaunchHeaderBlankV2 v-if="isFullScreen || alwaysShowHeaderBlank" />-->

        <slot name="header"></slot>

        <slot name="body">
          <div class="modal-v2--body-auto-wrapper-x">
            <slot name="body-auto-margin">
              <FormGenericV2
                :form-title="formTitle"
                :form-overview="formOverview"
              >
              </FormGenericV2>
            </slot>
          </div>
        </slot>

        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  SetupContext,
  onBeforeUnmount,
} from "@vue/composition-api";
import LaunchHeaderBlankV2 from "../../../../launch/v2/launch-header-blank-v2.vue";
import FormGenericV2 from "../form/form-generic-v2.vue";

export default defineComponent({
  name: "ModalV3",
  components: { FormGenericV2, LaunchHeaderBlankV2 },
  props: {
    isFullScreen: {
      type: Boolean,
      default: false,
    },
    alwaysShowHeaderBlank: {
      type: Boolean,
      default: false,
    },
    formTitle: {
      type: String,
      default: "",
    },
    formOverview: {
      type: String,
      default: "",
    },
  },
  setup(
    props: { isFullScreen: boolean; alwaysShowHeaderBlank: boolean },
    context: SetupContext
  ) {
    // const cacheOverflow = document.body.style.overflow;
    // document.body.style.overflow = "hidden";

    onBeforeUnmount(() => {
      // document.body.style.overflow = "auto";
    });

    return {};
  },
});
</script>

<style scoped>
.modal-v2--wrapper,
.modal-v2--overlay {
  position: fixed;
  z-index: 9998;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.modal-v2--overlay {
  z-index: 100;
  background-color: rgba(black, 0.85);
}

.modal-v2--wrapper {
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  //pointer-events: none;
}

.modal-v2--container {
  //min-width: 450px;
  position: relative;
  background-color: #fff;
}

.modal-v2--container-fullscreen {
  height: 100%;
  width: 100%;
}
</style>
