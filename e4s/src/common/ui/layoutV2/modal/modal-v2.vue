<template>
  <div class="qazwsxxedc">
    <div class="modal-v2--overlay"></div>
    <!--    <div class="modal-v2&#45;&#45;wrapper">-->
    <div
      class="modal-v2--container"
      :class="isFullScreen ? 'modal-v2--container-fullscreen' : ''"
    >
      <LaunchHeaderBlankV2 v-if="isFullScreen || alwaysShowHeaderBlank" />

      <slot name="header"></slot>

      <slot name="body">
        <div class="modal-v2--body-auto-wrapper-x">
          <slot name="body-auto-margin"> </slot>
        </div>
      </slot>

      <slot name="footer"></slot>
    </div>
    <!--    </div>-->
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  SetupContext,
  onBeforeUnmount,
} from "@vue/composition-api";
import LaunchHeaderBlankV2 from "../../../../launch/v2/launch-header-blank-v2.vue";

export default defineComponent({
  name: "modal-v2",
  components: { LaunchHeaderBlankV2 },
  props: {
    isFullScreen: {
      type: Boolean,
      default: false,
    },
    alwaysShowHeaderBlank: {
      type: Boolean,
      default: false,
    },
    disableScroll: {
      type: Boolean,
      default: false,
    },
  },
  setup(
    props: {
      isFullScreen: boolean;
      alwaysShowHeaderBlank: boolean;
      disableScroll: boolean;
    },
    context: SetupContext
  ) {
    const cacheOverflow = document.body.style.overflow;
    console.log("ModalV2 cacheOverflow", cacheOverflow);
    if (props.disableScroll) {
      document.body.style.overflow = "hidden";
    }
    // document.body.style.overflow = "hidden";

    onBeforeUnmount(() => {
      console.log("ModalV2 onBeforeUnmount setting overflow to 'auto'");
      document.body.style.overflow = "auto";
    });

    return {};
  },
});
</script>

<style scoped>
.modal-v2--overlay {
  position: fixed;
  z-index: 9998;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  overflow: scroll;
}

.modal-v2--overlay {
  z-index: 100;
  background-color: rgba(black, 0.85);
}

.modal-v2--wrapper {
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
}

.modal-v2--container {
  //min-width: 450px;
  z-index: 9999;
  //width: fit-content;
  max-width: var(--e4s-width-controller__width);
  height: fit-content;
  max-height: 85vh;
  position: fixed;
  background-color: #fff;
  inset: 0;
  margin: auto;
  overflow-y: auto;
}

.modal-v2--container-fullscreen {
  max-height: 100vh;
  max-width: 100vw;
  height: 100%;
  width: 100%;
}

@media screen and (max-width: 768px) {
  .modal-v2--container {
    width: 100%;
    max-width: 100%;
    height: 100vh;
    max-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    overflow-y: auto;
  }
}
</style>
