<template>
  <div class="e4s-content-wrapper">
    <div class="e4s-repeatable-grid" v-show="showSection === sections.SUMMARY">
      <div v-for="comp in compsInternal">
        <PublicCompCardHeaderV2
          :competition-summary-public="comp"
          v-on:enterComp="goToComp"
          v-on:showContactOrganiser="proceedShowContactOrganiser"
          v-on:showMoreInfo="showMoreInfoList"
        />
      </div>
    </div>

    <div v-if="showSection === sections.MORE_INFO">
      <PublicListCompBody
        :comp="moreInfoComp"
        class="list-body-wrapper"
        v-on:onShowStatus="openStatus"
        v-on:enterComp="goToComp"
        v-on:showContactOrganiser="proceedShowContactOrganiser"
        v-on:onCloseBody="showSection = sections.SUMMARY"
      >
      </PublicListCompBody>
    </div>



  </div>
</template>

<script lang="ts">
import {defineComponent, PropType, SetupContext} from "@vue/composition-api";

export default defineComponent({
  name: "common-ui-layout-v2-page",
  components: {},
  props: {
    entries: {
      type: Array as PropType<unknown[]>,
      default: () => {
        return [];
      },
    },
  },
  setup(props: {entries: unknown[]}, context: SetupContext) {

  },
});
</script>

<style>
</style>
