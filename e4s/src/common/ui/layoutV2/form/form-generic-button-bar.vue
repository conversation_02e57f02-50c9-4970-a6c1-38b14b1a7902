<template>
  <section
    class="e4s-flex-row e4s-justify-flex-end dat-e4s-form--action-container"
  >
    <slot name="form-bottom-buttons">
      <LoadingSpinnerV2 v-if="isLoading" />
      <slot name="extra-buttons"></slot>
      <ButtonGenericV2
        :disabled="isLoading"
        :text="cancelButtonText"
        button-type="tertiary"
        @click="cancel"
      />
      <ButtonGenericV2
        :disabled="isLoading"
        :text="submitButtonText"
        @click="submit"
      />
      <!--      <button-->
      <!--        :disabled="isLoading"-->
      <!--        class="-->
      <!--          e4s-flex-->
      <!--          align-e4s-flex-center-->
      <!--          e4s-justify-flex-center-->
      <!--          e4s-button&#45;&#45;tertiary-->
      <!--        "-->
      <!--      >-->
      <!--        <span-->
      <!--          class="e4s-body&#45;&#45;100"-->
      <!--          v-on:click="cancel"-->
      <!--          v-text="cancelButtonText"-->
      <!--        ></span>-->
      <!--      </button>-->
      <!--      <button-->
      <!--        :disabled="isLoading"-->
      <!--        class="-->
      <!--          e4s-flex-->
      <!--          align-e4s-flex-center-->
      <!--          e4s-justify-flex-center-->
      <!--          e4s-button&#45;&#45;primary-->
      <!--        "-->
      <!--      >-->
      <!--        <span-->
      <!--          class="e4s-body&#45;&#45;100"-->
      <!--          v-on:click.stop="submit"-->
      <!--          v-text="submitButtonText"-->
      <!--        ></span>-->
      <!--      </button>-->
    </slot>
  </section>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import LoadingSpinnerV2 from "../../loading-spinner-v2.vue";
import ButtonGenericV2 from "../buttons/button-generic-v2.vue";

export default defineComponent({
  name: "form-generic-button-bar",
  components: { ButtonGenericV2, LoadingSpinnerV2 },
  props: {
    submitButtonText: {
      type: String,
      default: () => {
        return "Submit";
      },
    },
    cancelButtonText: {
      type: String,
      default: () => {
        return "Cancel";
      },
    },
    isLoading: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  setup(
    props: {
      submitButtonText: string;
      cancelButtonText: string;
      isLoading: boolean;
    },
    context: SetupContext
  ) {
    function cancel() {
      console.log("form-generic-button-bar.cancel()");
      context.emit("cancel");
    }
    function submit() {
      console.log("form-generic-button-bar.submit()");
      context.emit("submit");
    }

    return { cancel, submit };
  },
});
</script>

<style>
.dat-e4s-form--action-container {
  padding: var(--e4s-form--action-container__padding);
  background-color: var(--e4s-form--action-container__background);
  border-radius: var(--e4s-form--action-container__border-radius);
  gap: 12px;
}
</style>
