/*
import { Ref, ref, UnwrapRef, watch } from "@vue/composition-api";

export function useFormController<T>(
  defaultValue: T,
  modelValue?: UnwrapRef<T | null | undefined> | Ref<T | null | undefined>
) {
  const initialValue = modelValue
    ? modelValue.value
      ? clone(model.value)
      : clone(modelValue)
    : defaultValue;

  // const form = ref<T>(modelValue?.value || defaultValue);
  const form = ref<T>(initialValue);

  // add support for no v-model provided with a conditional
  if (modelValue) {
    // still supporting data change coming DOWN from the parent
    watch(
      modelValue,
      () => {
        form.value = clone(modelValue.value);
      },
      { deep: true }
    );
  }

  function clone(obj: any) {
    return JSON.parse(JSON.stringify(obj));
  }

  function handleSubmit() {
    if (!modelValue) {
      return;
    }
    modelValue.value = clone(form.value);
  }

  // expose the local data and the submit function to the form component
  return {
    form,
    handleSubmit,
  };
}
*/
