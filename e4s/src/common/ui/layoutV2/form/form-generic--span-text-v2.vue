<template>
  <div class="e4s-flex-column e4s-input--container">
    <span class="dat-e4s-input--info-label" v-if="showLabel">
      <label
        class="e4s-input--label e4s-read--label"
        :class="formLabelClass"
        v-text="formLabel"
      >
      </label>
      <div
        v-if="showHelp"
        class="
          e4s-flex-column e4s-flex-center e4s-justify-flex-center
          dat-e4s-input--info-icon-container
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 12 12"
        >
          <path
            d="M12,6A6,6,0,1,1,6,0a6,6,0,0,1,6,6ZM4.928,4.525h-.99A1.84,1.84,0,0,1,6,2.625c1.048,0,2,.548,2,1.68a1.831,1.831,0,0,1-.933,1.543c-.553.419-.758.576-.758,1.115v.266H5.338l-.005-.347a1.708,1.708,0,0,1,.876-1.49c.443-.333.724-.552.724-1.028a.871.871,0,0,0-.986-.876A.957.957,0,0,0,4.928,4.525Zm.938,4.832a.7.7,0,1,1,0-*******,0,1,1,0,1.4Z"
          />
        </svg>
      </div>
    </span>
    <slot name="field">
      <div class="e4s-read-field" v-text="value"></div>
    </slot>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "@vue/composition-api";
import FieldTextV2 from "../fields/field-text-v2.vue";

/**
 * Use this as follows, obv' pass whatever native events needed:
 *       <FormGenericSpanTextV2
 *        form-label="Priority Code"
 *        :value="priorityCode"
 *        />
 */
export default defineComponent({
  name: "FormGenericSpanTextV2",
  components: { FieldTextV2 },
  inheritAttrs: false,
  props: {
    formLabel: {
      type: String,
      default: () => {
        return "";
      },
    },
    formLabelClass: {
      type: String,
      default: () => {
        return "";
      },
    },
    value: {
      type: String,
      default: () => {
        return "";
      },
    },
    showHelp: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    showLabel: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
  },
});
</script>
