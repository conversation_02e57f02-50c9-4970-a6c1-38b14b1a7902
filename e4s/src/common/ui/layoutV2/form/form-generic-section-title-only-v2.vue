<template>
  <h2 :class="'e4s-header--' + titleSize" v-text="sectionTitle"></h2>
</template>

<script lang="ts">
import { defineComponent, PropType } from "@vue/composition-api";
import ButtonGenericV2 from "../buttons/button-generic-v2.vue";
import ButtonGenericBackV2 from "../buttons/button-generic-back-v2.vue";

export default defineComponent({
  name: "form-generic-section-title-only-v2",
  components: { ButtonGenericBackV2, ButtonGenericV2 },
  props: {
    sectionTitle: {
      type: String,
      default: () => {
        return "";
      },
    },
    titleSize: {
      type: String as PropType<"100" | "200" | "300" | "400" | "500">,
      default: () => {
        return "500";
      },
    },
  },
});
</script>
