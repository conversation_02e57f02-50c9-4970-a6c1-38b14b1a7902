<template>
  <div class="e4s-flex-row e4s-justify-flex-row-vert-center">
    <DateEntryMat
      :iso-date="isoDateTimeInternal"
      :is-required-field="isRequiredField"
      :debug="debug"
      v-on:onSelected="onDateSelected"
    >
    </DateEntryMat>
    <TimeEntry
      :iso-date-time="isoDateTimeInternal"
      :minute-interval="minuteInterval"
      :is-required-field="isRequiredField"
      v-on:onSelected="onTimeSelected"
    >
    </TimeEntry>
  </div>
</template>

<script lang="ts">
import { format, parse } from "date-fns";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import DateEntryMat from "./date-entry-mat.vue";
import { CommonService } from "../../common-service";
import TimeEntry from "./time-entry.vue";

@Component({
  name: "date-time-entry",
  components: {
    DateEntryMat,
    TimeEntry,
  },
})
export default class DateTimeEntryV2 extends Vue {
  @Prop({ default: "" }) public debug: boolean;
  @Prop({ default: "" }) public value: string;
  @Prop({ default: "" }) public isoDateTime: string;
  @Prop({ default: 1 }) public readonly minuteInterval: number;
  @Prop({ default: false }) public readonly isRequiredField: boolean;

  public isoDateTimeInternal: string = "";
  public datePart: string = "";
  public timePart: string = "";

  public commonService: CommonService = new CommonService();

  // public created() {
  //   if (this.isoDateTime.length > 0) {
  //     this.isoDateTimeInternal = this.isoDateTime;
  //     this.init();
  //   }
  // }

  /**
   * N.B.  Use v-model or "value".
   * @param isoDateNew
   */
  @Watch("isoDateTime", { immediate: true })
  public inputIsoDateChanged(isoDateNew: string) {
    this.isoDateTimeInternal = isoDateNew;
    this.init();
  }

  @Watch("value", { immediate: true })
  public inputvalueChanged(isoDateNew: string) {
    this.isoDateTimeInternal = isoDateNew;
    this.init();
  }

  public init() {
    //if (this.isoDateTimeInternal.length > 0) {
    this.datePart = format(parse(this.isoDateTimeInternal), "YYYY-MM-DD");
    this.timePart = format(parse(this.isoDateTimeInternal), "HH:mm");
    //}
  }

  public onDateSelected(isoDate: string) {
    if (this.debug) {
      console.log("DateTimeEntry onDateSelected isoDate: " + isoDate);
    }
    if (this.debug) {
      console.log("DateTimeEntry onDateSelected datePart a: " + this.datePart);
    }
    this.datePart = format(parse(isoDate), "YYYY-MM-DD");
    if (this.debug) {
      console.log("DateTimeEntry onDateSelected datePart b: " + this.datePart);
    }
    this.outputResult();
  }

  public onTimeSelected(isoTime: string) {
    this.timePart = isoTime;
    this.outputResult();
  }

  public onChangeTime() {
    this.outputResult();
  }

  public get getTimeDisplayFormat() {
    return "HH:MM";
  }

  public outputResult() {
    if (this.debug) {
      console.log(
        "DateTimeEntry outputResult datePart | timePart b: " +
          this.datePart +
          " : " +
          this.timePart
      );
    }
    const result = this.commonService.convertToIsoDateTimeWithOffset(
      this.datePart,
      this.timePart
    );
    if (this.debug) {
      console.log("DateTimeEntry outputResult datePart b: " + result);
    }
    if (result.toUpperCase() !== "INVALID DATE") {
      this.isoDateTimeInternal = result;
      this.$emit("onSelected", result);
      this.$emit("input", result);
      this.$emit("onChange", result);
    }
  }
}
</script>
