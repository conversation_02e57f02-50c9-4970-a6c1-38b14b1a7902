<template>
  <div class="e4s-flex-row e4s-justify-flex-row-vert-center">
    <!--        B isoDate {{isoDate}} B isoDateInternal {{isoDateInternal}}-->
    <!--        B isoDateInternal {{isoDateInternal}}-->
    <input
      style="display: none"
      type="text"
      placeholder=""
      ref="dateEntry"
      :id="id"
    />
    <div class="date-width">
      <date-entry-type
        :iso-date="isoDateInternal"
        :is-required-field="isRequiredField"
        v-on:onInputChanged="onManualDateChanged"
      >
      </date-entry-type>
    </div>
    <div class="time-width">
      <i
        v-on:click="openDatePicker"
        class="small material-icons e4s-force-inline-block"
        >date_range</i
      >
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { CommonService } from "../../common-service";
import { format, parse } from "date-fns";
import DateEntryType from "./date-entry-type.vue";

@Component({
  name: "date-entry-mat",
  components: {
    "date-entry-type": DateEntryType,
  },
})
export default class DateEntryMat extends Vue {
  @Prop({ default: "" }) public debug: boolean;
  @Prop({ default: "" }) public readonly isoDate: string;
  @Prop({ default: false }) public readonly isRequiredField: boolean;

  public isoDateInternal: string = "";

  public commonService: CommonService = new CommonService();
  public dateDisplay: string = "";
  public isValid: boolean = true;

  public instance: any;
  public id: string = Math.random().toString(36).substring(2) + "date-picker";

  public created() {
    this.isoDateInternal = this.checkDateCorrect(this.isoDate);
  }

  public mounted() {
    this.isoDateInternal = this.checkDateCorrect(this.isoDate);

    this.instance = (window as any).M.Datepicker.init(this.$refs.dateEntry, {
      format: "dd/mm/yyyy",
      autoClose: true,
      onSelect: (val: any) => {
        this.dateDisplay = format(
          val,
          this.commonService.getDateDisplayFormat()
        );
        const output = this.commonService.convertUserDateTimeInputToIso(
          this.dateDisplay,
          "00:00"
        );
        this.isoDateInternal = output;
        this.$emit("onSelected", output);
      },
    });
    this.init();
  }

  @Watch("isoDate")
  public inputIsoDateChanged(isoDateNew: string) {
    this.isoDateInternal = this.checkDateCorrect(this.isoDate);
    this.init();
  }

  public checkDateCorrect(isoDate: string): string {
    if (!isoDate || isoDate.length === 0) {
      return "";
    }
    return isoDate.indexOf("T") > -1 ? isoDate.split("T")[0] : isoDate;
  }

  public init() {
    let dateDisplay = "";
    if (this.isoDateInternal.length > 0) {
      // const arrayDate: string[] = this.isoDateInternal.split("-");
      // this.instance.setDate(
      //   new Date(
      //     Number(arrayDate[0]),
      //     Number(arrayDate[1]),
      //     Number(arrayDate[2])
      //   ),
      //   true
      // );
      this.instance.setDate(parse(this.isoDateInternal));

      dateDisplay = this.commonService.transformIsoForInputField(
        this.isoDateInternal,
        true,
        false
      );
    }
    this.dateDisplay = dateDisplay;
  }

  public openDatePicker() {
    this.instance.open();
  }

  public onManualDateChanged(isoDate: string) {
    this.dateDisplay = isoDate;
    // const arrayDate: string[] = isoDate.split("-");
    // this.instance.setDate(
    //   new Date(
    //     Number(arrayDate[0]),
    //     Number(arrayDate[1]),
    //     Number(arrayDate[2])
    //   ),
    //   true
    // );
    // this.instance.setDate(parse(this.isoDateInternal))
    // this.$emit("onSelected", this.dateDisplay);
    this.instance.setDate(parse(isoDate));
    this.$emit("onSelected", isoDate);
  }

  public onSelected() {
    const output = this.commonService.convertUserDateTimeInputToIso(
      this.dateDisplay,
      "00:00"
    );
    console.log("DateEntryMat.onSelected output: ", output);
    this.$emit("onSelected", output);
  }

  public beforeDestroy() {
    this.instance.destroy();
  }
}
</script>
