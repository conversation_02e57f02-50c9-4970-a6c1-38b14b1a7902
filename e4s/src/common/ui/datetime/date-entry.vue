<template>
  <div>
    <select
      class="browser-default"
      style="width: auto; display: inline-block"
      id="dob-day"
      v-model="dayInput"
      v-on:change="inputChanged"
    >
      <option value="" disabled selected hidden>Day</option>
      <option v-for="day in days" :key="day" :value="day">{{ day }}</option>
    </select>
    <select
      class="browser-default"
      style="width: auto; display: inline-block"
      v-model="monthInput"
      v-on:change="inputChanged"
    >
      <option value="" disabled selected hidden>Month</option>
      <option v-for="month in months" :key="month.value" :value="month.value">
        {{ month.label }}
      </option>
    </select>
    <select
      class="browser-default"
      style="width: auto; display: inline-block"
      v-model="yearInput"
      v-on:change="inputChanged"
    >
      <option value="" disabled selected hidden>Year</option>
      <option v-for="year in years" :key="year" :value="year">
        {{ year }}
      </option>
    </select>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { IObjectKey } from "../../common-models";

@Component({
  name: "date-entry",
})
export default class DateEntry extends Vue {
  @Prop({ default: "" })
  public readonly value: string;

  @Prop({
    default: false,
  })
  public readonly allowNullReset: boolean;

  @Prop({
    default: "",
    required: false,
  })
  public readonly isoDate: string;

  @Prop({
    default: "DD/MM/YYYY",
  })
  public readonly userInputFormat: string;

  @Prop({ default: false })
  public readonly debug: boolean;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly restrictToTheseYears: number[];

  public dayInput: string = "";
  public monthInput: string = "";
  public yearInput: string = "";

  public days: string[] = [];
  public months: IObjectKey[] = [
    { value: "01", label: "Jan" },
    { value: "02", label: "Feb" },
    { value: "03", label: "Mar" },
    { value: "04", label: "Apr" },
    { value: "05", label: "May" },
    { value: "06", label: "Jun" },
    { value: "07", label: "Jul" },
    { value: "08", label: "Aug" },
    { value: "09", label: "Sep" },
    { value: "10", label: "Oct" },
    { value: "11", label: "Nov" },
    { value: "12", label: "Dec" },
  ];
  public years: string[] = [];

  public created() {
    if (this.debug) {
      debugger;
    }
    const days: string[] = [];
    for (let i = 1; i < 32; i++) {
      const day = (i + "").length === 1 ? "0" + i : "" + i;
      days.push(day);
    }
    this.days = days;

    this.setUpYears();

    // let yearNow: number = new Date().getFullYear() - 100;
    // let years: string[] = [];
    // if (this.restrictToTheseYears.length > 0) {
    //   years = this.restrictToTheseYears.map( year => year.toString());
    // } else {
    //   for (let i = 0; i < 100; i++) {
    //     yearNow++;
    //     years.push(yearNow + "");
    //   }
    // }
    //
    // this.years = years;

    this.initDate(this.isoDate);
  }

  @Watch("value", { immediate: true })
  public inputValueChanged(isoDateNew: string) {
    this.initDate(isoDateNew);
  }

  @Watch("isoDate")
  public inputIsoDateChanged(isoDateNew: string) {
    this.initDate(isoDateNew);
  }

  @Watch("restrictToTheseYears")
  public restrictToTheseYearsChanged(yearsRestrict: number[]) {
    this.setUpYears();
  }

  public initDate(isoDate: string) {
    if (isoDate.length > 0) {
      const isoSplit: string[] = isoDate.split("T");
      const dateIso = isoSplit[0].split("-");

      this.yearInput = dateIso[0];
      this.monthInput = dateIso[1];
      this.dayInput = dateIso[2];
    } else {
      if (this.allowNullReset) {
        this.yearInput = "";
        this.monthInput = "";
        this.dayInput = "";
      }
    }
  }

  public setUpYears() {
    let yearNow: number = new Date().getFullYear() - 100;
    let years: string[] = [];
    if (this.restrictToTheseYears.length > 0) {
      years = this.restrictToTheseYears.map((year) => year.toString());
    } else {
      for (let i = 0; i < 100; i++) {
        yearNow++;
        years.push(yearNow + "");
      }
    }

    this.years = years;
  }

  public inputChanged() {
    if (
      this.dayInput.length > 0 &&
      this.monthInput.length > 0 &&
      this.yearInput.length > 0
    ) {
      const dateEntryIso: string =
        this.yearInput + "-" + this.monthInput + "-" + this.dayInput;
      this.$emit("onSelected", dateEntryIso);
      this.$emit("input", dateEntryIso);
    }
  }
}
</script>
