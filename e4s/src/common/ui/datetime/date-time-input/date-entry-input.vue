<template>
  <FieldTextV2
    :value="userInput"
    place-holder="dd/mm/yyyy"
    v-on:input="convertUserInputToIsoDate"
    v-on:keyUpEnter="keyUpEnter"
    v-on:keyUp="doKeyUp"
    :error-message="isValid ? '' : 'dd/mm/yyyy'"
    :is-disabled="isDisabled"
  />
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { IsoDate } from "../../../common-models";
import * as DateEntryInputService from "./date-entry-input-service";
import FieldTextV2 from "../../layoutV2/fields/field-text-v2.vue";

export default defineComponent({
  name: "DateEntryInput",
  components: { FieldTextV2 },
  props: {
    dateIso: {
      type: String as PropType<IsoDate>,
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  setup(props: { dateIso: IsoDate; isDisabled: boolean }, context: SetupContext) {
    const userInput = ref("");
    const dateIsoInternal = ref(props.dateIso);
    const isValid = ref(true);
    const yearSplit = ref(
      Number(new Date().getFullYear().toString().slice(0, 2))
    );

    if (props.dateIso.length > 0) {
      convertIsoDateToUserInput(props.dateIso);
    }

    watch(
      () => props.dateIso,
      (newValue: IsoDate) => {
        if (dateIsoInternal.value !== props.dateIso) {
          dateIsoInternal.value = newValue;
          convertIsoDateToUserInput(newValue);
        }
      },
      {
        immediate: true,
      }
    );

    function convertIsoDateToUserInput(isoDate: IsoDate) {
      if (isoDate.length === 0) {
        userInput.value = "";
        return;
      }
      const isoDatePartOnly = isoDate.slice(0, 10)
      userInput.value = DateEntryInputService.convertIsoToUserInput(
        isoDatePartOnly,
        "/"
      );
    }

    function convertUserInputToIsoDate(dateInput: string) {
      // if (dateInput.length === 0) {
      //   isValid.value = true;
      // }
      userInput.value = dateInput;
      isValid.value = DateEntryInputService.isUserInputValid(dateInput);
      context.emit("onValidChanged", isValid.value);

      if (dateInput.length === 0) {
        isValid.value = true;
        context.emit("onReset");
        return;
      }

      if (isValid.value) {
        dateIsoInternal.value = DateEntryInputService.convertUserInputToIso(
          dateInput,
          "/",
          yearSplit.value
        );
        context.emit("onChanged", dateIsoInternal.value);
      }
    }

    function keyUpEnter() {
      context.emit("keyUpEnter", dateIsoInternal.value);
    }

    function doKeyUp(dobValue: string) {
      convertUserInputToIsoDate(dobValue);
      context.emit("keyUp", dateIsoInternal.value);
    }

    return {
      userInput,
      isValid,
      convertUserInputToIsoDate,
      yearSplit,
      dateIsoInternal,
      keyUpEnter,
      doKeyUp
    };
  },
});
</script>
