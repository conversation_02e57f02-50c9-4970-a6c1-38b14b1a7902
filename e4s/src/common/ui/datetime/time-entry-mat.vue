<template>
    <input type="text" ref="timeEntry" :id="id">
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop} from "vue-property-decorator";

    @Component({
        name: "time-entry-mat"
    })
    export default class TimeEntryMat extends Vue {
        @Prop() public isoDate: string;

        public instances: any;
        public  id: string = Math.random().toString(36).substring(2) + "time-picker";

        public mounted() {
            this.instances = (window as any).M.Timepicker.init(this.$refs.timeEntry, {
                onSelect: (val: any) => {
                    // console.log("TimeEntryMat.onSelect()", val);
                }
            });
        }

        public beforeDestroy() {
            this.instances.destroy();
        }
    }
</script>
