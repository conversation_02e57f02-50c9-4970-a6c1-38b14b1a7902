<template>
<!--  :style="{'height': '50vh'}"-->
    <VueEditor :id="quillId"
               v-model="contentInternal"
               :editor-toolbar="customToolbar"
               v-on:text-change="onTextChange"
               v-on:selection-change="onSelectionChange">
    </VueEditor>
</template>

<script lang="ts">
    import Vue from "vue"
    import Component from "vue-class-component";
    import {VueEditor} from "vue2-editor";
    import {Prop, Watch} from "vue-property-decorator"

    @Component({
        name: "html-editor-quill",
        components: {
            VueEditor
        }
    })
    export default class HtmlEditorQuill extends Vue {
        @Prop({
            default: ""
        }) public readonly content: string;

        public editor: any;
        public PREFIX = Math.random().toString(36).substring(2);
        public quillId = this.PREFIX + "-quill";

        public contentInternal: string = "";

        public created() {
            console.log("HtmlEditorQuill.created()...");
            this.contentInternal = this.content;
        }

        public mounted() {
            console.log("HtmlEditorQuill.created()...");
          setTimeout(() => {
            const quilEditor: any = document.getElementsByClassName('ql-editor')[0];
            quilEditor.style.cssText += 'height: 60vh;';
          }, 500);
        }

        @Watch("content")
        public onContentChanged(newValue: string, oldValue: string) {
            if (newValue !== oldValue) {
                this.contentInternal = newValue;
            }
        }

        public get customToolbar() {
            return [
                ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
                // [{ 'header': 1 }, { 'header': 2 }],               // custom button values
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                // [{ 'script': 'sub'}, { 'script': 'super' }],      // superscript/subscript
                // [{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent
                // [{ 'direction': 'rtl' }],                         // text direction

                // [{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],

                // [{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme
                // [{ 'font': [] }],
                // [{ 'align': [] }],

                ['clean']                                         // remove formatting button
            ];
        }

        public onTextChange(changes: any) {
            console.log("HtmlEditorQuill.onTextChanged()...", changes);
            this.$emit("onTextChange", changes);
            this.$emit("onContentChange", this.contentInternal);
        }

        public onSelectionChange(changes: any) {
            console.log("HtmlEditorQuill.onSelectionChange()...", changes);
            this.$emit("onTextChange", changes);
        }
    }
</script>

<style lang="css">
    @import "~vue2-editor/dist/vue2-editor.css";

    /* Import the Quill styles you want */
    @import '~quill/dist/quill.core.css';
    /*@import '~quill/dist/quill.bubble.css';*/
    @import '~quill/dist/quill.snow.css';
</style>

<style>
  .q1-container {
    height: 50vh !important;
  }
</style>
