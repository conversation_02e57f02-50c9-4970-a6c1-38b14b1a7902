<template>
    <div class="preloader-wrapper small active">
        <div class="spinner-layer spinner-red-only">
            <div class="circle-clipper left">
                <div class="circle"></div>
            </div>
            <div class="gap-patch">
                <div class="circle"></div>
            </div>
            <div class="circle-clipper right">
                <div class="circle"></div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";

@Component({
    name: "loading-spinner"
})
export default class LoadingSpinner extends Vue {
}
</script>
