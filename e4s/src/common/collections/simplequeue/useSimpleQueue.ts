export interface ISimpleQueueState<Data> {
  events: Data[];
  counter: number;
}

const simpleQueueStateGlobal: ISimpleQueueState<any> = {
  events: [],
  counter: 0,
};

export function useSimpleQueue<Data>(
  useGlobalState: boolean,
  addElementsToEnd = true
) {
  let maxSize = 15;
  let addAtEnd = addElementsToEnd;

  const simpleQueueStateInternal: ISimpleQueueState<any> = {
    events: [],
    counter: 0,
  };

  const simpleQueueState: ISimpleQueueState<Data> = useGlobalState
    ? simpleQueueStateGlobal
    : simpleQueueStateInternal;

  function addMessage(logging: Data): void {
    if (simpleQueueState.events.length >= maxSize) {
      addAtEnd ? simpleQueueState.events.shift() : simpleQueueState.events.pop();
    }

    simpleQueueState.counter++;
    addAtEnd
      ? simpleQueueState.events.push(logging)
      : simpleQueueState.events.unshift(logging);
  }

  function setMaxSize(size: number) {
    maxSize = size;
  }

  function setAddElementsToEnd(toEnd: boolean) {
    addAtEnd = toEnd;
  }

  const state: ISimpleQueueState<any> = simpleQueueState;

  return {
    state,
    addMessage,
    setMaxSize,
    setAddElementsToEnd,
  };
}
