import {Collection} from "./collection"

export interface IQueue<T> {
  enqueue(item: T): void;
  dequeue(): T | undefined;
  size(): number;
}

export class QueueCollection<T> extends Collection<T> implements IQueue<T> {
  constructor(private capacity: number = Infinity, private allowCapacityPurge = true) {
    super();
  }
  enqueue(item: T): void {
    if (this.isFull()) {
      if (this.allowCapacityPurge) {
        this.dequeue();
      } else {
        throw Error("Queue has reached max capacity, you cannot add more items");
      }
    }
    // In the derived class, we can access protected properties of the abstract class
    this.storage.push(item);
  }
  dequeue(): T | undefined {
    return this.storage.shift();
  }

  isFull(): boolean {
    return this.capacity === this.size();
  }
}
