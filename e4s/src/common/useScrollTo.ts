export interface ScrollToOptions {
  toMiddleOfScreen: boolean;
}

export function useScrollTo() {
  // const scrollTo = (element: HTMLElement) => {
  //   element.scrollIntoView({
  //     behavior: 'smooth',
  //     block: 'start',
  //   });
  // };

  function scrollTo(id: string, options?: ScrollToOptions) {
    const element = document.getElementById(id);
    if (element) {
      if (options) {
        if (options.toMiddleOfScreen) {
          const targetRect = element.getBoundingClientRect(); // Position relative to the viewport
          const scrollOffset = window.scrollY; // Current scroll position
          const viewportHeight = window.innerHeight; // Height of the viewport

          // Centering calculation
          const targetMiddle =
            targetRect.top +
            scrollOffset -
            viewportHeight / 2 +
            targetRect.height / 2;

          window.scrollTo({
            top: targetMiddle,
            behavior: "smooth",
          });
          return;
        }
      }

      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  }

  return { scrollTo };
}
