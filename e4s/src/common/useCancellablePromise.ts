export function useCancellablePromise() {
  const controller: AbortController = new AbortController();
  const signal: AbortSignal = controller.signal;
  let longRunningTaskTimeout: number = 0;
  let resultSuccess: boolean = false;
  let resultReturned: boolean = false;
  // let result: unknown = undefined;

  function doLongRunningTask(
    someFunc: () => Promise<unknown>,
    waitForTaskToCompleteMilliSecs: number = 4000
  ): Promise<unknown> {

    resultSuccess = false;
    resultReturned = false;

    if (signal.aborted) {
      console.log("longRunningAsync aborted at " + new Date().toISOString());
      return Promise.reject(-1);
    }
    return new Promise<unknown>(function (resolve, reject) {
      someFunc().then((resp: unknown) => {
        console.log("doLongRunningTask someFunc got result");
        if (!resultReturned) {
          console.log(
            "A doLongRunningTask someFunc got result...within: " +
              waitForTaskToCompleteMilliSecs
          );
          resultSuccess = true;
          resultReturned = true;
          window.clearTimeout(longRunningTaskTimeout);
          resolve(resp);
        } else {
          console.log(
            "B doLongRunningTask someFunc got result...outside: " +
              waitForTaskToCompleteMilliSecs +
              " Do NOT do anything."
          );
        }
      });

      longRunningTaskTimeout = window.setTimeout(() => {
        console.log("doLongRunningTask setTimeout " + new Date().toISOString());
        if (resultSuccess) {
          console.log("doLongRunningTask setTimeout someFunc already returned response, do nothing.");
        } else {
          console.log("doLongRunningTask setTimeout someFunc NOT returned response, this is the EXIT route.");
          // resolve("EXIT_ROUTE_RESPONSE");
          controller.abort();
        }
        resultReturned = true;
      }, waitForTaskToCompleteMilliSecs);

      // Listen for abort event on signal
      signal.addEventListener("abort", () => {
        console.log("doLongRunningTask signal.abort....");
        window.clearTimeout(longRunningTaskTimeout);
        // reject(new DOMException("Aborted!!", "AbortError"));
        resolve("ABORT_CONTROLLER_RESPONSE");
      });
    });
  }

  /**
   * Could also cancel externally if required.
   */
  function cancelLongRunningTask() {
    controller.abort();
  }

  return {
    doLongRunningTask,
    cancelLongRunningTask
  };
}
