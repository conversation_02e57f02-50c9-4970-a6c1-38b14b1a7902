import { ref, computed, onMounted, onUnmounted } from "@vue/composition-api";

export function useIsMobile(customBreakpoint?: number) {
  // Default mobile breakpoint (should match your CSS breakpoint)
  const mobileBreakpoint = customBreakpoint || 768;
  
  // Reactive reference for mobile device detection
  const screenWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1024);

  // Function to check if device is mobile based on screen width
  const checkIfMobile = () => {
    if (typeof window !== 'undefined') {
      screenWidth.value = window.innerWidth;
    }
  };

  // Add and remove event listeners for window resize
  onMounted(() => {
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', checkIfMobile);
      // Initial check
      checkIfMobile();
    }
  });

  onUnmounted(() => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', checkIfMobile);
    }
  });

  // Computed property to check if the device is mobile
  const isMobileDevice = computed(() => {
    return screenWidth.value < mobileBreakpoint;
  });

  return {
    screenWidth,
    isMobileDevice
  };
}