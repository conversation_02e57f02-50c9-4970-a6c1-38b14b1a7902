import {AgeGroupService} from "./agegroup-service";
import {iAgeGroupMasters} from "./agegroup-data-mock"
import {simpleClone} from "../common/common-service-utils"
// import {IAgeGroup, IAgeGroupOption} from "./agegroup-models";
// import {ValidationController} from "../validation/validation-controller";

const ageGroupService = new AgeGroupService();

describe("agegroup-builder", () => {

    test("addAgeGroup array empty", () => {
        expect(1).toBe(1);
    });

    test("isDefaultAgeGroup", () => {
        let res;
        let data;

        data = simpleClone(iAgeGroupMasters)
        expect(data.options[0].base).toBe(2);
        res = ageGroupService.isDefaultAgeGroup(data, "EA")
        expect(res).toBe(true);


        data = simpleClone(iAgeGroupMasters)
        expect(data.options[0].aocode).toBe("EA");
        expect(data.options[0].base).toBe(2);

        data.options[0].base = 1;
        res = ageGroupService.isDefaultAgeGroup(data, "EA")
        expect(res).toBe(true);
    });

    // const ageGroupService = new AgeGroupService();
    //
    // test("addAgeGroup array empty", () => {
    //     const ageGroup: IAgeGroup = {
    //         id: 1,
    //         name: "U11"
    //     } as IAgeGroup;
    //     const result = ageGroupService.addAgeGroup(ageGroup, []);
    //     // console.log("", result);
    //     expect(result.length).toBe(1);
    // });
    //
    // test("addAgeGroup array", () => {
    //     const ageGroup: IAgeGroup = {
    //         id: 1,
    //         name: "U11"
    //     } as IAgeGroup;
    //     const ageGroups = [
    //         {
    //             id: 1,
    //             name: "U11"
    //         } as IAgeGroup,
    //         {
    //             id: 2,
    //             name: "U12"
    //         } as IAgeGroup
    //     ];
    //
    //     const result = ageGroupService.addAgeGroup(ageGroup, ageGroups);
    //     expect(result.length).toBe(2);
    // });
    //
    // test("sortAgeGroups", () => {
    //     // const ageGroup: IAgeGroup = {
    //     //     id: 1,
    //     //     name: "U11"
    //     // } as IAgeGroup;
    //     const ageGroups = [
    //         {
    //             id: 1,
    //             name: "U11",
    //             minAge: 10
    //         } as IAgeGroup,
    //         {
    //             id: 2,
    //             name: "U12",
    //             minAge: 11
    //         } as IAgeGroup,
    //         {
    //             id: 3,
    //             name: "U9",
    //             minAge: 8
    //         } as IAgeGroup
    //     ];
    //
    //     const result = ageGroupService.sortAgeGroups(ageGroups);
    //     expect(result.length).toBe(3);
    //     expect(result[0].id).toBe(3);
    // });
    //
    // test("isAgeGroupDefaultForAoCode", () => {
    //     let ageGroup = {
    //             id: 1,
    //             name: "U11",
    //             minAge: 10
    //         } as IAgeGroup;
    //
    //     let result = ageGroupService.isAgeGroupDefaultForAoCode(ageGroup, "IRL");
    //     expect(result).toBe(false);
    //
    //     ageGroup = {
    //         id: 1,
    //         name: "U11",
    //         minAge: 10,
    //         options: [] as IAgeGroupOption[]
    //     } as IAgeGroup;
    //
    //     result = ageGroupService.isAgeGroupDefaultForAoCode(ageGroup, "IRL");
    //     expect(result).toBe(false);
    //
    //     ageGroup = {
    //         id: 1,
    //         name: "U11",
    //         minAge: 10,
    //         options: [
    //             {
    //                 aocode: "IRL"
    //             } as IAgeGroupOption
    //         ] as IAgeGroupOption[]
    //     } as IAgeGroup;
    //
    //     result = ageGroupService.isAgeGroupDefaultForAoCode(ageGroup, "IRL");
    //     expect(result).toBe(false);
    //
    //     ageGroup = {
    //         id: 1,
    //         name: "U11",
    //         minAge: 10,
    //         options: [
    //             {
    //                 aocode: "IRL",
    //                 default: true
    //             } as IAgeGroupOption
    //         ] as IAgeGroupOption[]
    //     } as IAgeGroup;
    //
    //     result = ageGroupService.isAgeGroupDefaultForAoCode(ageGroup, "IRL");
    //     expect(result).toBe(true);
    //
    //     ageGroup = {
    //         id: 1,
    //         name: "U11",
    //         minAge: 10,
    //         options: [
    //             {
    //                 aocode: "ANI",
    //                 default: true
    //             } as IAgeGroupOption,
    //             {
    //                 aocode: "IRL",
    //                 default: true
    //             } as IAgeGroupOption
    //         ] as IAgeGroupOption[]
    //     } as IAgeGroup;
    //
    //     result = ageGroupService.isAgeGroupDefaultForAoCode(ageGroup, "IRL");
    //     expect(result).toBe(true);
    //
    //     ageGroup = {
    //         id: 1,
    //         name: "U11",
    //         minAge: 10,
    //         options: [
    //             {aocode: "IRL", default: true} as IAgeGroupOption,
    //             {aocode: "ANI", default: true} as IAgeGroupOption
    //         ] as IAgeGroupOption[]
    //     } as IAgeGroup;
    //
    //     result = ageGroupService.isAgeGroupDefaultForAoCode(ageGroup, "IRL");
    //     expect(result).toBe(true);
    //
    // });
    //
    // test("isDateInputOk", () => {
    //     expect(ageGroupService.isDayInputOk("0")).toBe(true);
    //     expect(ageGroupService.isDayInputOk("1")).toBe(true);
    //     expect(ageGroupService.isDayInputOk("21")).toBe(true);
    //     expect(ageGroupService.isDayInputOk("31")).toBe(true);
    //
    //     expect(ageGroupService.isDayInputOk("32")).toBe(false);
    //     expect(ageGroupService.isDayInputOk("-1")).toBe(false);
    //     expect(ageGroupService.isDayInputOk("1.1")).toBe(false);
    // });
    //
    // test("isMonthInputOk", () => {
    //     expect(ageGroupService.isMonthInputOk("13")).toBe(false);
    //     expect(ageGroupService.isMonthInputOk("11.5")).toBe(false);
    //     expect(ageGroupService.isMonthInputOk("-11.5")).toBe(false);
    //
    //     expect(ageGroupService.isMonthInputOk("0")).toBe(true);
    //     expect(ageGroupService.isMonthInputOk("1")).toBe(true);
    //     expect(ageGroupService.isMonthInputOk("12")).toBe(true);
    // });
    //
    // test("isAgeOk", () => {
    //     expect(ageGroupService.isAgeOk("1")).toBe(true);
    //     expect(ageGroupService.isAgeOk("13")).toBe(true);
    //     expect(ageGroupService.isAgeOk("99")).toBe(true);
    //
    //     expect(ageGroupService.isAgeOk("0")).toBe(false);
    //     expect(ageGroupService.isAgeOk("-1")).toBe(false);
    //     expect(ageGroupService.isAgeOk("1.1")).toBe(false);
    //     expect(ageGroupService.isAgeOk("120")).toBe(false);
    // });
    //
    // test("isYearRange", () => {
    //     const ageGroup: IAgeGroup = ageGroupService.factoryGetAgeGroup();
    //     expect(ageGroupService.isYearRange(ageGroup)).toBe(false);
    //
    //     ageGroup.maxAge = 4;
    //     ageGroup.minAge = 4;
    //     expect(ageGroupService.isYearRange(ageGroup)).toBe(false);
    //
    //     ageGroup.maxAge = 4;
    //     ageGroup.minAge = 3;
    //     expect(ageGroupService.isYearRange(ageGroup)).toBe(true);
    // });
    //
    // test("validate", () => {
    //     const ageGroup: IAgeGroup = ageGroupService.factoryGetAgeGroup();
    //     const validationController: ValidationController = new ValidationController();
    //
    //     validationController.setErrors(ageGroupService.validate(ageGroup));
    //     expect(validationController.isValid).toBe(false);
    //     expect(validationController.hasError("name")).toBe(true);
    //
    //     ageGroup.name = "test";
    //     ageGroup.maxAge = 4;
    //     ageGroup.minAge = 4;
    //
    //     validationController.setErrors(ageGroupService.validate(ageGroup));
    //     expect(validationController.isValid).toBe(true);
    //     expect(validationController.hasError("name")).toBe(false);
    //
    //     ageGroup.name = "test";
    //     ageGroup.maxAge = 4;
    //     ageGroup.minAge = 44;
    //
    //     validationController.setErrors(ageGroupService.validate(ageGroup));
    //     expect(validationController.isValid).toBe(false);
    //     expect(validationController.hasError("minAge")).toBe(true);
    //
    //     ageGroup.name = "test";
    //     ageGroup.maxAge = 5;
    //     ageGroup.minAge = 4;
    //
    //     validationController.setErrors(ageGroupService.validate(ageGroup));
    //     expect(validationController.isValid).toBe(true);
    //     expect(validationController.hasError("minAge")).toBe(false);
    //
    // });
    //
    //
    // test("preProcessForSubmit", () => {
    //     let ageGroup: IAgeGroup = ageGroupService.factoryGetAgeGroup();
    //     const validationController: ValidationController = new ValidationController();
    //
    //     ageGroup.name = "test";
    //     ageGroup.maxAge = 7;
    //     ageGroup = ageGroupService.preProcessForSubmit(ageGroup);
    //     expect(ageGroup.minAge).toBe(7);
    //     validationController.setErrors(ageGroupService.validate(ageGroup));
    //     expect(validationController.isValid).toBe(true);
    //     expect(validationController.hasError("name")).toBe(false);
    //
    // });
    //
    // test("getAgeGroupsFromDefaults", () => {
    //     const ageGroupsSource: IAgeGroup[] = [
    //         {
    //             ...ageGroupService.factoryGetAgeGroup(),
    //             id: 1,
    //             name: "one"
    //         },
    //         {
    //             ...ageGroupService.factoryGetAgeGroup(),
    //             id: 2
    //         },
    //         {
    //             ...ageGroupService.factoryGetAgeGroup(),
    //             id: 3,
    //             name: "three",
    //             keyName: "three source"
    //         },
    //         {
    //             ...ageGroupService.factoryGetAgeGroup(),
    //             id: 4
    //         }
    //     ];
    //
    //     const ageGroupsChecked: IAgeGroup[] = [
    //         {
    //             ...ageGroupService.factoryGetAgeGroup(),
    //             id: 1
    //         },
    //         {
    //             ...ageGroupService.factoryGetAgeGroup(),
    //             id: 3,
    //             keyName: "three checked"
    //         }
    //     ];
    //
    //     const result = ageGroupService.getAgeGroupsFromDefaults(ageGroupsSource, ageGroupsChecked);
    //     expect(result.length).toBe(2);
    //
    //     const resultThree = result.filter((ag) => {
    //         return ag.id === 3;
    //     });
    //     expect(resultThree.length).toBe(1);
    //     expect(resultThree[0].keyName).toBe("three source");
    // });

});



