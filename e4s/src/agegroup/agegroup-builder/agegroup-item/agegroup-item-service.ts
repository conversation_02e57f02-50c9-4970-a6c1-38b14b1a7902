import { IAgeGroup, IAgeGroupCompCoverageModel } from "../../agegroup-models";
import { format, parse } from "date-fns";

export interface IAgeGroupItemLayout {
  id: number;
  title: string;
  yearSpread: string;
  dateRange: string;
}

export function createAgeGroupItemLayout(
  ageGroup: IAgeGroup | IAgeGroupCompCoverageModel,
  isAdmin: boolean
): IAgeGroupItemLayout {
  if ((ageGroup as IAgeGroupCompCoverageModel).fromDate) {
    const ageGroupCompCoverageModel: IAgeGroupCompCoverageModel =
      ageGroup as IAgeGroupCompCoverageModel;

    return {
      id: ageGroupCompCoverageModel.id,
      title: ageGroupCompCoverageModel.name,
      yearSpread:
        "[" +
        ageGroupCompCoverageModel.minAge +
        "-" +
        ageGroupCompCoverageModel.maxAge +
        "]",
      dateRange:
        format(parse(ageGroupCompCoverageModel.fromDate), "Do MMM YYYY") +
        " to " +
        format(parse(ageGroupCompCoverageModel.toDate), "Do MMM YYYY"),
    };
  }

  // return "(" + ageGroup.minAge + "-" + ageGroup.maxAge + ")";
  return {
    id: ageGroup.id,
    title: ageGroup.name,
    yearSpread: "[" + ageGroup.minAge + "-" + ageGroup.maxAge + "]",
    dateRange: "",
  };
}
