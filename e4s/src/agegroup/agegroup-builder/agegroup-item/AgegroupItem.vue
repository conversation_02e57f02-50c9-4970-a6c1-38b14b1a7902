<template>
  <label class="input-checkbox-v2--label">
    <input
      type="checkbox"
      class="browser-default e4s-input-field input-checkbox-v2--input"
      style="accent-color: var(--e4s-button--primary__background)"
      :value="ageGroup"
      v-on:change="ageGroupsSelected"
      v-model="checkAgeGroupsInternal"
    />
    <div
      class="e4s-flex-column"
      :class="
        isDefaultAgeGroup(ageGroup)
          ? 'agegroup-checkbox--default'
          : 'agegroup-checkbox--non-default'
      "
    >
      <div class="e4s-flex-row">
        <div v-text="'x-' + ageGroupItemLayout.title"></div>
      </div>
      <div v-text="ageGroupItemLayout.dateRange"></div>
    </div>
  </label>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { IAgeGroup, IAgeGroupCompCoverageModel } from "../../agegroup-models";
import {
  createAgeGroupItemLayout,
  IAgeGroupItemLayout,
} from "./agegroup-item-service";
import { simpleClone } from "../../../common/common-service-utils";

export default defineComponent({
  name: "AgegroupItem",
  components: {},
  props: {
    ageGroup: {
      type: Object as PropType<IAgeGroup | IAgeGroupCompCoverageModel>,
      required: true,
    },
    checkedAgeGroups: {
      type: Array as PropType<(IAgeGroup | IAgeGroupCompCoverageModel)[]>,
      required: true,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
  },
  setup(
    props: {
      ageGroup: IAgeGroup | IAgeGroupCompCoverageModel;
      checkedAgeGroups: (IAgeGroup | IAgeGroupCompCoverageModel)[];
      isAdmin: boolean;
    },
    context: SetupContext
  ) {
    const ageGroupItemLayout = ref<IAgeGroupItemLayout>({
      id: 0,
      title: "",
      yearSpread: "",
      dateRange: "",
    });

    const checkAgeGroupsInternal = ref(simpleClone(props.checkedAgeGroups));

    watch(
      () => props.ageGroup,
      (newValue) => {
        ageGroupItemLayout.value = createAgeGroupItemLayout(
          newValue,
          props.isAdmin
        );
      },
      {
        immediate: true,
      }
    );

    watch(
      () => props.checkedAgeGroups,
      (newValue) => {
        checkAgeGroupsInternal.value = simpleClone(newValue);
      },
      {
        immediate: true,
      }
    );

    function ageGroupsSelected() {
      context.emit("input", props.checkedAgeGroups);
      context.emit("onChange", props.checkedAgeGroups);
    }

    function isDefaultAgeGroup(
      ageGroup: IAgeGroup | IAgeGroupCompCoverageModel
    ) {
      return true;
    }

    return {
      ageGroupItemLayout,
      ageGroupsSelected,
      checkAgeGroupsInternal,
      isDefaultAgeGroup,
    };
  },
});
</script>
