hi
<template>
  <div>
    <FormGenericFieldGridV2 class="dat-e4s-form-input-control--grid-2-col">
      <template slot="content">
        <div class="e4s-header--400">Registration System Athlete Details</div>
        <div class="e4s-vertical-spacer-standard"></div>

        <FormGenericInputTextV2
          form-label="Forename"
          v-model="athlete.firstName"
          :is-disabled="true"
        />
        <FormGenericInputTextV2
          form-label="Surname"
          v-model="athlete.surName"
          :is-disabled="true"
        />

        <FormGenericInputTextV2
          form-label="Gender"
          v-model="athlete.gender"
          :is-disabled="true"
        />

        <FormGenericInputTemplateV2 form-label="Date of Birth">
          <template slot="field">
            <DateHuman :date-iso="athlete.dob" />
          </template>
        </FormGenericInputTemplateV2>

        <FormGenericInputTextV2
          form-label="Club"
          v-model="athlete.clubName"
          :is-disabled="true"
        />
        <FormGenericInputTextV2
          form-label="County"
          v-model="athlete.county"
          :is-disabled="true"
        />

        <FormGenericInputTextV2
          form-label="Region"
          v-model="athlete.region"
          :is-disabled="true"
        />
        <FormGenericInputTextV2
          form-label="Club 2"
          v-model="athlete.club2Name"
          :is-disabled="true"
        />
      </template>
    </FormGenericFieldGridV2>

    <hr class="dat-e4s-hr-only" />
    <div class="e4s-vertical-spacer-standard"></div>
    <div class="e4s-header--400">Registration Details</div>

    <FormGenericFieldGridV2 class="dat-e4s-form-input-control--grid-2-col">
      <template slot="content">
        <FormGenericInputTextV2
          form-label="URN"
          v-model="athlete.urn"
          :is-disabled="true"
        />
        <FormGenericInputTemplateV2 form-label="Registration Expires">
          <template slot="field">
            <DateHuman
              :date-iso="athlete.activeEndDate"
              class="e4s-flex-grow"
            />
          </template>
        </FormGenericInputTemplateV2>

        <FormGenericInputTextV2
          form-label="Registration Status"
          v-model="athlete.status"
          :is-disabled="true"
        />

        <FormGenericInputTextV2
          form-label="Last Updated"
          v-model="athlete.lastUpdated"
          :is-disabled="true"
        />

        <FormGenericInputTextV2
          form-label="System"
          v-model="athlete.system"
          :is-disabled="true"
        />
      </template>
    </FormGenericFieldGridV2>

    <!--    <FormGenericFieldGridV2>-->
    <!--      <template slot="content">-->
    <!--        <FormGenericInputTextV2-->
    <!--          form-label="Registration Status"-->
    <!--          v-model="athlete.status"-->
    <!--          :is-disabled="true"-->
    <!--        />-->
    <!--      </template>-->
    <!--    </FormGenericFieldGridV2>-->
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { IEventMasterAthlete } from "./eventmaster-models";
import FormGenericFieldGridV2 from "../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputTextV2 from "../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import DateOfBirthV2 from "../v2/form/DateOfBirthV2.vue";
import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import DateHuman from "../../common/ui/layoutV2/fields/date-human.vue";
import FieldTextV2 from "../../common/ui/layoutV2/fields/field-text-v2.vue";

export default defineComponent({
  name: "EventMasterAthlete",
  components: {
    FieldTextV2,
    DateHuman,
    FormGenericInputTemplateV2,
    DateOfBirthV2,
    FormGenericInputTextV2,
    FormGenericFieldGridV2,
  },
  props: {
    athlete: {
      type: Object as PropType<IEventMasterAthlete>,
      default: () => {
        return {
          system: "IRL",
          status: "INACTIVE",
          dob: "",
          clubId: 0,
          clubName: "",
          county: "",
          region: "",
          club2Id: 0,
          club2Name: "",
          firstName: "",
          surName: "",
        } as IEventMasterAthlete;
      },
    },
  },
  setup(props: { athlete: IEventMasterAthlete }, context: SetupContext) {
    return {};
  },
});
</script>
