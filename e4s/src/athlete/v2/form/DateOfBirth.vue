<template>
  <div class="e4s-flex-row e4s-gap--standard e4s-flex-center">
    <DateEntry
      :iso-date="dob"
      :restrict-to-these-years="compAgeGroupYears"
      :allow-null-reset="true"
      v-on:onSelected="onSelected"
    />

    <a
      href="#"
      v-if="competitionBase.id > 0"
      v-on:click.prevent="toggleAgeGroupYears"
      v-text="
        compAgeGroupYears.length === 0 ? 'Show Comp Years' : 'Show All Years'
      "
    ></a>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext, watch,
} from "@vue/composition-api"
import DateEntry from "../../../common/ui/datetime/date-entry.vue";
import { IsoDate } from "../../../common/common-models";
import { ICompetitionBase } from "../../../competition/competition-models";
import { factoryCompetitionBase } from "../../../competition/v2/competiton-service-v2";
import { getYearsFromCompAgeGroup } from "../athlete-service-v2";

export default defineComponent({
  name: "DateOfBirth",
  components: { DateEntry },
  props: {
    dob: {
      type: String as PropType<IsoDate>,
      required: true,
    },
    competitionBase: {
      type: Object as PropType<ICompetitionBase>,
      default: () => {
        return factoryCompetitionBase();
      },
    },
  },
  setup(
    props: { dob: IsoDate; competitionBase: ICompetitionBase },
    context: SetupContext
  ) {
    const compAgeGroupYears = ref<number[]>([]);
    const dobSelected = ref("");

    function onSelected(dobIso: string) {
      dobSelected.value = dobIso;
      context.emit("dobSelected", dobIso);
    }

    function toggleAgeGroupYears() {
      if (props.competitionBase.id > 0) {
        if (compAgeGroupYears.value.length > 0) {
          compAgeGroupYears.value = [];
        } else {
          compAgeGroupYears.value = getYearsFromCompAgeGroup(
            props.competitionBase.compAgeGroups
          );
        }
      }
    }


    watch(
      () => props.competitionBase,
      (newValue: ICompetitionBase) => {
        toggleAgeGroupYears();
      },
      {
        immediate: true,
      }
    );


    return { compAgeGroupYears, onSelected, toggleAgeGroupYears };
  },
});
</script>
