+
<template>
  <div>
    <!--    athleteState.isLoading.value{{ athleteState.isLoading.value }}-->
    <LoadingSpinnerV2 v-if="athleteState.isLoading.value" />
    <AthleteFormV2
      v-if="athleteState.athlete.value.id > 0"
      :athlete="athleteState.athlete.value"
      :default-ao="defaultAo"
      :aos="aos"
      :is-admin="configController.isAdmin.value"
      :show-cancel-button="false"
    />
  </div>
</template>

<script lang="ts">
import { computed, defineComponent } from "@vue/composition-api";
import AthleteFormV2 from "./athlete-form-v2.vue";
import { useRoute } from "../../../router/migrateRouterVue3";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import {
  useConfigController,
  useConfigStore,
} from "../../../config/useConfigStore";
import { useAthleteState } from "./useAthleteState";

export default defineComponent({
  name: "athlete-form-route-v2",
  components: { LoadingSpinnerV2, AthleteFormV2 },
  setup() {
    const route = useRoute();

    const configStore = useConfigStore();
    const configController = useConfigController();

    const athleteState = useAthleteState();

    const defaultAo = computed(() => {
      return configStore.configApp.defaultao;
    });

    const aos = computed(() => {
      return configStore.configApp.aos;
    });

    const id = isNaN(Number(route.params.id))
      ? 0
      : parseInt(route.params.id, 0);

    if (id > 0) {
      athleteState.getAthlete(id);
    }
    return { configController, defaultAo, aos, athleteState };
  },
});
</script>
