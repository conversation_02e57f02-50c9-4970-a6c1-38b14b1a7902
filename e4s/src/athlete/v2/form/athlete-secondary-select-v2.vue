<template>
  <select
    v-model="selectedValue"
    v-on:change="onSelected"
    class="browser-default e4s-input-field--primary"
  >
    <option
      v-for="opt in getOptions"
      :key="opt.id"
      :value="opt"
      v-text="opt.name + (isAdmin ? ' (' + opt.id + ')' : '')"
    ></option>
  </select>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
} from "@vue/composition-api";
import { IBaseConcrete } from "../../../common/common-models";
import { IAthlete } from "../../athlete-models";

export default defineComponent({
  name: "athlete-secondary-select-v2",
  components: {
  },
  props: {
    athleteSummary: {
      type: Object as PropType<IAthlete>,
      required: true,
    },
    isAdmin: {
      type: Boolean,
      default: function () {
        return false;
      },
    },
  },
  setup(
    props: {
      athleteSummary: IAthlete;
      isAdmin: boolean;
    },
    context: SetupContext
  ) {
    const selectedValue = ref({
      id: props.athleteSummary.clubId!,
      name: props.athleteSummary.club,
    });

    const getOptions = computed<IBaseConcrete[]>(() => {
      const options: IBaseConcrete[] = [
        {
          id: props.athleteSummary.clubId!,
          name: props.athleteSummary.club,
        },
      ];

      if (props.athleteSummary.club2Id > 0) {
        options.push({
          id: props.athleteSummary.club2Id,
          name: props.athleteSummary.club2,
        });
      }
      return options;
    });

    function onSelected() {
      context.emit("onSelected", selectedValue.value);
    }

    return {
      selectedValue,
      getOptions,
      onSelected,
    };
  },
});
</script>
