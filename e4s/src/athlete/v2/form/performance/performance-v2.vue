<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <!--    <FormGenericSectionTitleV2-->
    <!--      title-size="400"-->
    <!--      section-title="Athlete Performance"-->
    <!--      section-overview="EP: Expected Performance  SB: Seasons Best.  PB: Personal Best"-->
    <!--      :show-cancel-button="false"-->
    <!--    />-->

    <FormGenericSectionSubTitleV2 sub-title="Latest event entries by athlete" />
    <!--    <p class="e4s-subheader&#45;&#45;general e4s-subheader&#45;&#45;500">-->
    <!--      EP: Expected Performance SB: Seasons Best. PB: Personal Best-->
    <!--    </p>-->

    <!--    <PowerOfTenLinkV2-->
    <!--      :athlete-ao-code="athlete.aocode"-->
    <!--      :urn="athlete.URN"-->
    <!--      :show-pre-text="true"-->
    <!--    />-->
    <AthletePo10Link
      style="width: fit-content"
      v-if="hasPowerOf10"
      :athlete="athlete"
      :image-styles="{ height: '20px', verticalAlign: 'text-bottom' }"
    >
      <span slot="pre-text">Link to athlete on Power of Ten</span>
    </AthletePo10Link>

    <PerformanceGridV2
      :athlete="athlete"
      :is-admin="configController.isAdmin.value"
    />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import {
  useConfigController,
  useConfigStore,
} from "../../../../config/useConfigStore";
import { IAthlete } from "../../../athlete-models";
import { AthleteService } from "../../../athlete-service";
import AthletePo10Link from "../../../athlete-po10-link.vue";
import PerformanceGridV2 from "./performance-grid-v2.vue";
import { hasPowerOf10Claim } from "../../athlete-service-v2";
import FormGenericSectionSubTitleV2 from "../../../../common/ui/layoutV2/form/form-generic-section-sub-title-v2.vue";
import PowerOfTenLinkV2 from "../../../../common/ui/layoutV2/PowerOfTenLinkV2.vue";

const athleteService = new AthleteService();

export default defineComponent({
  name: "performance-v2",
  methods: { hasPowerOf10Claim },
  components: {
    PowerOfTenLinkV2,
    FormGenericSectionSubTitleV2,
    PerformanceGridV2,
    AthletePo10Link,
  },
  props: {
    athlete: {
      type: Object as PropType<IAthlete>,
      default: () => {
        return athleteService.factoryGetAthlete();
      },
    },
    isAdmin: {
      type: Boolean,
      default: function () {
        return false;
      },
    },
  },
  setup(props: { athlete: IAthlete; isAdmin: boolean }, context: SetupContext) {
    const configStore = useConfigStore();
    const configController = useConfigController();

    /*
    watch(
      () => props.consult,
      (newValue: any, oldValue: any) => {
        console.log("");
      }
    );
     */

    function cancel() {
      context.emit("cancel");
    }

    const hasPowerOf10 = computed(() => {
      return hasPowerOf10Claim(props.athlete);
    });

    return { cancel, configStore, configController, hasPowerOf10 };
  },
});
</script>
