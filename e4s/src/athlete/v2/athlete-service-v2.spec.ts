import * as AthleteServiceV2 from "./athlete-service-v2";
import { IAthleteSummary } from "../athlete-models";
import {
  athletesLookLikeRep,
  userInfoLooksLikeClubRep,
} from "./mock/athlete-mock-data";
import { simpleClone } from "../../common/common-service-utils";
import { ICompEventAgeGroupBase } from "../../compevent/compeventschedule/compevent-age-group-coverage/compevent-age-group-coverage-models";

describe("athlete-service-v2", () => {
  test("getAthleteDescription", () => {
    const athleteSummary: IAthleteSummary = {
      id: 208938,
      firstName: "Joe",
      surName: "Bloggs",
      gender: "M",
      ageInfo: {
        ageGroups: [
          {
            id: 0,
            shortName: "N/A",
            Name: "No events for athletes age group (Un-registered not allowed)",
          },
        ],
        currentAge: 15,
        ageGroup: {
          id: 0,
          shortName: "N/A",
          Name: "No events for athletes age group (Un-registered not allowed)",
        },
      },
    } as any as IAthleteSummary;

    expect(AthleteServiceV2.getAthleteDescription(athleteSummary)).toBe(
      "Joe Bloggs (M - 15yrs)"
    );
  });

  test("getAthleteDescription", () => {
    const athleteSummary: IAthleteSummary = {
      id: 208938,
      firstName: "Joe",
      surName: "Bloggs",
      gender: "M",
      ageInfo: {
        ageGroups: [
          {
            id: 0,
            shortName: "N/A",
            Name: "No events for athletes age group (Un-registered not allowed)",
          },
        ],
        currentAge: 15,
        ageGroup: {
          id: 4,
          shortName: "U15",
          Name: "Under 15",
        },
      },
    } as any as IAthleteSummary;

    expect(AthleteServiceV2.getAthleteDescription(athleteSummary)).toBe(
      "Joe Bloggs (M - U15)"
    );
  });

  test("shouldBeClubRep", () => {
    let userInfoData = simpleClone(userInfoLooksLikeClubRep);
    expect(
      AthleteServiceV2.resultShouldUserBeClubRep(
        athletesLookLikeRep,
        userInfoData
      ).result
    ).toBe(false);

    userInfoData = simpleClone(userInfoLooksLikeClubRep);
    userInfoData.clubs = [];
    expect(
      AthleteServiceV2.resultShouldUserBeClubRep(
        athletesLookLikeRep,
        userInfoData
      ).result
    ).toBe(false);

    userInfoData = simpleClone(userInfoLooksLikeClubRep);
    userInfoData.clubs = [];
    userInfoData.areas = [];
    const result = AthleteServiceV2.resultShouldUserBeClubRep(
      athletesLookLikeRep,
      userInfoData
    );
    //  all same club
    expect(Object.keys(result.clubMap).length).toBe(1);
    // but 3 different surnames.
    expect(Object.keys(result.surNameMap).length).toBe(3);
    //  ...so looks like should be club rep
    expect(result.result).toBe(true);
  });

  test("shouldBeClubRep with big family", () => {
    const userInfoData = {
      ...simpleClone(userInfoLooksLikeClubRep),
      clubs: [],
      areas: [],
    };
    const athletes = simpleClone(athletesLookLikeRep).map((athlete) => {
      athlete.surName = "Bloggs";
      return athlete;
    });

    const result = AthleteServiceV2.resultShouldUserBeClubRep(
      athletes,
      userInfoData
    );
    //  all same club
    expect(Object.keys(result.clubMap).length).toBe(1);
    // but 3 same surnames.
    expect(Object.keys(result.surNameMap).length).toBe(1);
    //  ...so looks like is a parent
    expect(result.result).toBe(false);
  });

  test("getCompAgeGroup", () => {
    let result;

    const ageGroups: ICompEventAgeGroupBase[] = [
      { id: 38, name: "Juniors", fromDate: "2008-09-01", toDate: "2010-08-31" },
      { id: 39, name: "Inters", fromDate: "2006-09-01", toDate: "2008-08-31" },
      { id: 40, name: "Seniors", fromDate: "2004-09-01", toDate: "2006-08-31" },
    ];

    result = AthleteServiceV2.getCompAgeGroup(ageGroups, "2020-06-06");
    expect(result).toBe(null);

    result = AthleteServiceV2.getCompAgeGroup(ageGroups, "2008-09-02");
    expect(result!.name).toBe("Juniors");

    result = AthleteServiceV2.getCompAgeGroup(ageGroups, "2007-09-01");
    expect(result!.name).toBe("Inters");

    result = AthleteServiceV2.getCompAgeGroup(ageGroups, "2006-08-31");
    expect(result!.name).toBe("Seniors");

    result = AthleteServiceV2.getCompAgeGroup(ageGroups, "1932-06-06");
    expect(result).toBe(null);
  });

  test("getCompAgeGroup", () => {
    let result;

    const ageGroups: ICompEventAgeGroupBase[] = [
      { id: 38, name: "Juniors", fromDate: "2008-09-01", toDate: "2010-08-31" },
      { id: 39, name: "Inters", fromDate: "2006-09-01", toDate: "2008-08-31" },
      { id: 40, name: "Seniors", fromDate: "2004-09-01", toDate: "2006-08-31" },
    ];

    result = AthleteServiceV2.getYearsFromCompAgeGroup(ageGroups);
    expect(result.length).toBe(7);
    expect(Math.max(...result)).toBe(2010);
    expect(Math.min(...result)).toBe(2004);
  });

  /*

  test getCompAgeGroup() with following data

  [
    {
        "id": 214,
        "name": "ES Inters",
        "fromDate": "2008-09-01",
        "toDate": "2010-08-31"
    },
    {
        "id": 215,
        "name": "ES Seniors",
        "fromDate": "2006-09-01",
        "toDate": "2008-08-31"
    },
    {
        "id": 213,
        "name": "ES Juniors",
        "fromDate": "2010-09-01",
        "toDate": "2012-08-31"
    },
    {
        "id": 263,
        "name": "All Age Groups",
        "fromDate": "2006-09-01",
        "toDate": "2012-08-31"
    }
]



"2008-05-16"
   */
  test("getCompAgeGroup", () => {
    let result;

    const ageGroups: ICompEventAgeGroupBase[] = [
      {
        id: 214,
        name: "ES Inters",
        fromDate: "2008-09-01",
        toDate: "2010-08-31",
      },
      {
        id: 215,
        name: "ES Seniors",
        fromDate: "2006-09-01",
        toDate: "2008-08-31",
      },
      {
        id: 213,
        name: "ES Juniors",
        fromDate: "2010-09-01",
        toDate: "2012-08-31",
      },
      {
        id: 263,
        name: "All Age Groups",
        fromDate: "2006-09-01",
        toDate: "2012-08-31",
      },
    ];

    result = AthleteServiceV2.getCompAgeGroup(ageGroups, "2008-05-16");
    expect(result!.name).toBe("ES Seniors");
  });
});
