<template>
  <div style="padding: 8px" class="e4s-flex-row e4s-justify-flex-center">
    <AthleteSearchV2 class="e4s-width-controller-entry" />
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import AthleteSearchV2 from "./athlete-search-v2.vue";

export default defineComponent({
  name: "AthleteSearchHybrid",
  components: { AthleteSearchV2 },
  props: {},
  setup(props: any, context: SetupContext) {
    return {};
  },
});
</script>
