import { IAthleteSummary } from "../../athlete-models";
import { GenderType, IPaging } from "../../../common/common-models";

export type AthleteSearchStateSection = "SEARCH" | "CREATE" | "VIEW_ATHLETE";

export interface IAthleteSearchState {
  isLoading: boolean;
  searchPreloadDone: boolean;
  athleteSummaries: IAthleteSummary[];
  paging: IPaging;
  showSearchFilters: boolean;
  showMoreFilter: boolean;
  showSection: AthleteSearchStateSection;
  filterValues: {
    firstName: string;
    surName: string;
    club: string;
    county: string;
    region: string;
    urn: string;
    gender: GenderType | "";
  };
  showRegSystemAthletes: boolean;
}
