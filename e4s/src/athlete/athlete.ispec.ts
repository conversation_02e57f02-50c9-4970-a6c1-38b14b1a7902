import Vue from "vue";
import Vuex from "vuex";

import {IServerGenericPagingResponse} from "../common/common-models";
import {doJwtAuth} from "../common/common.ispec";
import {AthleteData} from "./athlete-data";
import {IAthleteSearch, IAthleteSummary} from "./athlete-models";
import {AthleteService} from "./athlete-service";

const athleteService: AthleteService = new AthleteService();
const athleteData: AthleteData = new AthleteData();

Vue.use(Vuex);

beforeAll((done) => {
    doJwtAuth(done);
});

describe("Athletes", () => {

    test("getAthletes", () => {
        const athleteSearch: IAthleteSearch = athleteService.factoryGetAthleteSearch();
        const compId = 77;
        const teamId = 43;
        const pageNumber = 1;
        const pageSize = 20;
        const sortKey = "";
        return athleteData.findAthletes(compId, teamId, pageNumber, pageSize, sortKey, athleteSearch )
            .then((response: IServerGenericPagingResponse) => {

                //  console.log("....findAthletes()", response);

                expect(response.errNo === 0).toBe(true);

                const athleteSummariees: IAthleteSummary[] = response.data as IAthleteSummary[];
                expect(athleteSummariees.length > 0).toBe(true);
                const athleteSummary: IAthleteSummary = athleteSummariees[0];

                expect(athleteSummary.firstName === undefined).toBe(false);
                expect(athleteSummary.firstName.length > 0).toBe(true);
            });
    });
});
