import {Gender} from "./athlete-models";
<template>
  <div class="row athlete-filter-wrapper">
    <div class="input-field col s12 m12 l12">
      <p>
        <label>
          <input
            id="show-all-athletes"
            class="e4s-checkbox"
            type="checkbox"
            v-model="showOnlyEligible"
          />
          <span>Eligible athletes only</span>
        </label>
      </p>
    </div>
    <div class="input-field col s12 m6 l6">
      <input
        type="text"
        id="firstName"
        placeholder="Enter at least 2 characters"
        name="First Name"
        v-model="athleteSearch.firstname"
        v-on:keyup.enter="onSubmit"
      />
      <label for="firstName" class="active">First Name</label>
    </div>
    <div class="input-field col s12 m6 l6">
      <input
        type="text"
        id="surname"
        placeholder="Enter at least 2 characters"
        name="Surname"
        v-model="athleteSearch.surname"
        v-on:keyup.enter="onSubmit"
      />
      <label for="surname" class="active">Surname</label>
    </div>
    <div class="input-field col s12 m6 l6">
      <label class="active" for="gender">Gender</label>
      <select
        id="gender"
        class="generic-select"
        name="gender"
        v-model="athleteSearch.gender"
      >
        <option :value="null">Select Option</option>
        <option :value="gender.FEMALE">Female</option>
        <option :value="gender.MALE">Male</option>
      </select>
    </div>
    <!--ageGroup{{ageGroup}}-->
    <div class="input-field col s12 m6 l6">
      <select
        id="age-group"
        class="generic-select"
        name="age-group"
        v-model="athleteSearch.ageGroupId"
      >
        <option :value="null">Select Option</option>
        <option v-for="option in ageGroup" :key="option.id" :value="option.id">
          {{ option.Name }}
        </option>
      </select>
      <label class="active" for="gender">Age Group</label>
    </div>

    <div class="input-field col s12 m6 l6">
      <input
        type="text"
        id="athlete-filter--urn"
        placeholder=""
        name="athlete-filter--urn"
        v-model="athleteSearch.urn"
        v-on:keyup.enter="onSubmit"
      />
      <label for="surname" class="active">URN</label>
    </div>

    <div class="input-field col s12 m6 l6">
      <input
        type="text"
        id="club"
        name="Club"
        v-model="athleteSearch.club"
        v-on:keyup.enter="onSubmit"
      />
      <label for="club">Club</label>
    </div>

    <div class="input-field col s12 m6 l6">
      <input
        type="text"
        id="county"
        name="County"
        v-model="athleteSearch.county"
        v-on:keyup.enter="onSubmit"
      />
      <label for="county">County</label>
    </div>

    <div class="col s12">
      <div class="e4s-flex-row">
        <div class="e4s-flex-row e4s-flex-row--end e4s-gap--standard">
          <ButtonGenericV2
            text="Cancel"
            button-type="secondary"
            @click="cancel"
            :disabled="errors.any()"
          /><ButtonGenericV2
            text="Search"
            @click="onSubmit"
            :disabled="errors.any()"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { GENDER } from "../common/common-models";
import {
  CompetitionType,
  ICompetition,
} from "../competition/competition-models";
import { IAgeGroup, IAthleteSearch } from "./athlete-models";
import { AthleteService } from "./athlete-service";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";

@Component({
  name: "athlete-filter",
  components: { ButtonGenericV2 },
})
export default class AthleteFilter extends Vue {
  @Prop() public competition: ICompetition;
  @Prop() public ageGroup: IAgeGroup;

  public athleteService: AthleteService = new AthleteService();

  public gender = GENDER;
  public athleteSearch: IAthleteSearch =
    this.athleteService.factoryGetAthleteSearch();
  public showOnlyEligible: boolean = false;

  public competitionType = CompetitionType;

  @Watch("showOnlyEligible")
  public onShowOnlyEligibleChanged(showOnlyEligible: boolean) {
    this.athleteSearch.showAllAthletes = showOnlyEligible ? "0" : "1";
  }

  public onSubmit() {
    this.$validator.validateAll().then((result) => {
      if (!result) {
        // do stuff if not valid.
        return;
      }
      this.$emit("onSubmit", { ...this.athleteSearch });
    });
  }

  public clearFilter() {
    this.athleteSearch = this.athleteService.factoryGetAthleteSearch();
    this.showOnlyEligible = false;

    this.$emit("onSubmit", { ...this.athleteSearch });
  }

  public cancel() {
    this.clearFilter();
    this.$emit("onCancel", { ...this.athleteSearch });
  }

  public hideFilter() {
    this.$emit("onHide");
  }
}
</script>
