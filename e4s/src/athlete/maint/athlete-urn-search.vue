<template>
    <div class="row">
        <div class="col s12 m6 l3">
<!--            <label class="active" :for="PREFIX + 'ao-select'">Country</label>-->
<!--            <athletic-org-drop-down :id="PREFIX + 'ao-select'"-->
<!--                                    :def="defaultao"-->
<!--                                    :orgs="aos"-->
<!--                                    v-on:onSelected="onAoSelected">-->
<!--            </athletic-org-drop-down>-->
            <div class="ao-pad">
                <label v-for="ao in aos">
                    <input type="radio"
                           :id="'ao-radio-' + ao.id"
                           :value="ao"
                           v-model="athelticOrganisationSelected"
                           v-on:change="urnChanged">
                    <span></span>
                    <img :src="ao.icon" class="ao-icon">
                    <span v-text="ao.code" class="ao-label"></span>
                </label>
            </div>
        </div>
        <div class="col s12 m6 l3">
            <label class="active" :for="PREFIX + 'urn'">Association ID</label>
            <input
                    :id="PREFIX + 'urn'"
                    name="URN"
                    type="text"
                    v-model="URN"
                    class="validate"
                    v-on:keyup="urnChanged">
            <span v-if="!isUrnValid" class="error-label">{{ athelticOrganisationSelected.urnmessage}}</span>
        </div>

        <div class="col s12 m6 l3">
            <label class="active" :for="PREFIX + 'dob'">Date of Birth</label>
            <div class="dob-fields-wrap" :id="PREFIX + 'dob'">
                <date-entry v-on:onSelected="dobSelected"></date-entry>
            </div>
        </div>
        <div class="col s12 m6 l3">
            <div class="right">
                <loading-spinner v-if="isLoading"></loading-spinner>
                <slot name="cancel-button">
                    <button class="btn xxx-btn-small btn-flat red-text e4s-bold"
                            v-on:click.stop="onCancel()">
                        <span v-text="$t('buttons.close')"></span>
                    </button>
                </slot>
                <button :disabled="isSearchButtonDisabled"
                        class="btn waves-effect waves green sidenav-close"
                        v-on:click.stop="getByUrn">
                    Search
                </button>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import LoadingSpinner from "../../common/ui/loading-spinner.vue";
    import {IAthleticsOrganisation} from "../../common/ui/athletic-org/athletic-org-models";
    import {Prop, Watch} from "vue-property-decorator";
    import {AthleteService} from "../athlete-service";
    import DateEntry from "../../common/ui/datetime/date-entry.vue";
    import AthleticOrgDropDown from "../../common/ui/athletic-org/athletic-org-drop-down.vue";
    import {AthleteDataService} from "../athlete-data-service";
    import {IServerGenericResponse} from "../../common/common-models";
    import {messageDispatchHelper} from "../../user-message/user-message-store";
    import {USER_MESSAGE_LEVEL} from "../../user-message/user-message-models";
    // import {E4S_WARN_CODES} from "../common/config";

    @Component({
        name: "athlete-urn-search",
        components: {
            "loading-spinner": LoadingSpinner,
            "date-entry": DateEntry,
            "athletic-org-drop-down": AthleticOrgDropDown
        }
    })
    export default class AthleteUrnSearch extends Vue {
        @Prop({}) public defaultao: IAthleticsOrganisation;
        @Prop({}) public aos: IAthleticsOrganisation[];

        public PREFIX = Math.random().toString(36).substring(2);
        public athleteDataService: AthleteDataService = new AthleteDataService();
        public athleteService: AthleteService = new AthleteService();
        // public athlete: IAthlete = this.athleteService.factoryGetAthlete();
        public athelticOrganisationSelected: IAthleticsOrganisation = {id: 0} as IAthleticsOrganisation;

        public URN: string = "";
        public isUrnValid: boolean = false;
        public dob: string = "";
        public isLoading: boolean = false;

        @Watch("defaultao", {immediate: true})
        public onDefaultAoChanged(newValue: IAthleticsOrganisation, oldValue: IAthleticsOrganisation) {
            this.aos.forEach((athleticsOrganisation: IAthleticsOrganisation) => {
               if (athleticsOrganisation.id === this.defaultao.id) {
                   this.athelticOrganisationSelected = athleticsOrganisation;
               }
            });
        }

        public onAoSelected(athleticOrganisation: IAthleticsOrganisation) {
            this.athelticOrganisationSelected = athleticOrganisation;
        }

        public urnChanged() {
            this.validateUrn();
        }

        public validateUrn() {
            this.isUrnValid = this.athleteService.validateUrn(this.URN, this.athelticOrganisationSelected.urnformat);
        }

        public dobSelected(dobIso: string) {
            this.dob = dobIso;
        }

        public get isDobValid() {
            return this.dob.length > 0;
        }

        public get isSearchButtonDisabled() {
            return !(this.isUrnValid && this.isDobValid && (this.athelticOrganisationSelected.id > 0) && !this.isLoading);
        }

        public getByUrn() {
            this.isLoading = true;
            this.athleteDataService.getAthleteByUrn(
                this.athelticOrganisationSelected.code, this.URN, this.dob
            )
                .then((response: IServerGenericResponse) => {
                    if (response.errNo > 0 ) {
                        messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR);
                        // if (response.errNo !== E4S_WARN_CODES.GENERIC_WARN) {
                        //     return;
                        // }
                        return;
                    }
                    // this.athlete = response.data;
                    this.$emit("onResponse", R.clone(response.data));
                })
                .catch((error) => {
                    messageDispatchHelper("Error finding athlete: " + error, USER_MESSAGE_LEVEL.INFO);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        }

        public onCancel() {
            this.$emit("onCancel");
        }

    }
</script>

<style scoped>
    .ao-icon {
        height: 1.8rem;
        margin-right: 0rem;
        vertical-align: bottom;
    }
    .ao-label {
        font-size: 1rem;
    }
    .ao-pad {
        padding-bottom: 2rem;
    }
</style>
