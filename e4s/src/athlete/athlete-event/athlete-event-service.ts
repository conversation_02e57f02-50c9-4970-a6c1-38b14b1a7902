import { IAthleteEvent } from "./athlete-event-models";
import { ATHLETE_PERF_TEXT_PREFIX } from "../../athleteCompSched/pb/v3/edit-pb-v3-models";

export function factoryAthleteEvent(): IAthleteEvent {
  return {
    ceid: 0,
    compdate: "",
    compid: 0,
    compname: "",
    entryid: 0,
    eventid: 0,
    eventname: "",
    eventtime: "",
    options: {
      cancelUser: 0,
      reason: "",
      refunds: [],
    },
    orderno: 0,
    paid: 0,
    perfInfo: {
      athleteId: 0,
      eventId: 0,
      eventType: "D",
      info: "",
      ageGroup: "",
      curAgeGroup: "",
      eventName: "",
      limits: {
        min: 0,
        max: 0,
      },
      perf: 0,
      perfText: "",
      pb: 0,
      pbAchieved: "",
      pbText: "",
      sb: 0,
      sbAchieved: "",
      sbText: "",
      uom: [],
    },
  };
}

export function getPerfForDisplay(athleteEvent: IAthleteEvent): string {
  if (athleteEvent.perfInfo.perf === 0) {
    return "";
  }
  return athleteEvent.perfInfo.perfText.replace(ATHLETE_PERF_TEXT_PREFIX, "");
}
