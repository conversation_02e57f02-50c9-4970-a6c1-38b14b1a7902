<template>
  <div>
    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <span class="e4s-bold-input">Athletes</span>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m6 l6">
        <input
          id="firstName"
          name="First Name"
          v-model="firstName"
          class="validate"
          placeholder="E.g. <PERSON>, <PERSON>"
          v-on:keyup.enter="search"
          :disabled="isLoading"
        />
        <label class="active" for="firstName">First Name</label>
      </div>
      <div class="input-field col s12 m6 l6">
        <input
          id="surName"
          name="Surname"
          v-model="surName"
          placeholder="E.g. Bloggs, Blo"
          class="validate"
          v-on:keyup.enter="search"
          :disabled="isLoading"
        />
        <label class="active" for="surName">Surname</label>
      </div>
    </div>

    <div v-show="moreFilter">
      <div class="row">
        <div class="input-field col s12 m6 l6">
          <input
            id="urn"
            name="urn"
            v-model="urn"
            v-on:keyup.enter="search"
            placeholder="Enter complete URN, not partial."
            class="validate"
            :disabled="isLoading"
          />
          <label class="active" for="firstName">
            URN
            <a
              href="#"
              v-if="isAdmin"
              v-on:click.prevent="showRegSystemAthlete = true"
              v-text="getRegistrationSystemAoCode + ' link'"
            >
            </a>
          </label>
        </div>
        <div class="input-field col s12 m6 l6">
          <gender-select
            :show-all="true"
            v-on:onSelected="onGenderSelected"
          ></gender-select>
          <label class="active" for="surName">Gender</label>
        </div>
      </div>

      <div class="row">
        <div class="input-field col s12 m6 l6">
          <label for="club">Club</label>
          <input
            type="text"
            id="club"
            name="Club"
            v-model="club"
            v-on:keyup.enter="search"
          />
        </div>
        <div class="input-field col s12 m6 l6">
          <label for="county">County</label>
          <input
            type="text"
            id="county"
            name="County"
            v-model="county"
            v-on:keyup.enter="search"
          />
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div class="button-pad left">
          <ButtonGenericV2
            v-if="showCreateButton"
            class="e4s-button--auto"
            text="Create Athlete"
            @click="createAthlete"
          />
        </div>
        <div class="button-pad right">
          <loading-spinner v-if="isLoading"></loading-spinner>

          <ButtonGenericV2
            :text="moreFilter ? 'Less search options' : 'More search options'"
            class="e4s-button--auto"
            @click="toggleMore"
            button-type="tertiary"
          />
          <ButtonGenericV2
            text="Search"
            @click="search"
            button-type="primary"
          />

          <slot name="cancel-button"> </slot>
        </div>
      </div>
    </div>

    <div
      class="athlete-card-small"
      v-for="athlete in athletes"
      :key="athlete.id"
    >
      <div class="row" v-on:click="onAthleteClicked(athlete)">
        <div class="col s12 m12 l12">
          <athlete-maint-card
            :athlete="athlete"
            :show-athlete-profile-url-link="showAthleteProfileUrlLink"
            :show-athlete-delete-button="showAthleteDeleteButton"
            @deleteAthlete="deleteAthlete"
          />
        </div>

        <div class="e4s-card-standard-sep"></div>
      </div>
    </div>

    <ModalV2 v-if="showRegSystemAthlete" :always-show-header-blank="true">
      <div slot="body" style="margin: 8px">
        <CardGenericV2>
          <div slot="all">
            <EaAthleteWrapper
              :urn="urn"
              v-if="getRegistrationSystemAoCode === 'EA'"
            />

            <EventMasterAthleteWrapper
              :urn="urn"
              v-if="getRegistrationSystemAoCode === 'IRL'"
            />

            <div class="e4s-vertical-spacer-standard"></div>
            <div class="e4s-flex-row">
              <ButtonGenericV2
                text="Close"
                class="e4s-flex-row--end"
                @click="showRegSystemAthlete = false"
              />
            </div>
          </div>
        </CardGenericV2>
      </div>
    </ModalV2>

    <AthleteDelete
      :athlete="athleteToDelete"
      :user-id="configApp.userInfo.user.id"
      @athleteDeleted="search"
    />
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { AthleteDataService } from "../athlete-data-service";
import { IAthlete, IAthleteSearch, IAthleteSummary } from "../athlete-models";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { mapGetters, mapState } from "vuex";
import { AthleteService } from "../athlete-service";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import AthleteMaintCard from "./athlete-maint-card.vue";
import LoadingSpinner from "../../common/ui/loading-spinner.vue";
import GenderSelect from "../../common/ui/gender-select.vue";
import { GENDER, GenderType } from "../../common/common-models";
import { LAUNCH_ROUTES_PATHS } from "../../launch/launch-routes";
import { Prop } from "vue-property-decorator";
import EventMasterAthleteWrapper from "../eventmaster/EventMasterAthleteWrapper.vue";
import CardGenericV2 from "../../common/ui/layoutV2/card-generic-v2.vue";
import ModalV2 from "../../common/ui/layoutV2/modal/modal-v2.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import EaAthleteWrapper from "../ea/ea-athlete-wrapper.vue";
import AthleteDelete from "../athlete-delete.vue";
import { IConfigApp } from "../../config/config-app-models";
import { simpleClone } from "../../common/common-service-utils";

@Component({
  name: "athletes-container",
  components: {
    AthleteDelete,
    EaAthleteWrapper,
    ButtonGenericV2,
    ModalV2,
    CardGenericV2,
    EventMasterAthleteWrapper,
    "athlete-maint-card": AthleteMaintCard,
    "loading-spinner": LoadingSpinner,
    "gender-select": GenderSelect,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class AthletesContainer extends Vue {
  public readonly configApp!: IConfigApp;
  public isAdmin: boolean;

  @Prop({ default: true }) public readonly showCreateButton: boolean;

  @Prop({
    default: true,
  })
  public showAthleteProfileUrlLink: boolean;

  @Prop({
    default: true,
  })
  public showAthleteDeleteButton: boolean;

  public athleteDataService: AthleteDataService = new AthleteDataService();
  public athleteService: AthleteService = new AthleteService();
  public athletes: IAthlete[] = [];
  public isLoading: boolean = false;

  public athleteToDelete: IAthleteSummary =
    this.athleteService.factoryAthleteSummary();

  public firstName: string = "";
  public surName: string = "";
  public club: string = "";
  public county: string = "";
  public region: string = "";
  public urn: string = "";
  public gender: GenderType = "";

  public moreFilter: boolean = false;
  public showRegSystemAthlete = false;

  public mounted() {
    console.log("AthletesContainer.mounted");

    if (this.$route.query.urn) {
      this.urn = this.$route.query.urn as string;
      this.search();
      return;
    }

    if (!this.isAdmin) {
      this.search();
    }
  }

  public onGenderSelected(gender: GENDER) {
    this.gender = gender;
  }

  public toggleMore() {
    this.moreFilter = !this.moreFilter;
  }

  public createAthlete() {
    this.$router.push({
      name: LAUNCH_ROUTES_PATHS.ATHLETE_CREATE,
      params: {
        id: "0",
      },
    });
  }

  public onAthleteClicked(athlete: IAthlete) {
    this.$emit("onAthleteClicked", Object.assign({}, athlete));
  }

  public search() {
    const competitionId: number = 0;
    const teamId: number = 0;
    const pageNumber: number = 1;
    const pageSize: number = 25;
    const sortKey: string = "surName";
    const athleteSearch: IAthleteSearch =
      this.athleteService.factoryGetAthleteSearch();
    athleteSearch.firstname = this.firstName;
    athleteSearch.surname = this.surName;
    athleteSearch.club = this.club;
    athleteSearch.county = this.county;
    athleteSearch.region = this.region;
    athleteSearch.gender = this.gender;
    athleteSearch.urn = this.urn;

    this.isLoading = true;
    this.athleteDataService
      .findAthletes(
        competitionId,
        teamId,
        pageNumber,
        pageSize,
        sortKey,
        athleteSearch
      )
      .then((response) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        this.athletes = response.data;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return;
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public get getRegistrationSystemAoCode() {
    return this.$store.getters[
      CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_REGISTRATION_SYSTEM_AO_CODE
    ];
  }

  public deleteAthlete(athlete: IAthleteSummary) {
    console.log("AthletesContainer.deleteAthlete()", simpleClone(athlete));
    this.athleteToDelete = athlete;
  }
}
</script>

<style scoped>
.button-pad {
  padding-bottom: 1rem;
}
</style>
