<template>
  <div>
    <E4sModal
      v-if="showAddAthlete"
      :is-full-size-form="true"
      css-class="e4s-modal-container--full-size"
      v-on:destroyed="onShowAddAthleteDestroy"
    >
      <div slot="header"></div>

      <StandardForm title="Add Athlete" slot="body" style="overflow: auto">
        <AthleteCreateFormV2
          slot="form-content"
          style="padding: 1rem"
          :competition-base="selectedCompetitionGrid"
          v-on:submitted="onSubmitAthlete"
          v-on:cancelled="onShowAddAthlete(false)"
        />
      </StandardForm>

      <div slot="footer"></div>
    </E4sModal>

    <div class="athlete-grid-wrapper" v-show="!showAddAthlete">
      <SchedInfo class="e4s-card-v1" v-bind:schedInfo="getSchedInfo">
        <div
          slot="extra"
          class="e4s-flex-column e4s-gap--small"
          style="margin-top: var(--e4s-gap--small)"
          v-if="selectedCompetitionGrid && selectedCompetitionGrid.options"
        >
          <hr class="dat-e4s-hr dat-e4s-hr--slightly-lighter dat-e4s-hr-only" />
          <div v-text="selectedCompetitionGrid.options.helpText.athletes"></div>
        </div>
      </SchedInfo>

      <div class="athlete-filter-wrapper" v-if="showFilterAddSection">
        <div class="row">
          <div class="col s6 m6 l6">
            <ButtonGenericV2
              class="e4s-button--auto"
              :text="shouldShowFilter ? 'Hide Filter' : 'Filter Athletes'"
              @click="showAthleteFilter(!shouldShowFilter)"
            />

            <span v-if="isAthleteFilterOn" class="filter-on-text">ON</span>
          </div>

          <div class="col s6 m6 l6">
            <div class="e4s-flex-row e4s-justify-flex-end e4s-gap--standard">
              <ButtonGenericV2 text="Add Athlete" @click="addAthlete(true)" />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col s12 m12 l12">
            <EntryUserRequiresEntity
              :athletes="athletes"
              :user-info="userInfo"
            />
          </div>
        </div>

        <div
          v-if="showQuickSearch"
          class="e4s-flex-column e4s-gap--standard"
          style="margin-top: var(--e4s-gap--standard)"
        >
          <!--          <hr class="dat-e4s-hr" />-->

          <p>
            If URN and date of birth known, try a quick search, else
            <a href="#" v-on:click.prevent="onShowAddAthlete(true)">add new</a>.
          </p>

          <AthleteUrnSearchSimpleV2
            :default-ao="defaultAo"
            v-on:onCancel="showQuickSearch = false"
          >
            <ButtonGenericV2
              slot="buttons-left"
              button-type="primary"
              text="Add New"
              v-on:click="onShowAddAthlete(true)"
            />
          </AthleteUrnSearchSimpleV2>

          <hr class="dat-e4s-hr" />
        </div>

        <div class="athlete-filter-splitter"></div>

        <div class="row" v-if="renderFilterButton">
          <div class="col s12 m12 l12">
            Sort By
            <select
              class="e4s-select e4s-select-mobile"
              v-model="sortKey"
              @change="triggerRefreshAthletes"
            >
              <option
                v-for="option in getSortKeys"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </option>
            </select>

            <div class="right">
              Page Size
              <select
                class="e4s-select e4s-select-mobile"
                v-model.number="pageSize"
                @change="triggerRefreshAthletes"
              >
                <option
                  v-for="option in pageKeys"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <AthleteFilter
        v-show="shouldShowFilter"
        v-bind:competition="competition"
        v-bind:age-group="ageGroup"
        v-on:onSubmit="onAthleteFilter"
        v-on:onCancel="onAthleteFilterCancel"
        v-on:onHide="showAthleteFilter(false)"
      >
      </AthleteFilter>

      <LoadingSpinner v-if="isLoading"></LoadingSpinner>

      <div class="athlete-grid--section-wrapper">
        <div class="row" v-if="getIndivEventsHaveSecurity">
          <div class="s12 m12 l12">
            <UserTeamAccess
              :in-team-section="false"
              :user-info="userInfo"
              :competition="competition"
              :user-entity="userEntity"
              :event-team-headers="eventTeamHeaders"
              v-on:submitted="selfServiceSubmitted"
            />
          </div>
        </div>

        <div class="row" v-if="!isLoading && athletes.length === 0">
          <div class="s12 m12 l12">
            <div v-if="isAthleteFilterOn">
              Can't find athlete you are looking for? Use "Add Athlete" button
              above to enter new athlete or click
              <a href="#" v-on:click.prevent="addAthlete">here</a>.
            </div>
            <div v-if="!isAthleteFilterOn">
              Looks like you don't have any athlete(s) entered, this could be
              yourself or someone else. Use "Add Athlete" button above to enter
              new athlete or click
              <a href="#" v-on:click.prevent="addAthlete">here</a>. You only
              need to do this once per athlete. Competition events are set up
              for age groups, so we need some basic information to ensure we
              match the athlete with the correct events.
            </div>
          </div>
        </div>
      </div>

      <!--      <EntityEntriesLoader-->
      <!--        :comp-id="selectedCompetitionGrid.id"-->
      <!--        :club-comp-info="selectedCompetitionGrid.clubCompInfo"-->
      <!--      />-->

      <AthleteCardGrid
        v-show="!shouldAthleteGridBeHidden"
        class="athlete-card-grid"
        :isLoading="isLoading"
        :athletes="athletes"
        :selected-athlete="selectedAthlete"
        :athletesVisible="athletesVisible"
        :club-comp-info="clubCompInfo"
        :has-builder-permission-for-comp="getHasBuilderPermissionForComp"
        :competition="competition"
        v-on:onSelectedGrid="onRowClickedGrid"
      >
      </AthleteCardGrid>

      <div v-show="!shouldAthleteGridBeHidden">
        <div v-if="showPager" class="col s12 m12 l12 center">
          <a
            class="pagination-icon"
            href="#"
            v-on:click.prevent="onFirstPageClicked()"
          >
            <i class="material-icons">first_page</i>
          </a>
          <a
            class="pagination-icon"
            href="#"
            v-on:click.prevent="onPreviousPageClicked()"
          >
            <i class="material-icons">chevron_left</i>
          </a>
          <div
            v-if="paging && paging.totalCount > 0"
            class="input-field inline"
          >
            <input
              class="paging-input"
              v-model="pageNumber"
              :maxlength="3"
              v-on:change="getAthletes"
            />
            <span> of {{ calcTotalPages(this.paging) }}</span>
          </div>
          <a
            class="pagination-icon"
            href="#"
            v-on:click.prevent="onNextPageClicked()"
          >
            <i class="material-icons">chevron_right</i>
          </a>
          <a
            class="pagination-icon"
            href="#"
            v-on:click.prevent="onLastPageClicked()"
          >
            <i class="material-icons">last_page</i>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ICompetitionInfo } from "../competition/competition-models";
import { AthleteData, IAthletePagingResponseList } from "./athlete-data";
import { IAthlete, IAthleteSearch, IAthleteSummary } from "./athlete-models";

import AthleteFilter from "../athlete/athlete-filter.vue";
import { IObjectKey, IPaging } from "../common/common-models";
import { CompetitionService } from "../competition/competiton-service";
import { ENTRY_STORE_CONST, IEntryStoreState } from "../entry/entry-store";
import { ITeam } from "../team/team-models";
import AthleteCardGrid from "./athlete-card-grid.vue";
import { AthleteService } from "./athlete-service";
import { ISchedInfo } from "../athletecompsched/athletecompsched-models";
import SchedInfo from "../athletecompsched/sched-info/sched-info.vue";
import E4sModal from "../common/ui/e4s-modal.vue";
import AthleteAdd from "./maint/athlete-add.vue";
import StandardForm from "../common/ui/standard-form/standard-form.vue";
import { handleResponseMessages } from "../common/handle-http-reponse";
import UserTeamAccess from "../athleteCompSched/comp-event-teams/user-team-access/user-team-access.vue";
import {
  COMP_EVENT_TEAMS_STORE_CONST,
  ICompEventTeamsStoreState,
} from "../athleteCompSched/comp-event-teams/comp-event-store";
import { mapState } from "vuex";
import { CONFIG_STORE_CONST, IConfigStoreState } from "../config/config-store";
import { IConfigApp, IEntity, IUserInfo } from "../config/config-app-models";
import { IEventTeamHeader } from "../athleteCompSched/comp-event-teams/event-teams-models";
import { BuilderService } from "../builder/builder-service";
import { IAgeGroup } from "../agegroup/agegroup-models";
import AthleteCreateForm from "./maint/athlete-create-form.vue";
import EntryUserRequiresEntity from "../entry/EntryUserRequiresEntity.vue";
import * as ClubCompInfoService from "../entry/v2/schools/clubCompInfoService";
import { IClubCompInfo } from "../entry/v2/schools/clubCompInfo-models";
import AthleteUrnSearchSimpleV2 from "./v2/form/athlete-urn-search-simple-v2.vue";
import { IAthleticsOrganisation } from "../common/ui/athletic-org/athletic-org-models";
import { ConfigService } from "../config/config-service";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";
import AthleteCreateFormV2 from "./v2/form/athlete-create-form-v2.vue";
import { simpleClone } from "../common/common-service-utils";
// import EntityEntriesLoader from "../entry/v2/entity-entries/EntityEntriesLoader.vue";

const builderService: BuilderService = new BuilderService();

@Component({
  name: "athlete-grid",
  components: {
    AthleteCreateFormV2,
    ButtonGenericV2,
    AthleteUrnSearchSimpleV2,
    EntryUserRequiresEntity,
    AthleteCreateForm,
    UserTeamAccess,
    StandardForm,
    AthleteAdd,
    E4sModal,
    AthleteFilter,
    AthleteCardGrid,
    SchedInfo,
  },
  computed: {
    ...mapState(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME,
      {
        eventTeamHeaders: (state: ICompEventTeamsStoreState) =>
          state.eventTeamHeaders,
        eventTeamHeadersLoading: (state: ICompEventTeamsStoreState) =>
          state.eventTeamHeadersLoading,
      }
    ),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
      userInfo: (state: IConfigStoreState) => state.configApp.userInfo,
      userEntity: (state: IConfigStoreState) => state.userEntity,
      defaultAo: (state: IConfigStoreState) => state.configApp.defaultao,
    }),
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      selectedCompetitionGrid: (state: IEntryStoreState) =>
        state.entryForm.selectedCompetition,
      clubCompInfo: (state: IEntryStoreState) => state.entryForm.clubCompInfo,
    }),
  },
})
export default class AthleteGrid extends Vue {
  public readonly configApp: IConfigApp;
  public readonly userInfo: IUserInfo;
  public readonly userEntity: IEntity;
  public readonly selectedCompetitionGrid: ICompetitionInfo;
  public readonly clubCompInfo: IClubCompInfo;
  public readonly defaultAo: IAthleticsOrganisation;

  public readonly eventTeamHeaders: IEventTeamHeader[];
  public eventTeamHeadersLoading: boolean;

  @Prop() public athletesVisible: boolean;
  @Prop() public competition: ICompetitionInfo;
  @Prop() public selectedTeam: ITeam;
  @Prop() public triggerRefresh: string;

  public athleteService: AthleteService = new AthleteService();
  public competitionService = new CompetitionService();
  public configService = new ConfigService();
  public athletes: IAthleteSummary[] = [];
  public paging: IPaging = {} as IPaging;
  public pageNumber: number = 1;
  public pageSize: number = 25;
  public sortKey: string = "surName";
  public showAllAthletes: string = "";
  public totalPages: number = 1;
  public isLoading: boolean = false;
  public ageGroup: Record<string, IAgeGroup> = {};
  public selectedAthlete: IAthleteSummary = {} as IAthleteSummary;

  public showFilter = false;
  public showAddAthlete = false;
  public showQuickSearch = false;

  public athleteSearch: IAthleteSearch = {
    region: "",
    county: "",
    club: "",
    firstname: "",
    surname: "",
    gender: "",
  } as IAthleteSearch;

  public pageKeys: number[] = [5, 10, 25, 50, 100];

  public get getSortKeys() {
    const athlete = Object.assign({
      firstName: "First",
      surName: "Last",
      region: "Region",
      club: "Club",
      URN: "Reg ID",
      dob: "DOB",
    }) as IAthleteSummary;

    //  This is total anti-pattern
    // @See https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-1.html
    const ath: any = athlete;
    const options: IObjectKey[] = Object.keys(athlete).reduce((accum, key) => {
      const fu = {
        label: ath[key],
        value: key,
      } as IObjectKey;

      accum.push(fu);
      return accum;
    }, [] as IObjectKey[]);
    return options;
  }

  @Watch("competition")
  public onCompetitionChanged(
    competitionNew: ICompetitionInfo,
    competitionOld: ICompetitionInfo
  ) {
    if (competitionNew.id !== competitionOld.id) {
      this.athletes = [];
      this.paging = {} as IPaging;

      //  Wait for team to be selected.
      if (competitionNew.teamid === 0) {
        this.getAthletes();
      }
    }
  }

  @Watch("selectedTeam")
  public onTeamChanged(newValue: ITeam, oldValue: ICompetitionInfo) {
    if (newValue.teamId !== oldValue.teamid) {
      this.athletes = [];
      this.paging = {} as IPaging;

      //  Wait for team to be selected.
      //  TODO
      if (newValue.teamId > 0) {
        this.getAthletes();
      }
    }
  }

  @Watch("triggerRefresh")
  public onTriggerRefreshChanged(newValue: string, oldValue: string) {
    if (newValue !== oldValue) {
      this.athletes = [];
      this.paging = {} as IPaging;

      this.getAthletes();
    }
  }

  public getAthletes() {
    console.log("athlete-grid getAthletes()...start...");
    const athleteData: AthleteData = new AthleteData();

    if (this.competition.id === undefined || this.competition.id === -1) {
      return;
    }

    if (this.pageNumber) {
      if (this.pageNumber > this.calcTotalPages(this.paging)) {
        this.pageNumber = this.calcTotalPages(this.paging);
      }

      if (this.pageNumber < 1) {
        this.pageNumber = 1;
      }
    }

    this.selectedAthlete = {} as IAthleteSummary;

    //  this.athletes = [];
    this.isLoading = true;

    const prom = athleteData.findAthletes(
      this.competition.id,
      this.selectedTeam.teamId,
      this.pageNumber,
      this.pageSize,
      this.sortKey,
      this.athleteSearch
    );
    handleResponseMessages(prom);

    prom.then((response: IAthletePagingResponseList) => {
      console.log("athlete-grid getAthletes()...got response:", response);
      this.ageGroup = response.ageGroupInfo ? { ...response.ageGroupInfo } : {};
      this.athletes = simpleClone(response.data) as IAthleteSummary[];
      this.paging = response.meta;
      this.totalPages = this.calcTotalPages(this.paging);
      this.isLoading = false;

      if (this.athletes.length === 1) {
        this.onRowClickedGrid(this.athletes[0]);
      }

      console.log("athlete-grid getAthletes()...commit to store...");
      this.$store.commit(
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
          "/" +
          ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_SEARCHED_ATHLETES,
        response.data
      );
    });
  }

  public onRowClickedGrid(athlete: IAthleteSummary) {
    this.selectedAthlete = athlete;
    this.$emit("onSelected", athlete);
  }

  public triggerRefreshAthletes() {
    this.onAthleteFilter(this.athleteSearch);
  }

  public onAthleteFilter(athleteSearch: IAthleteSearch) {
    this.pageNumber = 1; //  Reset paging.
    this.athleteSearch = { ...athleteSearch };
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_ATHLETE_FILTER,
      athleteSearch
    );
    this.getAthletes();
    this.showFilter = false;
  }

  public onAthleteFilterCancel(athleteSearch: IAthleteSearch) {
    this.onAthleteFilter(athleteSearch);
    this.showFilter = false;
  }

  public onFirstPageClicked() {
    this.pageNumber = 1;
    this.getAthletes();
  }

  public onLastPageClicked() {
    this.pageNumber = this.calcTotalPages(this.paging);
    this.getAthletes();
  }

  public onNextPageClicked() {
    if (this.pageNumber !== this.calcTotalPages(this.paging)) {
      this.pageNumber++;
      this.getAthletes();
    }
  }

  public onPreviousPageClicked() {
    if (this.pageNumber !== 1) {
      this.pageNumber--;
      this.getAthletes();
    }
  }

  public get showPager() {
    return this.athletes.length > 0;
  }

  public get renderFilterButton() {
    return (
      this.isCompSelected &&
      (this.isAthleteFilterOn || this.athletes.length > 10)
    );
  }

  public showAthleteFilter(showIt: boolean) {
    this.showFilter = showIt;
  }

  public get isAthleteFilterOn() {
    return this.athleteService.isAthleteSearchFilterEnabled(this.athleteSearch);
  }

  public get shouldAthleteGridBeHidden() {
    return this.isLoading;
  }

  private calcTotalPages(paging: IPaging): number {
    if (!(paging && paging.totalCount)) {
      return 0;
    }
    return Math.ceil(paging.totalCount / paging.pageSize);
  }

  public get showFilterAddSection() {
    return this.isCompSelected;
  }

  public get showAddAthleteMessage() {
    return !this.isCompSelected;
  }

  public get isCompSelected() {
    return this.competition && this.competition.id && this.competition.id > 0;
  }

  public get shouldShowFilter() {
    return this.showFilter;
  }

  public get getSchedInfo() {
    const schedInfo: ISchedInfo = {
      title: "Athletes",
    } as ISchedInfo;
    schedInfo.schedInfoDetails = [];
    schedInfo.autoExpand = true;
    schedInfo.shortDescription = "";
    schedInfo.showLinks = false;

    if (this.showAddAthleteMessage) {
      schedInfo.shortDescription =
        "Athletes can be selected/added once an Organiser and Competition have been selected.";
    }

    if (!this.showAddAthleteMessage) {
      schedInfo.shortDescription =
        "Select athlete to retrieve their competition schedule. Add athletes via the 'Add' button.";
    }

    if (ClubCompInfoService.hasClubCompInfoCompetition(this.clubCompInfo)) {
      // const clubCompInfo: IClubCompInfo = this.selectedCompetitionGrid
      //   .clubCompInfo as IClubCompInfo;
      schedInfo.schedInfoDetails.push({
        title: "",
        body: ClubCompInfoService.userMessageEntriesHtml(this.clubCompInfo),
      });
    }

    return schedInfo;
  }

  public addAthlete() {
    // this.showAddAthlete = true;
    this.onShowAddAthlete(true);
  }

  public onShowAddAthlete(showIt: boolean) {
    if (showIt) {
      document.body.style.overflow = "hidden";
    } else {
      // document.body.style.overflow = "auto";
    }

    this.showAddAthlete = showIt;
  }

  public onShowAddAthleteDestroy() {
    console.log("onShowAddAthleteDestroy");
    document.body.style.overflow = "auto";
  }

  public onSubmitAthlete(athlete: IAthlete) {
    console.log("athlete-grid onSubmitAthlete athlete:", athlete);
    this.showAddAthlete = false;
    this.getAthletes();
  }

  public get getShowSelfService(): boolean {
    return builderService.getSelfServices(this.competition.options).length > 0;
  }

  public get getIndivEventsHaveSecurity(): boolean {
    //  We can't actually tell if there are any Indiv events with security at the moment,
    //  can only test for teams.
    return this.competition.hasSecureEvents
      ? this.competition.hasSecureEvents
      : false;
    // return eventTeamService.getTeamEventsHaveSecurity(this.eventTeamHeaders);
  }

  public selfServiceSubmitted() {
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_ATHLETE_TRIGGER_REFRESH
    );
  }

  public doShowQuickSearch() {
    this.showQuickSearch = true;
  }

  public get getHasBuilderPermissionForComp(): boolean {
    return this.configService.hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.selectedCompetitionGrid.compOrgId,
      this.selectedCompetitionGrid.id
    );
  }
}
</script>

<style scoped>
.filter-on-text {
  font-weight: 500;
  color: red;
}

.athlete-grid--section-wrapper {
  padding: 1em;
}
</style>
