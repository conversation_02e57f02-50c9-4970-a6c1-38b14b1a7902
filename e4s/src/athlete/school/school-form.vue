<template>
    <div>
        <div class="row">
            <div class="col s12 m6 l6">
                <input
                    id="school-name"
                    name="school-name"
                    v-model="schoolInternal.name">
                <label class="active" for="school-name">School Name</label>
            </div>
            <div class="col s12 m6 l6">
                <label class="active" for="school-area">Area</label>
                <AreaLookup id="school-area" v-on:onSelected="areaSelected"></AreaLookup>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {ISchool} from "./school-models"
import AreaLookup from "../../area/area-lookup.vue";

@Component({
    name: "school-form",
  components: {AreaLookup}
})
export default class SchoolForm extends Vue {
    @Prop({
        default: () => {
            return {
                id: 0,
                name: ""
            }
        }
    })
    public readonly school!: ISchool;

    public schoolInternal: ISchool = {
        id: 0,
        name: "",
        area: {
            id: 0,
            name: ""
        }
    }

    public id: number = 0

    public created() {
        this.schoolInternal = R.clone(this.school);
    }

    @Watch("school")
    public onSchoolChanged(newValue: ISchool) {
        this.schoolInternal = R.clone(newValue);
    }

    public areaSelected() {

    }

    public submit() {
        this.$emit("submit", R.clone(this.schoolInternal));
    }
}
</script>
