<template>
  <div>
    <LoadingSpinnerV2 v-if="competitionSummaryPublic.compId === 0" />
    <div
      class="
        e4s-content-max-center e4s-width-controller e4s-hybrid-content-wrapper
      "
      id="entry-public-v2-v1-wrapper--entry-conditions-v2"
    >
      <!--      class="e4s-content-max-center e4s-width-controller"-->
      <EntryConditionsV2
        class="e4s-width-controller-yyy"
        style="padding-top: var(--e4s-gap--large)"
        v-if="competitionSummaryPublic.compId > 0"
        :competition-summary-public="competitionSummaryPublic"
        v-on:proceedToComp="proceedToComp"
        v-on:cancel="cancel"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from "@vue/composition-api";
import { CompetitionService } from "../../competition/competiton-service";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import { CompetitionData } from "../../competition/competition-data";
import { useRoute, useRouter } from "../../router/migrateRouterVue3";
import EntryConditionsV2 from "./entry-conditions-v2.vue";
import { LAUNCH_ROUTES_PATHS } from "../../launch/launch-routes";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";

export default defineComponent({
  name: "entry-conditions-route",
  components: { LoadingSpinnerV2, EntryConditionsV2 },
  setup() {
    const isLoading = ref(false);
    const route = useRoute();
    const router = useRouter();

    const competitionService: CompetitionService = new CompetitionService();
    const competitionSummaryPublic = ref(
      competitionService.factorySummaryPublic()
    );

    const compId = isNaN(Number(route.params.id))
      ? 0
      : parseInt(route.params.id, 0);
    if (compId > 0) {
      isLoading.value = true;
      const prom = new CompetitionData().getCompById(compId);
      handleResponseMessages(prom);
      prom
        .then((resp) => {
          if (resp.errNo === 0) {
            competitionSummaryPublic.value = resp.data;
          }
        })
        .finally(() => {
          isLoading.value = false;
        });
    }

    function proceedToComp() {
      // E4S-386
      router.push({
        path: "/" + LAUNCH_ROUTES_PATHS.ENTRY,
        query: {
          comporgid: competitionSummaryPublic.value.compOrgId.toString(),
          compid: competitionSummaryPublic.value.compId.toString(),
        },
      });
    }

    function cancel() {
      router.push({
        path:
          "/" +
          LAUNCH_ROUTES_PATHS.SHOW_ENTRY +
          "/" +
          competitionSummaryPublic.value.compId.toString(),
      });
    }

    return {
      competitionSummaryPublic,
      proceedToComp,
      cancel,
    };
  },
});
</script>

<style scoped>
@media screen and (max-width: 768px) {
  .e4s-width-controller {
    min-width: 280px;
    max-width: var(--e4s-width-controller__width);
    width: 100%;
  }
}
</style>
