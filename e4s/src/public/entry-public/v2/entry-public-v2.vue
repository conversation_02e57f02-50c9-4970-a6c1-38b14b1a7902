<template>
  <div>
    <div class="e4s-vertical-spacer--large"></div>

    <template v-if="state.showSection === 'SUMMARY'">
      <InfoSectionV2
        info-type="warn"
        v-if="configStore.configApp.options.homeMessage.length > 0"
      >
        <div
          slot="default"
          v-text="configStore.configApp.options.homeMessage"
        ></div>
      </InfoSectionV2>
    </template>

    <div class="e4s-content-wrapper e4s-content-max-center">
      <LoadingSpinnerV2 v-if="compStoreState.isLoadingCompSummary" />

      <div
        class="e4s-flex-column e4s-gap--standard"
        v-show="state.showSection === 'SUMMARY'"
      >
        <div
          :class="isShowingAllFilters ? '' : 'e4s-repeatable-grid'"
          style="row-gap: 0"
        >
          <EntryPublicFilter
            :is-loading="compStoreState.isLoadingCompSummary"
            :comp-filter-params="stickyCompFilters"
            :user-orgs="configController.userOrgs.value"
            :locations="configStore.configMeta.locations"
            :organisers="configStore.configMeta.organisers"
            :competitions="compStoreState.competitionSummaryPublic"
            @doCompFilterSearch="doCompFilterSearch"
            @showAllFilters="showAllFilters"
          />
          <!-- Dummy div...because of the grid and actually only want to show the filter -->
          <div></div>
        </div>

        <div class="e4s-repeatable-grid">
          <div
            v-for="comp in compStoreState.competitionSummaryPublic"
            v-if="!compStoreState.isLoadingCompSummary"
          >
            <PublicCompCardV2
              :id="'comp-' + comp.compId"
              :competition-summary-public="comp"
              :show-links="configController.isAdmin.value"
              :is-displaying-more-info="false"
              :show-more-info="false"
              :is-admin="configController.isAdmin.value"
              @showContactOrganiser="proceedShowContactOrganiser"
              @showMoreInfo="showMoreInfo"
            />
          </div>
        </div>

        <PagingV2
          v-if="compStoreState.page.totalCount > compStoreState.page.pageSize"
          :paging="compStoreState.page"
          v-on:onPageClicked="onPageClicked"
        />
      </div>

      <div
        class="e4s-flex-row e4s-justify-flex-center"
        v-if="state.showSection === 'MORE_INFO'"
      >
        <div class="e4s-flex-column e4s-gap--standard e4s-width-controller">
          <div class="e4s-flex-row">
            <button-generic-back-v2
              text="Home"
              v-on:click="showSummary"
              class="e4s-hyperlink--400"
            />
          </div>

          <CompMoreInfoV2
            :competition-summary-public="state.selectedCompetitionSummaryPublic"
            :show-comp-results-buttons="true"
            :set-section="state.moreInfo.showSection"
            @close="hideMoreInfo"
            @showContactOrganiser="proceedShowContactOrganiser"
            @goToCompV1="goToCompV1"
            @onOrgApproved="onOrgApproved"
          />
        </div>
      </div>

      <div
        class="e4s-flex-row e4s-justify-flex-center"
        v-if="state.showSection === 'ASK_ORGANISER'"
      >
        <div class="e4s-flex-column e4s-gap--standard e4s-width-controller">
          <div class="e4s-vertical-spacer--large"></div>

          <PublicCompCardV2
            :competition-summary-public="state.selectedCompetitionSummaryPublic"
          >
            <ButtonGotoCompV2
              slot="button-more-info"
              class="e4s-flex-row--end"
              :competition-summary-public="
                state.selectedCompetitionSummaryPublic
              "
              :config-version="configController.getVersion.value"
              @goToCompV1="goToCompV1"
            />
          </PublicCompCardV2>

          <div class="e4s-card e4s-card--generic">
            <AskOrganiserFormV2
              :competition-summary-public="
                state.selectedCompetitionSummaryPublic
              "
              v-on:cancel="hideAskOrganiser"
            >
              <PrimaryLink
                slot="top-right-back-button"
                link-text="Back"
                @onClick="hideAskOrganiser"
              />
            </AskOrganiserFormV2>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import {
  useConfigController,
  useConfigStore,
} from "../../../config/useConfigStore";
import { EntryPublicSection, IEntryPublicState } from "./entry-public-models";
import { useStore } from "../../../app.store";
import {
  PUBLIC_COMPS_STORE_CONST,
  useCompStoreState,
} from "../public-comps-store";
import PublicCompCardV2 from "../public-list/v2/public-list-comp-card-v2.vue";
import { ICompetitionSummaryPublic } from "../../../competition/competition-models";
import { CompetitionService } from "../../../competition/competiton-service";
import * as CommonserviceUtils from "../../../common/common-service-utils";
import CompMoreInfoV2 from "../public-list/v2/moreinfo/comp-more-info-v2.vue";
import AskOrganiserFormV2 from "../../../competition/askorganiser/ask-organiser-form-v2.vue";
import EntryPublicFilter from "./comp-filter/entry-public-filter.vue";
import PagingV2 from "../../../common/ui/layoutV2/paging/paging-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import ButtonGenericBackV2 from "../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import { CompMoreInfoSection } from "../public-list/v2/moreinfo/comp-more-info-models";
import InfoSectionV2 from "../../../common/ui/layoutV2/info-section-v2.vue";
import { ICompFilterParams } from "../../../competition/v2/competition-data-v2";
import { factoryCompFilterParams } from "./comp-filter/entry-public-filter-service";
import { simpleClone } from "../../../common/common-service-utils";
import SectionLinksWrapper from "../../../common/ui/layoutV2/tabs/section-links-wrapper.vue";
import SectionLink from "../../../common/ui/layoutV2/tabs/section-link.vue";
import SectionLinkSimple from "../../../common/ui/layoutV2/tabs/section-link-simple.vue";
import { useGotoCompController } from "../../../common/ui/layoutV2/buttons/useGotoCompController";
import SectionLinksComp from "../../../common/ui/layoutV2/tabs/section-links-comp.vue";
import { CurrentCompSelection } from "../../../common/ui/layoutV2/tabs/section-links-models";
import { useRoute } from "../../../router/migrateRouterVue3";
import { LAUNCH_ROUTES_PATHS_V2 } from "../../../launch/v2/launch-routes-v2";
import {
  IE4sRouteControllerError,
  useE4sRouteController,
} from "../../../router/useE4sRouteController";
import { useEntryStore } from "../../../entry/entry-store";
import CompetitionHeaderSimple from "../../../competition/v2/CompetitionHeaderSimple.vue";
import ButtonGotoCompV2 from "../../../common/ui/layoutV2/buttons/button-goto-comp-v2.vue";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";

const competitionService = new CompetitionService();

const state = reactive<IEntryPublicState>({
  isLoading: false,
  showSection: "SUMMARY",
  moreInfo: {
    showSection: "INFO",
  },
  lastSectionShown: "SUMMARY",
  sectionLinkMap: {
    SUMMARY: {
      iconId: "1",
      uniqueDesc: "SUMMARY",
      title: "HOME",
    },
    MORE_INFO: {
      iconId: "2",
      uniqueDesc: "MORE_INFO",
      title: "More Info",
    },
    ASK_ORGANISER: {
      iconId: "3",
      uniqueDesc: "ASK_ORGANISER",
      title: "CONTACT",
    },
    ENTER: {
      iconId: "4",
      uniqueDesc: "ENTER",
      title: "ENTER",
    },
  },
  compsInternal: [],
  selectedCompetitionSummaryPublic: competitionService.factorySummaryPublic(),
  compFilterParams: factoryCompFilterParams(),
});

export default defineComponent({
  name: "entry-public-v2",
  components: {
    PrimaryLink,
    ButtonGotoCompV2,
    CompetitionHeaderSimple,
    SectionLinksComp,
    SectionLinkSimple,
    SectionLink,
    SectionLinksWrapper,
    InfoSectionV2,
    ButtonGenericBackV2,
    LoadingSpinnerV2,
    ButtonGenericV2,
    PagingV2,
    EntryPublicFilter,
    AskOrganiserFormV2,
    CompMoreInfoV2,
    PublicCompCardV2,
  },
  props: {
    defaultToSection: {
      type: String as PropType<EntryPublicSection>,
      default: "SUMMARY",
    },
  },
  setup(
    props: { defaultToSection: EntryPublicSection },
    context: SetupContext
  ) {
    const store = useStore();
    const configStore = useConfigStore();
    const configController = useConfigController();
    const compStoreState = useCompStoreState();

    const gotoComp = useGotoCompController();
    const e4sRouteController = useE4sRouteController();
    const routeInternal = useRoute();
    const entryStore = useEntryStore();
    const isShowingAllFilters = ref(false);

    //  Reset store
    // store.commit(
    //   PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_CONST_MODULE_NAME +
    //     "/" +
    //     PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_MUTATIONS_SET_COMPS,
    //   []
    // );

    // const state = reactive<IEntryPublicState>({
    //   isLoading: false,
    //   showSection: "SUMMARY",
    //   moreInfo: {
    //     showSection: "INFO",
    //   },
    //   lastSectionShown: "SUMMARY",
    //   sectionLinkMap: {
    //     SUMMARY: {
    //       iconId: "1",
    //       uniqueDesc: "SUMMARY",
    //       title: "HOME",
    //     },
    //     MORE_INFO: {
    //       iconId: "2",
    //       uniqueDesc: "MORE_INFO",
    //       title: "More Info",
    //     },
    //     ASK_ORGANISER: {
    //       iconId: "3",
    //       uniqueDesc: "ASK_ORGANISER",
    //       title: "CONTACT",
    //     },
    //     ENTER: {
    //       iconId: "4",
    //       uniqueDesc: "ENTER",
    //       title: "ENTER",
    //     },
    //   },
    //   compsInternal: [],
    //   selectedCompetitionSummaryPublic:
    //     competitionService.factorySummaryPublic(),
    //   compFilterParams: factoryCompFilterParams(),
    // });

    const sectionMap: Record<CurrentCompSelection, EntryPublicSection> = {
      HOME: "SUMMARY",
      COMP: "MORE_INFO",
      ENTER: "ENTER",
      CONTACT: "ASK_ORGANISER",
    };

    state.showSection = "SUMMARY";

    watch(
      () => entryStore.routeError,
      (newValue: IE4sRouteControllerError) => {
        //  User is clicking "Home" but already there, so reset state.
        if (
          newValue.name.toUpperCase() === "NAVIGATIONDUPLICATED" &&
          routeInternal.name === LAUNCH_ROUTES_PATHS_V2.HOME_V2 &&
          state.showSection !== "SUMMARY"
        ) {
          state.showSection = "SUMMARY";
        }
      }
    );

    watch(
      () => props.defaultToSection,
      (newValue: EntryPublicSection) => {
        console.log("EntryPublicV2.defaultToSection (watch): " + newValue);
        if (newValue.length === 0) {
          return;
        }

        if (newValue !== state.showSection) {
          state.showSection = newValue;
        }
      },
      {
        immediate: true,
      }
    );

    //  This is the initial search. Set to todays date...or since this section now
    //  has a date picker, it will be set to the date selected.does "sticky" state
    //  should we issue again...even if showing SUMMARY.  Also, should we
    //  "watch" state.showSection and issue search again if switching
    //  back to SUMMARY?
    // if (state.showSection === "SUMMARY") {

    if (state.compFilterParams.fromDate === "") {
      // Let's go back 1 week, so userrs can find results from a comp just gone.
      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - 7);
      state.compFilterParams.fromDate = fromDate.toISOString().split("T")[0];
      //state.compFilterParams.fromDate = new Date().toISOString().split("T")[0];
    }

    doCompFilterSearch(compStoreState.compFilter, false);
    // doCompFilterSearch({
    //   ...compStoreState.compFilter,
    //   fromDate: new Date().toISOString().split("T")[0],
    // });
    // }

    function setShowSection(currentCompSelection: CurrentCompSelection) {
      // "HOME" | "COMP" | "ENTER" | "CONTACT";
      // SUMMARY" | "MORE_INFO" | "ENTER" | "ASK_ORGANISER";
      if (sectionMap[currentCompSelection]) {
        if (currentCompSelection === "ENTER") {
          gotoComp.process(state.selectedCompetitionSummaryPublic);
          return;
        }
        state.showSection = sectionMap[currentCompSelection];
      }
    }

    const getSectionMap = computed(() => {
      const keys: CurrentCompSelection[] = Object.keys(
        sectionMap
      ) as CurrentCompSelection[];
      return keys.reduce<CurrentCompSelection>(
        (accum: CurrentCompSelection, key: CurrentCompSelection) => {
          const keyValue: EntryPublicSection = sectionMap[key];
          if (keyValue === state.showSection) {
            accum = key;
          }
          return accum;
        },
        "HOME"
      );
    });

    function proceedShowContactOrganiser(
      competitionSummaryPublic: ICompetitionSummaryPublic
    ) {
      state.lastSectionShown = state.showSection;
      state.selectedCompetitionSummaryPublic = CommonserviceUtils.simpleClone(
        competitionSummaryPublic
      );
      state.showSection = "ASK_ORGANISER";
      window.scrollTo(0, 0);
    }

    function hideAskOrganiser() {
      state.showSection = state.lastSectionShown;
      state.lastSectionShown = "ASK_ORGANISER";
    }

    function showMoreInfo(payload: {
      competitionSummaryPublic: ICompetitionSummaryPublic;
      compMoreInfoSection?: CompMoreInfoSection;
    }) {
      state.lastSectionShown = state.showSection;
      //  Get the more info to default to a specific tab.
      if (payload.compMoreInfoSection) {
        state.moreInfo.showSection = payload.compMoreInfoSection;
      }
      state.selectedCompetitionSummaryPublic = CommonserviceUtils.simpleClone(
        payload.competitionSummaryPublic
      );
      state.showSection = "MORE_INFO";
    }

    function hideMoreInfo() {
      state.showSection = state.lastSectionShown;
      state.lastSectionShown = "MORE_INFO";
    }

    function showSummary() {
      state.lastSectionShown = state.showSection;
      state.showSection = "SUMMARY";

      window.setTimeout(() => {
        document
          .getElementById(
            "comp-" + state.selectedCompetitionSummaryPublic.compId
          )!
          .scrollIntoView();
      }, 250);
    }

    function onPageClicked(pageNumber: number) {
      state.compFilterParams.pagenumber = pageNumber;
      doCompFilterSearch(state.compFilterParams, false);
    }

    function doCompFilterSearch(
      compFilterParams: ICompFilterParams,
      resetPageNumber = true
    ) {
      console.log("EntryPublicV2.doCompFilterSearch: ", compFilterParams);
      if (resetPageNumber) {
        compFilterParams.pagenumber = 1;
      }

      state.compFilterParams = simpleClone(compFilterParams);
      store.dispatch(
        PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_CONST_MODULE_NAME +
          "/" +
          PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_ACTIONS__FILTER_COMPS,
        {
          compFilterParams,
        }
      );
    }

    function showAllFilters(showAll: boolean) {
      console.log("EntryPublicV2.showAllFilters: " + showAll);
      isShowingAllFilters.value = showAll;
    }

    const stickyCompFilters = computed(() => {
      return compStoreState.compFilter;
    });

    const getShowHomeMessage = computed(() => {
      const options = configStore.configApp.options;
      return options && options.homeMessage && options.homeMessage.length > 0;
    });

    function goToCompV1() {
      console.log("EntryPublicV2.goToCompV1");
      context.emit("goToCompV1", state.selectedCompetitionSummaryPublic);
    }

    function onOrgApproved() {
      console.log("EntryPublicV2.onOrgApproved");
      doCompFilterSearch(state.compFilterParams, false);
    }

    return {
      configStore,
      configController,
      state,
      compStoreState,
      proceedShowContactOrganiser,
      hideAskOrganiser,
      showMoreInfo,
      hideMoreInfo,
      showSummary,
      doCompFilterSearch,
      showAllFilters,
      isShowingAllFilters,
      stickyCompFilters,
      getShowHomeMessage,
      onPageClicked,
      gotoComp,
      setShowSection,
      getSectionMap,
      e4sRouteController,
      goToCompV1,
      onOrgApproved,
    };
  },
});
</script>

<style>
.e4s-card--competition-small {
  flex-direction: column;
  min-width: 280px;
  max-width: 770px;
  width: 100%;
  height: 100%;
  /*padding: var(--e4s-card--primary__padding);*/
  background: var(--e4s-card--primary__background);
}

.e4s-card-competition__competition-actions-container {
  /*display: grid;*/
  /*grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));*/
  /*grid-gap: 8px;*/
  display: flex;
  flex-direction: row;
  grid-gap: 8px;
  justify-content: space-between;
}

.e4s-card-competition__competition-actions-container button {
  width: 150px;
}

@media screen and (max-width: 768px) {
  .e4s-card-competition__competition-actions-container {
    display: flex;
    flex-direction: row;
    grid-gap: 8px;
    justify-content: space-between;
  }
}

.e4s-card--competition > hr,
.e4s-card--competition hr {
  border-top: 0;
  border-color: var(--e4s-card--primary__border);
  margin: 16px 0;
}

.e4s-card-competition__information-overview-container h5 {
  margin: 0 0 16px 0;
}

.e4s-card-competition__information-overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  grid-gap: 16px;
}

@media screen and (max-width: 768px) {
  .e4s-card-competition__information-overview-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.e4s-card-competition__information-overview-entry > p.e4s-subheader--300 {
  color: var(--slate-500);
}
.e4s-card-competition__information-overview-entry > p.e4s-body--100 {
  color: var(--slate-800);
}

.e4s-card-competition__more-information-container {
  margin: 28px 0 0 0;
}
</style>
