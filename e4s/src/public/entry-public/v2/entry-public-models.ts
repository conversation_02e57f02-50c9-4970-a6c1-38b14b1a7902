import { ICompetitionSummaryPublic } from "../../../competition/competition-models";
import {CompMoreInfoSection} from "../public-list/v2/moreinfo/comp-more-info-models"
import {ICompFilterParams} from "../../../competition/v2/competition-data-v2"
import {ISectionLink} from "../../../common/ui/layoutV2/tabs/section-links-models"

export type EntryPublicSection = "SUMMARY" | "MORE_INFO" | "ENTER" | "ASK_ORGANISER";

export interface IEntryPublicState {
  isLoading: boolean;
  showSection: EntryPublicSection;
  moreInfo: {
    showSection: CompMoreInfoSection;
  };
  sectionLinkMap: Record<EntryPublicSection, ISectionLink<EntryPublicSection>>,
  lastSectionShown: EntryPublicSection;
  compsInternal: ICompetitionSummaryPublic[];
  selectedCompetitionSummaryPublic: ICompetitionSummaryPublic;
  compFilterParams: ICompFilterParams;
}
