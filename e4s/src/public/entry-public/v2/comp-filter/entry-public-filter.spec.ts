import * as EntryPublicFilterService from "./entry-public-filter-service";

describe("EntryPublicFilterService", () => {
  test("whichFiltersEnabled", () => {
    expect(
      EntryPublicFilterService.whichFiltersEnabled({
        fromDate: "2022-08-20T00:00:00+01:00",
        toDate: "2022-08-24T00:00:00+01:00",
        freeTextSearch: "nun",
        organiser: { id: 44, name: "Connacht Athletics" },
        location: { id: 42, name: "CIT Arena" },
        type: "T",
        compOrg: { id: 1, name: "some org" },
        event: [{ id: 1, name: "100m" }],
        pagenumber: 1,
      }).length
    ).toBe(9);

    expect(
      EntryPublicFilterService.whichFiltersEnabled({
        fromDate: "",
        toDate: "",
        freeTextSearch: "",
        organiser: { id: 0, name: "" },
        location: { id: 0, name: "" },
        type: "ALL",
        compOrg: { id: 0, name: "" },
        event: [],
        pagenumber: 1,
      }).length
    ).toBe(0);
  });

  test("whichAdvancedFiltersEnabled", () => {
    expect(
      EntryPublicFilterService.whichAdvancedFiltersEnabled({
        fromDate: "2022-08-20T00:00:00+01:00",
        toDate: "2022-08-24T00:00:00+01:00",
        freeTextSearch: "nun",
        organiser: { id: 44, name: "Connacht Athletics" },
        location: { id: 42, name: "CIT Arena" },
        type: "T",
        compOrg: { id: 1, name: "some org" },
        event: [{ id: 1, name: "100m" }],
        pagenumber: 1,
      }).length
    ).toBe(7);

    expect(
      EntryPublicFilterService.whichAdvancedFiltersEnabled({
        fromDate: "",
        toDate: "",
        freeTextSearch: "",
        organiser: { id: 0, name: "" },
        location: { id: 0, name: "" },
        type: "ALL",
        compOrg: { id: 0, name: "" },
        event: [],
        pagenumber: 1,
      }).length
    ).toBe(0);

    expect(
      EntryPublicFilterService.whichAdvancedFiltersEnabled({
        fromDate: "2022-08-20T00:00:00+01:00",
        toDate: "",
        freeTextSearch: "fef",
        organiser: { id: 1, name: "some org" },
        location: { id: 0, name: "" },
        type: "ALL",
        compOrg: { id: 0, name: "" },
        event: [],
        pagenumber: 1,
      }).length
    ).toBe(2);

    expect(
      EntryPublicFilterService.whichAdvancedFiltersEnabled(
        {
          fromDate: "2022-08-20T00:00:00+01:00",
          toDate: "",
          freeTextSearch: "",
          organiser: { id: 0, name: "" },
          location: { id: 0, name: "" },
          type: "ALL",
          compOrg: { id: 0, name: "" },
          event: [],
          pagenumber: 1,
        },
        "2022-08-21"
      ).length
    ).toBe(1);
  });

  test("whichAdvancedFiltersEnabledWithValues", () => {
    let result;

    result = EntryPublicFilterService.whichAdvancedFiltersEnabledWithValues(
      {
        fromDate: "2022-08-20T00:00:00+01:00",
        toDate: "",
        freeTextSearch: "",
        organiser: { id: 0, name: "" },
        location: { id: 0, name: "" },
        type: "ALL",
        compOrg: { id: 0, name: "" },
        event: [],
        pagenumber: 1,
      },
      "2022-08-21"
    );

    expect(result.fromDate).toBe("2022-08-20T00:00:00+01:00");
    expect(result.toDate).toBe(undefined);
  });

  test("whichAdvancedFiltersEnabledWithValues", () => {
    let result;

    result =
      EntryPublicFilterService.whichAdvancedFiltersEnabledWithValuesDisplay(
        {
          fromDate: "2022-08-20T00:00:00+01:00",
          toDate: "",
          freeTextSearch: "",
          organiser: { id: 1, name: "some org" },
          location: { id: 0, name: "" },
          type: "ALL",
          compOrg: { id: 0, name: "" },
          event: [],
          pagenumber: 1,
        },
        "2022-08-21"
      );

    expect(result.fromDate!.label).toBe("From");
    expect(result.fromDate!.value).toBe("20th Aug 2022");
    expect(result.organiser!.value).toBe("some org");
  });
});
