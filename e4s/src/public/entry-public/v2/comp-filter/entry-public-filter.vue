<template>
  <div class="e4s-flex-column e4s-gap--large-zxc">
    <!--QuickFilter-->
    <div
      v-if="!entryPublicFilterController.state.showAllFilters"
      class="e4s-flex-row e4s-full-width"
    >
      <div
        class="
          e4s-flex-row
          e4s-justify-flex-space-between
          e4s-full-width
          e4s-content-max-center
          e4s-flex-center
        "
      >
        <FormGenericFieldGridV2 style="margin: 0">
          <template slot="content">
            <div class="e4s-flex-row e4s-flex-wrap e4s-gap--standard-xyz">
              <InputWithButton class="e4s-full-width">
                <ButtonGenericV2
                  class="e4s-button--auto"
                  with-input="left"
                  button-type="primary"
                  :text="
                    'Filters' +
                    (entryPublicFilterController.whichAdvancedFiltersEnabled
                      .value.length > 0
                      ? ' (' +
                        entryPublicFilterController.whichAdvancedFiltersEnabled
                          .value.length +
                        ')'
                      : '')
                  "
                  v-on:click="setShowAllFilters(true)"
                  slot="before"
                >
                  <template slot="button-content">
                    <div
                      class="
                        e4s-flex-row
                        e4s-gap--tiny
                        e4s-justify-flex-row-vert-center
                      "
                    >
                      <span
                        style="color: var(--e4s-button--primary__text-color)"
                        v-text="
                          'Filters' +
                          (entryPublicFilterController
                            .whichAdvancedFiltersEnabled.value.length > 0
                            ? ' (' +
                              entryPublicFilterController
                                .whichAdvancedFiltersEnabled.value.length +
                              ')'
                            : '')
                        "
                      ></span>
                      <!--                      <TriangleDown />-->
                      <PlayMinor :arrow-direction="'down'" fill="white" />
                    </div>
                  </template>
                </ButtonGenericV2>

                <FieldTextV2
                  slot="field"
                  class="e4s-square--left e4s-square--right e4s-flex-grow"
                  v-model="
                    entryPublicFilterController.state.compFilterParams
                      .freeTextSearch
                  "
                  place-holder="Enter search..."
                  v-on:keyUpEnter="doQuickSearch"
                />
                <button-generic-v2
                  class="e4s-button--auto"
                  with-input="right"
                  v-on:click="doQuickSearch"
                  slot="after"
                />
              </InputWithButton>
              <!--              <div-->
              <!--                class="e4s-flex-row e4s-gap&#45;&#45;standard"-->
              <!--                style="margin-top: var(&#45;&#45;e4s-gap&#45;&#45;small)"-->
              <!--              >-->
              <!--                <ButtonGenericV2-->
              <!--                  id="aaaaa"-->
              <!--                  v-if="-->
              <!--                    entryPublicFilterController.whichAdvancedFiltersEnabled-->
              <!--                      .value.length > 1-->
              <!--                  "-->
              <!--                  button-type="tertiary"-->
              <!--                  text="Reset"-->
              <!--                  v-on:click="entryPublicFilterController.doReset"-->
              <!--                />-->
              <!--              </div>-->
            </div>
          </template>
        </FormGenericFieldGridV2>
      </div>
    </div>
    <!--/QuickFilter-->

    <!--Advanced Search-->
    <div
      v-if="entryPublicFilterController.state.showAllFilters"
      class="e4s-flex-column e4s-card e4s-card--generic"
      style="
        margin-top: var(--e4s-gap--standard);
        margin-bottom: var(--e4s-gap--standard);
      "
    >
      <div>
        <div
          class="e4s-flex-row e4s-gap--standard e4s-justify-flex-space-between"
        >
          <div class="e4s-header--400">Filters</div>
          <div class="e4s-show-only-mobile">
            <!--            <ButtonGenericV2-->
            <!--              class="e4s-button&#45;&#45;100"-->
            <!--              button-type="tertiary"-->
            <!--              text="Cancel M"-->
            <!--              v-on:click="cancel"-->
            <!--            />-->
            <ButtonGenericV2
              class="e4s-button--100"
              button-type="tertiary"
              text="Close"
              v-on:click="close"
            />
            <ButtonGenericV2
              class="e4s-button--100"
              text="Search"
              v-on:click="doAdvancedSearch"
            />
          </div>
        </div>
      </div>

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputTextV2
            v-model="
              entryPublicFilterController.state.compFilterParams.freeTextSearch
            "
            v-on:keyUpEnter="doAdvancedSearch"
            form-label="Search Text"
            place-holder=""
          />

          <FieldDateV2
            form-label="From Date"
            v-model="
              entryPublicFilterController.state.compFilterParams.fromDate
            "
            :only-date="true"
            :auto-close="true"
          />

          <FieldDropDownV2
            form-label="Organiser"
            :data-array="getOrganisers"
            v-if="entryPublicFilterController.showOrganisers"
            v-model="
              entryPublicFilterController.state.compFilterParams.organiser
            "
          >
            <template v-slot="{ obj }">
              <span
                v-text="entryPublicFilterController.getDropDownName(obj)"
              ></span>
            </template>
          </FieldDropDownV2>

          <FieldDropDownV2
            form-label="Location"
            :data-array="getLocations"
            v-if="entryPublicFilterController.showLocations"
            v-model="
              entryPublicFilterController.state.compFilterParams.location
            "
          >
            <template v-slot="{ obj }">
              <span
                v-text="entryPublicFilterController.getDropDownName(obj)"
              ></span>
            </template>
          </FieldDropDownV2>
        </template>
      </FormGenericFieldGridV2>

      <div class="e4s-flex-row">
        <!--        <ButtonGenericV2-->
        <!--          class="e4s-button&#45;&#45;100"-->
        <!--          button-type="tertiary"-->
        <!--          text="Cancel A"-->
        <!--          v-on:click="cancel"-->
        <!--        />-->
        <ButtonGenericV2
          class="e4s-button--100"
          button-type="tertiary"
          text="Close"
          v-on:click="close"
        />

        <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
          <ButtonGenericV2
            id="mnbv"
            class="e4s-button--100"
            button-type="secondary"
            text="Reset"
            v-on:click="entryPublicFilterController.doReset()"
          />

          <ButtonGenericV2
            class="e4s-button--100"
            text="Search"
            v-on:click="doAdvancedSearch"
          />
        </div>
      </div>
    </div>
    <!--/Advanced Search-->
    <div
      v-if="
        !entryPublicFilterController.state.showAllFilters &&
        entryPublicFilterController.whichAdvancedFiltersEnabled.value.length > 0
      "
      class="e4s-flex-row e4s-gap--standard e4s-header--400 e4s-flex-wrap"
    >
      <span class="e4s-subheader--general"
        >Competition Filters<template
          v-text="
            entryPublicFilterController.whichAdvancedFiltersEnabled.value
              .length > 1
              ? 's'
              : ''
          "
        ></template>
      </span>
      <span class="e4s-subheader--general"> > </span>
      <!--      <div-->
      <!--        v-text="entryPublicFilterController.whichFiltersEnabledText.value"-->
      <!--      ></div>-->

      <div
        v-for="filter in entryPublicFilterController
          .whichAdvancedFiltersEnabledWithValuesDisplay.value"
        :key="filter.label"
      >
        <div class="e4s-flex-row e4s-gap--standard">
          <div
            class="e4s-subheader--general"
            v-text="filter.label + ': '"
          ></div>
          <div v-text="filter.value"></div>
        </div>
      </div>
    </div>

    <InfoSectionV2
      info-type="warn"
      v-if="
        competitions.length === 0 &&
        entryPublicFilterController.isSearchBeingDone
      "
    >
      <div>No competitions found. Try refining the search.</div>
    </InfoSectionV2>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  watch,
  SetupContext,
  PropType,
  computed,
} from "@vue/composition-api";
import * as CommonServiceUtils from "../../../../common/common-service-utils";
import FormGenericSectionTitleV2 from "../../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import FormGenericInputTextV2 from "../../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FormGenericFieldGridV2 from "../../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FieldDropDownV2 from "../../../../common/ui/layoutV2/fields/field-drop-down-v2.vue";
import FieldDateV2 from "../../../../common/ui/layoutV2/fields/field-date-v2.vue";
import { IBaseConcrete } from "../../../../common/common-models";
import FormGenericButtonBar from "../../../../common/ui/layoutV2/form/form-generic-button-bar.vue";
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import {
  useConfigController,
  useConfigStore,
} from "../../../../config/useConfigStore";
import * as EntryPublicFilterService from "./entry-public-filter-service";
import InputWithButton from "../../../../common/ui/layoutV2/fields/InputWithButton.vue";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FieldTextV2 from "../../../../common/ui/layoutV2/fields/field-text-v2.vue";
import { ICompFilterParams } from "../../../../competition/v2/competition-data-v2";
import TriangleDown from "../../../../common/ui/svg/TriangleDown.vue";
import PlayMinor from "../../../../common/ui/svg/PlayMinor.vue";
import InputCheckboxV2 from "../../../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import {
  IUseEntryPublicFilterInput,
  useEntryPublicFilterController,
} from "./useEntryPublicFilter";
import InfoSectionV2 from "../../../../common/ui/layoutV2/info-section-v2.vue";
import { ICompetitionSummaryPublic } from "../../../../competition/competition-models";

export default defineComponent({
  name: "entry-public-filter",
  components: {
    InfoSectionV2,
    InputCheckboxV2,
    PlayMinor,
    TriangleDown,
    FieldTextV2,
    FormGenericInputTemplateV2,
    InputWithButton,
    ButtonGenericV2,
    LoadingSpinnerV2,
    FormGenericButtonBar,
    FieldDateV2,
    FieldDropDownV2,
    FormGenericFieldGridV2,
    FormGenericInputTextV2,
    FormGenericSectionTitleV2,
  },
  props: {
    compFilterParams: {
      type: Object as PropType<ICompFilterParams>,
      default: () => {
        return EntryPublicFilterService.factoryCompFilterParams();
      },
    },
    isLoading: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    userOrgs: {
      type: Array as PropType<IBaseConcrete[]>,
    },
    events: {
      type: Array as PropType<IBaseConcrete[]>,
    },
    locations: {
      type: Array as PropType<IBaseConcrete[]>,
    },
    organisers: {
      type: Array as PropType<IBaseConcrete[]>,
    },
    competitions: {
      type: Array as PropType<ICompetitionSummaryPublic[]>,
      default: () => {
        return [];
      },
    },
  },
  setup(
    props: {
      compFilterParams: ICompFilterParams;
      isLoading: boolean;
      userOrgs: IBaseConcrete[];
      events: IBaseConcrete[];
      locations: IBaseConcrete[];
      organisers: IBaseConcrete[];
      competitions: ICompetitionSummaryPublic[];
    },
    context: SetupContext
  ) {
    // const configStore = useConfigStore();

    const configStore = useConfigStore();
    const configController = useConfigController();

    const useEntryPublicFilterInput: IUseEntryPublicFilterInput = {
      compFilterParams: props.compFilterParams,
      isLoading: props.isLoading,
      userOrgs: configController.userOrgs.value,
      events: props.events,
      locations: configController.getStore.value.configMeta.locations,
      organisers: configController.getStore.value.configMeta.organisers,
    };

    const entryPublicFilterController = useEntryPublicFilterController(
      useEntryPublicFilterInput
    );

    watch(
      () => props.compFilterParams,
      (newValue: ICompFilterParams, oldValue: ICompFilterParams) => {
        const compFilterParams = CommonServiceUtils.simpleClone(newValue);
        // state.compFilterParams = compFilterParams;
        entryPublicFilterController.setConfig({
          compFilterParams: compFilterParams,
        });
      }
    );

    watch(
      () => configStore.configMeta.locations,
      (newValue: IBaseConcrete[], oldValue: IBaseConcrete[]) => {
        entryPublicFilterController.setConfig({
          locations: newValue,
        });
      }
    );

    watch(
      () => configStore.configMeta.organisers,
      (newValue: IBaseConcrete[], oldValue: IBaseConcrete[]) => {
        entryPublicFilterController.setConfig({
          organisers: newValue,
        });
      }
    );

    watch(
      () => configStore.configMeta.events,
      (newValue: IBaseConcrete[], oldValue: IBaseConcrete[]) => {
        entryPublicFilterController.setConfig({
          events: newValue,
        });
      }
    );

    watch(
      () => props.competitions,
      (
        newValue: ICompetitionSummaryPublic[],
        oldValue: ICompetitionSummaryPublic[]
      ) => {
        entryPublicFilterController.nudgeUserToAdvancedSearch(newValue);
      }
    );

    function cancel() {
      entryPublicFilterController.cancel();
      context.emit("cancel");
      context.emit(
        "doCompFilterSearch",
        CommonServiceUtils.simpleClone(
          entryPublicFilterController.state.compFilterParams
        )
      );
    }

    function close() {
      entryPublicFilterController.close();
      context.emit("close");
      context.emit(
        "doCompFilterSearch",
        CommonServiceUtils.simpleClone(
          entryPublicFilterController.state.compFilterParams
        )
      );
      // context.emit(
      //   "doCompFilterSearch",
      //   CommonServiceUtils.simpleClone(
      //     entryPublicFilterController.state.compFilterParams
      //   )
      // );
    }

    /**
     * User prob know what they are after, e.g. 313, Might of 1500...
     * since comp may have been 2 years ago...just ignore the date and
     * search all past hist...???  but if user types "Nu"...
     * it might bring back sched loads?...but I suppose it will page them.
     */
    function doQuickSearch() {
      entryPublicFilterController.doQuickSearch();
      context.emit(
        "doCompFilterSearch",
        CommonServiceUtils.simpleClone(
          entryPublicFilterController.state.compFilterParams
        )
      );
    }

    function doSearch() {
      entryPublicFilterController.doSearch();
      context.emit(
        "doCompFilterSearch",
        CommonServiceUtils.simpleClone(
          entryPublicFilterController.state.compFilterParams
        )
      );
    }

    function doAdvancedSearch() {
      entryPublicFilterController.doAdvancedSearch();
      context.emit(
        "doCompFilterSearch",
        CommonServiceUtils.simpleClone(
          entryPublicFilterController.state.compFilterParams
        )
      );
    }

    function setShowAllFilters(showAllFilters: boolean) {
      entryPublicFilterController.setShowAllFilters(showAllFilters);
      context.emit("showAllFilters", showAllFilters);
    }

    const getLocations = computed<IBaseConcrete[]>(() => {
      return [{ id: 0, name: "ALL" }].concat(configStore.configMeta.locations);
    });

    const getOrganisers = computed<IBaseConcrete[]>(() => {
      return [{ id: 0, name: "ALL" }].concat(configStore.configMeta.organisers);
    });

    return {
      doSearch,
      doQuickSearch,
      doAdvancedSearch,
      cancel,
      close,
      configStore,
      configController,
      entryPublicFilterController,
      setShowAllFilters,
      getLocations,
      getOrganisers,
    };
  },
});
</script>

<style>
.e4s-input--label-comp-filter {
  color: var(--e4s-sub-nav--primary__text-color) !important;
}
</style>
