import { ICompFilterParams } from "../../../../competition/v2/competition-data-v2";
import {
  eventDateDisplay,
  simpleClone,
} from "../../../../common/common-service-utils";

export function factoryCompFilterParams(): ICompFilterParams {
  // fromDate: new Date().toISOString().split("T")[0],
  return {
    fromDate: new Date().toISOString().split("T")[0],
    toDate: "",
    freeTextSearch: "",
    organiser: {
      id: 0,
      name: "",
    },
    location: {
      id: 0,
      name: "",
    },
    type: "ALL",
    compOrg: {
      id: 0,
      name: "",
    },
    event: [],
    pagenumber: 1,
  };
}

export function whichFiltersEnabled(
  compFilterParams: ICompFilterParams
): (keyof ICompFilterParams)[] {
  const filtersEnabled: (keyof ICompFilterParams)[] = [];
  if (compFilterParams.fromDate.length > 0) {
    filtersEnabled.push("fromDate");
  }
  if (compFilterParams.toDate.length > 0) {
    filtersEnabled.push("toDate");
  }
  return [...filtersEnabled, ...whichAdvancedFiltersEnabled(compFilterParams)];
}

export function whichAdvancedFiltersEnabled(
  compFilterParams: ICompFilterParams,
  dateNow?: string
): (keyof ICompFilterParams)[] {
  dateNow = dateNow ? dateNow : new Date().toISOString().split("T")[0];

  const filtersEnabled: (keyof ICompFilterParams)[] = [];
  if (compFilterParams.fromDate.length > 0) {
    // if (dateNow !== compFilterParams.fromDate.split("T")[0]) {
    filtersEnabled.push("fromDate");
    // }
  }
  if (compFilterParams.toDate.length > 0) {
    filtersEnabled.push("toDate");
  }
  if (compFilterParams.organiser.id > 0) {
    filtersEnabled.push("organiser");
  }
  if (compFilterParams.location.id > 0) {
    filtersEnabled.push("location");
  }
  if (compFilterParams.type !== "ALL") {
    filtersEnabled.push("type");
  }
  if (compFilterParams.compOrg.id > 0) {
    filtersEnabled.push("compOrg");
  }
  if (compFilterParams.event.length > 0) {
    filtersEnabled.push("event");
  }
  return filtersEnabled;
}

export function whichAdvancedFiltersEnabledWithValues(
  compFilterParams: ICompFilterParams,
  dateNow?: string
): Partial<ICompFilterParams> {
  dateNow = dateNow ? dateNow : new Date().toISOString().split("T")[0];

  compFilterParams = simpleClone(compFilterParams);

  const filtersEnabled: Partial<ICompFilterParams> = {};
  if (compFilterParams.fromDate.length > 0) {
    filtersEnabled.fromDate = compFilterParams.fromDate;
  }
  if (compFilterParams.toDate.length > 0) {
    filtersEnabled.toDate = compFilterParams.toDate;
  }
  if (compFilterParams.organiser.id > 0) {
    filtersEnabled.organiser = compFilterParams.organiser;
  }
  if (compFilterParams.location.id > 0) {
    filtersEnabled.location = compFilterParams.location;
  }
  if (compFilterParams.type !== "ALL") {
    filtersEnabled.type = compFilterParams.type;
  }
  if (compFilterParams.compOrg.id > 0) {
    filtersEnabled.compOrg = compFilterParams.compOrg;
  }
  if (compFilterParams.event.length > 0) {
    filtersEnabled.event = compFilterParams.event;
  }
  return filtersEnabled;
}

export type WhichFiltersEnabled = Partial<
  Record<keyof ICompFilterParams, FilterEnabled>
>;

export type FilterEnabled = { label: string; value: string };

export function whichAdvancedFiltersEnabledWithValuesDisplay(
  compFilterParams: ICompFilterParams,
  dateNow?: string
): WhichFiltersEnabled {
  dateNow = dateNow ? dateNow : new Date().toISOString().split("T")[0];

  compFilterParams = simpleClone(compFilterParams);

  const filtersEnabledDisplay: WhichFiltersEnabled = {};

  if (compFilterParams.fromDate.length > 0) {
    filtersEnabledDisplay["fromDate"] = {
      label: "From",
      value: eventDateDisplay(compFilterParams.fromDate.split("T")[0]),
    };
  }
  if (compFilterParams.toDate.length > 0) {
    filtersEnabledDisplay["toDate"] = {
      label: "To",
      value: compFilterParams.toDate,
    };
  }
  if (compFilterParams.organiser.id > 0) {
    filtersEnabledDisplay["organiser"] = {
      label: "Organiser",
      value: compFilterParams.organiser.name,
    };
  }
  if (compFilterParams.location.id > 0) {
    filtersEnabledDisplay["location"] = {
      label: "Location",
      value: compFilterParams.location.name,
    };
  }
  if (compFilterParams.type !== "ALL") {
    filtersEnabledDisplay["type"] = {
      label: "Event Type",
      value: compFilterParams.type,
    };
  }
  if (compFilterParams.compOrg.id > 0) {
    filtersEnabledDisplay["compOrg"] = {
      label: "Comp Organiser",
      value: compFilterParams.compOrg.name,
    };
  }
  if (compFilterParams.event.length > 0) {
    filtersEnabledDisplay["event"] = {
      label: "Organiser",
      value: compFilterParams.event.map((e) => e.name).join(", "),
    };
  }
  return filtersEnabledDisplay;
}
