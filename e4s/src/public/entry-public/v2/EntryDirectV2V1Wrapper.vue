<template>
  <div style="padding: 8px">
    <LoadingSpinnerV2 v-if="state.isLoading" />
    <div v-if="state.proceedToCompetitionSummaryPublic.compId > 0">
      <CompMoreInfoV2
        v-if="state.showSection === 'SUMMARY'"
        :competition-summary-public="state.proceedToCompetitionSummaryPublic"
        :show-comp-results-buttons="true"
        :set-section="state.showSection"
        @showContactOrganiser="setShowSection('CONTACT_ORGANISER')"
        @onOrgApproved="onOrgApproved"
        @goToCompV1="goToComp"
      />

      <div v-if="state.showSection === 'PRIORITY'">
        <PriorityV2
          :competition-summary-public="state.proceedToCompetitionSummaryPublic"
          v-on:cancel="priorityCodeResult(false)"
          v-on:submit="priorityCodeResult(true)"
        />
      </div>

      <div v-if="state.showSection === 'TERMS_CONDITIONS'">
        <TermsConditionsV2
          :competition-summary-public="state.proceedToCompetitionSummaryPublic"
          v-on:cancel="priorityCodeResult(false)"
          v-on:submit="handleTermsConditionsResult(true)"
        />
      </div>

      <div
        v-if="state.showSection === 'CONTACT_ORGANISER'"
        class="e4s-flex-column e4s-gap--standard"
      >
        <PublicCompCardV2
          :competition-summary-public="state.proceedToCompetitionSummaryPublic"
        >
          <ButtonGotoCompV2
            slot="button-more-info"
            class="e4s-flex-row--end"
            :competition-summary-public="
              state.proceedToCompetitionSummaryPublic
            "
            :config-version="configController.getVersion.value"
            @goToCompV1="goToComp"
          />
        </PublicCompCardV2>

        <div class="e4s-card e4s-card--generic">
          <AskOrganiserFormV2
            :competition-summary-public="
              state.proceedToCompetitionSummaryPublic
            "
            v-on:cancel="setShowSection('SUMMARY')"
          >
            <PrimaryLink
              slot="top-right-back-button"
              link-text="Back"
              @onClick="setShowSection('SUMMARY')"
            />
          </AskOrganiserFormV2>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, SetupContext } from "@vue/composition-api";
import { ICompetitionSummaryPublic } from "../../../competition/competition-models";
import { isBefore, parse } from "date-fns";
import { RawLocation } from "vue-router";
import { LAUNCH_ROUTES_PATHS } from "../../../launch/launch-routes";
import { simpleClone } from "../../../common/common-service-utils";
import { useAuthStoreController } from "../../../auth/useAuthStore";
import { useRoute, useRouter } from "../../../router/migrateRouterVue3";
import Priority from "../priority/priority.vue";
import TermsConditions from "../terms-conditions/terms-conditions.vue";
import CompMoreInfoV2 from "../public-list/v2/moreinfo/comp-more-info-v2.vue";
import ButtonGotoCompV2 from "../../../common/ui/layoutV2/buttons/button-goto-comp-v2.vue";
import AskOrganiserFormV2 from "../../../competition/askorganiser/ask-organiser-form-v2.vue";
import PublicCompCardV2 from "../public-list/v2/public-list-comp-card-v2.vue";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import { useConfigController } from "../../../config/useConfigStore";
import { CompetitionData } from "../../../competition/competition-data";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import { CompetitionService } from "../../../competition/competiton-service";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import TermsConditionsV2 from "../terms-conditions/terms-conditions-v2.vue";
import PriorityV2 from "../priority/priority-v2.vue";

type EntryDirectV2V1WrapperSectionType =
  | "SHOP_ONLY"
  | "PRIORITY"
  | "TERMS_CONDITIONS"
  | "SUMMARY"
  | "CONTACT_ORGANISER"
  | "";

interface IEntryPublicV2V1WrapperState {
  proceedToCompetitionSummaryPublic: ICompetitionSummaryPublic;
  showSection: EntryDirectV2V1WrapperSectionType;
  isLoading: boolean;
}

export default defineComponent({
  name: "EntryPublicV2V1Wrapper",
  components: {
    PriorityV2,
    TermsConditionsV2,
    LoadingSpinnerV2,
    PrimaryLink,
    PublicCompCardV2,
    AskOrganiserFormV2,
    ButtonGotoCompV2,
    CompMoreInfoV2,
    TermsConditions,
    Priority,
  },
  props: {},
  setup(props: any, context: SetupContext) {
    const route = useRoute();

    const competitionService = new CompetitionService();

    // const competitionSummaryPublic = ref(
    //   competitionService.factorySummaryPublic()
    // );

    const configController = useConfigController();

    // const showSection = ref<EntryDirectSectionType>("MORE_INFO");

    const state = reactive<IEntryPublicV2V1WrapperState>({
      proceedToCompetitionSummaryPublic:
        competitionService.factorySummaryPublic(),
      showSection: "SUMMARY",
      isLoading: false,
    });

    const authStoreController = useAuthStoreController();
    const routerInternal = useRouter();

    function goToComp(comp: ICompetitionSummaryPublic) {
      console.log("EntryPublicV2V1Wrapper.goToCompV1");
      state.proceedToCompetitionSummaryPublic = simpleClone(comp);

      const priorityDate =
        comp.options.priority &&
        comp.options.priority.dateTime &&
        comp.options.priority.dateTime.length > 0
          ? comp.options.priority.dateTime
          : "";

      const priorityAlwaysRequired =
        priorityDate.length === 0 && comp.options.priority.required;

      if (comp.options.priority.required) {
        if (
          isBefore(new Date(), parse(comp.options.priority.dateTime)) ||
          priorityAlwaysRequired
        ) {
          let location: RawLocation;
          if (!authStoreController.isLoggedIn.value) {
            location = {
              path: "/login",
              query: {
                redirectFrom:
                  "/" +
                  LAUNCH_ROUTES_PATHS.ENTRY_CONDITIONS +
                  "/" +
                  comp.compId,
              },
            };
          } else {
            location = {
              path:
                "/" + LAUNCH_ROUTES_PATHS.ENTRY_CONDITIONS + "/" + comp.compId,
            };
          }
          routerInternal.push(location);
          return;
        }
      }

      if (comp.termsConditions.length > 0) {
        state.showSection = "TERMS_CONDITIONS";
        return;
      }

      proceedToComp(comp);
    }

    function priorityCodeResult(isOk: boolean) {
      if (!isOk) {
        state.showSection = "SUMMARY";
        return;
      }
      if (
        state.proceedToCompetitionSummaryPublic.termsConditions &&
        state.proceedToCompetitionSummaryPublic.termsConditions.length > 0
      ) {
        state.showSection = "TERMS_CONDITIONS";
        return;
      }
      proceedToComp(state.proceedToCompetitionSummaryPublic);
    }

    function handleTermsConditionsResult(isOk: boolean) {
      if (!isOk) {
        state.showSection = "SUMMARY";
        return;
      }
      proceedToComp(state.proceedToCompetitionSummaryPublic);
    }

    function proceedToComp(comp: ICompetitionSummaryPublic) {
      // E4S-386
      if (comp.options.ui.entryDefaultPanel === "SHOP_ONLY") {
        routerInternal.push({
          path: "/shop/" + comp.compId,
        });
        return;
      }

      let location: RawLocation;
      if (!authStoreController.isLoggedIn.value) {
        location = {
          path: "/login",
          query: {
            redirectFrom:
              "/entry?comporgid=" + comp.compOrgId + "&compid=" + comp.compId,
          },
        };
      } else {
        location = {
          path: "/entry",
          query: {
            comporgid: comp.compOrgId.toString(),
            compid: comp.compId.toString(),
          },
        };
      }

      routerInternal.push(location);
    }

    const compId = isNaN(Number(route.params.id))
      ? 0
      : parseInt(route.params.id, 0);
    if (compId > 0) {
      state.isLoading = true;
      const prom = new CompetitionData().getCompById(compId);
      handleResponseMessages(prom);
      prom
        .then((resp) => {
          if (resp.errNo === 0) {
            const comp = resp.data;
            // if (!comp.newsFlash) {
            //   comp.newsFlash = "";
            // }
            //
            // if (!comp.information) {
            //   comp.information = "";
            // }
            //
            // if (!comp.termsConditions) {
            //   comp.termsConditions = "";
            // }

            state.proceedToCompetitionSummaryPublic = comp;
          }
        })
        .finally(() => {
          state.isLoading = false;
        });
    }

    function setShowSection(
      entryDirectSectionType: EntryDirectV2V1WrapperSectionType
    ) {
      state.showSection = entryDirectSectionType;
    }

    function onOrgApproved() {
      // state.showSection = "SUMMARY";
    }

    return {
      goToComp,
      state,
      priorityCodeResult,
      handleTermsConditionsResult,
      setShowSection,
      configController,
      onOrgApproved,
    };
  },
});
</script>
