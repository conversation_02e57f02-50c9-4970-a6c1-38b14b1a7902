<template>
  <div>
    <div class="row public-comp-list--row-spacing">
      <div class="col s6 m6 l6">
        <img class="public-comp-list--body-logo" :src="getLogo" />
      </div>

      <div class="col s6 m6 l6">
        <div class="right">
          <slot name="close-top-right">
            <CloseBack v-on:close="onCloseBody"></CloseBack>
          </slot>
        </div>
      </div>
    </div>

    <div class="row public-comp-list--row-spacing">
      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-name"> Competition !!</label>
        <span v-text="getCompName" id="comp-name"></span>
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-date">
          Date<span v-if="this.comp.dates.length > 0">(s)</span>
        </label>
        <span v-text="getCompDate" id="comp-date"></span>
      </div>
    </div>

    <div class="row">
      <div class="col s12" v-if="$mq === VUE_MQ_SIZES.MOBILE.name">
        <button
          class="
            btn
            waves-effect waves
            green
            public-comp-list--enter-button
            col
            s12
          "
          v-if="comp.options.resultsAvailable"
          v-on:click="goToResults"
        >
          <i class="material-icons normal">format_list_numbered</i>
          Results
        </button>

        <button
          :style="getCanEnter ? '' : 'visibility: hidden;'"
          class="
            btn
            waves-effect waves
            green
            public-comp-list--enter-button
            col
            s12
          "
          v-on:click="goToComp"
        >
          <span v-text="getEnterButtonText"></span>
        </button>

        <button
          class="
            btn
            waves-effect waves
            green
            public-comp-list--enter-button
            col
            s12
          "
          v-if="hasLinkingTicketComp"
          v-on:click="goToLinkingTicketComp"
        >
          <span v-text="comp.options.ui.ticketCompButtonText"></span>
        </button>
      </div>

      <div class="col s12 m12 l12" v-if="$mq !== VUE_MQ_SIZES.MOBILE.name">
        <button
          class="btn waves-effect waves green public-comp-list--enter-button"
          v-if="comp.options.resultsAvailable"
          v-on:click="goToResults"
        >
          <i class="material-icons normal">format_list_numbered</i>
          Results
        </button>

        <button
          :style="getCanEnter ? '' : 'visibility: hidden;'"
          class="btn waves-effect waves green public-comp-list--enter-button"
          v-on:click="goToComp"
        >
          <span v-text="getEnterButtonText"></span>
        </button>

        <button
          class="btn waves-effect waves green public-comp-list--enter-button"
          v-if="hasLinkingTicketComp"
          v-on:click="goToLinkingTicketComp"
        >
          <span v-text="comp.options.ui.ticketCompButtonText"></span>
        </button>
      </div>
    </div>
    <!--    /buttons-->
    <!--    /buttons-->
    <!--    /buttons-->

    <div class="row public-comp-list--row-spacing">
      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-location"> Location </label>
        <div v-text="getLocationText" id="comp-location"></div>
        <a
          v-if="comp.location.map.length > 0"
          :href="comp.location.map"
          target="_blank"
          >View Location on Map</a
        >
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-entry-open"> Entries Open </label>
        <span v-text="getEntryOpenDate" id="comp-entry-open"></span>
      </div>
    </div>

    <div class="row public-comp-list--row-spacing">
      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-entry-close"> Entries Close </label>
        <span v-text="getEntryCloseDate" id="comp-entry-close"></span>
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-price-increase"> Price Increase </label>
        <span v-text="getSaleEndDate" id="comp-price-increase"></span>
      </div>
    </div>

    <div class="row public-comp-list--row-spacing">
      <div class="input-field col s12 m6 l6">
        <label class="active" for="comp-entry-counts"> Entry Counts </label>
        <div id="comp-entry-counts">
          <a
            :href="getAllEntriesUrl"
            v-if="getShowAthleteCount"
            target="_blank"
          >
            Athletes: <span v-text="comp.entries.athletes"></span>
          </a>
          &nbsp;&nbsp;
          <a :href="getAllEntriesUrl" v-if="getShowIndivCount" target="_blank">
            Entries: <span v-text="comp.entries.indiv"></span>
          </a>
          &nbsp;&nbsp;
          <a :href="getAllEntriesUrl" v-if="getShowTeamCount" target="_blank">
            Teams: <span v-text="comp.entries.team"></span>
          </a>
        </div>
      </div>
    </div>

    <div class="list-card--restricted" v-if="getShowCompRestrictedMessage">
      <div class="e4s-section-padding-separator"></div>
      <CompRestricted :athlete-security="comp.options.athleteSecurity" />
    </div>

    <div class="row public-comp-list--row-spacing">
      <div
        class="input-field col s12 m6 l6"
        v-if="comp.options.contact.visible > 0"
      >
        <label class="active" for="comp-contact"> Contact </label>
        <div v-text="comp.options.contact.userName" id="comp-contact"></div>
      </div>

      <div class="input-field col s12 m6 l6">
        <template v-if="">
          <label class="active" for="comp-contact-details">
            Contact Details
          </label>
          <span
            v-text="getContactDetails"
            id="comp-contact-details"
            v-if="comp.options.contact.visible > 0"
          ></span>
        </template>

        <button
          v-if="!getHasBuilderAccess"
          class="btn waves-effect waves green right"
          v-on:click="showContactOrganiser"
        >
          Contact Organiser
        </button>
        <button
          v-if="getHasBuilderAccess"
          class="btn waves-effect waves green right"
          v-on:click="goToEmailMessages"
        >
          Contact Athletes
        </button>
      </div>

      <div
        class="input-field col s12 m6 l6"
        v-if="comp.areaname.toUpperCase() !== 'ALL'"
      >
        <label class="active" for="comp-athletes-from"> Athletes From </label>
        <span v-text="comp.areaname" id="comp-athletes-from"></span>
      </div>

      <div
        class="input-field col s12 m6 l6"
        v-if="comp.options.checkIn.enabled"
      >
        <label class="active" for="comp-athletes-from"> Online Check-in </label>
        <span v-if="getCanCheckinBeActive">
          <a
            :href="
              '#/' + LAUNCH_ROUTES_PATHS.ATHLETE_CHECKIN + '/' + comp.compId
            "
            >Check-in</a
          >
          &nbsp;&nbsp;|&nbsp;&nbsp;
          <a :href="'#/' + LAUNCH_ROUTES_PATHS.WHOISHERE + '/' + comp.compId">
            <span>Who has checked in</span>
          </a>
        </span>
        <span v-if="!getCanCheckinBeActive">
          Available from <span v-text="getCheckinDateTime"></span>
        </span>
      </div>
    </div>

    <div
      class="row public-comp-list--row-spacing"
      v-if="competitionMoreInfoV2.text.information > 0"
    >
      <div class="input-field col s12 m12 l12">
        <label class="active" for="comp-information"> Information </label>
        <div v-html="comp.information" id="comp-information"></div>
      </div>
    </div>

    <div
      class="row public-comp-list--row-spacing"
      v-if="comp.options.stadium && comp.options.stadium.length > 0"
    >
      <div class="col s12 m12 l12">
        <!--                <label class="active" for="comp-information">-->
        <!--                    Stadium Seating Layout-->
        <!--                </label>-->
        <button
          class="btn waves-effect waves green"
          v-on:click="openStadiumSeating"
        >
          View Seating Layout
        </button>
        <!--                <div-->
        <!--                    style="font-weight: 500"-->
        <!--                >-->
        <!--                    <a :href="getStadiumUrl" target="_seating">View Seating Layout</a>-->
        <!--                </div>-->
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <!--    buttons-->
    <!--    buttons-->
    <!--    buttons-->

    <!--    <div class="row public-comp-list&#45;&#45;row-spacing">-->
    <!--      <div class="col s12 m12 l12">-->
    <!--        <button-->
    <!--          v-if="getCanEnter"-->
    <!--          :style="getCanEnter ? '' : 'visibility: hidden;'"-->
    <!--          class="btn waves-effect waves green public-comp-list&#45;&#45;enter-button"-->
    <!--          v-on:click="goToComp"-->
    <!--        >-->
    <!--          <span v-text="getEnterButtonText"></span>-->
    <!--        </button>-->
    <!--      </div>-->
    <!--    </div>-->

    <div class="row" v-if="false">
      <div class="col s12" v-if="$mq === VUE_MQ_SIZES.MOBILE.name">
        <button
          class="
            btn
            waves-effect waves
            green
            public-comp-list--enter-button
            col
            s12
          "
          v-if="comp.options.resultsAvailable"
          v-on:click="goToResults"
        >
          <i class="material-icons normal">format_list_numbered</i>
          Results
        </button>

        <button
          :style="getCanEnter ? '' : 'visibility: hidden;'"
          class="
            btn
            waves-effect waves
            green
            public-comp-list--enter-button
            col
            s12
          "
          v-on:click="goToComp"
        >
          <span v-text="getEnterButtonText"></span>
        </button>

        <button
          class="
            btn
            waves-effect waves
            green
            public-comp-list--enter-button
            col
            s12
          "
          v-if="hasLinkingTicketComp"
          v-on:click="goToLinkingTicketComp"
        >
          <span v-text="comp.options.ui.ticketCompButtonText"></span>
        </button>
      </div>

      <div class="col s12 m12 l12" v-if="$mq !== VUE_MQ_SIZES.MOBILE.name">
        <button
          class="btn waves-effect waves green public-comp-list--enter-button"
          v-if="comp.options.resultsAvailable"
          v-on:click="goToResults"
        >
          <i class="material-icons normal">format_list_numbered</i>
          Results
        </button>

        <button
          :style="getCanEnter ? '' : 'visibility: hidden;'"
          class="btn waves-effect waves green public-comp-list--enter-button"
          v-on:click="goToComp"
        >
          <span v-text="getEnterButtonText"></span>
        </button>

        <button
          class="btn waves-effect waves green public-comp-list--enter-button"
          v-if="hasLinkingTicketComp"
          v-on:click="goToLinkingTicketComp"
        >
          <span v-text="comp.options.ui.ticketCompButtonText"></span>
        </button>
      </div>
    </div>
    <!--    /buttons-->
    <!--    /buttons-->
    <!--    /buttons-->

    <div>
      <div class="public-comp-list--section-separator"></div>

      <div class="row public-comp-list--row-spacing">
        <div
          class="
            col
            s12
            m12
            l12
            public-comp-list--label public-comp-list--section-label
          "
        >
          Links
        </div>
      </div>

      <div class="row public-comp-list--row-spacing">
        <div class="input-field col s12 m12 l12">
          <div class="row" id="org-links">
            <div class="col s6 m4 l3">
              <button
                v-if="comp.options.resultsAvailable"
                class="
                  btn
                  waves-effect waves
                  green
                  public-comp-list--enter-button
                "
                v-on:click="goToResults"
              >
                <i class="material-icons normal">format_list_numbered</i>
                Results
              </button>
            </div>

            <div class="col s6 m4 l3">
              <i class="material-icons normal">schedule</i>
              <a :href="getScheduleUrl" target="_blank">
                <span>Schedule</span>
              </a>
            </div>

            <div class="col s6 m4 l3" v-if="showFlyerUrl">
              <i class="material-icons normal">description</i>
              <a :href="comp.link" target="_blank">
                <span>Flyer</span>
              </a>
            </div>

            <div class="col s6 m4 l3" v-if="comp.options.cardInfo.enabled">
              <i class="material-icons normal">web</i>
              <a
                :href="
                  CONFIG.E4S_HOST +
                  CONFIG.ORGANISER_AND_PUBLIC_CARD.replace(
                    '{COMP_ID}',
                    comp.compId.toString()
                  )
                "
                target="_blank"
              >
                <span>Seeding</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="public-comp-list--section-separator"></div>

      <div
        class="row public-comp-list--row-spacing"
        v-if="
          getHasBuilderAccess ||
          getShowOrganiserCheckIn ||
          hasTicketAccess ||
          hasScoreboardAccess
        "
      >
        <div
          class="
            col
            s12
            m12
            l12
            public-comp-list--label public-comp-list--section-label
          "
        >
          Admin
        </div>
      </div>

      <div class="row public-comp-list--row-spacing">
        <div class="input-field col s12 m12 l12">
          <div class="row" id="org-links-admin">
            <template v-if="getHasBuilderAccess">
              <div class="col s6 m4 l3">
                <i class="material-icons normal">build</i>
                <a :href="'#/builder/' + comp.compId">
                  <span>Builder</span>
                </a>
              </div>

              <div class="col s6 m4 l3">
                <i class="material-icons normal">show_chart</i>
                <a
                  :href="
                    CONFIG.E4S_HOST +
                    CONFIG.ORGANISER_REPORT.replace(
                      '{COMP_ID}',
                      comp.compId.toString().replace('EVENT_ID', '')
                    )
                  "
                  target="_blank"
                >
                  <span>Organiser Report</span>
                </a>
              </div>

              <div class="col s6 m4 l3">
                <i class="material-icons normal">web</i>
                <a :href="'#/bibs/' + comp.compId">
                  <span>Bibs</span>
                </a>
              </div>

              <div class="col s6 m4 l3">
                <i class="material-icons normal">email</i>
                <a
                  :href="
                    '#/' +
                    LAUNCH_ROUTES_PATHS.EMAIL_MESSAGES +
                    '/' +
                    comp.compId
                  "
                >
                  <span>Email\Message</span>
                </a>
              </div>
            </template>

            <template v-if="hasTicketAccess">
              <div class="col s6 m4 l3">
                <i class="material-icons normal">camera_front</i>
                <a :href="'#/ticket-form-gatekeeper/' + comp.compId">
                  <span>Tickets (Scan)</span>
                </a>
              </div>

              <div class="col s6 m4 l3">
                <i class="material-icons normal">camera_rear</i>
                <a :href="'#/ticket-admin/' + comp.compId">
                  <span>Tickets (Admin)</span>
                </a>
              </div>
            </template>

            <!--                        <div class="col s6 m4 l3" v-if="getHasBuilderAccess">-->
            <!--                            <a :href="'/wp-json/e4s/v5/competition/checklist/' + comp.compId" target="_blank">-->
            <!--                                <span v-text="$t('public.checkLink')"></span>-->
            <!--                            </a>-->
            <!--                        </div>-->

            <div class="col s6 m4 l3" v-if="getShowOrganiserCheckIn">
              <i class="material-icons normal">access_alarm</i>
              <a :href="'#/checkin/' + comp.compId">
                <span>Check-in</span>
              </a>
            </div>

            <!--            <div class="col s6 m4 l3" v-if="getHasBuilderAccess">-->
            <!--              <i class="material-icons normal">camera_rear</i>-->
            <!--              <a :href="'#/ticket-admin/' + comp.compId">-->
            <!--                <span>Tickets (Admin)</span>-->
            <!--              </a>-->
            <!--            </div>-->

            <template v-if="hasScoreboardAccess">
              <div class="col s6 m4 l3">
                <i class="material-icons normal">slideshow</i>
                <a :href="'#/scoreboard-output-list/' + comp.compId">
                  <span>Scoreboard</span>
                </a>
              </div>

              <div class="col s6 m4 l3">
                <i class="material-icons normal">slideshow</i>
                <a
                  :href="
                    '#/' +
                    LAUNCH_ROUTES_PATHS.RESULTS_IMPORT_FILE +
                    '/' +
                    comp.compId
                  "
                >
                  <span>PhotoFinish</span>
                </a>
              </div>

              <div class="col s6 m4 l3">
                <!--                <i class="material-icons normal">web</i>-->
                <!--                <a-->
                <!--                  :href="'/wp-json/e4s/v5/public/uicard/' + comp.compId"-->
                <!--                  target="_blank"-->
                <!--                >-->
                <!--                  <span>Result Entry</span>-->
                <!--                </a>-->

                <span>
                  <i class="material-icons normal">show_chart</i>
                  <a
                    :href="
                      '#/' +
                      LAUNCH_ROUTES_PATHS.R4S_RESULTS_PUBLIC +
                      '/' +
                      comp.compId
                    "
                  >
                    <span>Results Entry</span>
                  </a>
                </span>
              </div>
            </template>

            <div class="col s6 m4 l3" v-if="getHasAutoEntryAccess">
              <i class="material-icons normal">directions_run</i>
              <a
                :href="
                  '#/' + LAUNCH_ROUTES_PATHS.AUTO_ENTRIES + '/' + comp.compId
                "
              >
                <span>Auto Entry</span>
              </a>
            </div>

            <div class="col s6 m4 l3" v-if="getHasAutoEntryAccess">
              <i class="material-icons normal">directions_run</i>
              <a :href="getFeederUrl">
                <span>Results Feeder</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="row" v-if="getHasBuilderAccess && !getCanEnter">
        <div class="col s12 m12 l12">
          <button
            class="
              btn
              waves-effect waves
              red
              public-comp-list--enter-button
              right
            "
            v-on:click="goToComp"
          >
            <span>Org Enter</span>
          </button>
        </div>
      </div>
    </div>

    <div v-if="isAdmin">
      <div class="public-comp-list--section-separator"></div>

      <div class="row public-comp-list--row-spacing">
        <div
          class="
            col
            s6
            m6
            l6
            public-comp-list--label public-comp-list--section-label
          "
        >
          E4S Section
        </div>
      </div>

      <div class="row public-comp-list--row-spacing">
        <div class="input-field col s12 m6 l6">
          <label class="active" for="e4s-status"> Status </label>
          <a href="#" v-on:click.prevent="showStatus" id="e4s-status">
            <span
              v-text="comp.status.description"
              :class="getStatusClass"
            ></span>
          </a>
        </div>
      </div>

      <BuilderContactOrganisers
        :org-name="comp.club"
        :organisers="comp.organisers"
      ></BuilderContactOrganisers>
    </div>

    <slot name="close">
      <div class="row public-comp-list--row-spacing">
        <div class="col s12 m12 l12">
          <div class="right">
            <slot name="close-button">
              <CloseBack v-on:close="onCloseBody"></CloseBack>
            </slot>
          </div>
        </div>
      </div>
    </slot>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import { format, isBefore, parse } from "date-fns";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { AUTH_STORE_CONST, IAuthStoreState } from "../../../auth/auth-store";
import {
  ICompetitionMoreInfoV2,
  ICompSummary,
} from "../../../competition/competition-models";
import { IConfigApp } from "../../../config/config-app-models";
import { EntryPublicService } from "../entry-public-service";
import { CompetitionService } from "../../../competition/competiton-service";
import { CONFIG, STADIUM_URL_MANCHESTER } from "../../../common/config";

import { LAUNCH_ROUTES_PATHS } from "../../../launch/launch-routes";
import { ConfigService } from "../../../config/config-service";
import CloseBack from "../../../common/ui/close-back.vue";
import { RawLocation } from "vue-router";
import CompRestricted from "../../../competition/restricted/comp-restricted.vue";
import { VUE_MQ_SIZES } from "../../../index";

@Component({
  name: "PubicListCompBodySummary",
  components: {
    CompRestricted,
    CloseBack,
    BuilderContactOrganisers: () => {
      return import(
        "../../../builder/form/builder-contact/builder-contact-organisers.vue"
      );
    },
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapState(AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME, {
      isLoggedIn: (state: IAuthStoreState) => state.isLoggedIn,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class PublicListCompBody extends Vue {
  public configApp: IConfigApp;
  public isLoggedIn: boolean;
  public isAdmin: boolean;

  @Prop({
    required: true,
  })
  public readonly comp: ICompSummary;

  @Prop({
    required: true,
  })
  public readonly competitionMoreInfoV2: ICompetitionMoreInfoV2;

  public entryPublicService: EntryPublicService = new EntryPublicService();
  public competitionService = new CompetitionService();
  public configService = new ConfigService();

  public LAUNCH_ROUTES_PATHS = LAUNCH_ROUTES_PATHS;
  public $mq: any;
  public VUE_MQ_SIZES = VUE_MQ_SIZES;

  public CONFIG = CONFIG;

  public get getCompDate() {
    const d = this.comp.dates.reduce((accum, currentDate, index) => {
      const startTime = format(parse(currentDate), "Do MMM YYYY");
      // accum += startTime;

      accum = index > 0 ? accum + " - " : accum;
      accum += startTime;

      return accum;
    }, "");
    return d;
  }

  public get getCompName() {
    return this.competitionService.getCompetitionTitle(this.comp);
  }

  public get getCanEnter(): boolean {
    return this.competitionService.canUserEnter(this.comp);
  }

  public get isPastLateEntry() {
    return this.competitionService.isPastLateEntry(this.comp);
  }

  public get getHasSaleEndDate() {
    return this.competitionService.hasSaleEndDate(this.comp);
  }

  public get getSaleEndDate() {
    return this.getHasSaleEndDate
      ? this.competitionService.getSaleEndDate(this.comp)
      : "Not Applicable";
  }

  public get getHasBuilderAccess() {
    return this.configService.hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.comp.compOrgId,
      this.comp.compId
    );
  }

  public get getHasAutoEntryAccess() {
    return (
      this.getHasBuilderAccess &&
      this.comp.options.autoEntries &&
      this.comp.options.autoEntries.selectedTargetComp.id > 0
    );
  }

  public get getHasLiveDataAccess() {
    return this.getHasBuilderAccess;
  }

  public get getLocationText() {
    return (
      this.comp.location.name +
      ": " +
      ["address1", "address2", "town", "county", "postcode"]
        .reduce((accum, prop) => {
          //  @ts-ignore
          if (this.comp.location[prop]) {
            //  @ts-ignore
            accum.push(this.comp.location[prop]);
          }
          return accum;
        }, [])
        .join(", ")
    );
  }

  public get showFlyerUrl() {
    return this.comp.link && this.comp.link.length > 0;
  }

  public get getScheduleUrl() {
    return (
      CONFIG.E4S_HOST +
      "/entry/v5/competition/schedule.php?compid=" +
      this.comp.compId
    );
  }

  public get getAllEntriesUrl() {
    return CONFIG.E4S_HOST + "/" + this.comp.compId + "/entries";
  }

  public get getLogo() {
    return CONFIG.E4S_HOST + "/" + this.comp.logo;
  }

  public get isLive() {
    return this.competitionService.isLive(this.comp);
  }

  public goToComp() {
    this.$emit("enterComp", this.comp);
  }

  public get getStatusClass() {
    return this.entryPublicService.getStatusClass(this.comp);
  }

  public get getShowTeamCount() {
    return !R.isNil(this.comp.entries.teamEventCount);
  }

  public get getShowIndivCount() {
    return !R.isNil(this.comp.entries.indiv);
  }

  public get getShowAthleteCount() {
    return !R.isNil(this.comp.entries.athletes);
  }

  public get getEntryOpenDate() {
    const pattern = "Do MMM HH:mm";
    return format(parse(this.comp.opendate), pattern);
  }

  public get getIsEntryOpen() {
    return this.competitionService.isEntryOpen(this.comp);
  }

  public get getEntryCloseDate() {
    const dateClose = format(parse(this.comp.closedate), "Do MMM HH:mm");
    if (this.competitionService.hasSaleEndDate(this.comp)) {
      return (
        dateClose +
        " (" +
        this.competitionService.getSaleEndMessage(this.comp) +
        ")"
      );
    }
    return dateClose;
  }

  public get getContactDetails() {
    const contact = this.comp.options.contact;
    const email = contact.email;
    const tel = contact.tel.length > 0 ? "tel: " + contact.tel : "";
    return email + (contact.email.length > 0 ? " " : "") + tel;
  }

  public showStatus() {
    this.$emit("onShowStatus", R.clone(this.comp));
  }

  public onCloseBody() {
    this.$emit("onCloseBody");
  }

  public get getShowOrganiserCheckIn() {
    //  getCanCheckinBeActive checks if past Check in date, this is wrong.
    // return this.getCanCheckinBeActive && this.hasCheckinAccess;
    return this.comp.options.checkIn.enabled && this.hasCheckinAccess;
  }

  public get getCanCheckinBeActive() {
    return this.entryPublicService.canCheckinLinkBeActive(this.comp);
  }

  public get hasCheckinAccess() {
    return this.configService.hasCheckinPermissionForComp(
      this.configApp.userInfo,
      this.comp.compOrgId,
      this.comp.compId
    );
  }

  public get hasTicketAccess() {
    return this.configService.hasTicketPermissionForComp(
      this.configApp.userInfo,
      this.comp.compOrgId,
      this.comp.compId
    );
  }

  public get hasScoreboardAccess() {
    return this.configService.hasScoreBoardPermissionForComp(
      this.configApp.userInfo,
      this.comp.compOrgId,
      this.comp.compId
    );
  }

  public get getCheckinDateTime() {
    const checkInDateTimeOpens = this.comp.options.checkIn.checkInDateTimeOpens;
    if (checkInDateTimeOpens.length === 0) {
      return "";
    }
    return format(parse(checkInDateTimeOpens), "Do MMM HH:mm");
  }

  public get getStadiumUrl() {
    return STADIUM_URL_MANCHESTER;
  }

  public openStadiumSeating() {
    window.open(this.getStadiumUrl, "_seating");
  }

  public get getEnterButtonText() {
    return this.competitionService.getEnterButtonText(this.comp.options);
  }

  public get getShowCompRestrictedMessage(): boolean {
    if (
      this.comp.options.athleteSecurity.clubs &&
      this.comp.options.athleteSecurity.clubs.length > 0
    ) {
      if (
        this.comp.options.athleteSecurity.onlyClubsUpTo &&
        this.comp.options.athleteSecurity.onlyClubsUpTo.length > 0
      ) {
        const onlyClubsUpTo = parse(
          this.comp.options.athleteSecurity.onlyClubsUpTo
        );
        return isBefore(new Date(), onlyClubsUpTo);
      }
      //  No date set, so comp completely locked down to these clubs.
      return true;
    }
    return false;
  }

  public showContactOrganiser() {
    this.$emit("showContactOrganiser", this.comp);
  }

  public goToEmailMessages() {
    let location: RawLocation;
    location = {
      path: "/" + LAUNCH_ROUTES_PATHS.EMAIL_MESSAGES + "/" + this.comp.compId,
    };
    this.$router.push(location);
  }

  public goToResults() {
    let location: RawLocation;
    location = {
      path:
        "/" + LAUNCH_ROUTES_PATHS.R4S_RESULTS_PUBLIC + "/" + this.comp.compId,
    };
    this.$router.push(location);
    // window.location.href = this.getResultsUrl;
  }

  public get hasLinkingTicketComp(): boolean {
    return !!(
      this.comp.options.ui.ticketComp && this.comp.options.ui.ticketComp > 0
    );
  }

  public get getLinkingTicketCompUrl(): string {
    return CONFIG.E4S_HOST + "/" + this.comp.options.ui.ticketComp;
  }

  public goToLinkingTicketComp() {
    window.location.href = this.getLinkingTicketCompUrl;
  }

  public get getFeederUrl(): string {
    return (
      "#/" +
      LAUNCH_ROUTES_PATHS.RESULTS_IMPORT +
      "/" +
      this.comp.options.autoEntries.selectedTargetComp.id +
      "?sourceid=" +
      this.comp.compId
    );
  }
}
</script>
