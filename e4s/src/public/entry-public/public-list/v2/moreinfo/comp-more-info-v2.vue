<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <LoadingSpinnerV2
      v-if="competitionSummaryPublicController.isLoading.value"
    />
    <PublicCompCardV2
      :competition-summary-public="competitionSummaryPublic"
      :is-displaying-results="
        state.sectionLinkSelected.uniqueDesc ===
        state.sectionLinkMap.SCHEDULE.uniqueDesc
      "
      @showMoreInfo="onChangedDefaultSection('SCHEDULE')"
    >
      <template slot="button-more-info">
        <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
          <div
            v-if="competitionSummaryPublicController.hasLinkingTicketComp.value"
          >
            <ButtonGenericV2
              class="e4s-button--150"
              :text="competitionSummaryPublic.options.ui.ticketCompButtonText"
              @click="
                competitionSummaryPublicController.goToLinkingTicketComp()
              "
            />
          </div>

          <ButtonGotoCompV2
            v-if="showGoToComp"
            :competition-summary-public="competitionSummaryPublic"
            :config-version="configController.getVersion.value"
            @goToCompV1="goToCompV1"
          />
        </div>
      </template>
    </PublicCompCardV2>

    <div class="e4s-card e4s-card--generic">
      <!--      <h1 class="e4s-header&#45;&#45;400">-->
      <!--        <div class="e4s-flex-row e4s-gap&#45;&#45;standard e4s-flex-wrap">-->
      <!--          <div>Information.</div>-->
      <!--        </div>-->
      <!--      </h1>-->

      <!--Tabs-->
      <div class="e4s-flex-row e4s-tab-links--section-links-bottom">
        <SectionLink
          class="e4s-header--400"
          :section-link="state.sectionLinkMap.INFO"
          :is-active="
            state.sectionLinkSelected.uniqueDesc ===
            state.sectionLinkMap.INFO.uniqueDesc
          "
          v-on:selected="onLinkSelected"
        />

        <SectionLink
          class="e4s-header--400"
          :section-link="state.sectionLinkMap.SCHEDULE"
          :is-active="
            state.sectionLinkSelected.uniqueDesc ===
            state.sectionLinkMap.SCHEDULE.uniqueDesc
          "
          v-on:selected="onLinkSelected"
        />

        <SectionLink
          v-if="compPermissions.hasSomeBuilderTypePermission.value"
          class="e4s-header--400"
          :section-link="state.sectionLinkMap.ORGANISER"
          :is-active="
            state.sectionLinkSelected.uniqueDesc ===
            state.sectionLinkMap.ORGANISER.uniqueDesc
          "
          v-on:selected="onLinkSelected"
        />
      </div>
      <!--/Tabs-->

      <div class="e4s-vertical-spacer--large"></div>

      <div
        class="e4s-flex-column"
        v-if="state.sectionLinkSelected.uniqueDesc === 'ORGANISER'"
      >
        <div class="e4s-vertical-spacer--standard"></div>

        <!--Organiser links-->
        <div
          class="e4s-flex-column e4s-gap--standard"
          v-if="compPermissions.hasBuilderPermissionForComp.value"
        >
          <div class="e4s-flex-row">
            <!--            <ButtonGenericV2-->
            <!--              text="Clone"-->
            <!--              @click="showCloneComp"-->
            <!--              class="e4s-flex-row&#45;&#45;end"-->
            <!--            />-->

            <ModalV2
              :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
              v-if="cloneComp.showConfirmation"
              :always-show-header-blank="true"
            >
              <div
                slot="body"
                style="margin: 8px; min-width: 300px; min-height: 100px"
              >
                <CloneComp
                  :comp-id="competitionSummaryPublic.compId"
                  @cancelClone="cloneComp.showConfirmation = false"
                  @success="goToClonedComp"
                />
              </div>
            </ModalV2>
          </div>

          <h5 class="e4s-header--500">Links</h5>

          <QuickLinksTableV2
            :competition-summary-public="competitionSummaryPublic"
            v-if="configController.getVersion.value === 'v2'"
          />
          <QuickLinksTableV1
            :competition-summary-public="competitionSummaryPublic"
            @clone-competition="showCloneComp"
            v-if="configController.getVersion.value === 'v1'"
          />
        </div>
        <!--/Organiser links-->

        <div
          v-if="configController.isAdmin.value"
          class="e4s-flex-column e4s-gap--standard"
        >
          <!--Admin-->
          <hr class="dat-e4s-hr-only" />

          <div class="e4s-header--400">Admin Section</div>

          <PrimaryHref
            :link="
              '#/' +
              LAUNCH_ROUTES_PATHS.RESULTS_IMPORT +
              '/' +
              competitionSummaryPublic.compId
            "
            link-text="Feed Results"
          />

          <div class="e4s-flex-row" v-if="!isOrgApproved">
            <OrgApprove
              :value="{
                id: competitionSummaryPublic.compOrgId,
                name: competitionSummaryPublic.club,
              }"
              @onApproved="onOrgApproved"
            />
          </div>

          <BuilderContactOrganisers
            :organisers="competitionSummaryPublic.organisers"
          />
          <!--/Admin-->
        </div>
      </div>

      <!--INFO-->
      <div
        class="e4s-flex-column"
        v-if="state.sectionLinkSelected.uniqueDesc === 'INFO'"
      >
        <div class="e4s-vertical-spacer--standard"></div>
        <!--        <div class="e4s-vertical-spacer&#45;&#45;standard"></div>-->

        <CompSectionDates
          :competition-summary-public="competitionSummaryPublic"
        />

        <!--location-->
        <div class="e4s-flex-column e4s-gap--standard" v-if="showMapLink">
          <hr class="dat-e4s-hr dat-e4s-hr-only" />

          <h5 class="e4s-header--500">Location</h5>
          <PrimaryLink
            :link="getLocationMapLink"
            :link-text="getLocationText"
            :link-options="{ widthFitContent: true }"
          />

          <div class="e4s-vertical-spacer--standard"></div>
        </div>
        <!--/location-->

        <!--flyer-->
        <div
          class="e4s-flex-column e4s-gap--standard"
          v-if="competitionSummaryPublicController.hasFlyer.value"
        >
          <hr class="dat-e4s-hr dat-e4s-hr-only" />

          <h5 class="e4s-header--500">Flyer</h5>
          <PrimaryLink
            :link="competitionSummaryPublicController.getFlyerLink.value"
            :link-text="getFlyerLinkLabel"
            :link-options="{ widthFitContent: true }"
          />
        </div>
        <!--/flyer-->

        <!--contact-->
        <div class="e4s-flex-column e4s-gap--standard" v-if="hasContactInfo">
          <div class="e4s-vertical-spacer--standard"></div>
          <hr class="dat-e4s-hr dat-e4s-hr-only" />

          <CompetitionContactInfo
            :competition-summary-public="competitionSummaryPublic"
            @contactOrganiser="contactOrganiser"
          />
        </div>
        <!--/contact-->

        <!--news-flash-->
        <div
          class="e4s-flex-column e4s-gap--standard"
          v-if="competitionSummaryPublic.newsFlash.length > 0"
        >
          <hr class="dat-e4s-hr dat-e4s-hr-only" />

          <h5 class="e4s-header--500">News Flash</h5>
          <div v-html="competitionSummaryPublic.newsFlash"></div>
        </div>
        <!--/news-flash-->

        <!--Information-->
        <div
          class="e4s-flex-column e4s-gap--standard"
          v-if="competitionSummaryPublic.information.length > 0"
        >
          <div class="e4s-vertical-spacer--standard"></div>
          <hr class="dat-e4s-hr dat-e4s-hr-only" />

          <h5 class="e4s-header--500">Information</h5>
          <div v-html="competitionSummaryPublic.information"></div>
        </div>
        <!--/Information-->

        <!--Age Groups-->
        <div class="e4s-vertical-spacer--standard"></div>
        <hr class="dat-e4s-hr dat-e4s-hr-only" />
        <div class="e4s-vertical-spacer--standard"></div>

        <h5 class="e4s-header--500">Age Groups</h5>
        <CompMoreInfoAgeGroups
          :age-groups="competitionSummaryPublicController.getAgeGroups.value"
        />
        <!--/Age Groups-->

        <div class="e4s-vertical-spacer--standard"></div>
        <hr class="dat-e4s-hr dat-e4s-hr-only" />
        <div class="e4s-vertical-spacer--standard"></div>

        <div class="e4s-flex-row">
          <ButtonGotoCompV2
            v-if="showGoToComp"
            class="e4s-flex-row--end"
            :competition-summary-public="competitionSummaryPublic"
            :config-version="configController.getVersion.value"
            @goToCompV1="goToCompV1"
          />
        </div>
      </div>
      <!--/INFO-->

      <div
        class="e4s-flex-column e4s-full-width"
        v-if="state.sectionLinkSelected.uniqueDesc === 'SCHEDULE'"
      >
        <LoadingSpinnerV2 v-if="isLoadingSchedule" />

        <!--        <ScheduleCompPublicV2-->
        <!--          v-if="!isLoadingSchedule"-->
        <!--          :r4s-comp-schedule="r4sCompScheduleState"-->
        <!--        />-->
        <!--        <ScheduleCompPublic-->
        <!--          v-if="!isLoadingSchedule"-->
        <!--          :use-simple-header="true"-->
        <!--          :r4s-comp-schedule="r4sCompScheduleState"-->
        <!--          @reloadSchedule="getScheduleData(true)"-->
        <!--        />-->

        <div
          class="e4s-flex-row e4s-gap--standard"
          v-if="configController.isAdmin.value"
        >
          <InputCheckboxV2
            v-model="showNewResults"
            value-label="Show new results"
          />
        </div>

        <div class="e4s-flex-column e4s-gap--standard">
          <!--          <div>-->
          <!--            {{ embeddedResultsMessage }}-->
          <!--          </div>-->
          <div class="e4s-flex-row e4s-gap--standard" v-if="!showNewResults">
            <ButtonGotoGenericV2
              goto-type="SCHEDULE"
              :comp-id="
                competitionSummaryPublicController
                  .competitionSummaryPublicInternal.value.compId
              "
            />

            <ButtonGotoGenericV2
              goto-type="ENTRIES"
              :comp-id="
                competitionSummaryPublicController
                  .competitionSummaryPublicInternal.value.compId
              "
            />

            <ButtonGotoGenericV2
              goto-type="SEEDING"
              :comp-id="
                competitionSummaryPublicController
                  .competitionSummaryPublicInternal.value.compId
              "
            />
          </div>
          <iframe
            v-if="showNewResults"
            :src="getLiteResultsLink"
            scrolling="no"
            width="100%"
            style="overflow: hidden; border: none"
            :height="embeddedResultsMessage.height + 'px'"
          >
          </iframe>
        </div>
      </div>

      <div v-if="state.sectionLinkSelected.uniqueDesc === 'AGE_GROUP'">
        <CompMoreInfoAgeGroups
          :age-groups="competitionSummaryPublicController.getAgeGroups.value"
        />
      </div>

      <div v-if="state.sectionLinkSelected.uniqueDesc === 'WAITING_LIST'">
        <CompMoreWaitingListV2
          :builder-subscription="competitionSummaryPublic.options.subscription"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeUnmount,
  onMounted,
  PropType,
  reactive,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { ICompetitionSummaryPublic } from "../../../../../competition/competition-models";
import CardGenericV2 from "../../../../../common/ui/layoutV2/card-generic-v2.vue";
import CompHeaderNameLocationV2 from "../comp-header-name-location-v2.vue";
import CompHeaderLogoCountsV2 from "../comp-header-logo-counts-v2.vue";
import ButtonGenericV2 from "../../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import CompContentV2 from "../comp-content-v2.vue";
import CompContentSectionCellV2 from "../comp-content-section-cell-v2.vue";
import { CONFIG } from "../../../../../common/config";
import { RawLocation } from "vue-router";
import { LAUNCH_ROUTES_PATHS } from "../../../../../launch/launch-routes";
import { useRouter } from "../../../../../router/migrateRouterVue3";
import { useCompPermissions } from "../../../../../config/useCompPermissions";
import ButtonGotoCompOrganiserV2 from "../../../../../common/ui/layoutV2/buttons/button-goto-comp-organiser-v2.vue";
import FormGenericV2 from "../../../../../common/ui/layoutV2/form/form-generic-v2.vue";
import FormGenericButtonBar from "../../../../../common/ui/layoutV2/form/form-generic-button-bar.vue";
import SectionLinks from "../../../../../common/ui/layoutV2/tabs/section-links.vue";
import { ISectionLink } from "../../../../../common/ui/layoutV2/tabs/section-links-models";
import { simpleClone } from "../../../../../common/common-service-utils";
import PublicCompCardV2 from "../public-list-comp-card-v2.vue";
import CompMoreInfoAgeGroups from "./comp-more-info-age-groups.vue";
import { Rs4Service } from "../../../../../competition/scoreboard/rs4/rs4-service";
import { IR4sCompSchedule } from "../../../../../competition/scoreboard/rs4/rs4-scoreboard-models";
import { handleResponseMessages } from "../../../../../common/handle-http-reponse";
import { BuilderService } from "../../../../../builder/builder-service";
import { ScoreboardData } from "../../../../../competition/scoreboard/scoreboard-data";
import LoadingSpinnerV2 from "../../../../../common/ui/loading-spinner-v2.vue";
import CompMoreWaitingListV2 from "./comp-more-waiting-list-v2.vue";
import { ILocation } from "../../../../../location/location-models";
import { CompMoreInfoSection } from "./comp-more-info-models";
import QuickLinksV2 from "../../../../../common/ui/layoutV2/links/QuickLinksV2.vue";
import SectionLink from "../../../../../common/ui/layoutV2/tabs/section-link.vue";
import { useGotoCompController } from "../../../../../common/ui/layoutV2/buttons/useGotoCompController";
import ButtonGotoCompV2 from "../../../../../common/ui/layoutV2/buttons/button-goto-comp-v2.vue";
import { useCompetitionSummaryPublicController } from "../../../../../competition/v2/useCompetitionSummaryPublicController";
import E4sLogoSvg from "../../../../../common/ui/svg/E4sLogoSvg.vue";
import CompetitionEntryCounts from "../../../../../competition/v2/CompetitionEntryCounts.vue";
import CompetitionHeaderSimple from "../../../../../competition/v2/CompetitionHeaderSimple.vue";
import CompSectionDates from "./CompSectionDates.vue";
import CompetitionContactInfo from "../../../../../competition/v2/CompetitionContactInfo.vue";
import * as CompetitonServiceV2 from "../../../../../competition/v2/competiton-service-v2";
import { useConfigController } from "../../../../../config/useConfigStore";
import PrimaryLink from "../../../../../common/ui/layoutV2/href/PrimaryLink.vue";
import QuickLinksTableV2 from "../../../../../common/ui/layoutV2/links/QuickLinksTableV2.vue";
import QuickLinksTableV1 from "../../../../../common/ui/layoutV2/links/v1/QuickLinksTableV1.vue";
import BuilderContactOrganisers from "../../../../../builder/form/builder-contact/builder-contact-organisers.vue";
import ButtonGotoGenericV2 from "../../../../../common/ui/layoutV2/buttons/button-goto-generic-v2.vue";
import InputCheckboxV2 from "../../../../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import OrgApprove from "../../../../../org/onboarding/ui/OrgApprove.vue";
import ModalV2 from "../../../../../common/ui/layoutV2/modal/modal-v2.vue";
import { VUE_MQ_SIZES } from "../../../../../index";
import CloneComp from "../../../../../competition/v2/clone/CloneComp.vue";
import PrimaryHref from "../../../../../common/ui/layoutV2/href/PrimaryHref.vue";
export type CompMoreInfoSectionLink = ISectionLink<CompMoreInfoSection>;

export interface ICompMoreInfoState {
  sectionLinkMap: Record<CompMoreInfoSection, CompMoreInfoSectionLink>;
  sectionLinks: CompMoreInfoSectionLink[];
  sectionLinkSelected: CompMoreInfoSectionLink;
}

export interface EmbeddedResultsMessage {
  width: number;
  height: number;
}

export default defineComponent({
  name: "comp-more-info-v2",
  computed: {
    VUE_MQ_SIZES() {
      return VUE_MQ_SIZES;
    },
  },
  components: {
    PrimaryHref,
    CloneComp,
    ModalV2,
    OrgApprove,
    InputCheckboxV2,
    ButtonGotoGenericV2,
    BuilderContactOrganisers,
    QuickLinksTableV1,
    QuickLinksTableV2,
    PrimaryLink,
    CompetitionContactInfo,
    CompSectionDates,
    CompetitionHeaderSimple,
    CompetitionEntryCounts,
    E4sLogoSvg,
    ButtonGotoCompV2,
    SectionLink,
    QuickLinksV2,
    CompMoreWaitingListV2,
    LoadingSpinnerV2,
    CompMoreInfoAgeGroups,
    PublicCompCardV2,
    SectionLinks,
    FormGenericButtonBar,
    FormGenericV2,
    ButtonGotoCompOrganiserV2,
    CompContentSectionCellV2,
    CompContentV2,
    ButtonGenericV2,
    CompHeaderLogoCountsV2,
    CompHeaderNameLocationV2,
    CardGenericV2,
  },
  props: {
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
    isLoading: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    showCompResultsButtons: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    showBottomButtonBar: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    showGoToComp: {
      type: Boolean,
      default: true,
    },
    setSection: {
      type: String as PropType<CompMoreInfoSection>,
      default: "INFO",
    },
  },
  setup(
    props: {
      competitionSummaryPublic: ICompetitionSummaryPublic;
      isLoading: boolean;
      showCompResultsButtons: boolean;
      showBottomButtonBar: boolean;
      showGoToComp: boolean;
      setSection: CompMoreInfoSection;
    },
    context: SetupContext
  ) {
    const router = useRouter();
    const compPermissions = useCompPermissions(props.competitionSummaryPublic);
    const rs4Service: Rs4Service = new Rs4Service();
    const r4sCompScheduleState = ref<IR4sCompSchedule>(
      rs4Service.factoryR4sCompSchedule()
    );
    const isLoadingSchedule = ref(false);
    const gotoCompController = useGotoCompController();
    const competitionSummaryPublicController =
      useCompetitionSummaryPublicController(props.competitionSummaryPublic);

    const showNewResults = ref(false);

    const configController = useConfigController();

    const cloneComp = reactive({
      showConfirmation: false,
    });

    const sectionLinkMap: Record<CompMoreInfoSection, CompMoreInfoSectionLink> =
      {
        INFO: {
          iconId: "1",
          uniqueDesc: "INFO",
          title: "Competition",
        },
        AGE_GROUP: {
          iconId: "2",
          uniqueDesc: "AGE_GROUP",
          title: "Age Groups",
        },
        SCHEDULE: {
          iconId: "3",
          uniqueDesc: "SCHEDULE",
          title: "Event Information",
        },
        WAITING_LIST: {
          iconId: "4",
          uniqueDesc: "WAITING_LIST",
          title: "Waiting List",
        },
        ENTER: {
          iconId: "5",
          uniqueDesc: "ENTER",
          title: "Enter",
        },
        ORGANISER: {
          iconId: "6",
          uniqueDesc: "ORGANISER",
          title: "Organiser",
        },
      };

    let sectionLinks: CompMoreInfoSectionLink[] = [
      sectionLinkMap.INFO,
      sectionLinkMap.SCHEDULE,
      sectionLinkMap.AGE_GROUP,
      sectionLinkMap.WAITING_LIST,
      sectionLinkMap.ORGANISER,
    ];

    sectionLinks = sectionLinks.map((sectionLink, index) => {
      sectionLink.iconId = (index + 1).toString();
      return sectionLink;
    });

    const defSection = sectionLinkMap[props.setSection];

    const state = reactive<ICompMoreInfoState>({
      sectionLinkMap: sectionLinkMap,
      sectionLinks: sectionLinks,
      sectionLinkSelected: defSection,
    });

    if (defSection.uniqueDesc === "SCHEDULE") {
      onLinkSelected(defSection);
    }

    window.scrollTo(0, 0);

    onMounted(() => {
      console.log("CompMoreInfoV2.onMounted");
      window.addEventListener("message", processIframeMessage);
    });

    onBeforeUnmount(() => {
      console.log("CompMoreInfoV2.onBeforeUnmount");
      window.removeEventListener("message", processIframeMessage);
    });

    watch(
      () => props.competitionSummaryPublic,
      (newValue: ICompetitionSummaryPublic) => {
        competitionSummaryPublicController.getMoreInfoData(newValue.compId);
        competitionSummaryPublicController.init(newValue);
      },
      {
        immediate: true,
      }
    );

    watch(
      () => props.setSection,
      (newValue: CompMoreInfoSection) => {
        onChangedDefaultSection(newValue);
      }
    );

    // function getMoreInfoData(compId: number) {
    //   if (compId === 0) {
    //     return;
    //   }
    //   CompetitionDataV2.getCompMoreInfo(compId).then((result) => {
    //     if (result.errNo === 0) {
    //       competitionMoreInfoV2.value = result.data;
    //     }
    //   });
    // }

    function processIframeMessage(message: MessageEvent) {
      embeddedResultsMessage.value = message.data as EmbeddedResultsMessage;
    }

    function contactOrganiser() {
      context.emit("showContactOrganiser", props.competitionSummaryPublic);
    }

    function goToComp() {
      context.emit("enterComp", props.competitionSummaryPublic);
    }

    const getScheduleUrl = computed(() => {
      return (
        CONFIG.E4S_HOST +
        "/entry/v5/competition/schedule.php?compid=" +
        props.competitionSummaryPublic.compId
      );
    });

    const getAllEntriesUrl = computed(() => {
      return (
        CONFIG.E4S_HOST +
        "/entry/v5/competition/showEntriesV2.php?compid=" +
        +props.competitionSummaryPublic.compId
      );
    });

    const hasFlyer = computed(() => {
      return (
        props.competitionSummaryPublic.link &&
        props.competitionSummaryPublic.link.length > 0
      );
    });

    function goToResults() {
      let location: RawLocation;
      location = {
        path:
          "/" +
          LAUNCH_ROUTES_PATHS.R4S_RESULTS_PUBLIC +
          "/" +
          props.competitionSummaryPublic.compId,
      };
      router.push(location);
    }

    function close() {
      context.emit("close", props.competitionSummaryPublic);
    }

    const hasInformation = computed(() => {
      return (
        props.competitionSummaryPublic.information &&
        props.competitionSummaryPublic.information.length > 0
      );
    });

    const showAdminSection = computed(() => {
      return (
        compPermissions.hasBuilderPermissionForComp.value ||
        compPermissions.hasTicketPermissionForComp.value ||
        compPermissions.hasTicketPermissionForComp.value ||
        compPermissions.hasAutoEntryAccess.value
      );
    });

    function onChangedDefaultSection(compMoreInfoSection: CompMoreInfoSection) {
      if (compMoreInfoSection === state.sectionLinkSelected.uniqueDesc) {
        return;
      }
      const compMoreInfoSectionLink: CompMoreInfoSectionLink =
        sectionLinkMap[compMoreInfoSection];
      if (compMoreInfoSectionLink) {
        onLinkSelected(compMoreInfoSectionLink);
      }
    }

    function onLinkSelected(compMoreInfoSectionLink: CompMoreInfoSectionLink) {
      state.sectionLinkSelected = simpleClone(compMoreInfoSectionLink);
      if (state.sectionLinkSelected.uniqueDesc === "SCHEDULE") {
        // getScheduleData();
        return;
      }
      if (state.sectionLinkSelected.uniqueDesc === "ENTER") {
        gotoCompController.process(props.competitionSummaryPublic);
        return;
      }
    }

    const getContactName = computed(() => {
      const contact = props.competitionSummaryPublic.options.contact;
      return contact.userName.length > 0 ? contact.userName : "";
    });

    const getContactTel = computed<string>(() => {
      const contact = props.competitionSummaryPublic.options.contact;
      const tel = contact.tel.toString();
      return tel.length > 0 ? tel : "";
    });

    const getContactEmail = computed(() => {
      const contact = props.competitionSummaryPublic.options.contact;
      return contact.email.length > 0 ? contact.email : "";
    });

    const hasContactInfo = computed(() => {
      return (
        getContactName.value.length > 0 ||
        getContactTel.value.length > 0 ||
        getContactEmail.value.length > 0
      );
    });

    const getFlyerLink = computed(() => {
      return CONFIG.E4S_HOST + props.competitionSummaryPublic.link;
    });

    const getFlyerLinkLabel = computed(() => {
      if (
        props.competitionSummaryPublic.options.flyerInfo &&
        props.competitionSummaryPublic.options.flyerInfo.length > 0
      ) {
        return props.competitionSummaryPublic.options.flyerInfo;
      }

      const flyerPrimaryLabel = "Link to open flyer";
      let secondaryLabel =
        props.competitionSummaryPublic.options.timetable === "final"
          ? "(Final)"
          : "(Provisional)";
      return flyerPrimaryLabel + " " + secondaryLabel;
    });

    function getScheduleData(forceReload?: boolean) {
      console.log("CompMoreInfoV2.getScheduleData  Start...");
      if (!forceReload) {
        if (
          r4sCompScheduleState.value.compId ===
          props.competitionSummaryPublic.compId
        ) {
          console.log(
            "CompMoreInfoV2.getScheduleData Already loaded...exiting."
          );
          return;
        }
      }

      isLoadingSchedule.value = true;
      const prom = new ScoreboardData().getCompSchedule(
        props.competitionSummaryPublic.compId,
        false
      );
      handleResponseMessages(prom);
      return prom
        .then((resp) => {
          if (resp.errNo === 0) {
            const r4sCompSchedule = resp.data;
            if (!r4sCompSchedule.autoEntries) {
              r4sCompSchedule.autoEntries =
                new BuilderService().factoryAutoEntries();
            }
            r4sCompScheduleState.value = r4sCompSchedule;
          }
          return;
        })
        .finally(() => {
          isLoadingSchedule.value = false;
        });
    }

    const getLocationMapLink = computed(() => {
      return (
        "https://www.google.com/maps?q=" +
        encodeURIComponent(
          getLocationArray(props.competitionSummaryPublic.location).join(",")
        )
      );
    });

    function getLocationArray(location: ILocation): string[] {
      return [
        location.name,
        location.address1,
        location.address2,
        location.town,
        location.postcode,
      ].filter((line) => {
        return line.length > 0;
      });
    }

    const getLocationText = computed(() => {
      return getLocationArray(props.competitionSummaryPublic.location).join(
        ", "
      );
    });

    const showMapLink = computed(() => {
      return getLocationArray(props.competitionSummaryPublic.location).join(
        ", "
      );
    });

    const canUserNotEnterCompetitionReasons = computed(() => {
      return CompetitonServiceV2.canUserNotEnterCompetitionReasons(
        props.competitionSummaryPublic,
        configController.getStore.value.configApp.userInfo
      ).join(", ");
    });

    function goToCompV1() {
      console.log("CompMoreInfoV2.goToCompV1");
      context.emit("goToCompV1", props.competitionSummaryPublic);
    }

    const getLiteResultsLink = computed(() => {
      return (
        "https://" +
        configController.getStore.value.configApp.lite +
        "/lite/#/results-em/" +
        props.competitionSummaryPublic.compId
      );
    });

    const isOrgApproved = computed(() => {
      return CompetitonServiceV2.isOrganiserApproved(
        props.competitionSummaryPublic
      );
    });

    function onOrgApproved() {
      context.emit("onOrgApproved");
    }

    const embeddedResultsMessage = ref<EmbeddedResultsMessage>({
      width: 0,
      height: 0,
    });

    function showCloneComp() {
      cloneComp.showConfirmation = true;
    }

    function goToClonedComp(compId: number) {
      console.log("CompMoreInfoV2.goToClonedComp");
      const location: RawLocation = {
        path: "/" + LAUNCH_ROUTES_PATHS.BUILDER + "/" + compId,
      };

      router.push(location);
    }

    return {
      state,
      contactOrganiser,
      goToComp,
      close,
      getScheduleUrl,
      getAllEntriesUrl,
      hasFlyer,
      hasInformation,
      goToResults,
      compPermissions,
      showAdminSection,
      LAUNCH_ROUTES_PATHS,
      onLinkSelected,
      onChangedDefaultSection,
      getContactName,
      getContactTel,
      getContactEmail,
      hasContactInfo,
      getFlyerLink,
      getFlyerLinkLabel,
      r4sCompScheduleState,
      isLoadingSchedule,
      getLocationMapLink,
      getLocationText,
      showMapLink,
      competitionSummaryPublicController,
      canUserNotEnterCompetitionReasons,
      configController,
      goToCompV1,
      getScheduleData,
      getLiteResultsLink,
      embeddedResultsMessage,
      showNewResults,
      onOrgApproved,
      isOrgApproved,
      cloneComp,
      goToClonedComp,
      showCloneComp,
    };
  },
});
</script>

<style></style>
