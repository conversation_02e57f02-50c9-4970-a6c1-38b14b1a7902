<template>
  <div class="e4s-flex-column">
    <!--CompDatesSection-->
    <CompDatesSection :competition-summary-public="competitionSummaryPublic" />
    <!--/CompDatesSection-->
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { ICompetitionSummaryPublic } from "../../../../../competition/competition-models";
import CompDatesSection from "./CompDatesSection.vue";

export default defineComponent({
  name: "CompInfoSection",
  components: { CompDatesSection },
  props: {
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
  },
  setup(
    props: { competitionSummaryPublic: ICompetitionSummaryPublic },
    context: SetupContext
  ) {
    return {};
  },
});
</script>
