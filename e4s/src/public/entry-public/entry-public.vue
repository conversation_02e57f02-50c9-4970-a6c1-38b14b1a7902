<template>
  <div>
    <LoadingSpinnerV2 v-if="isLoadingAnything" />
    <div v-show="showSection === sections.SUMMARY">
      <div class="row" v-if="getShowHomeMessage">
        <div class="col s12 m12 l12">
          <div class="comp-alert-message">
            <div v-text="configApp.options.homeMessage"></div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col s12 m12 l12">
          <input
            id="filter"
            class="e4s-bold-input"
            :disabled="isLoadingCompSummary"
            placeholder="FILTER: Enter filter term here e.g. competition number, organiser, name, location"
            v-model="filterTerm"
            v-on:keyup="startFilter"
          />
          <!--          <a href="#"-->
          <!--             v-on:click.prevent="setDisplayType('CARD')"-->
          <!--             title="view as cards"-->
          <!--             v-if="displayType === 'LIST'">-->
          <!--            <i class="material-icons">grid_on</i>-->
          <!--          </a>-->
          <!--          <a href="#"-->
          <!--             v-on:click.prevent="setDisplayType('LIST')"-->
          <!--             title="view as list"-->
          <!--             v-if="displayType === 'CARD'">-->
          <!--            <i class="material-icons">list</i>-->
          <!--          </a>-->
        </div>
      </div>

      <div class="row">
        <div class="col s12 m12 l12">
          <LoadingSpinner v-if="isLoadingCompSummary"></LoadingSpinner>

          <div
            class="e4s-section-wrapper e4s-section-wrapper-mobile-public"
            v-if="!isLoadingCompSummary"
          >
            <div class="e4s-section-padding-separator"></div>

            <div>
              <div v-for="comp in compsInternal">
                <PublicListCompHeader
                  :comp="comp"
                  v-on:showMoreInfo="showMoreInfoList"
                  v-on:enterComp="goToComp"
                  v-on:doShowNewsFlash="doShowNewsFlash"
                  v-on:onShowStatus="openStatus"
                  class="list-card-wrapper"
                >
                </PublicListCompHeader>
              </div>
            </div>

            <E4sModal v-if="showStatus">
              <div slot="header"></div>
              <StatusContainer
                slot="header"
                :competition-summary-public-prop="selectedCompetitionPublic"
                :work-flows="workFlows"
                v-on:onClose="closeStatus"
              >
              </StatusContainer>
              <div slot="footer"></div>
            </E4sModal>
          </div>
        </div>
      </div>
    </div>

    <div v-if="showSection === sections.MORE_INFO">
      <!--      <PublicListCompBody-->
      <!--        :comp="moreInfoComp"-->
      <!--        class="list-body-wrapper"-->
      <!--        v-on:onShowStatus="openStatus"-->
      <!--        v-on:enterComp="goToComp"-->
      <!--        v-on:showContactOrganiser="proceedShowContactOrganiser"-->
      <!--        v-on:onCloseBody="showSection = sections.SUMMARY"-->
      <!--      >-->
      <!--      </PublicListCompBody>-->
      <PublicListCompBodySummary
        :comp="moreInfoComp"
        :competition-more-info-v2="competitionMoreInfoV2"
        class="list-body-wrapper"
        v-on:onShowStatus="openStatus"
        v-on:enterComp="goToComp"
        v-on:showContactOrganiser="proceedShowContactOrganiser"
        v-on:onCloseBody="showSection = sections.SUMMARY"
      />
    </div>

    <E4sModal v-if="showStatus">
      <div slot="header"></div>
      <StatusContainer
        slot="header"
        :competition-summary-public-prop="selectedCompetitionPublic"
        :work-flows="workFlows"
        v-on:onClose="closeStatus"
      >
      </StatusContainer>
      <div slot="footer"></div>
    </E4sModal>

    <E4sModal
      v-if="showNewsFlash"
      header-message="Important Message"
      :no-padding="true"
      v-on:closePrimary="showNewsFlash = false"
    >
      <div
        slot="header"
        style="
          font-size: 1.5em;
          padding: 0.25em;
          background-color: #006940;
          color: white;
        "
      >
        Important Message
      </div>
      <div
        slot="body"
        v-html="selectedCompetitionPublic.newsFlash"
        style="padding: 0 1em 0 1em"
      ></div>
      <div slot="button-close-secondary"></div>
    </E4sModal>

    <div v-if="showSection === sections.PRIORITY">
      <Priority
        :competition-summary-public="proceedToCompetitionSummaryPublic"
        v-on:cancel="priorityCodeResult(false)"
        v-on:submit="priorityCodeResult(true)"
      >
      </Priority>
    </div>

    <div v-if="showSection === sections.TERMS_CONDITIONS">
      <TermsConditions
        :competition-summary-public="proceedToCompetitionSummaryPublic"
        v-on:cancel="priorityCodeResult(false)"
        v-on:submit="handleTermsConditionsResult(true)"
      >
      </TermsConditions>
    </div>

    <AskOrganiserModal
      :show-contact-organiser="showContactOrganiser"
      :selected-competition="selectedCompetitionPublic"
      v-on:onClose="showContactOrganiser = false"
    >
    </AskOrganiserModal>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Watch } from "vue-property-decorator";
import { mapState } from "vuex";
import { AUTH_STORE_CONST, IAuthStoreState } from "../../auth/auth-store";
import LoadingSpinner from "../../common/ui/loading-spinner.vue";
import {
  ICompetitionMoreInfoV2,
  ICompetitionSummaryPublic,
  ICompSummary,
  IWorkFlow,
} from "../../competition/competition-models";
import { CompetitionService } from "../../competition/competiton-service";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import {
  PUBLIC_COMPS_STORE_CONST,
  IPublicCompsStoreState,
} from "./public-comps-store";
import { IObjectKeyType } from "../../common/common-models";
import { CommonService } from "../../common/common-service";
import E4sModal from "../../common/ui/e4s-modal.vue";
import StatusContainer from "../../competition/status/status-container.vue";
import PublicListCompHeader from "./public-list/public-list-comp-header.vue";
import { RawLocation } from "vue-router";
import PublicListCompBody from "./public-list/public-list-comp-body.vue";
import PublicCompCard from "./public-card/public-comp-card.vue";
import { WindowController } from "../../common/window-controller";
import { parse, isBefore } from "date-fns";
import Priority from "./priority/priority.vue";
import TermsConditions from "./terms-conditions/terms-conditions.vue";
import { IConfigApp } from "../../config/config-app-models";
import { LAUNCH_ROUTES_PATHS } from "../../launch/launch-routes";
import AskOrganiserModal from "../../competition/askorganiser/ask-organiser-modal.vue";
import { simpleClone } from "../../common/common-service-utils";
import PublicListCompBodySummary from "./public-list/public-list-comp-body-summary.vue";
import * as CompetitionDataV2 from "../../competition/v2/competition-data-v2";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";

@Component({
  name: "entry-public",
  components: {
    LoadingSpinnerV2,
    PublicListCompBodySummary,
    AskOrganiserModal,
    TermsConditions,
    Priority,
    PublicCompCard,
    PublicListCompHeader,
    PublicListCompBody,
    StatusContainer,
    E4sModal,
    LoadingSpinner,
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapState(AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME, {
      isLoggedIn: (state: IAuthStoreState) => state.isLoggedIn,
    }),
    ...mapState(PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_CONST_MODULE_NAME, {
      competitionSummaryPublic: (state: IPublicCompsStoreState) =>
        state.competitionSummaryPublic,
      isLoadingCompSummary: (state: IPublicCompsStoreState) =>
        state.isLoadingCompSummary,
      isLoadingClubs: (state: IPublicCompsStoreState) =>
        state.isLoadingCompSummary,
      workFlows: (state: IPublicCompsStoreState) => state.workFlows,
      compSimpleFilter: (state: IPublicCompsStoreState) =>
        state.compSimpleFilter,
      displayType: (state: IPublicCompsStoreState) => state.displayType,
    }),
  },
})
export default class EntryPublic extends Vue {
  public readonly competitionSummaryPublic: ICompetitionSummaryPublic[];
  public readonly isLoadingCompSummary: boolean;
  public readonly compSimpleFilter: string;
  public readonly workFlows: IWorkFlow[];
  public readonly displayType: string;
  public readonly configApp: IConfigApp;

  public windowController: WindowController = new WindowController();

  public competitionService = new CompetitionService();
  public isLoggedIn: boolean;
  public filterTerm: string = "";

  public compsInternal: ICompetitionSummaryPublic[] = [];
  public isLoadingInternal: boolean = false;

  public $mq: any;
  public compChunks: any = [];

  public moreInfoComp: ICompSummary =
    this.competitionService.factoryCompSummary();

  public competitionMoreInfoV2: ICompetitionMoreInfoV2 = {
    ageGroups: {},
    text: {
      checkinTerms: "",
      checkinText: "",
      e4sNotes: "",
      emailText: "",
      homeInfo: "",
      information: "",
      newsFlash: "",
      termsConditions: "",
    },
  };

  public commonService: CommonService = new CommonService();
  public selectedCompetitionPublic: ICompetitionSummaryPublic =
    this.competitionService.factorySummaryPublic();
  public showStatus: boolean = false;
  public showNewsFlash: boolean = false;

  public proceedToCompetitionSummaryPublic: ICompetitionSummaryPublic =
    this.competitionService.factorySummaryPublic();

  public displayTypeCookieName: string = "E4S_DISPLAY_TYPE";
  public showContactOrganiser: boolean = false;

  public sections = {
    SUMMARY: "SUMMARY",
    MORE_INFO: "MORE_INFO",
    PRIORITY: "PRIORITY",
    TERMS_CONDITIONS: "TERMS_CONDITIONS",
  };
  public showSection: string = this.sections.SUMMARY;

  public created() {
    // this.compsInternal = R.clone(this.competitionSummaryPublic);
    this.startFilter();

    let displayTypeCookie = this.windowController.getCookie(
      this.displayTypeCookieName
    );
    displayTypeCookie =
      displayTypeCookie.length === 0 ? "LIST" : displayTypeCookie;
    if (displayTypeCookie.length > 0) {
      this.$store.commit(
        PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_CONST_MODULE_NAME +
          "/" +
          PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_MUTATIONS_SET_DISPLAY_TYPE,
        displayTypeCookie
      );
    }
  }

  public mounted() {
    this.$store.dispatch(
      PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_CONST_MODULE_NAME +
        "/" +
        PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_ACTIONS_GET_COMPS
    );

    this.filterTerm = this.compSimpleFilter;
    if (this.filterTerm.length > 0) {
      this.startFilter();
    }
  }

  @Watch("competitionSummaryPublic")
  public onCompetitionSummaryPublicChanged(
    newValue: ICompetitionSummaryPublic[]
  ) {
    this.compsInternal = R.clone(this.competitionSummaryPublic);
    // if (this.filterTerm.length > 0) {
    this.startFilter();
    // }
  }

  public startFilter() {
    this.$store.commit(
      PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_CONST_MODULE_NAME +
        "/" +
        PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_MUTATIONS_SET_SIMPLE_FILTER,
      this.filterTerm
    );
    let comps = simpleClone(this.competitionSummaryPublic);
    if (this.filterTerm.length > 0) {
      comps = this.competitionService.filterPublicCompetitions(
        this.filterTerm,
        comps
      );
    }
    comps = comps.sort(function (a, b) {
      let aString = a.compDate ? a.compDate : "";
      if (a.dates && a.dates.length > 0) {
        aString = a.dates[a.dates.length - 1];
      }

      let bString = b.compDate ? b.compDate : "";
      if (b.dates && b.dates.length > 0) {
        bString = b.dates[b.dates.length - 1];
      }

      return bString.localeCompare(aString);
    });
    this.compsInternal = comps;
  }

  @Watch("$mq")
  public onMqChanged(mq: string) {
    this.calcChunks();
  }

  @Watch("compsInternal")
  public onCompsChanged(newValue: ICompetitionSummaryPublic[]) {
    this.calcChunks();
  }

  public get getShowHomeMessage() {
    return (
      this.configApp.options &&
      this.configApp.options.homeMessage &&
      this.configApp.options.homeMessage.length > 0
    );
  }

  public setDisplayType(displayType: "CARD" | "LIST") {
    // this.displayType = displayType;
    this.$store.commit(
      PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_CONST_MODULE_NAME +
        "/" +
        PUBLIC_COMPS_STORE_CONST.PUBLIC_COMPS_MUTATIONS_SET_DISPLAY_TYPE,
      displayType
    );

    this.windowController.setCookie(this.displayTypeCookieName, displayType);
  }

  public calcChunks() {
    const chunkSize: number = this.getChunkSize(this.$mq);
    this.compChunks = this.commonService.chunkArray(
      this.compsInternal,
      chunkSize
    );
  }

  public getChunkSize(mq: string): number {
    const chunks: IObjectKeyType<number> = {
      mobile: 1,
      tablet: 2,
      desktop: 3,
    } as IObjectKeyType<number>;
    return chunks[mq];
  }

  public openStatus(comp: ICompetitionSummaryPublic) {
    this.selectedCompetitionPublic = R.clone(comp) as ICompetitionSummaryPublic;
    this.showStatus = true;
  }

  public closeStatus() {
    this.showStatus = false;
  }

  public showMoreInfoList(comp: ICompSummary) {
    if (comp.compId === 0) {
      return;
    }
    this.moreInfoComp = simpleClone(comp);
    this.isLoadingInternal = true;
    CompetitionDataV2.getCompMoreInfo(comp.compId)
      .then((result) => {
        if (result.errNo === 0) {
          this.competitionMoreInfoV2 = result.data;
        }
        this.showSection = this.sections.MORE_INFO;
      })
      .finally(() => {
        this.isLoadingInternal = false;
      });
  }

  public doShowNewsFlash(comp: ICompetitionSummaryPublic) {
    this.selectedCompetitionPublic = R.clone(comp);
    this.showNewsFlash = true;
  }

  public goToComp(comp: ICompetitionSummaryPublic) {
    this.proceedToCompetitionSummaryPublic = R.clone(comp);

    const priorityDate =
      comp.options.priority &&
      comp.options.priority.dateTime &&
      comp.options.priority.dateTime.length > 0
        ? comp.options.priority.dateTime
        : "";

    const priorityAlwaysRequired =
      priorityDate.length === 0 && comp.options.priority.required;

    // if (comp.options.priority.required ||  priorityDate.length > 0) {
    if (comp.options.priority.required) {
      if (
        isBefore(new Date(), parse(comp.options.priority.dateTime)) ||
        priorityAlwaysRequired
      ) {
        // this.showSection = this.sections.PRIORITY
        let location: RawLocation;
        if (!this.isLoggedIn) {
          location = {
            path: "/login",
            query: {
              redirectFrom:
                "/" + LAUNCH_ROUTES_PATHS.ENTRY_CONDITIONS + "/" + comp.compId,
            },
          };
        } else {
          location = {
            path:
              "/" + LAUNCH_ROUTES_PATHS.ENTRY_CONDITIONS + "/" + comp.compId,
          };
        }
        this.$router.push(location);
        return;
      }
    }

    if (comp.termsConditions.length > 0) {
      this.showSection = this.sections.TERMS_CONDITIONS;
      return;
    }

    this.proceedToComp(comp);
  }

  public priorityCodeResult(isOk: boolean) {
    if (!isOk) {
      this.showSection = this.sections.SUMMARY;
      return;
    }
    if (
      this.proceedToCompetitionSummaryPublic.termsConditions &&
      this.proceedToCompetitionSummaryPublic.termsConditions.length > 0
    ) {
      this.showSection = this.sections.TERMS_CONDITIONS;
      return;
    }
    this.proceedToComp(this.proceedToCompetitionSummaryPublic);
  }

  public handleTermsConditionsResult(isOk: boolean) {
    if (!isOk) {
      this.showSection = this.sections.SUMMARY;
      return;
    }
    this.proceedToComp(this.proceedToCompetitionSummaryPublic);
  }

  public proceedToComp(comp: ICompetitionSummaryPublic) {
    if (comp.options.ui.entryDefaultPanel === "SHOP_ONLY") {
      this.$router.push({
        path: "/shop/" + comp.compId,
      });
      return;
    }

    let location: RawLocation;
    if (!this.isLoggedIn) {
      location = {
        path: "/login",
        query: {
          redirectFrom:
            "/entry?comporgid=" + comp.compOrgId + "&compid=" + comp.compId,
        },
      };
    } else {
      location = {
        path: "/entry",
        query: {
          comporgid: comp.compOrgId.toString(),
          compid: comp.compId.toString(),
        },
      };
    }

    this.$router.push(location);
  }

  public proceedShowContactOrganiser(comp: ICompetitionSummaryPublic) {
    this.selectedCompetitionPublic = R.clone(comp);
    this.showContactOrganiser = true;
  }

  public get isLoadingAnything() {
    return this.isLoadingCompSummary || this.isLoadingInternal;
  }
}
</script>

<style scoped>
.std {
  font-size: 16px !important;
}
.drop-wrap {
  padding-top: 1rem !important;
}
.club-drop-filter-label {
  padding-right: 1rem !important;
  /*font-weight: 500 !important;*/
  font-size: 1.5rem !important;
}
.card.small {
  height: auto !important;
}
.public-list-comp {
  margin-bottom: 0.5em;
}
.list-card-wrapper {
  border: 1px solid #d0d0d0;
  padding: 0.5em;
  margin-bottom: 0.5em;
}
.list-body-wrapper {
  margin-top: 1em;
}
</style>
