<template>
    <div>
        <div class="e4s-section-padding-separator"></div>

        <slot name="header">
            <div class="row">
                <div class="col s12 m12 l12">
                    <div v-text="getCompName" class="e4s-1-5-em"></div>
                </div>
            </div>
        </slot>

<!--        <div class="e4s-section-padding-separator"></div>-->

<!--        <div class="row">-->
<!--            <div class="col s12 m12 l12">-->
<!--                There are terms-->
<!--            </div>-->
<!--        </div>-->

        <SectionDividerLine></SectionDividerLine>

        <div class="row">
            <div class="col s12 m12 l12">
                <div v-html="competitionSummaryPublic.termsConditions"></div>
            </div>
        </div>

<!--        <div class="e4s-section-padding-separator"></div>-->
        <SectionDividerLine></SectionDividerLine>

        <slot name="buttons">
            <div class="row">
                <div class="input-field col s12 m12 l12">

                    <div class="right">

                        <LoadingSpinner v-if="isLoading"></LoadingSpinner>

                        <button v-on:click.stop="cancel"
                                class="btn waves-effect waves red">
                            <span>Cancel</span>
                        </button>

                        <button class="btn waves-effect waves green"
                                v-on:click.stop="submit">
                            <span>I Agree</span>
                        </button>
                    </div>

                </div>
            </div>
        </slot>

    </div>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop} from "vue-property-decorator"
    import {CompetitionService} from "../../../competition/competiton-service"
    import {ICompetitionSummaryPublic} from "../../../competition/competition-models"

    import SectionDividerLine from "../../../common/ui/section-divider-line.vue";

    const competitionService = new CompetitionService();

    @Component({
        name: "terms-conditions",
        components: {
            SectionDividerLine
        }
    })
    export default class TermsConditions extends Vue {

        @Prop({
            default: () => {
                return competitionService.factorySummaryPublic()
            }
        }) public readonly competitionSummaryPublic: ICompetitionSummaryPublic;

        @Prop({default:false}) public readonly isLoading: boolean;

        public get getCompName() {
            return competitionService.getCompetitionTitle(this.competitionSummaryPublic);
        }

        public cancel() {
            this.$emit("cancel");
        }

        public submit() {
            this.$emit("submit");
        }

    }

</script>
