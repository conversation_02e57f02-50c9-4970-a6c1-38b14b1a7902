<template>
  <div>
    <div class="e4s-flex-column">
      <HeaderSectionThickLine title="Discipline Details" />

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputTextV2
            form-label="Name"
            v-model="eventE4s.name"
            :is-required-field="true"
          />

          <FormGenericInputTemplateV2
            form-label="Gender"
            :is-required-field="true"
          >
            <select
              slot="field"
              :id="PREFIX + 'gender'"
              :name="PREFIX + 'gender'"
              class="browser-default e4s-input-field e4s-input-field--primary"
              v-validate="'required'"
              v-model="eventE4s.gender"
            >
              <option value="" disabled selected hidden>Select</option>
              <option :value="gender.FEMALE">Female</option>
              <option :value="gender.MALE">Male</option>
            </select>
          </FormGenericInputTemplateV2>
        </template>
      </FormGenericFieldGridV2>

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputTemplateV2
            form-label="Event Type"
            :is-required-field="true"
          >
            <select
              slot="field"
              :id="PREFIX + 'type'"
              class="browser-default e4s-input-field e4s-input-field--primary"
              v-model="eventE4s.tf"
            >
              <option value="" disabled selected hidden>Select</option>
              <option
                v-for="option in getEventTypes"
                :key="option.id"
                :value="option.name"
              >
                {{ option.name }}
              </option>
            </select>
          </FormGenericInputTemplateV2>

          <FormGenericInputTemplateV2
            form-label="Unit Of Measure (UOM)"
            :is-required-field="true"
          >
            <select
              slot="field"
              :id="PREFIX + 'uom'"
              class="browser-default e4s-input-field e4s-input-field--primary"
              v-model="eventE4s.uom"
            >
              <option value="" disabled selected hidden>Select</option>
              <option v-for="option in uoms" :key="option.id" :value="option">
                {{ getUomLabel(option) }}
              </option>
            </select>
          </FormGenericInputTemplateV2>
        </template>
      </FormGenericFieldGridV2>

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputTemplateV2 form-label="Help Text">
            <FieldTextV2 v-model="eventE4s.options.helpText" slot="field" />
            <InputCheckboxV2
              slot="below-field"
              class="e4s-align-self-flex-start"
              v-model="eventE4s.options.rowOptions.autoExpandHelpText"
              value-label="Auto Show Help Text"
            />
          </FormGenericInputTemplateV2>
        </template>
      </FormGenericFieldGridV2>

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputNumberV2
            form-label="PB Min"
            v-model="eventE4s.options.min"
          />
          <FormGenericInputNumberV2
            form-label="PB Max"
            v-model="eventE4s.options.max"
          />
        </template>
      </FormGenericFieldGridV2>

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputNumberV2
            form-label="Max athletes per heat"
            v-model="eventE4s.options.maxInHeat"
          />

          <div></div>
        </template>
      </FormGenericFieldGridV2>

      <HeaderSectionThickLine title="Row Options" />

      <FormGenericFieldGridV2>
        <template slot="content">
          <InputCheckboxV2
            slot="below-field"
            class="e4s-align-self-flex-start"
            v-model="eventE4s.options.excludeFromCntRule"
            value-label="Exclude From Row Count"
          />

          <InputCheckboxV2
            slot="below-field"
            class="e4s-align-self-flex-start"
            v-model="eventE4s.options.rowOptions.showPB"
            value-label="Estimated Performance Must be added/exist before Entry"
          />
        </template>
      </FormGenericFieldGridV2>

      <FormGenericFieldGridV2>
        <template slot="content">
          <InputCheckboxV2
            slot="below-field"
            class="e4s-align-self-flex-start"
            v-model="eventE4s.options.rowOptions.showPrice"
            value-label="Show Price"
          />

          <InputCheckboxV2
            slot="below-field"
            class="e4s-align-self-flex-start"
            v-model="eventE4s.options.rowOptions.showEntryCount"
            value-label="Show Entry Count"
          />
        </template>
      </FormGenericFieldGridV2>

      <HeaderSectionThickLine title="Team Options" />

      <InputCheckboxV2
        slot="below-field"
        class="e4s-align-self-flex-start"
        v-model="eventE4s.options.isTeamEvent"
        value-label="Is Team Event"
      />

      <event-team-options
        v-if="eventE4s.options.isTeamEvent"
        :event-team-prop="eventE4s.options.eventTeam"
        v-on:onInputChange="eventTeamOptionsChanged"
      ></event-team-options>

      <HeaderSectionThickLine title="Restrictions" />

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputTemplateV2
            form-label="Unique (if user selects this event, disable the following)"
          >
            <event-selector
              slot="field"
              :gender="eventE4s.gender"
              v-on:onSelected="uniqueEventSelected"
            ></event-selector>
          </FormGenericInputTemplateV2>

          <!--          <div class="row">-->
          <!--            <div class="input-field col s12 m12 l12">-->
          <!--              Unique (if user selects this event, disable the following)-->
          <!--            </div>-->
          <!--          </div>-->

          <!--          <div class="row">-->
          <!--            <div class="input-field col s12 m12 l12">-->
          <!--              <label class="active" :for="PREFIX + 'event-select'">Event</label>-->
          <!--              <event-selector-->
          <!--                :gender="eventE4s.gender"-->
          <!--                v-on:onSelected="uniqueEventSelected"-->
          <!--              ></event-selector>-->
          <!--            </div>-->
          <!--          </div>-->
        </template>
      </FormGenericFieldGridV2>

      <div class="row">
        <div v-for="option in eventE4s.options.unique" :key="option.id">
          <div class="input-field col s1 m1 l1">
            <span v-text="option.id"></span>
          </div>
          <div class="input-field col s1 m1 l1">
            <span v-text="option.tf"></span>
          </div>
          <div class="input-field col s1 m1 l1">
            <span v-text="option.gender"></span>
          </div>
          <div class="input-field col s7 m7 l7">
            <span v-text="option.name"></span>
          </div>
          <div class="input-field col s2 m2 l2">
            <div class="e4s-flex-row">
              <ButtonGenericV2
                button-type="destructive"
                text="x"
                class="e4s-button--destructive-x e4s-flex-row--end"
                @click="removeUnique(option.id)"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="row" v-if="validationResults.length > 0">
        <div class="input-field col s12 m12 l2">
          <span class="errors">Please enter:</span>
          <span
            v-for="message in validationResults"
            :key="message.name"
            class="errors"
            v-html="message.message"
          >
          </span>
        </div>
      </div>

      <div class="e4s-flex-row">
        <div class="e4s-gap--standard e4s-flex-row--end">
          <ButtonGenericV2
            text="Cancel"
            button-type="tertiary"
            @click="onCancel"
          />

          <ButtonGenericV2 text="Save" @click="onSubmit" />
        </div>
      </div>
    </div>

    <!--    <div class="row">-->
    <!--      <div class="input-field col s12 m12 l12">-->
    <!--        <div class="e4s-form-header">Event Builder</div>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <div class="row">-->
    <!--      <div class="input-field col s12 m12 l6">-->
    <!--        <input-->
    <!--          :id="PREFIX + 'name'"-->
    <!--          :name="PREFIX + 'name'"-->
    <!--          v-validate="'required'"-->
    <!--          type="text"-->
    <!--          v-model="eventE4s.name"-->
    <!--          class="validate"-->
    <!--          placeholder=""-->
    <!--        />-->
    <!--        <label class="active" :for="PREFIX + 'name'">Name</label>-->
    <!--      </div>-->

    <!--      <div class="input-field col s12 m12 l4">-->
    <!--        <select-->
    <!--          :id="PREFIX + 'gender'"-->
    <!--          :name="PREFIX + 'gender'"-->
    <!--          class="generic-select"-->
    <!--          v-validate="'required'"-->
    <!--          v-model="eventE4s.gender"-->
    <!--        >-->
    <!--          <option value="" disabled selected hidden>Select</option>-->
    <!--          <option :value="gender.FEMALE">Female</option>-->
    <!--          <option :value="gender.MALE">Male</option>-->
    <!--        </select>-->
    <!--        <label class="active" :for="PREFIX + 'gender'">Gender</label>-->
    <!--        <span class="error-label" v-if="errors.has(PREFIX + 'gender')">{{-->
    <!--          errors.first(PREFIX + "gender")-->
    <!--        }}</span>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div class="input-field col s12 m12 l6">-->
    <!--        <select-->
    <!--          :id="PREFIX + 'type'"-->
    <!--          class="generic-select"-->
    <!--          v-model="eventE4s.tf"-->
    <!--        >-->
    <!--          <option value="" disabled selected hidden>Select</option>-->
    <!--          <option-->
    <!--            v-for="option in getEventTypes"-->
    <!--            :key="option.id"-->
    <!--            :value="option.name"-->
    <!--          >-->
    <!--            {{ option.name }}-->
    <!--          </option>-->
    <!--        </select>-->
    <!--        <label class="active" :for="PREFIX + 'type'">Event Type</label>-->
    <!--        <span class="error-label" v-if="errors.has(PREFIX + 'type')">{{-->
    <!--          errors.first(PREFIX + "type")-->
    <!--        }}</span>-->
    <!--      </div>-->

    <!--      <div class="input-field col s12 m12 l6">-->
    <!--        <select-->
    <!--          :id="PREFIX + 'uom'"-->
    <!--          class="generic-select"-->
    <!--          v-model="eventE4s.uom"-->
    <!--        >-->
    <!--          <option value="" disabled selected hidden>Select</option>-->
    <!--          <option v-for="option in uoms" :key="option.id" :value="option">-->
    <!--            {{ getUomLabel(option) }}-->
    <!--          </option>-->
    <!--        </select>-->
    <!--        <label class="active" :for="PREFIX + 'uom'">Uom</label>-->
    <!--        <span class="error-label" v-if="errors.has(PREFIX + 'uom')">{{-->
    <!--          errors.first(PREFIX + "uom")-->
    <!--        }}</span>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div class="input-field col s10 m10 l10">-->
    <!--        <input-->
    <!--          :id="PREFIX + 'help-text'"-->
    <!--          :name="PREFIX + 'help-text'"-->
    <!--          type="text"-->
    <!--          v-model="eventE4s.options.helpText"-->
    <!--          placeholder="Enter any text that may further explain event"-->
    <!--        />-->
    <!--        <label class="active" for="PREFIX + 'help-text'">Help text</label>-->
    <!--      </div>-->

    <!--      <div class="input-field col s12 m12 l2">-->
    <!--        <p>-->
    <!--          <label>-->
    <!--            <input-->
    <!--              class="e4s-checkbox"-->
    <!--              type="checkbox"-->
    <!--              v-model="eventE4s.options.rowOptions.autoExpandHelpText"-->
    <!--            />-->
    <!--            <span v-html="'Auto Show'"></span>-->
    <!--          </label>-->
    <!--        </p>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div class="input-field col s6 m6 l6">-->
    <!--        <input-->
    <!--          :id="PREFIX + 'options-min'"-->
    <!--          :name="PREFIX + 'options-min'"-->
    <!--          type="number"-->
    <!--          v-model="eventE4s.options.min"-->
    <!--          placeholder=""-->
    <!--        />-->
    <!--        <label class="active" for="PREFIX + 'options-min'">PB Min</label>-->
    <!--      </div>-->

    <!--      <div class="input-field col s6 m6 l6">-->
    <!--        <input-->
    <!--          :id="PREFIX + 'options-max'"-->
    <!--          :name="PREFIX + 'options-max'"-->
    <!--          type="number"-->
    <!--          v-model="eventE4s.options.max"-->
    <!--          placeholder=""-->
    <!--        />-->
    <!--        <label class="active" for="PREFIX + 'options-min'">PB Max</label>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div class="input-field col s6 m6 l6">-->
    <!--        <input-->
    <!--          :id="PREFIX + 'max-in-heat'"-->
    <!--          :name="PREFIX + 'max-in-heat'"-->
    <!--          type="number"-->
    <!--          v-model="eventE4s.options.maxInHeat"-->
    <!--          placeholder=""-->
    <!--        />-->
    <!--        <label class="active" for="PREFIX + 'max-in-heat'"-->
    <!--          >Max athletes per heat</label-->
    <!--        >-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div class="input-field col s12 m12 l12">Row options</div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div class="input-field col s6 m6 l6">-->
    <!--        <p>-->
    <!--          <label>-->
    <!--            <input-->
    <!--              class="e4s-checkbox"-->
    <!--              type="checkbox"-->
    <!--              v-model="eventE4s.options.excludeFromCntRule"-->
    <!--            />-->
    <!--            <span v-html="'Exclude from row count'"></span>-->
    <!--          </label>-->
    <!--        </p>-->
    <!--      </div>-->

    <!--      <div class="input-field col s6 m6 l6">-->
    <!--        <p>-->
    <!--          <label>-->
    <!--            <input-->
    <!--              class="e4s-checkbox"-->
    <!--              type="checkbox"-->
    <!--              v-model="eventE4s.options.rowOptions.showPB"-->
    <!--            />-->
    <!--            <span v-html="'Show PB'"></span>-->
    <!--          </label>-->
    <!--        </p>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div class="input-field col s6 m6 l6">-->
    <!--        <p>-->
    <!--          <label>-->
    <!--            <input-->
    <!--              class="e4s-checkbox"-->
    <!--              type="checkbox"-->
    <!--              v-model="eventE4s.options.rowOptions.showPrice"-->
    <!--            />-->
    <!--            <span v-html="'Show Price'"></span>-->
    <!--          </label>-->
    <!--        </p>-->
    <!--      </div>-->

    <!--      <div class="input-field col s6 m6 l6">-->
    <!--        <p>-->
    <!--          <label>-->
    <!--            <input-->
    <!--              class="e4s-checkbox"-->
    <!--              type="checkbox"-->
    <!--              v-model="eventE4s.options.rowOptions.showEntryCount"-->
    <!--            />-->
    <!--            <span v-html="'Show Entry Count'"></span>-->
    <!--          </label>-->
    <!--        </p>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div class="input-field col s12 m12 l12">Team options</div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div class="input-field col s12 m12 l12">-->
    <!--        <p>-->
    <!--          <label>-->
    <!--            <input-->
    <!--              class="e4s-checkbox"-->
    <!--              type="checkbox"-->
    <!--              v-model="eventE4s.options.isTeamEvent"-->
    <!--            />-->
    <!--            <span v-html="'Is Team Event'"></span>-->
    <!--          </label>-->
    <!--        </p>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <event-team-options-->
    <!--      v-if="eventE4s.options.isTeamEvent"-->
    <!--      :event-team-prop="eventE4s.options.eventTeam"-->
    <!--      v-on:onInputChange="eventTeamOptionsChanged"-->
    <!--    ></event-team-options>-->

    <!--    <div class="row">-->
    <!--      <div class="input-field col s12 m12 l12">-->
    <!--        Unique (if user selects this event, disable the following)-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div class="input-field col s12 m12 l12">-->
    <!--        <label class="active" :for="PREFIX + 'event-select'">Event</label>-->
    <!--        <event-selector-->
    <!--          :gender="eventE4s.gender"-->
    <!--          v-on:onSelected="uniqueEventSelected"-->
    <!--        ></event-selector>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div v-for="option in eventE4s.options.unique" :key="option.id">-->
    <!--        <div class="input-field col s1 m1 l1">-->
    <!--          <span v-text="option.id"></span>-->
    <!--        </div>-->
    <!--        <div class="input-field col s1 m1 l1">-->
    <!--          <span v-text="option.tf"></span>-->
    <!--        </div>-->
    <!--        <div class="input-field col s1 m1 l1">-->
    <!--          <span v-text="option.gender"></span>-->
    <!--        </div>-->
    <!--        <div class="input-field col s7 m7 l7">-->
    <!--          <span v-text="option.name"></span>-->
    <!--        </div>-->
    <!--        <div class="input-field col s2 m2 l2">-->
    <!--          <button-->
    <!--            class="btn waves-effect waves red right"-->
    <!--            v-on:click.stop="removeUnique(option.id)"-->
    <!--          >-->
    <!--            <span v-text="$t('buttons.remove')"></span>-->
    <!--          </button>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div class="row" v-if="validationResults.length > 0">-->
    <!--      <div class="input-field col s12 m12 l2">-->
    <!--        <span class="errors">Please enter:</span>-->
    <!--        <span-->
    <!--          v-for="message in validationResults"-->
    <!--          :key="message.name"-->
    <!--          class="errors"-->
    <!--          v-html="message.message"-->
    <!--        >-->
    <!--        </span>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div class="row">-->
    <!--      <div class="col s12 m12 l12">-->
    <!--        <div class="right">-->
    <!--          <div v-if="uomsLoading" class="e4s-force-inline-block">-->
    <!--            <loading-spinner></loading-spinner>-->
    <!--          </div>-->
    <!--          <button-->
    <!--            class="btn xxx-btn-small btn-flat red-text e4s-bold"-->
    <!--            v-on:click.stop="onCancel"-->
    <!--          >-->
    <!--            <span v-text="$t('buttons.cancel')"></span>-->
    <!--          </button>-->
    <!--          <button-->
    <!--            :disabled="errors.any()"-->
    <!--            class="btn waves-effect waves green"-->
    <!--            v-on:click.stop="onSubmit"-->
    <!--          >-->
    <!--            <span v-text="$t('buttons.save')"></span>-->
    <!--          </button>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--        <div class="row">-->
    <!--            <div class="input-field col s12 m12 l2">-->
    <!--                eventE4s : {{eventE4s}}-->
    <!--            </div>-->
    <!--        </div>-->
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { IEventE4S, IUniqueEventDisplay } from "./event-models";
import { EventService } from "./event-service";
import { GENDER, IValidationResult } from "../common/common-models";
import {
  EVENT_TYPE,
  IUnique,
  IEventTeam,
} from "../athletecompsched/athletecompsched-models";
import { IBuilderStoreState } from "../builder/builder-store";
import { BUILDER_STORE_CONST } from "../builder/builder-store-constants";
import { mapState } from "vuex";
import { IEventUomType } from "../uom/uom-models";
import LoadingSpinner from "../common/ui/loading-spinner.vue";
import AutoCompleteMat from "../common/ui/autocomplete/auto-complete-mat.vue";
import { ICustom } from "../common/ui/autocomplete/auto-complete-mat-models";
import { BuilderService } from "../builder/builder-service";
import EventTeamOptions from "../event/event-team-options.vue";
import EventSelector from "./event-selector.vue";
import FormGenericFieldGridV2 from "../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputTextV2 from "../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FormGenericInputTemplateV2 from "../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FieldTextV2 from "../common/ui/layoutV2/fields/field-text-v2.vue";
import InputCheckboxV2 from "../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import FormGenericInputNumberV2 from "../common/ui/layoutV2/form/form-generic--input-number-v2.vue";
import HeaderSectionThickLine from "../common/ui/layoutV2/form/HeaderSectionThickLine.vue";
import { simpleClone } from "../common/common-service-utils";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";

@Component({
  name: "event-form",
  components: {
    ButtonGenericV2,
    HeaderSectionThickLine,
    FormGenericInputNumberV2,
    InputCheckboxV2,
    FieldTextV2,
    FormGenericInputTemplateV2,
    FormGenericInputTextV2,
    FormGenericFieldGridV2,
    "auto-complete-mat": AutoCompleteMat,
    "event-team-options": EventTeamOptions,
    "loading-spinner": LoadingSpinner,
    "event-selector": EventSelector,
  },
  computed: {
    ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
      uoms: (state: IBuilderStoreState) => state.uoms,
      uomsLoading: (state: IBuilderStoreState) => state.uomsLoading,
      events: (state: IBuilderStoreState) => state.events,
      eventsLoading: (state: IBuilderStoreState) => state.eventsLoading,
    }),
  },
})
export default class EventForm extends Vue {
  @Prop({
    default: () => {
      return {
        id: 0,
      };
    },
  })
  public eventProp: IEventE4S;

  public PREFIX = Math.random().toString(36).substring(2);
  public eventService: EventService = new EventService();
  public builderService: BuilderService = new BuilderService();
  public eventE4s: IEventE4S = this.eventService.factoryGetEvent();
  public gender = GENDER;
  public eventTypes = EVENT_TYPE;
  public validationResults: IValidationResult[] = [];

  @Watch("eventProp")
  public athletePropChanged(newValue: IEventE4S) {
    this.eventE4s = simpleClone(newValue);
  }

  public get getEventTypes() {
    return this.eventService.convertEventTypeEnumToArray(this.eventTypes);
  }

  public getUomLabel(eventUomType: IEventUomType) {
    return this.eventService.getUomLabel(eventUomType);
  }

  public get getCustomForEventAutoComplete(): ICustom {
    return {
      dropDownLabelFunc: this.eventService.getLabelForEvent,
    } as ICustom;
  }

  public get showEventSelect() {
    return this.eventE4s.gender.length > 0;
  }

  public eventSearchTermChanged(searchKey: string) {
    if (searchKey.length === 0) {
      return;
    }
    const listParams = this.builderService.getListParamsDefault(searchKey);
    listParams.pagesize = 20;
    this.$store.dispatch(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_ACTIONS_LIST_EVENTS,
      { listParams, gender: this.eventE4s.gender }
    );
  }

  public uniqueEventSelected(eventE4s: IEventE4S) {
    const eventE4sDisplay: IUniqueEventDisplay = simpleClone(
      eventE4s
    ) as any as IUniqueEventDisplay;
    eventE4sDisplay.e = eventE4s.id;
    if (eventE4s && eventE4s.id > 0) {
      this.eventE4s.options.unique.push(simpleClone(eventE4sDisplay));
    }
  }

  public removeUnique(id: number) {
    id = Number(id);
    this.eventE4s.options.unique = this.eventE4s.options.unique.filter(
      (evt: IUnique) => {
        return evt.e !== id;
      }
    );
  }

  public eventTeamOptionsChanged(eventTeam: IEventTeam) {
    this.eventE4s.options.eventTeam = simpleClone(eventTeam);
  }

  public onCancel() {
    this.$emit("onCancel");
  }

  public onSubmit() {
    this.validationResults = this.eventService.validate(this.eventE4s);
    if (this.validationResults.length > 0) {
      return;
    }
    const eventE4s = this.eventService.removeUnwantedDataForSubmission(
      simpleClone(this.eventE4s)
    );
    this.$emit("onSubmit", eventE4s);
  }
}
</script>
