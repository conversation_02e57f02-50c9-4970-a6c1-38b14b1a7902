<template>

</template>

<script lang="ts">
    // import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    // import {Prop, Watch} from "vue-property-decorator";
    import {BUILDER_STORE_CONST} from "../builder/builder-store-constants";
    import {mapState} from "vuex";

    @Component({
        name: "event-options",
        components: {
        },
        computed: {
            ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
            })
        }
    })
    export default class EventOptions extends Vue {

    }
</script>
