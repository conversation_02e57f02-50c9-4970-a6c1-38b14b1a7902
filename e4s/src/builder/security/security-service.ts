import { ISecurity } from "../../athletecompsched/athletecompsched-models";
import { IBaseConcrete } from "../../common/common-models";
import {
  simpleClone,
  uniqueArrayById,
} from "../../common/common-service-utils";

export class SecurityService {
  public factory(): ISecurity {
    return {
      clubs: [],
      counties: [],
      regions: [],
    };
  }

  public isEmptyObject(obj: IBaseConcrete) {
    return obj.id === 0 && obj.name === "";
  }

  public isAllObject(obj: IBaseConcrete) {
    return obj.id === 0 && obj.name === "ALL";
  }

  public filterOutEmptyObjects(objs: IBaseConcrete[]) {
    return objs.filter((obj) => !this.isEmptyObject(obj));
  }

  public filterOutEmptyAndDuplicateObjects(objs: IBaseConcrete[]) {
    return uniqueArrayById(this.filterOutEmptyObjects(objs));
  }

  public addAndFilterOutEmptyAndDuplicateObjects(
    objs: IBaseConcrete[],
    obj: IBaseConcrete
  ) {
    const objsLocal = simpleClone(objs);

    console.log(
      "addAndFilterOutEmptyAndDuplicateObjects() objsLocal",
      objsLocal
    );

    objsLocal.push(simpleClone(obj));

    console.log(
      "addAndFilterOutEmptyAndDuplicateObjects() objsLocal",
      simpleClone(objsLocal)
    );

    // return this.filterOutEmptyAndDuplicateObjects(objsLocal);

    // loop through objsLocal and remove duplicates.
    // remove isEmptyObject and isAllObject
    // return objsLocal
    const uniqueArray = uniqueArrayById(objsLocal);

    console.log(
      "addAndFilterOutEmptyAndDuplicateObjects() uniqueArray",
      uniqueArray
    );

    const filteredArray = uniqueArray.filter(
      (obj) => !this.isEmptyObject(obj) && !this.isAllObject(obj)
    );

    console.log(
      "addAndFilterOutEmptyAndDuplicateObjects() filteredArray",
      filteredArray
    );

    return filteredArray;
  }
}
