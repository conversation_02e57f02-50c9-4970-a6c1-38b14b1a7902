import {IAgeCeidLink, IBuilderCompEvent} from "./builder-comp-event-models"
import {CompEventService} from "../../compevent/compevent-service";
import {IAgeGroup} from "../../agegroup/agegroup-models";
import {ICompEvent} from "../../compevent/compevent-models"
import * as R from "ramda"

const compEventService: CompEventService = new CompEventService();

export class BuilderCompEventService {
    public factory(): IBuilderCompEvent {
        const builderCompEvent: IBuilderCompEvent = compEventService.factoryGetCompEvent() as IBuilderCompEvent;
        builderCompEvent.ageGroups = [] as IAgeGroup[];
        builderCompEvent.ceids = [];
        builderCompEvent.ageCeidLink = [];
        return builderCompEvent;
    }


    public convertCompEventsToBuilderCompEvent(compEventPrimary: ICompEvent, compEvents: ICompEvent[]): IBuilderCompEvent {
        const builderCompEvent: IBuilderCompEvent = {
            ...this.factory(),
            ...R.clone(compEventPrimary) as IBuilderCompEvent
        };

        const selectedAgeGroups: IAgeGroup[] = compEvents
            .map((compEvent) => {
                return compEvent.ageGroup;
            });
        builderCompEvent.ageGroups = selectedAgeGroups;

        const ids: number[] = compEvents
            .map((compEvent) => {
                return compEvent.id;
            });
        builderCompEvent.ceids = ids;

        const ageCiedLinks: IAgeCeidLink[] = compEvents
            .map((compEvent) => {
                return {
                    ceid: compEvent.id,
                    agid: compEvent.ageGroup.id,
                    crud: "U"
                } as IAgeCeidLink;
            });
        builderCompEvent.ageCeidLink = ageCiedLinks;
        builderCompEvent.compEventsFromSchedule = compEvents;

        return builderCompEvent;
    }
}
