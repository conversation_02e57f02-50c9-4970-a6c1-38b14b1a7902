<template>
  <div id="qaz-1">
    <div v-show="showSection === 'EVENT_ADD'" id="qaz-2">
      <collapse-section
        icon-name="info"
        header-message="Basic Event Information"
        :allow-expand-collapse="false"
        :is-expanded="true"
      >
        <div slot="section-content">
          <!--New section V2-->
          <div class="e4s-flex-row">
            <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
              <ButtonGenericV2
                text="Reset form"
                @click="resetForm"
                :disabled="isCompEventLoading"
              />
              <ButtonGenericV2
                class="e4s-button--auto"
                button-type="destructive"
                text="Cancel and return to schedule"
                @click="cancelForm"
                :disabled="isCompEventLoading"
              />
            </div>
          </div>

          <template v-if="!getIsNewEvent">
            <div class="e4s-vertical-spacer--large"></div>
            <header-section-thick-line title="Status" />
            <div class="e4s-vertical-spacer-standard"></div>

            <FormGenericInputTemplateV2
              form-label="Is Event Open"
              :show-help="true"
              @helpClicked="doShowHelp('IS_EVENT_OPEN')"
            >
              <div
                class="e4s-flex-row e4s-flex-nowrap e4s-input--container"
                slot="field"
              >
                <label>
                  <input
                    type="radio"
                    class="
                      browser-default
                      e4s-input-field e4s-input-field--primary
                    "
                    :value="1"
                    v-model="compEvent.isOpen"
                  />
                  <span>Open</span>
                </label>
                <label>
                  <input
                    type="radio"
                    class="
                      browser-default
                      e4s-input-field e4s-input-field--primary
                    "
                    :value="0"
                    v-model="compEvent.isOpen"
                  />
                  <span>Closed</span>
                </label>
              </div>

              <p slot="below-field" class="e4s-subheader--general">
                If 'Closed', event is still visible but can not be entered. This
                can also happen when an event is over subscribed and waiting
                lists are NOT being used. Manually set this to 'Closed' to
                prevent further entries.
              </p>
            </FormGenericInputTemplateV2>
          </template>

          <div class="e4s-vertical-spacer--large"></div>
          <header-section-thick-line
            title="Discipline/Event"
            :is-required="getIsNewEvent"
            :is-required-valid="isDisciplineValid"
          />
          <div class="e4s-vertical-spacer-standard"></div>

          <!--Discipline--Event Date-Time-->
          <FormGenericFieldGridV2 id="dddddd">
            <template slot="content">
              <!--Discipline-gender-->
              <FormGenericFieldGridV2 id="dddddd-2">
                <template slot="content">
                  <div class="e4s-flex-row e4s-gap--standard e4s-flex-wrap">
                    <FormGenericInputTemplateV2
                      form-label="Discipline"
                      style="flex-grow: 1"
                    >
                      <!--                      <PrimaryLink-->
                      <!--                        slot="after-label"-->
                      <!--                        link-text="Create New"-->
                      <!--                        @onClick="setShowEventForm(true)"-->
                      <!--                      />-->

                      <ButtonGenericV2
                        slot="right-label"
                        text="Create New"
                        v-if="getIsNewEvent"
                        @click="setShowEventForm(true)"
                        class="
                          e4s-flex-row--end
                          e4s-button--slim e4s-button--admin
                        "
                      />

                      <div slot="field" class="e4s-flex-row e4s-gap--tiny">
                        <EventSelectorV2
                          @input="onDisciplineSelected"
                          v-if="getIsNewEvent"
                        />
                        <FieldTextV2
                          class="e4s-full-width"
                          :is-disabled="true"
                          v-if="!getIsNewEvent"
                          :value="preLoadSearchTerm"
                        />
                      </div>

                      <div
                        slot="below-field"
                        class="
                          e4s-flex-row
                          e4s-gap--standard
                          e4s-justify-flex-row-vert-center
                        "
                      >
                        <span class="e4s-subheader--general"
                          >Can't find the discipline,</span
                        >

                        <PrimaryLink
                          link-text="contact support."
                          @onClick="showContactSupport"
                        />
                      </div>
                    </FormGenericInputTemplateV2>

                    <!--Gender-->
                    <FormGenericInputTemplateV2
                      form-label="Genders"
                      style="width: 180px"
                    >
                      <div
                        class="
                          e4s-flex-row
                          e4s-gap--large
                          e4s-justify-flex-row-vert-center
                        "
                        slot="field"
                      >
                        <div
                          class="e4s-flex-row e4s-gap--standard"
                          v-for="eventE4s in eventsForGender"
                          :key="eventE4s.id"
                        >
                          <div
                            class="
                              e4s-flex-row
                              e4s-gap--standard
                              e4s-justify-flex-row-vert-center
                            "
                          >
                            <label class="input-checkbox-v2--label">
                              <input
                                type="checkbox"
                                class="
                                  browser-default
                                  e4s-input-field
                                  input-checkbox-v2--input
                                "
                                :class="
                                  getIsNewEvent
                                    ? ''
                                    : 'input-checkbox-v2--label-disabled'
                                "
                                style="
                                  accent-color: var(
                                    --e4s-button--primary__background
                                  );
                                  height: 20px;
                                "
                                :value="eventE4s"
                                :disabled="!getIsNewEvent"
                                v-on:change="eventGendersChecked"
                                v-model="checkedEventGenders"
                              />
                            </label>
                            <span
                              class="e4s-input--label"
                              :class="
                                getIsNewEvent
                                  ? ''
                                  : 'input-checkbox-v2--label-disabled'
                              "
                              v-text="getGenderLabelAll(eventE4s.gender)"
                            ></span>
                          </div>
                        </div>
                      </div>
                    </FormGenericInputTemplateV2>
                    <!--/Gender-->
                  </div>
                </template>
              </FormGenericFieldGridV2>
              <!--/Discipline-gender-->

              <FormGenericInputTemplateV2
                form-label="Event Date/Time"
                help-text="If time 00:00, will display time portion as TBC (To Be Confirmed)"
              >
                <div
                  slot="field"
                  class="
                    e4s-flex-row e4s-justify-flex-row-vert-center
                    e4s-gap--standard
                  "
                >
                  <date-entry-mat
                    :iso-date="getEventDate"
                    :id="PREFIX + 'event-date2'"
                    v-on:onSelected="onDateSelected"
                  />
                  <time-entry
                    :iso-date-time="eventTime"
                    :minute-interval="1"
                    v-on:onSelected="onTimeSelected"
                  />
                </div>
              </FormGenericInputTemplateV2>
            </template>
          </FormGenericFieldGridV2>
          <!--/Discipline--Event Date-Time-->

          <hr class="dat-e4s-hr dat-e4s-hr-only dat-e4s-hr--slightly-lighter" />

          <!--Event Name-->
          <FormGenericFieldGridV2>
            <template slot="content">
              <!--              My flex css is not good enough-->
              <!--style="getIsNewEvent ? '' : 'max-width: 50%;'"-->
              <FormGenericInputTemplateV2 form-label="Event Name">
                <span
                  class="e4s-subheader--general e4s-flex-row--end"
                  slot="right-label"
                >
                  Max Length:
                  {{ getEventGroupNameLength }} of 50
                </span>

                <div slot="field">
                  <div v-if="!getIsEventGroupTypeAheadEditable">
                    <span v-text="compEvent.eventGroupSummary.name"></span>
                    <AdminIdV2
                      :obj="compEvent.eventGroupSummary"
                      :id="compEvent.eventGroupSummary.id"
                    />
                  </div>

                  <EventGroupTypeAheadV2
                    v-if="getIsEventGroupTypeAheadEditable"
                    :comp-events="compEventsSchedule"
                    :default-value="compEvent.eventGroup"
                    v-on:onUserInputChanged="eventGroupSelected"
                    v-on:onSelected="eventGroupSelected"
                  />
                </div>

                <p slot="below-field" class="e4s-subheader--general">
                  The name athletes will see when entering.
                </p>
              </FormGenericInputTemplateV2>

              <FormGenericInputTemplateV2
                form-label="Estimated Performance Must be added/exist before Entry"
              >
                <div slot="field" class="e4s-flex-row e4s-gap--large">
                  <FieldRadioV2
                    v-model="compEvent.options.mandatoryPB"
                    :option-value="true"
                  >
                    <span
                      :style="
                        compEvent.options.mandatoryPB
                          ? 'color: var(--e4s-info-section--error__text-color);'
                          : ''
                      "
                      >Required</span
                    >
                  </FieldRadioV2>
                  <FieldRadioV2
                    v-model="compEvent.options.mandatoryPB"
                    :option-value="false"
                    label="Not Required"
                  />
                </div>

                <span
                  slot="below-field"
                  v-if="compEvent.options.mandatoryPB"
                  class="e4s-subheader--general"
                  :class="
                    compEvent.options.mandatoryPB ? 'e4s-info-text--error' : ''
                  "
                >
                  Athletes must have a performance entered before they can enter
                  this event.
                </span>
              </FormGenericInputTemplateV2>
            </template>
          </FormGenericFieldGridV2>
          <!--/Event Name-->

          <!--Event Name--Matching Event-->
          <div
            class="e4s-flex-column"
            v-show="foundMatchingEventGroup && compEvent.id === 0"
          >
            <CompeventGridSelector
              :comp-events="compEventsSchedule"
              :event-group="compEvent.eventGroup"
              v-on:foundMatch="foundMatchingEventGroup = $event"
              v-on:onSelected="initCompEvent"
            />
            <div class="e4s-vertical-spacer-small"></div>
          </div>
          <!--/Event Name--Matching Event-->

          <!--Quick-Security-->
          <template
            v-if="
              configController.isIRL.value || configController.isAdmin.value
            "
          >
            <div class="e4s-vertical-spacer--large"></div>
            <header-section-thick-line title="Security" />
            <div class="e4s-vertical-spacer--standard"></div>

            <FormGenericInputTemplateV2
              form-label="Set event security"
              :class="
                configController.isAdmin.value && !configController.isIRL.value
                  ? 'e4s-admin--section'
                  : ''
              "
              v-if="isQuickMounted"
            >
              <span slot="after-label" class="e4s-subheader--general">
                (for more detailed settings use the
                <PrimaryLink link-text="Security" @onClick="scrollToSecurity" />
                section below)
              </span>
              <div
                slot="field"
                class="
                  e4s-flex-row e4s-justify-flex-row-vert-center
                  e4s-gap--x-large
                "
              >
                <InputCheckboxV2
                  :value="quickSecurityClubs"
                  value-label="Club Rep"
                  @input="quickSecurityChanged('clubs', $event)"
                />

                <InputCheckboxV2
                  :value="quickSecurityCounties"
                  value-label="County Rep"
                  @input="quickSecurityChanged('counties', $event)"
                />

                <InputCheckboxV2
                  :value="quickSecurityRegions"
                  value-label="Region Rep"
                  @input="quickSecurityChanged('regions', $event)"
                />
              </div>
            </FormGenericInputTemplateV2>
          </template>
          <!--/Quick-Security-->

          <!--          <hr class="dat-e4s-hr dat-e4s-hr-only dat-e4s-hr&#45;&#45;slightly-lighter" />-->
          <div class="e4s-vertical-spacer--large"></div>
          <header-section-thick-line
            title="Age Groups"
            :is-required="getIsNewEvent"
            :is-required-valid="isAgeGroupsValid"
          />
          <div class="e4s-vertical-spacer--standard"></div>

          <!--Age Groups-->
          <div class="e4s-flex-column e4s-gap--standard" id="qaz-c">
            <AgeGroupCheckBoxV2
              slot="field"
              :age-groups="ageGroupController.state.ageGroups"
              :age-groups-default="checkedAgeGroups"
              :ageCeidLinks="builderCompEvent.ageCeidLink"
              :allow-default-to-show-all-age-groups="false"
              :show-default-only-filter="true"
              :single-column="true"
              @onSelected="ageGroupsSelected"
              @showMoreAgeGroups="showMoreAgeGroups"
            >
              <div slot="field-title">
                <!--                <FormGenericSectionTitleV2 title="Age Groups" />-->
              </div>
            </AgeGroupCheckBoxV2>

            <InfoSectionV2
              info-type="error"
              v-if="compEvent.options.ageGroupOverlap"
            >
              <div>
                Age group overlap. If this is intended, when an athlete selects
                this event they will be presented with multiple "events" and can
                select either event.
              </div>
            </InfoSectionV2>

            <!--            v-show="getAgeGroupCompCoverageModels.length > 0"-->

            <Accordion
              title="Age Group Coverage"
              :is-expanded="
                isAgeGroupCoverageExpanded || compEvent.options.ageGroupOverlap
              "
            >
              <!--              style="overflow-x: auto"-->
              <CompeventAgeGroupCoverage
                id="qaz-d"
                style="overflow-x: auto"
                slot="content"
                :show-helper-row="false"
                :age-group-comp-coverage-models="getAgeGroupCompCoverageModels"
                :ao-code="configApp.defaultao.code"
                @hasOverLap="ageGroupCoverageHasOverlap"
              />
            </Accordion>
          </div>
          <!--/Age Groups-->

          <div class="e4s-vertical-spacer--large"></div>
          <header-section-thick-line
            title="Price"
            :is-required="getIsNewEvent"
            :is-required-valid="isPriceValid"
          />
          <div class="e4s-vertical-spacer-standard"></div>

          <!--Price-->
          <price-selector
            v-if="!getIsScheduleOnly"
            :price="price"
            :prices="prices"
            :price-selector-ui-display="priceSelectorUiDisplay"
            v-on:onSelected="onPriceSelected"
          >
            <span slot="delete-button"></span>
            <span slot="cancel-button"></span>
          </price-selector>
          <!--/Price-->

          <!--          <hr class="dat-e4s-hr dat-e4s-hr-only dat-e4s-hr&#45;&#45;slightly-lighter" />-->
          <div class="e4s-vertical-spacer--large"></div>
          <header-section-thick-line title="Optional Settings" />
          <div class="e4s-vertical-spacer--standard"></div>

          <!--Schedule Only-->
          <FormGenericFieldGridV2 v-if="compEvent.maxAthletes !== -1">
            <template slot="content">
              <div class="e4s-flex-column">
                <FormGenericInputTemplateV2
                  form-label="Are Entries Allowed"
                  class="comp-event-adder--grid"
                >
                  <label
                    class="input-checkbox-v2--label e4s-align-self-flex-start"
                    slot="field"
                  >
                    <input
                      type="checkbox"
                      :checked="false"
                      @change="setIsScheduelOnly"
                      class="
                        browser-default
                        e4s-input-field
                        input-checkbox-v2--input
                      "
                      style="
                        accent-color: var(--e4s-button--primary__background);
                        height: 20px;
                      "
                    />
                    <div class="comp-event-adder--ad-hoc-checkbox-text">
                      No (E.g. Schedule Only/Final)
                    </div>
                  </label>
                </FormGenericInputTemplateV2>

                <!--                <FormGenericInputTemplateV2-->
                <!--                  form-label="Include entries from"-->
                <!--                  class="comp-event-adder&#45;&#45;grid"-->
                <!--                >-->
                <!--                  <EventGroupSelect-->
                <!--                    slot="field"-->
                <!--                    v-model="compEvent.options.includeEntriesFromEgId"-->
                <!--                    :comp-events-schedule="compEventsSchedule"-->
                <!--                    event-group-type="NOT_FINALS"-->
                <!--                    @change="includeEntriesFromChanged"-->
                <!--                  />-->
                <!--                </FormGenericInputTemplateV2>-->
              </div>

              <!--              <div class="e4s-flex-column">-->
              <!--                <FormGenericInputTemplateV2-->
              <!--                  form-label="Max athletes\teams that can enter this event"-->
              <!--                  class="comp-event-adder&#45;&#45;grid"-->
              <!--                >-->
              <!--                  <FieldNumberV2-->
              <!--                    slot="field"-->
              <!--                    v-model="compEvent.maxAthletes"-->
              <!--                    class="e4s-input&#45;&#45;digit-number-medium"-->
              <!--                  />-->
              <!--                </FormGenericInputTemplateV2>-->

              <!--                <FormGenericInputTemplateV2-->
              <!--                  form-label="Max athletes in a heat"-->
              <!--                  class="comp-event-adder&#45;&#45;grid"-->
              <!--                >-->
              <!--                  <FieldNumberV2-->
              <!--                    slot="field"-->
              <!--                    v-model="compEvent.options.maxInHeat"-->
              <!--                    class="e4s-input&#45;&#45;digit-number-medium"-->
              <!--                  />-->
              <!--                </FormGenericInputTemplateV2>-->
              <!--              </div>-->
            </template>
          </FormGenericFieldGridV2>

          <FormGenericFieldGridV2 v-if="compEvent.maxAthletes === -1">
            <template slot="content">
              <FormGenericInputTemplateV2 form-label="Are Entries Allowed">
                <label
                  class="input-checkbox-v2--label e4s-align-self-flex-start"
                  slot="field"
                >
                  <input
                    type="checkbox"
                    :checked="true"
                    @change="setIsScheduelOnly"
                    class="
                      browser-default
                      e4s-input-field
                      input-checkbox-v2--input
                    "
                    style="
                      accent-color: var(--e4s-button--primary__background);
                      height: 20px;
                    "
                  />
                  <div class="comp-event-adder--ad-hoc-checkbox-text">
                    No (E.g. Schedule Only/Final)
                  </div>
                </label>
              </FormGenericInputTemplateV2>

              <FormGenericInputTemplateV2 form-label="Copy entries from">
                <EventGroupSelect
                  slot="field"
                  v-model="compEvent.options.entriesFrom"
                  :comp-events-schedule="compEventsSchedule"
                  event-group-type="NOT_FINALS"
                  @change="entriesFromChanged"
                />
              </FormGenericInputTemplateV2>
            </template>
          </FormGenericFieldGridV2>

          <!--          v-if="compEvent.maxAthletes > -1"-->
          <FormGenericFieldGridV2>
            <template slot="content">
              <FormGenericInputTemplateV2
                :form-label="
                  'Split Events by ' +
                  (checkedEventGenders.length > 1 ? 'Gender/' : '') +
                  'Age'
                "
              >
                <div
                  class="
                    e4s-flex-row
                    e4s-gap--large
                    form-generic-input-template-v2--input-container
                  "
                  slot="field"
                >
                  <div
                    v-if="checkedEventGenders.length > 1"
                    class="
                      e4s-flex-row
                      e4s-gap--standard
                      e4s-justify-flex-row-vert-center
                    "
                  >
                    <InputCheckboxV2
                      :is-required-field="false"
                      class="e4s-align-self-flex-start"
                      v-model="eventGroupCreateOptions.addGender"
                      value-label="Gender"
                    />
                  </div>

                  <div
                    class="
                      e4s-flex-row
                      e4s-gap--standard
                      e4s-justify-flex-row-vert-center
                    "
                  >
                    <InputCheckboxV2
                      :is-required-field="false"
                      class="e4s-align-self-flex-start"
                      v-model="eventGroupCreateOptions.addAge"
                      value-label="Age"
                    />
                  </div>
                </div>

                <p slot="below-field" class="e4s-subheader--general">
                  On save splits events automatically according to selection(s).
                </p>
              </FormGenericInputTemplateV2>

              <div></div>
            </template>
          </FormGenericFieldGridV2>

          <!--/Schedule Only-->

          <!--/New section V2-->
        </div>
      </collapse-section>

      <div class="e4s-vertical-spacer--large"></div>

      <!--      <div class="row">-->
      <!--        <div class="col s12 m12 l12">-->
      <!--          <div class="e4s-flex-row">-->
      <!--            <InputCheckboxV2-->
      <!--              :is-required-field="false"-->
      <!--              class="e4s-align-self-flex-start"-->
      <!--              v-model="showAdvancedOptions"-->
      <!--            >-->
      <!--              <div class="e4s-header&#45;&#45;400">Show Advanced Options</div>-->
      <!--            </InputCheckboxV2>-->

      <!--            <ButtonGenericV2-->
      <!--              class="e4s-flex-row&#45;&#45;end"-->
      <!--              text="Save"-->
      <!--              @click="submitCompEvent"-->
      <!--              :disabled="isCompEventLoading"-->
      <!--            />-->
      <!--          </div>-->
      <!--          -->
      <!--        </div>-->
      <!--      </div>-->

      <div v-show="getCanShowAdvancedOptions">
        <collapse-section
          icon-name="insert_comment"
          header-message="Help Text"
          id="help-text-section"
          :is-expanded="false"
        >
          <div slot="section-content">
            <div class="row">
              <div class="col s12 m12 l12">
                <div class="e4s-flex-column e4s-gap--standard">
                  <FormGenericInputTextV2
                    form-label="Text that may further explain event"
                    v-model="compEvent.options.helpText"
                  />

                  <!--Want it to always show but need to handle legacy already saved data.-->
                  <InputCheckboxV2
                    v-if="!compEvent.options.rowOptions.autoExpandHelpText"
                    :is-required-field="false"
                    class="e4s-align-self-flex-start"
                    v-model="compEvent.options.rowOptions.autoExpandHelpText"
                    value-label="Show the text automatically (else user must click to view)"
                  />
                </div>
              </div>
            </div>
          </div>
        </collapse-section>

        <!--End User Display Options-->
        <!--        <collapse-section-->
        <!--          v-if="false"-->
        <!--          icon-name="computer"-->
        <!--          header-message="End User Display Options"-->
        <!--          :is-expanded="false"-->
        <!--        >-->
        <!--          <div slot="section-content">-->
        <!--            <div class="row">-->
        <!--              <div class="input-field col s6 m6 l6">-->
        <!--                <p>-->
        <!--                  <label>-->
        <!--                    <input-->
        <!--                      class="e4s-checkbox"-->
        <!--                      type="checkbox"-->
        <!--                      v-model="compEvent.options.rowOptions.showPB"-->
        <!--                    />-->
        <!--                    <span>Show PB</span>-->
        <!--                  </label>-->
        <!--                </p>-->
        <!--                <p>-->
        <!--                  <label>-->
        <!--                    <input-->
        <!--                      class="e4s-checkbox"-->
        <!--                      type="checkbox"-->
        <!--                      v-model="compEvent.options.mandatoryPB"-->
        <!--                    />-->
        <!--                    <span>Mandatory PB</span>-->
        <!--                  </label>-->
        <!--                </p>-->
        <!--              </div>-->
        <!--            </div>-->

        <!--            <div class="row">-->
        <!--              <div class="input-field col s6 m6 l6">-->
        <!--                <p>-->
        <!--                  <label>-->
        <!--                    <input-->
        <!--                      class="e4s-checkbox"-->
        <!--                      type="checkbox"-->
        <!--                      v-model="compEvent.options.rowOptions.showPrice"-->
        <!--                    />-->
        <!--                    <span>Show Price</span>-->
        <!--                  </label>-->
        <!--                </p>-->
        <!--              </div>-->

        <!--              <div class="input-field col s6 m6 l6">-->
        <!--                <p>-->
        <!--                  <label>-->
        <!--                    <input-->
        <!--                      class="e4s-checkbox"-->
        <!--                      type="checkbox"-->
        <!--                      v-model="compEvent.options.rowOptions.showEntryCount"-->
        <!--                    />-->
        <!--                    <span>Show Entry Count</span>-->
        <!--                  </label>-->
        <!--                </p>-->
        <!--              </div>-->
        <!--            </div>-->

        <!--            <div class="row">-->
        <!--              <div class="input-field col s6 m6 l6">-->
        <!--                <p>-->
        <!--                  <label>-->
        <!--                    <input-->
        <!--                      class="e4s-checkbox"-->
        <!--                      type="checkbox"-->
        <!--                      v-model="compEvent.options.rowOptions.hideOnDisable"-->
        <!--                    />-->
        <!--                    <span>Hide on disabled</span>-->
        <!--                  </label>-->
        <!--                </p>-->
        <!--              </div>-->

        <!--              <div class="input-field col s6 m6 l6"></div>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </collapse-section>-->
        <!--/End User Display Options-->

        <!--Event Restrictions-->
        <collapse-section
          icon-name="block"
          header-message="Event Restrictions/Limits"
          :is-expanded="false"
        >
          <div slot="section-content">
            <div class="row">
              <div class="col s12 m12 l12">
                <div class="e4s-flex-column e4s-gap--large">
                  <FormGenericFieldGridV2>
                    <template slot="content">
                      <FormGenericInputTemplateV2 form-label="Event Status">
                        <FieldHelp
                          slot="after-label"
                          title="Is Event Open"
                          message="If 'Closed', event is still visible but can not be entered.  This can also
                             happen when an event is over subscribed and waiting lists are NOT being used.  Manually set this to 'Closed' to prevent further entries."
                        />
                        <template slot="field">
                          <div class="e4s-flex-row e4s-gap--large">
                            <FieldRadioV2
                              :option-value="1"
                              v-model="compEvent.isOpen"
                              label="Open"
                            />
                            <FieldRadioV2
                              :option-value="0"
                              v-model="compEvent.isOpen"
                              label="Closed"
                            />
                          </div>
                        </template>
                      </FormGenericInputTemplateV2>

                      <FormGenericInputTemplateV2
                        form-label="Available to"
                        v-show="showOpenToAll"
                      >
                        <template slot="field">
                          <div class="e4s-flex-row e4s-gap--large">
                            <InputCheckboxV2
                              class="e4s-align-self-flex-start"
                              v-model="compEvent.options.unregisteredAthletes"
                              value-label="Unregistered Athletes"
                            />

                            <InputCheckboxV2
                              :is-disabled="true"
                              class="e4s-align-self-flex-start"
                              v-model="compEvent.options.registeredAthletes"
                              value-label="Registered Athletes"
                            />
                          </div>
                        </template>
                      </FormGenericInputTemplateV2>
                    </template>
                  </FormGenericFieldGridV2>

                  <FormGenericFieldGridV2>
                    <template slot="content">
                      <FormGenericInputNumberV2
                        form-label="Max athletes that can enter this event"
                        help-text="0 = No restriction."
                        v-model="compEvent.maxAthletes"
                      />
                      <FormGenericInputNumberV2
                        form-label="Max athletes per heat"
                        help-text="0 = No restriction."
                        v-model="compEvent.options.maxInHeat"
                      />

                      <FormGenericInputTemplateV2
                        form-label="Heat Length"
                        help-text="0 = Not used."
                      >
                        <select
                          slot="field"
                          :id="PREFIX + 'heat-length'"
                          :name="PREFIX + 'heat-length'"
                          class="
                            browser-default
                            e4s-input-field e4s-input-field--primary
                            e4s-input--digit-number-medium
                          "
                          v-model="compEvent.options.heatInfo.heatDurationMins"
                        >
                          <option :value="0">0</option>
                          <option
                            v-for="index in 99"
                            :key="index"
                            :value="index"
                            v-text="index"
                          ></option>
                        </select>
                      </FormGenericInputTemplateV2>

                      <FormGenericInputTemplateV2
                        form-label="Lane usage"
                        v-show="isTrackEVent"
                      >
                        <select
                          slot="field"
                          id="lane-usage"
                          name="lane-usage"
                          class="
                            browser-default
                            e4s-input-field e4s-input-field--primary
                          "
                          v-model="compEvent.options.heatInfo.useLanes"
                        >
                          <option value="A">All Lanes</option>
                          <option value="O">Odd Lanes Only</option>
                          <option value="E">Even Lanes Only</option>
                        </select>
                      </FormGenericInputTemplateV2>
                    </template>
                  </FormGenericFieldGridV2>

                  <FormGenericFieldGridV2>
                    <template slot="content">
                      <FormGenericInputTemplateV2
                        form-label="Performance Restriction"
                        help-text="0 = No restriction."
                      >
                        <FieldHelp
                          slot="after-label"
                          help-key="comp-event--pb-split"
                          :get-from-server="true"
                        />

                        <template slot="field">
                          <FieldNumberV2
                            v-model="compEvent.split"
                            class="e4s-input--digit-number-medium"
                          />
                        </template>
                        <!--                        <p slot="below-field" class="e4s-subheader&#45;&#45;general">-->
                        <!--                          If track must be in seconds, e.g. 4mins 24secs = 264-->
                        <!--                        </p>-->
                      </FormGenericInputTemplateV2>

                      <FormGenericInputTemplateV2 form-label="">
                        <template slot="field">
                          <div class="e4s-flex-column e4s-gap--standard">
                            <!--                            <InputCheckboxV2-->
                            <!--                              class="e4s-align-self-flex-start"-->
                            <!--                              v-model="compEvent.options.mandatoryPB"-->
                            <!--                              value-label="Estimate Performance Must be added/exist before Entry"-->
                            <!--                            />-->

                            <div
                              class="
                                e4s-flex-row
                                e4s-gap--standard
                                e4s-justify-flex-row-vert-center
                              "
                            >
                              <InputCheckboxV2
                                class="e4s-align-self-flex-start"
                                v-model="compEvent.options.excludeFromCntRule"
                                value-label="Exclude from count rule"
                              />
                              <FieldHelp
                                help-key="comp-event--excludeFromCntRule"
                                :get-from-server="true"
                              />
                            </div>
                          </div>
                        </template>
                      </FormGenericInputTemplateV2>
                    </template>
                  </FormGenericFieldGridV2>

                  <SectionThickLine />

                  <FormGenericFieldGridV2>
                    <template slot="content">
                      <FormGenericInputTemplateV2
                        form-label="If an athlete enters this event, they cannot enter the following events"
                      >
                        <template slot="field">
                          <EventGroupSelect
                            :comp-events-schedule="
                              getEventGroupsForUniqueDropDown
                            "
                            v-on:input="uniqueCompEventGroupSelected"
                          />

                          <div class="e4s-flex-column e4s-gap--standard">
                            <div
                              v-for="eventGroup in getUniqueEventGroupsForDisplay"
                              :key="eventGroup.id"
                              class="e4s-grid-3-columns"
                            >
                              <span v-text="eventGroup.eventNo"></span>
                              <span>
                                <span v-text="eventGroup.name"></span> (<span
                                  v-text="eventGroup.id"
                                ></span
                                >)
                              </span>
                              <ButtonGenericV2
                                text="X"
                                @click="removeUniqueEventGroup(eventGroup.id)"
                                button-type="destructive"
                                style="width: 35px; justify-self: flex-end"
                              />
                            </div>
                          </div>
                        </template>
                      </FormGenericInputTemplateV2>

                      <span></span>
                    </template>
                  </FormGenericFieldGridV2>
                </div>
              </div>
            </div>
          </div>
        </collapse-section>
        <!--/Event Restrictions-->

        <!--Team Options-->
        <collapse-section
          icon-name="people"
          header-message="Team Options"
          :is-expanded="sectionsExpanded.team"
          v-on:isSectionBodyVisible="sectionsExpanded.team = $event"
        >
          <div slot="section-content">
            <div class="row">
              <div class="col s12 m12 l12">
                <div
                  class="
                    e4s-flex-row
                    e4s-gap--large
                    e4s-justify-flex-row-vert-center
                  "
                >
                  <InputCheckboxV2
                    class="e4s-align-self-flex-start"
                    v-model="compEvent.options.isTeamEvent"
                    value-label="Is Team Event"
                  />
                  <FieldHelp
                    help-key="comp-event-adder--team-event"
                    :get-from-server="true"
                  />
                </div>
              </div>
            </div>

            <div
              class="row"
              v-if="
                compEvent.options.isTeamEvent &&
                !builderService.hasSelfService(builderCompetition) &&
                configController.getCssEnvIdentifier.value === 'ia'
              "
            >
              <div class="col s12 m12 l12">
                Self service is not currently enabled for this competition.
                Enabling this on "General\Security" will help entities e.g.
                club, school, county representatives self assign.
              </div>
            </div>

            <event-team-options
              v-if="compEvent.options.isTeamEvent"
              :event-team-prop="compEvent.options.eventTeam"
              v-on:onInputChange="eventTeamOptionsChanged"
            >
            </event-team-options>
          </div>
        </collapse-section>
        <!--/Team Options-->

        <!--Card Information-->
        <collapse-section
          icon-name="people"
          header-message="Card Information"
          :is-expanded="false"
        >
          <div slot="section-content">
            <div class="row">
              <div class="col s12 m12 l12">
                <div class="e4s-flex-column e4s-gap--large">
                  <FieldHelp
                    help-key="comp-event-adder--card-info"
                    :get-from-server="true"
                  />

                  <FormGenericInputTemplateV2 form-label="Report Information">
                    <span slot="right-label" class="e4s-flex-row--end">
                      <span
                        :class="
                          compEvent.options.eventGroupInfo.reportInfo >= 500
                            ? 'e4s-info-text--error'
                            : 'e4s-subheader--general'
                        "
                        >Max Length:
                        <span
                          v-text="
                            compEvent.options.eventGroupInfo.reportInfo.length
                          "
                        ></span>
                        of 500
                      </span>
                    </span>
                    <InputRestrictLength
                      slot="field"
                      :max-length="500"
                      :use-text-area="true"
                      input-class="e4s-full-width"
                      v-model="compEvent.options.eventGroupInfo.reportInfo"
                    />
                  </FormGenericInputTemplateV2>

                  <FormGenericInputTemplateV2 form-label="Trial Information">
                    <span slot="right-label" class="e4s-flex-row--end">
                      <span
                        :class="
                          compEvent.options.eventGroupInfo.trialInfo >= 100
                            ? 'e4s-info-text--error'
                            : 'e4s-subheader--general'
                        "
                        >Max Length:
                        <span
                          v-text="
                            compEvent.options.eventGroupInfo.trialInfo.length
                          "
                        ></span>
                        of 100
                      </span>
                    </span>
                    <InputRestrictLength
                      slot="field"
                      :max-length="100"
                      input-class="e4s-full-width"
                      v-model="compEvent.options.eventGroupInfo.trialInfo"
                    />
                  </FormGenericInputTemplateV2>

                  <!--Seed Format-->
                  <FormGenericFieldGridV2
                    v-if="builderCompetition.options.cardInfo.enabled"
                  >
                    <template slot="content">
                      <div class="e4s-flex-column e4s-gap--standard">
                        <FormGenericInputTemplateV2 form-label="Seeding Format">
                          <div
                            class="
                              e4s-flex-row e4s-flex-nowrap
                              e4s-input--container
                            "
                            slot="field"
                          >
                            <label>
                              <input
                                type="radio"
                                class="
                                  browser-default
                                  e4s-input-field e4s-input-field--primary
                                "
                                value="O"
                                v-model="compEvent.options.seed.type"
                              />
                              <span>Open</span>
                            </label>
                            <label>
                              <input
                                type="radio"
                                class="
                                  browser-default
                                  e4s-input-field e4s-input-field--primary
                                "
                                value="H"
                                v-model="compEvent.options.seed.type"
                              />
                              <span>Heats</span>
                            </label>
                          </div>
                        </FormGenericInputTemplateV2>

                        <FormGenericInputTemplateV2
                          :form-label="getEventSeedingToLabel"
                          v-if="compEvent.options.seed.type === 'H'"
                        >
                          <EventGroupSelect
                            slot="field"
                            v-model="compEvent.options.seed.qualifyToEg"
                            :comp-events-schedule="compEventsSchedule"
                            event-group-type="FINALS"
                          />
                        </FormGenericInputTemplateV2>
                      </div>

                      <FormGenericInputTemplateV2 form-label="Seed By">
                        <div
                          slot="field"
                          class="
                            e4s-flex-row e4s-justify-flex-row-vert-center
                            e4s-gap--standard
                            form-generic-input-template-v2--input-container
                          "
                        >
                          <div
                            class="
                              e4s-flex-row
                              e4s-gap--large
                              e4s-justify-flex-row-vert-center
                            "
                          >
                            <InputCheckboxV2
                              :is-required-field="false"
                              class="e4s-align-self-flex-start"
                              v-model="compEvent.options.seed.gender"
                              value-label="Gender"
                            />

                            <InputCheckboxV2
                              :is-required-field="false"
                              class="e4s-align-self-flex-start"
                              v-model="compEvent.options.seed.age"
                              value-label="Age"
                            />
                          </div>
                        </div>
                      </FormGenericInputTemplateV2>
                    </template>
                  </FormGenericFieldGridV2>
                  <!--/Seed Format-->
                </div>
              </div>
            </div>
          </div>
        </collapse-section>
        <!--/Card Information-->

        <!--Security-->
        <collapse-section
          v-if="configController.isIRL.value || configController.isAdmin.value"
          id="security-section"
          :class="
            configController.isAdmin.value && !configController.isIRL.value
              ? 'e4s-admin--section'
              : ''
          "
          icon-name="security"
          header-message="Security"
          :is-expanded="sectionsExpanded.security"
          v-on:isSectionBodyVisible="sectionsExpanded.security = $event"
        >
          <div slot="section-content">
            <div class="row">
              <div class="col s12 m12 l12">
                <HeaderSectionThickLine title="User Entry Restrictions" />

                Make event entry available
                <strong>ONLY TO USERS</strong> assigned to the entities (E.g.
                club, county or regional representatives). Either secure to
                specific entities or use "all" to allow entry by any user with
                that given entity level. If nothing listed here, available to
                all users.
              </div>
            </div>
            <div class="row">
              <div class="col s12 m12 l12">
                <security
                  :security="compEvent.options.security"
                  v-on:onChanged="onSecurityChanged"
                />
              </div>
            </div>

            <div class="row">
              <div class="col s12 m12 l12">
                <HeaderSectionThickLine title="Athlete Entry Restrictions" />

                Make event entry available
                <strong>ONLY TO ATHLETES</strong> assigned to the entities (E.g.
                club, county, region). Either secure to specific entities or use
                "all" to allow entry by any user with that given entity level.
                If nothing listed here, available to all athletes.
              </div>
            </div>
            <div class="row">
              <div class="col s12 m12 l12">
                <security
                  :security="compEvent.options.athleteSecurity"
                  v-on:onChanged="onAthleteSecurityChanged"
                >
                </security>
              </div>
            </div>
          </div>
        </collapse-section>
        <!--/Security-->

        <!--Multi Event-->
        <collapse-section
          icon-name="apps"
          header-message="Multi Event"
          :is-expanded="sectionsExpanded.multiEvent"
          v-on:isSectionBodyVisible="sectionsExpanded.multiEvent = $event"
        >
          <div slot="section-content">
            <div v-if="getIsMultiEventChild">
              <div class="row">
                <div class="col s12 m12 l12">
                  <div class="e4s-flex-row e4s-gap--large">
                    This event is part of
                    <template
                      v-text="compEvent.options.entriesFrom.name"
                    ></template
                    >.

                    <FieldHelp
                      help-key="comp-event-adder--multi-event"
                      :get-from-server="true"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div v-if="!getIsMultiEventChild">
              <div class="row">
                <div class="col s12 m12 l12">
                  <div class="e4s-flex-row e4s-gap--large">
                    List below the events that the multi event is comprised of.

                    <FieldHelp
                      help-key="comp-event-adder--multi-event"
                      :get-from-server="true"
                    />
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col s12 m12 l12">
                  <MultiEventOptions
                    v-model="compEvent.options.multiEventOptions"
                    @changed="multiEventOptionsChanged"
                  />
                </div>
              </div>
            </div>
          </div>
        </collapse-section>
        <!--/Multi Event-->

        <!--Upscaling-->
        <collapse-section
          icon-name="apps"
          header-message="Upscaling (allow other age groups)"
          :is-expanded="false"
        >
          <div slot="section-content">
            <div class="row">
              <div class="col s12 m12 l12">
                <FormGenericInputTemplateV2
                  :form-label="
                    'Age Group Selector (Current Age group(s): ' +
                    getCurrentAgeGroups +
                    ')'
                  "
                >
                  <FieldHelp
                    slot="after-label"
                    help-key="comp-event-adder--upscaling"
                    :get-from-server="true"
                  />

                  <div class="e4s-flex-column" slot="field">
                    <div class="e4s-vertical-spacer--large"></div>

                    <InfoSectionV2
                      info-type="error"
                      v-if="configController.isAdmin.value"
                    >
                      Admin Mode: ALL age groups displaying. "Normal" user will
                      have current age group(s) removed from this list.
                    </InfoSectionV2>

                    <AgeGroupCheckBoxV2
                      slot="field"
                      :age-groups="getAgeGroupsForUpScale"
                      :age-groups-default="compEvent.options.ageGroups"
                      :allow-default-to-show-all-age-groups="false"
                      :show-default-only-filter="true"
                      :single-column="false"
                      @onSelected="onAgeGroupsUpScaleSelected"
                    >
                      <div slot="field-title">
                        <!--                <FormGenericSectionTitleV2 title="Age Groups" />-->
                      </div>
                    </AgeGroupCheckBoxV2>
                  </div>
                </FormGenericInputTemplateV2>

                <!--                <label class="active" :for="PREFIX + 'age-groups-comp'">-->
                <!--                  Age Group Selector (Current Age group:-->
                <!--                  <span v-text="getCurrentAgeGroups"></span>)-->
                <!--                </label>-->
                <!--                <age-group-check-box-->
                <!--                  :age-groups="getAgeGroupsForUpScale"-->
                <!--                  :age-groups-default="compEvent.options.ageGroups"-->
                <!--                  :allow-default-to-show-all-age-groups="true"-->
                <!--                  v-on:onSelected="onAgeGroupsUpScaleSelected"-->
                <!--                >-->
                <!--                </age-group-check-box>-->
              </div>
            </div>
          </div>
        </collapse-section>
        <!--/Upscaling-->

        <!--Time Options-->
        <collapse-section
          icon-name="access_time"
          header-message="Time Options"
          :is-expanded="false"
        >
          <div slot="section-content">
            <div class="row">
              <div class="col s12 m12 l12">
                <div class="e4s-flex-row e4s-gap--large">
                  Custom opening times for this event
                  <FieldHelp
                    help-key="acomp-event--time-options"
                    :get-from-server="true"
                  />
                </div>

                <div class="e4s-vertical-spacer--large"></div>

                <FormGenericFieldGridV2>
                  <template slot="content">
                    <FormGenericInputTemplateV2
                      form-label="Entry Available From"
                    >
                      <DateTimeEntryV2
                        slot="field"
                        v-model="compEvent.options.availableFrom"
                      />
                    </FormGenericInputTemplateV2>

                    <FormGenericInputTemplateV2
                      form-label="Until Available, Display Is"
                    >
                      <div slot="field" class="e4s-flex-row e4s-gap--large">
                        <FieldRadioV2
                          option-value="-1"
                          v-model.number="compEvent.options.availableFromStatus"
                          label="Hidden"
                        />
                        <FieldRadioV2
                          option-value="0"
                          v-model.number="compEvent.options.availableFromStatus"
                          label="Closed"
                        />
                      </div>
                    </FormGenericInputTemplateV2>
                  </template>
                </FormGenericFieldGridV2>

                <FormGenericFieldGridV2>
                  <template slot="content">
                    <FormGenericInputTemplateV2
                      form-label="Entry Available Until"
                    >
                      <DateTimeEntryV2
                        slot="field"
                        v-model="compEvent.options.availableTo"
                      />
                    </FormGenericInputTemplateV2>

                    <FormGenericInputTemplateV2
                      form-label="When Not Available, Display Is"
                    >
                      <div slot="field" class="e4s-flex-row e4s-gap--large">
                        <FieldRadioV2
                          option-value="-1"
                          v-model.number="compEvent.options.availableToStatus"
                          label="Hidden"
                        />
                        <FieldRadioV2
                          option-value="0"
                          v-model.number="compEvent.options.availableToStatus"
                          label="Closed"
                        />
                      </div>
                    </FormGenericInputTemplateV2>
                  </template>
                </FormGenericFieldGridV2>

                <FormGenericFieldGridV2
                  v-if="builderCompetition.options.checkIn.enabled"
                >
                  <template slot="content">
                    <FormGenericInputTemplateV2
                      form-label="Check in opens X minutes prior to event (override)"
                    >
                      <FieldNumberV2
                        slot="field"
                        v-model="compEvent.options.checkIn.from"
                        class="e4s-input--digit-number-medium"
                      />
                    </FormGenericInputTemplateV2>

                    <FormGenericInputTemplateV2
                      form-label="Check in closes X minutes prior to event (override)"
                    >
                      <FieldNumberV2
                        slot="field"
                        v-model="compEvent.options.checkIn.to"
                        class="e4s-input--digit-number-medium"
                      />
                    </FormGenericInputTemplateV2>
                  </template>
                </FormGenericFieldGridV2>
              </div>
            </div>

            <!--            <div class="row" v-if="builderCompetition.options.checkIn.enabled">-->
            <!--              <div class="input-field col s12 m6 l6">-->
            <!--                <label class="active" for="check-in-from"-->
            <!--                  >Check in opens X minutes prior to event (override)</label-->
            <!--                >-->
            <!--                <input-->
            <!--                  id="check-in-from"-->
            <!--                  name="checkinfrom"-->
            <!--                  type="number"-->
            <!--                  v-model.number="compEvent.options.checkIn.from"-->
            <!--                />-->
            <!--              </div>-->

            <!--              <div class="input-field col s12 m6 l6">-->
            <!--                <label class="active" for="check-in-to"-->
            <!--                  >Check in closes X minutes prior to event (override)</label-->
            <!--                >-->
            <!--                <input-->
            <!--                  id="check-in-to"-->
            <!--                  name="checkinto"-->
            <!--                  type="number"-->
            <!--                  v-model.number="compEvent.options.checkIn.to"-->
            <!--                />-->
            <!--              </div>-->
            <!--            </div>-->
          </div>
        </collapse-section>
        <!--/Time Options-->

        <!-- secondary -->
        <!--        <collapse-section-->
        <!--          icon-name="add_shopping_cart"-->
        <!--          header-message="Secondary"-->
        <!--          :is-expanded="false"-->
        <!--        >-->
        <!--          <div slot="section-content">-->
        <!--            <div class="row">-->
        <!--              <div class="col s12 m12 l12">-->
        <!--                <select-->
        <!--                  v-model="secondaryRefObjSelected"-->
        <!--                  class="browser-default"-->
        <!--                >-->
        <!--                  <option v-for="refObj in getSecondaryRefObjs" :value="refObj">-->
        <!--                    <span-->
        <!--                      v-text="-->
        <!--                        (refObj.objType === 'GROUP' ? 'Event Group - ' : '') +-->
        <!--                        refObj.objName-->
        <!--                      "-->
        <!--                    ></span>-->
        <!--                  </option>-->
        <!--                </select>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--            <SecondaryFormGrid-->
        <!--              :secondary-ref-obj="secondaryRefObjSelected"-->
        <!--              :display-select-button-in-grid="false"-->
        <!--            >-->
        <!--            </SecondaryFormGrid>-->
        <!--          </div>-->
        <!--        </collapse-section>-->
        <!-- /secondary -->
      </div>

      <div
        class="e4s-sticky-save--container"
        :class="
          'e4s-sticky-save--container-' +
          configController.getCssEnvIdentifier.value
        "
      >
        <div class="e4s-flex-row">
          <!--          <InputCheckboxV2-->
          <!--            :is-required-field="false"-->
          <!--            class="e4s-align-self-flex-start"-->
          <!--            :value="showAdvancedOptions"-->
          <!--            @onChange="setShowAdvancedOptions"-->
          <!--          >-->
          <!--            <div class="e4s-header&#45;&#45;400">Show Advanced Options</div>-->
          <!--          </InputCheckboxV2>-->

          <ButtonGenericV2
            class="e4s-flex-row--end"
            text="Save"
            @click="submitCompEvent"
            :disabled="isCompEventLoading"
          />
        </div>
      </div>

      <!--      <div v-if="showAdvancedOptions">-->
      <!--        <div class="e4s-section-padding-separator"></div>-->

      <!--        <div class="row">-->
      <!--          <div class="col s12 m12 l12">-->
      <!--            <div class="right">-->
      <!--              &lt;!&ndash;              <loading-spinner v-if="isCompEventLoading"></loading-spinner>&ndash;&gt;-->
      <!--              <LoadingSpinnerV2 v-if="isCompEventLoading" />-->
      <!--              <ButtonGenericV2-->
      <!--                text="Save"-->
      <!--                @click="submitCompEvent"-->
      <!--                :disabled="isCompEventLoading"-->
      <!--              />-->
      <!--              &lt;!&ndash;              <button&ndash;&gt;-->
      <!--              &lt;!&ndash;                class="btn waves-effect waves green"&ndash;&gt;-->
      <!--              &lt;!&ndash;                :disabled="isCompEventLoading"&ndash;&gt;-->
      <!--              &lt;!&ndash;                v-on:click.stop="submitCompEvent"&ndash;&gt;-->
      <!--              &lt;!&ndash;              >&ndash;&gt;-->
      <!--              &lt;!&ndash;                <span v-text="$t('buttons.save')"></span>&ndash;&gt;-->
      <!--              &lt;!&ndash;              </button>&ndash;&gt;-->
      <!--            </div>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->
    </div>

    <div class="row" v-show="showSection === 'CREATE_EVENT_FORM'">
      <div class="col s12 m12 l12">
        <event-form
          class="e4s-form-wrapper"
          v-on:onSubmit="eventFormOnSubmit"
          v-on:onCancel="eventFormOnCancel"
        />
      </div>
    </div>

    <!--    css-class="e4s-modal-container&#45;&#45;full-size"-->
    <ModalV2
      :always-show-header-blank="true"
      :is-full-screen="true"
      v-if="showSection === 'AGE_GROUP_SELECTOR'"
    >
      <div
        class="e4s-flex-column e4s-gap--large e4s-padding--standard"
        slot="body"
      >
        <ButtonGenericBackV2
          @click="setShowSection('EVENT_ADD')"
          class="e4s-align-self-flex-end"
        />
        <AgeGroupDefaultSelectContainer
          :comp-id="builderCompetition.id"
          :show-create-button="true"
        />
      </div>
    </ModalV2>

    <!--    <div class="row" v-show="showSection === 'AGE_GROUP_SELECTOR'">-->
    <!--      <div class="col s12 m12 l12">-->
    <!--        <AgeGroupDefaultSelectContainer :comp-id="builderCompetition.id" />-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <e4s-modal-->
    <!--      v-if="showPriceConfirmDelete"-->
    <!--      :header-message="'Price Delete'"-->
    <!--      :body-message="'Are you sure you want to delete this Price'"-->
    <!--      :button-primary-text="'Delete'"-->
    <!--      :isLoading="isLoadingPrice"-->
    <!--      v-on:closeSecondary="onPriceDeleteCancel"-->
    <!--      v-on:closePrimary="priceDeleteRecord"-->
    <!--    >-->
    <!--    </e4s-modal>-->

    <e4s-modal
      v-if="showValidationMessages"
      header-message="Validation"
      v-on:closePrimary="showValidationMessages = false"
    >
      <span slot="button-close-secondary"></span>
      <div
        v-html="
          validationController.getValidationSummaryWithTitle().join('<br>')
        "
        slot="body"
      ></div>
    </e4s-modal>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { mapState } from "vuex";
import {
  IAgeGroup,
  IAgeGroupCompCoverageModel,
} from "../../agegroup/agegroup-models";
import AutoCompleteMat from "../../common/ui/autocomplete/auto-complete-mat.vue";
import { ICompEvent } from "../../compevent/compevent-models";
import { BuilderService } from "../builder-service";
import { IBuilderStoreState } from "../builder-store";
import { BUILDER_STORE_CONST } from "../builder-store-constants";
import GenderSelect from "../../common/ui/gender-select.vue";
import { IEventE4S, IUniqueEventDisplay } from "../../event/event-models";
import {
  ICustom,
  IAutoCompleteValue,
} from "../../common/ui/autocomplete/auto-complete-mat-models";
import BuilderAgeGroupSelect from "../../agegroup/agegroup-builder/builder-age-group-select.vue";
import { AgeGroupService } from "../../agegroup/agegroup-service";
import EventForm from "../../event/event-form.vue";
import { EventService } from "../../event/event-service";
import { CompEventService } from "../../compevent/compevent-service";
import { CommonService } from "../../common/common-service";
import PriceSelector from "../../price/price-selector.vue";
import { IPrice, IPriceSelectorUiDisplay } from "../../price/price-models";
import { PriceService } from "../../price/price-service";
import { IBuilderCompetition } from "../builder-models";
import { Watch } from "vue-property-decorator";
import {
  GENDER,
  GenderType,
  IBase,
  IObjectKeyType,
  IServerResponse,
} from "../../common/common-models";
import E4sModal from "../../common/ui/e4s-modal.vue";
import {
  IBuilderCompEvent,
  IBuilderCompEventMessage,
  IAgeCeidGender,
  EventGroupCreateOptions,
} from "../buildercompevent/builder-comp-event-models";
import { BuilderCompEventService } from "../buildercompevent/builder-comp-event-service";
import {
  IUnique,
  IEventTeamCe,
  ISecurity,
  IEventGroupSelect,
} from "../../athletecompsched/athletecompsched-models";
import UserValidationMessages from "../../user-message/user-validation-messages.vue";
import { CompEventArrData } from "../buildercompevent/compeventarr-data";
import LoadingSpinner from "../../common/ui/loading-spinner.vue";
import EventTeamOptions from "../../event/event-team-options.vue";
import DateEntryMat from "../../common/ui/datetime/date-entry-mat.vue";
import TimeEntry from "../../common/ui/datetime/time-entry.vue";
import Security from "../security/security.vue";
import AgeGroupCheckBox from "../../agegroup/agegroup-builder/checkbox/agegroup-checkbox.vue";
import DateTimeEntry from "../../common/ui/datetime/date-time-entry.vue";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import { ValidationController } from "../../validation/validation-controller";
import EventSelector from "../../event/event-selector.vue";
import EventSelectorBuilder from "./event-selector-builder/event-selector-builder.vue";
import { IEventGender } from "./event-selector-builder/event-selector-builder-models";
import { EventSelectorBuilderService } from "./event-selector-builder/event-selector-builder-service";
import CollapseSection from "../../common/ui/collapse/collapse-section.vue";
import { ValidationService } from "../../validation/validation-service";
import { IValidationProp } from "../../validation/validation-models";
import FieldValidationLabel from "../../validation/validation-field-lable.vue";
import EventGroupTypeAhead from "./event-group-type-ahead.vue";
import { AthleteCompSchedService } from "../../athletecompsched/athletecompsched-service";
import InputRestrictLength from "../../common/ui/field/input-restrict-length/input-restrict-length.vue";
import InputRestrictWithLabel from "../../common/ui/field/input-restrict-length/input-restrict-with-label.vue";
import SecondaryFormGrid from "../../secondary/config/secondary-form-grid.vue";
import {
  ISecondaryDef,
  ISecondaryRefObj,
} from "../../secondary/secondary-models";
import {
  ISecondaryStoreState,
  SECONDARY_STORE_CONST,
} from "../../secondary/secondary-store";
import { SecondaryService } from "../../secondary/secondary-service";
import CompeventGridSelector from "./compevent-grid-selector.vue";
import EventGroupSelect from "./event-group/event-group-select.vue";
import FieldHelp from "../../common/ui/field/field-help/field-help.vue";
import {
  getE4sStandardHumanDateOutPut,
  getE4sStandardHumanDateTimeOutPut,
  getGenderLabelAll,
  simpleClone,
  unique,
} from "../../common/common-service-utils";
import DateTimeEntryV2 from "../../common/ui/datetime/date-time-entry-v2.vue";
import MultiEventOptions from "../../athleteCompSched/multi-event-options/MultiEventOptions.vue";
import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FormGenericFieldGridV2 from "../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import EventSelectorV2 from "./event-selector-builder/v2/EventSelectorV2.vue";
import AgeGroupCheckBoxV2 from "../../agegroup/agegroup-builder/checkbox/agegroup-checkbox-v2.vue";
import EventGroupTypeAheadV2 from "./event-group-type-ahead-v2.vue";
import FormGenericSectionTitleV2 from "../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import FieldNumberV2 from "../../common/ui/layoutV2/fields/field-number-v2.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import InfoSectionV2 from "../../common/ui/layoutV2/info-section-v2.vue";
import { useFieldHelpController } from "../../common/ui/field/field-help/useFieldHelp";
import { IHelpData } from "../../common/ui/field/field-help/field-help-store";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";
import FieldTextV2 from "../../common/ui/layoutV2/fields/field-text-v2.vue";
import AdminIdV2 from "../../common/ui/layoutV2/admin-id-v2.vue";
import InputCheckboxV2 from "../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import CompeventAgeGroupCoverage from "../../compevent/compeventschedule/compevent-age-group-coverage/compevent-age-group-coverage.vue";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { IConfigApp } from "../../config/config-app-models";
import PrimaryLink from "../../common/ui/layoutV2/href/PrimaryLink.vue";
import { useScrollTo } from "../../common/useScrollTo";
import AgeGroupDefaultSelectContainer from "../../agegroup/agegroup-builder/agegroup-default-select-container.vue";
import ModalV2 from "../../common/ui/layoutV2/modal/modal-v2.vue";
import ButtonGenericBackV2 from "../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import { useConfigController } from "../../config/useConfigStore";
import FormGenericInputTextV2 from "../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FieldRadioV2 from "../../common/ui/layoutV2/fields/field-radio-v2.vue";
import FormGenericInputNumberV2 from "../../common/ui/layoutV2/form/form-generic--input-number-v2.vue";
import SectionThickLine from "../../common/ui/layoutV2/form/SectionThickLine.vue";
import HeaderSectionThickLine from "../../common/ui/layoutV2/form/HeaderSectionThickLine.vue";
import Accordion from "../../common/ui/layoutV2/accordion/Accordion.vue";
import { useAgeGroupController } from "../../agegroup/useAgeGroupController";

const eventSelectorBuilderService: EventSelectorBuilderService =
  new EventSelectorBuilderService();
const validationService: ValidationService = new ValidationService();

type LocalHelpKey = "IS_EVENT_OPEN";
type EventAdderSection =
  | "EVENT_ADD"
  | "CREATE_EVENT_FORM"
  | "AGE_GROUP_SELECTOR";

@Component({
  name: "comp-event-adder",
  methods: { getGenderLabelAll },
  components: {
    Accordion,
    HeaderSectionThickLine,
    SectionThickLine,
    FormGenericInputNumberV2,
    FieldRadioV2,
    FormGenericInputTextV2,
    ButtonGenericBackV2,
    ModalV2,
    AgeGroupDefaultSelectContainer,
    PrimaryLink,
    CompeventAgeGroupCoverage,
    InputCheckboxV2,
    AdminIdV2,
    FieldTextV2,
    LoadingSpinnerV2,
    InfoSectionV2,
    ButtonGenericV2,
    FieldNumberV2,
    FormGenericSectionTitleV2,
    EventGroupTypeAheadV2,
    AgeGroupCheckBoxV2,
    EventSelectorV2,
    FormGenericFieldGridV2,
    FormGenericInputTemplateV2,
    MultiEventOptions,
    DateTimeEntryV2,
    FieldHelp,
    EventGroupSelect,
    CompeventGridSelector,
    SecondaryFormGrid,
    InputRestrictWithLabel,
    InputRestrictLength,
    EventSelectorBuilder,
    TimeEntry,
    "auto-complete-mat": AutoCompleteMat,
    "gender-select": GenderSelect,
    "builder-age-group-select": BuilderAgeGroupSelect,
    "event-form": EventForm,
    "price-selector": PriceSelector,
    "e4s-modal": E4sModal,
    "user-validation-messages": UserValidationMessages,
    EventTeamOptions: EventTeamOptions,
    "loading-spinner": LoadingSpinner,
    "date-entry-mat": DateEntryMat,
    "time-entry": TimeEntry,
    security: Security,
    "age-group-check-box": AgeGroupCheckBox,
    "date-time-entry": DateTimeEntry,
    "event-selector": EventSelector,
    CollapseSection,
    FieldValidationLabel,
    EventGroupTypeAhead,
  },
  computed: {
    ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
      builderCompetition: (state: IBuilderStoreState) =>
        state.builderCompetition,
      events: (state: IBuilderStoreState) => state.events,
      eventsLoading: (state: IBuilderStoreState) => state.eventsLoading,
      ageGroupsForComp: (state: IBuilderStoreState) => state.ageGroupsForComp,
      ageGroupsDefault: (state: IBuilderStoreState) => state.ageGroupsDefault,
      prices: (state: IBuilderStoreState) => state.prices,
      builderCompEventBeingEdited: (state: IBuilderStoreState) =>
        state.builderCompEventBeingEdited,
      compEventsSchedule: (state: IBuilderStoreState) =>
        state.compEventsSchedule,
      stickyDate: (state: IBuilderStoreState) => state.stickyEntryDate,
    }),
    ...mapState(SECONDARY_STORE_CONST.SECONDARY_CONST_MODULE_NAME, {
      // secondaries: (state: ISecondaryStoreState) => state.secondaryDefDisplays,
      secondariesLoading: (state: ISecondaryStoreState) =>
        state.secondariesLoading,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class CompEventAdder extends Vue {
  public readonly secondaries: ISecondaryDef[];
  public readonly secondariesLoading: boolean;
  public readonly stickyDate!: string;
  public readonly compEventsSchedule!: ICompEvent[];
  public readonly configApp: IConfigApp;
  public readonly builderCompetition: IBuilderCompetition;
  public readonly ageGroupsForComp: IAgeGroup[];
  public readonly ageGroupsDefault: IAgeGroup[];

  public isMounted: boolean = false;
  public isQuickMounted: boolean = true;

  public prices: IPrice[];
  public builderCompEventBeingEdited: IBuilderCompEvent;

  public PREFIX = Math.random().toString(36).substring(2);
  public eventService: EventService = new EventService();
  public compEventService: CompEventService = new CompEventService();
  public compEventArrData: CompEventArrData = new CompEventArrData();
  public priceService: PriceService = new PriceService();
  public ageGroupService: AgeGroupService = new AgeGroupService();
  public commonService: CommonService = new CommonService();
  public builderCompEventService: BuilderCompEventService =
    new BuilderCompEventService();
  public compEvent: ICompEvent = this.compEventService.factoryGetCompEvent();
  public builderCompEvent: IBuilderCompEvent =
    this.builderCompEventService.factory();
  public builderService: BuilderService = new BuilderService();

  public eventDateTime: string = "";
  public eventDate: string = "";
  public eventTime: string = "00:00";

  public showEventForm: boolean = false;

  public showSection: EventAdderSection = "EVENT_ADD";

  public gender: string = "";

  public eventE4s: IEventE4S = this.eventService.factoryGetEvent();
  public ageGroup: IAgeGroup = this.ageGroupService.factoryGetAgeGroup();
  public price: IPrice = this.priceService.factoryGetPrice();
  public eventNo: number = 1;

  public isCompEventLoading: boolean = false;

  public showPriceConfirmDelete: boolean = false;
  public isLoadingPrice: boolean = false;

  public checkedAgeGroups: IAgeGroup[] = [];

  public showSubmit: boolean = false;
  public allowGenderEventEdit: boolean = true;
  public eventPreload: string = "";
  public validationController: ValidationController =
    new ValidationController();
  public eventGender: IEventGender =
    eventSelectorBuilderService.factoryEventGender();
  public eventsForGender: IEventE4S[] = [];
  public checkedEventGenders: IEventE4S[] = [];
  public ageCeidGenders: IAgeCeidGender[] = [];
  public compEventsFromSchedule: ICompEvent[] = [];
  public preLoadSearchTerm: string = "";

  public athleteCompSchedService: AthleteCompSchedService =
    new AthleteCompSchedService();

  public secondaryService: SecondaryService = new SecondaryService();
  public secondaryRefObjSelected: ISecondaryRefObj =
    this.secondaryService.factorySecondaryRefObj();

  public showValidationMessages = false;
  public showAdvancedOptions = false;
  public foundMatchingEventGroup = false;

  public uniqueEventGroups: IEventGroupSelect[] = [];

  public fieldHelpController = useFieldHelpController();

  public configController = useConfigController();
  public ageGroupController = useAgeGroupController();

  public eventGroupCreateOptions: EventGroupCreateOptions = {
    addGender: false,
    addAge: false,
  };

  public isAgeGroupCoverageExpanded: boolean = false;

  public priceSelectorUiDisplay: IPriceSelectorUiDisplay = {
    showButtons: {
      delete: false,
      edit: false,
      select: true,
    },
  };

  public sectionsExpanded = {
    basic: true,
    restrictions: false,
    team: false,
    card: false,
    security: false,
    multiEvent: false,
    upscaling: false,
    time: false,
    secondary: false,
  };

  public quickSecurityClubs: boolean = false;
  public quickSecurityCounties: boolean = false;
  public quickSecurityRegions: boolean = false;

  public myScrollTo = useScrollTo();

  public DEBUG: boolean = true;

  public quickSecurityChanged(which: keyof ISecurity, value: boolean) {
    // this.quickSecurity[which] = value;

    if (which === "clubs") {
      this.quickSecurityClubs = value;
    } else if (which === "counties") {
      this.quickSecurityCounties = value;
    } else if (which === "regions") {
      this.quickSecurityRegions = value;
    }

    if (value) {
      this.compEvent.options.security[which] = [
        {
          id: 0,
          name: "ALL",
        },
      ];
    } else {
      this.compEvent.options.security[which] = [];
    }

    this.compEvent.options.security = simpleClone(
      this.compEvent.options.security
    );

    // make sure advanced options are shown
    this.showAdvancedOptions = true;

    // and show security section
    this.sectionsExpanded.security = true;
  }

  public doShowHelp(localHelpKey: LocalHelpKey) {
    const localHelpMap: Record<LocalHelpKey, IHelpData> = {
      IS_EVENT_OPEN: {
        id: 1,
        key: "",
        title: "Is Event Open",
        data:
          "If 'Closed', event is still visible but can not be entered.  This can also " +
          "happen when an event is over subscribed and waiting lists are NOT being used.  " +
          "Manually set this to 'Closed' to prevent further entries.",
        type: "T",
        preload: false,
      },
    };

    const helpData: IHelpData = localHelpMap[localHelpKey];

    this.fieldHelpController.sendLocalHelpData(helpData);
  }

  public created() {
    const compEvent = this.compEvent;
    compEvent.compId = this.builderCompetition.id;

    //  Entering multi day comps is a pain without this.
    const preloadDate =
      this.stickyDate.length > 0
        ? this.stickyDate
        : this.builderCompetition.date;

    this.eventDate = this.commonService.transformIsoForInputField(
      preloadDate,
      true,
      false
    );
    this.compEvent.eventNo = this.eventNo;
    if (this.prices.length === 1) {
      this.initPrice(this.prices);
    }

    this.eventTime = "00:00";
    this.transformsOut();
    this.rebuildCompEvent();
    // this.validate();

    this.initCompEvent(this.builderCompEventBeingEdited);

    if (this.getSecondaryRefObjs.length > 1) {
      this.secondaryRefObjSelected = this.getSecondaryRefObjs[0];
    }

    this.checkIfAgeGroupsNeedLoading();
  }

  public mounted() {
    this.isMounted = true;
  }

  public get getIsScheduleOnly(): boolean {
    return this.compEvent.maxAthletes === -1;
  }

  public setIsScheduelOnly(): void {
    if (this.compEvent.maxAthletes === -1) {
      this.compEvent.maxAthletes = 0;
    } else {
      this.compEvent.maxAthletes = -1;
    }

    if (this.compEvent.maxAthletes === -1) {
      // even though price is irrelevant, we need one to succesfully save
      // Set the price to the first one in the array
      this.compEvent.price = this.prices[0];

      this.compEvent.options.entriesFrom = {
        id: 0,
        name: "",
        eventNo: 0,
        isMultiEvent: false,
      };
    } else {
      this.compEvent.options.includeEntriesFromEgId = {
        id: 0,
        name: "",
        eventNo: 0,
        isMultiEvent: false,
      };
    }
  }

  public validate() {
    let errors: IObjectKeyType<IValidationProp> =
      this.compEventService.validate(this.compEvent);

    if (this.eventsForGender.length === 0) {
      // errors = validationService.addMessage("event", "Required", errors);
      errors = validationService.addMessageCompound(
        {
          title: "Basic Section",
          subTitle: "Event",
          propPath: "event",
          message: "Discipline Required",
        },
        errors
      );
    }

    if (this.checkedEventGenders.length === 0) {
      // errors = validationService.addMessage("gender", "Required", errors);
      errors = validationService.addMessageCompound(
        {
          title: "Basic Section",
          subTitle: "Gender",
          propPath: "gender",
          message: "Gender Required",
        },
        errors
      );
    }

    if (!(this.checkedAgeGroups && this.checkedAgeGroups.length > 0)) {
      // errors = validationService.addMessage("ageGroups", "Required", errors);
      errors = validationService.addMessageCompound(
        {
          title: "Basic Section",
          subTitle: "Age Groups",
          propPath: "ageGroups",
          message: "Age Group(s) Required",
        },
        errors
      );
    }

    if (!this.compEventService.isPriceOk(this.compEvent)) {
      // errors = validationService.addMessage("price", "Required", errors);
      errors = validationService.addMessageCompound(
        {
          title: "Basic Section",
          subTitle: "Price",
          propPath: "price",
          message: "Price Required",
        },
        errors
      );
    }

    this.validationController.setErrors(errors);
  }

  public get getValidationHtml(): string {
    return "";
  }

  public resetForm(compEvent?: ICompEvent) {
    if (compEvent) {
      this.compEvent = R.clone(compEvent);
    } else {
      this.compEvent = this.compEventService.factoryGetCompEvent();
    }
    this.compEvent.compId = this.builderCompetition.id;

    this.eventDate = this.commonService.transformIsoForInputField(
      this.builderCompetition.date,
      true,
      false
    );
    this.eventTime = "00:00";
    this.transformsOut();

    const builderCompEvent = this.builderCompEventService.factory();
    this.builderCompEvent = builderCompEvent;

    this.$store.commit(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_MUTATIONS_BUILDERCOMPEVENT_EDIT_SET,
      builderCompEvent
    );

    this.checkedAgeGroups = [];
    this.gender = "";

    if (this.prices.length === 1) {
      this.initPrice(this.prices);
    } else {
      this.price = this.priceService.factoryGetPrice();
    }

    this.eventGender = eventSelectorBuilderService.factoryEventGender();
    this.eventsForGender = [];
    this.checkedEventGenders = [];
    this.ageCeidGenders = [];
    this.compEventsFromSchedule = [];
    this.preLoadSearchTerm = "";

    this.resetSubmit();
    this.allowGenderEventEdit = true;
    this.rebuildCompEvent();
  }

  @Watch("builderCompetition")
  public onBuilderCompetitionChanged(newValue: IBuilderCompetition) {
    this.compEvent.compId = newValue.id;
    if (this.builderCompetition.date.length > 0) {
      this.eventDate = this.commonService.getE4sStandardDateOutPut(
        this.builderCompetition.date
      );
      this.eventTime = "00:00";
      this.transformsOut();
      this.rebuildCompEvent();
    }
  }

  @Watch("builderCompEventBeingEdited")
  public onBuilderCompEventBeingEditedChanged(newValue: IBuilderCompEvent) {
    this.initCompEvent(newValue);
  }

  public initCompEvent(newValue: IBuilderCompEvent) {
    if (newValue.id === 0) {
      console.log(">>>>>>>>>>> compEvent.id: " + this.compEvent.id);
      console.log(
        ">>>>>>>>>>> builderCompetition.options.pbMandatory: " +
          this.builderCompetition.options.pbMandatory
      );
      //  This is pointless...when you select event, it's
      //  that what sets it...unless user then overrides.
      // this.compEvent.options.mandatoryPB =
      //   this.builderCompetition.options.pbMandatory;

      return;
    }

    const compEvent = R.clone(newValue);

    if (!compEvent.options.uniqueEventGroups) {
      compEvent.options.uniqueEventGroups = [];
    }

    if (compEvent.options.availableToStatus === undefined) {
      compEvent.options.availableToStatus = 0;
    }
    if (compEvent.options.availableFromStatus === undefined) {
      compEvent.options.availableFromStatus = 0;
    }

    if (compEvent.options.ageGroupOverlap === undefined) {
      compEvent.options.ageGroupOverlap = false;
    }

    this.compEvent = compEvent;
    if (!this.compEvent.options.athleteSecurity) {
      this.compEvent.options.athleteSecurity = {
        clubs: [],
        counties: [],
        regions: [],
      };
    }

    if (!this.compEvent.options.entriesFrom) {
      this.compEvent.options.entriesFrom = {
        id: 0,
        name: "",
        eventNo: 0,
        isMultiEvent: false,
      };
    }
    if (this.compEvent.options.entriesFrom.id > 0) {
      const isParentMultiEvent =
        this.athleteCompSchedService.isEventGroupSelectFromAMultiEventGroup(
          this.compEventsSchedule,
          this.compEvent.options.entriesFrom
        );
      this.compEvent.options.entriesFrom.isMultiEvent = isParentMultiEvent;
    }

    if (!this.compEvent.options.multiEventOptions) {
      this.compEvent.options.multiEventOptions =
        this.athleteCompSchedService.factoryMultiEventOptions();
    }

    const ageGroups = newValue.compEventsFromSchedule.map(
      (compEvent) => compEvent.ageGroup
    );
    // this.builderCompEvent.ageGroups = R.clone(newValue.ageGroups);
    this.checkedAgeGroups = this.commonService.uniqueArrayById(
      ageGroups as IBase[]
    ) as IAgeGroup[];
    this.builderCompEvent.ageGroups = this.commonService.uniqueArrayById(
      ageGroups as IBase[]
    ) as IAgeGroup[];
    this.builderCompEvent.ageCeidLink = R.clone(newValue.ageCeidLink);

    //  Derprecated
    this.gender = newValue.event.gender;

    this.eventDate = this.commonService.transformIsoForInputField(
      newValue.startDateTime,
      true,
      false
    );
    this.eventTime = this.commonService.transformIsoForInputField(
      newValue.startDateTime,
      false,
      true
    );
    this.price = R.clone(newValue.price);

    this.allowGenderEventEdit = this.compEvent.id === 0;
    if (this.compEvent.id === 0) {
      this.builderCompEvent.ageGroups = [];
    }

    const eventGender: IEventGender =
      eventSelectorBuilderService.factoryEventGender();
    eventGender.eventName = newValue.event.name;
    eventGender.eventNameDisplay = newValue.event.name;
    eventGender.events[newValue.event.gender] = R.clone(newValue.event);
    this.eventGenderSelected(eventGender);

    const eventsFromSchedule = newValue.compEventsFromSchedule.map(
      (builderCompEvent) => builderCompEvent.event
    );
    this.eventsForGender = this.commonService.uniqueArrayById(
      eventsFromSchedule as IBase[]
    ) as IEventE4S[];
    this.checkedEventGenders = this.commonService.uniqueArrayById(
      eventsFromSchedule as IBase[]
    ) as IEventE4S[];
    this.compEventsFromSchedule = R.clone(newValue.compEventsFromSchedule);

    this.preLoadSearchTerm = this.compEventsFromSchedule[0].event.name;

    if (this.compEvent?.options?.security) {
      if (this.compEvent.options.security.clubs!.length > 0) {
        this.quickSecurityClubs = true;
      }
      if (this.compEvent.options.security.counties!.length > 0) {
        this.quickSecurityCounties = true;
      }
      if (this.compEvent.options.security.regions!.length > 0) {
        this.quickSecurityRegions = true;
      }
    }

    // check options.rowOptions.autoExpandHelpText exists and set to true
    if (!this.compEvent.options.rowOptions.autoExpandHelpText) {
      this.compEvent.options.rowOptions.autoExpandHelpText = true;
    }

    this.calculateAgeGenderEvents();
    this.transformsOut();
    this.validate();
  }

  public rebuildCompEvent() {
    this.mergeData();

    const ageGroups: IAgeGroup[] = this.checkedAgeGroups;

    //  TODO why do we do this.
    this.builderCompEvent = this.builderCompEventService.factory();
    this.builderCompEvent.ageGroups = this.checkedAgeGroups;

    this.builderCompEvent.ageCeidLink =
      this.builderService.mergeAgeGroupsIntoCeidLink(
        ageGroups,
        this.builderCompEventBeingEdited.ageCeidLink,
        []
      );

    //  Handle legacy events without this info...
    this.compEvent.options.checkIn = {
      ...this.athleteCompSchedService.factoryEventCheckIn(),
      ...this.compEvent.options.checkIn,
    };

    if (this.builderCompetition.options.checkIn.enabled) {
      if (this.builderCompEventBeingEdited.options.checkIn) {
        this.compEvent.options.checkIn.from =
          this.builderCompEventBeingEdited.options.checkIn.from;
        this.compEvent.options.checkIn.to =
          this.builderCompEventBeingEdited.options.checkIn.to;
      } else {
        this.compEvent.options.checkIn.from =
          this.builderCompetition.options.checkIn.defaultFrom;
        this.compEvent.options.checkIn.to =
          this.builderCompetition.options.checkIn.defaultTo;
      }
    }

    this.calculateAgeGenderEvents();
    this.resetSubmit();
    this.validate();
  }

  public calculateAgeGenderEvents() {
    const genderAgeGroups =
      this.builderService.mergeGenderAgeGroupsIntoCeidLink(
        this.checkedAgeGroups,
        this.checkedEventGenders,
        this.compEventsFromSchedule
      );
    this.ageCeidGenders =
      this.builderService.flattenGenderAgeGroups(genderAgeGroups);
    // console.log("x x x xx x x x x x calculateAgeGenderEvents", this.ageCeidGenders);
  }

  public ageGroupsSelected(ageGroups: IAgeGroup[]) {
    this.checkedAgeGroups = R.clone(ageGroups);
    this.builderCompEvent.ageGroups = this.checkedAgeGroups;
    this.builderCompEvent.ageCeidLink =
      this.builderService.mergeAgeGroupsIntoCeidLink(
        ageGroups,
        this.builderCompEventBeingEdited.ageCeidLink,
        []
      );

    // this.rebuildCompEvent();
    this.calculateAgeGenderEvents();
    this.validate();
  }

  public get isAgeGroupsValid(): boolean {
    return this.checkedAgeGroups.length > 0;
  }

  @Watch("prices")
  public onPricesChanged(prices: IPrice[]) {
    if (prices.length === 1) {
      this.initPrice(prices);
    }
  }

  public initPrice(prices: IPrice[]) {
    if (prices.length === 1) {
      this.onPriceSelected(prices[0]);
    }
  }

  public setShowSection(section: EventAdderSection) {
    this.showSection = section;
  }

  public showMoreAgeGroups() {
    this.showSection = "AGE_GROUP_SELECTOR";
  }

  public setShowEventForm(show: boolean) {
    this.showSection = show ? "CREATE_EVENT_FORM" : "EVENT_ADD";

    // this.showEventForm = show;
  }

  public eventSearchTermChanged(searchKey: string) {
    this.eventPreload = searchKey;
    if (searchKey.length === 0) {
      return;
    }
    const listParams = this.builderService.getListParamsDefault(searchKey);
    listParams.pagesize = 20;
    this.$store.dispatch(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_ACTIONS_LIST_EVENTS,
      { listParams, gender: this.gender }
    );
  }

  public mergeData() {
    this.compEvent.ageGroup = this.ageGroup;
    this.compEvent.price = this.price;
    this.compEvent.startDateTime = this.eventDateTime;
    this.compEvent.eventNo = this.eventNo;
  }

  public resetSubmit() {
    this.showSubmit = false;
  }

  public genderSelected(gender: string) {
    this.gender = gender;
    //  Reset the event, as needs re-selecting.
    this.eventE4s = this.eventService.factoryGetEvent();
    this.eventPreload = "";
    let currentEventName: string =
      this.compEvent && this.compEvent.event && this.compEvent.event.name
        ? this.compEvent.event.name
        : "";
    this.rebuildCompEvent();
    if (currentEventName.length > 0) {
      const fromGender = gender === GENDER.FEMALE ? GENDER.MALE : GENDER.FEMALE;
      currentEventName = currentEventName.replace(
        "(" + fromGender + ")",
        "(" + gender + ")"
      );
      this.eventSearchTermChanged(currentEventName);
    }
    this.validate();
  }

  public eventSelected(autoComplete: IAutoCompleteValue) {
    this.eventE4s = R.clone(autoComplete.value);
    this.compEvent = this.compEventService.addInitialEventToCompEvent(
      this.compEvent,
      this.eventE4s
    );
    this.validate();
  }

  public onDisciplineSelected(eventGender: IEventGender) {
    console.log("onDisciplineSelected(): ", eventGender);
    if (eventGender.eventName.length > 0) {
      const eventsForGender = this.commonService.convertObjectToArray(
        eventGender.events
      );

      if (eventsForGender.length > 0) {
        const firstEvent = eventsForGender[0];
        console.log("onDisciplineSelected() firstEvent: ", firstEvent);

        // By default, Track is mandatory EP, everything else is not.
        console.warn("onDisciplineSelected() firstEvent.tf: " + firstEvent.tf);
        //  E.g. IRL will be set to false, do NOT set mandatory PB.
        if (this.configApp.options.defaultMandatoryPB !== false) {
          this.compEvent.options.mandatoryPB = firstEvent.tf === "T";
        }

        if (firstEvent.options.isTeamEvent) {
          this.showAdvancedOptions = true;
          this.compEvent.options.isTeamEvent = true;
          this.sectionsExpanded.team = true;
          this.sectionsExpanded.security = true;
        } else {
          this.compEvent.options.isTeamEvent = false;
          this.sectionsExpanded.team = false;
          this.sectionsExpanded.security = false;
        }
      }
    }

    this.eventGenderSelected(eventGender);
  }

  public eventGenderSelected(eventGender: IEventGender) {
    console.log("eventGenderSelected: ", eventGender);
    if (eventGender.eventName.length === 0) {
      this.eventGender = eventSelectorBuilderService.factoryEventGender();
      this.eventsForGender = [];
    } else {
      this.eventGender = R.clone(eventGender);
      this.eventsForGender = this.commonService.convertObjectToArray(
        eventGender.events
      );
      //  set as checked.
      this.checkedEventGenders = this.eventsForGender;
      if (this.compEvent.eventGroup.length === 0) {
        this.eventGroupSelected(eventGender.eventName);
      }

      const firstEvent = this.eventsForGender[0];
      console.log("firstEvent: ", firstEvent);
      if (
        firstEvent.options &&
        firstEvent.options.multiEventOptions &&
        firstEvent.options.multiEventOptions.length > 0
      ) {
        this.compEvent.options.multiEventOptions = {
          childEvents: firstEvent.options.multiEventOptions.map((multi) => {
            return {
              ...multi,
              egId: 0,
            };
          }),
        };

        const eventNames: string =
          this.compEvent.options.multiEventOptions.childEvents
            .map((child) => child.name)
            .join(", ");
        messageDispatchHelper(
          "This event has 'multi events': " +
            eventNames +
            ". Please check the 'Multi Event' section in the 'Advanced Options' and amend as required.",
          USER_MESSAGE_LEVEL.ERROR.toString()
        );
        this.showAdvancedOptions = true;
        this.sectionsExpanded.multiEvent = true;
      }

      //  If team event, set the default team options and display security.
      // if (firstEvent.options.isTeamEvent) {
      //   this.compEvent.options.isTeamEvent = true;
      //   this.sectionsExpanded.team = true;
      //   this.sectionsExpanded.security = true;
      // }

      // By default, Track is mandatory EP, everything else is not.
      // console.warn("firstEvent.tf: " + firstEvent.tf);
      // this.compEvent.options.mandatoryPB = firstEvent.tf === "T";
    }

    this.rebuildCompEvent();
  }

  public eventGendersChecked() {
    if (this.checkedEventGenders.length === 0) {
      this.compEvent.event = this.eventService.factoryGetEvent();
    } else {
      this.compEvent.event = R.clone(this.checkedEventGenders[0]);
    }

    this.rebuildCompEvent();
  }

  public get getEventDate(): string {
    if (this.eventDate && this.eventDate.length > 0) {
      return this.commonService.convertToIsoDate(this.eventDate);
    }
    return "";
  }

  public onDateSelected(isoDate: string) {
    this.eventDate = this.commonService.transformIsoForInputField(
      isoDate,
      true,
      false
    );

    const eventDateArray = this.eventDate.split("/");

    this.$store.commit(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_MUTATIONS__STICKY_DATE_SET,
      eventDateArray[2] + "-" + eventDateArray[1] + "-" + eventDateArray[0]
    );
    this.transformsOut();
    this.validate();
  }

  public onTimeSelected(isoDateTime: string) {
    this.eventTime = isoDateTime.split("T")[0];
    this.transformsOut();
    this.validate();
  }

  public transformsOut() {
    if (this.eventDate.length > 0 && this.eventTime.length > 0) {
      this.eventDateTime = this.commonService.convertUserDateTimeInputToIso(
        this.eventDate,
        this.eventTime
      );
      const compEvent = R.clone(this.compEvent);

      compEvent.startDateTime = this.eventDateTime;
      this.compEvent = compEvent;

      this.builderCompEvent.startDateTime = this.eventDateTime;
      this.resetSubmit();
    }
  }

  public onPriceSelected(price: IPrice) {
    this.price = price;
    this.compEvent.price = price;
    this.compEvent = R.clone(this.compEvent);

    this.builderCompEvent.price = R.clone(price);
    this.validate();
    this.resetSubmit();
  }

  public get getCustomForEventAutoComplete(): ICustom {
    return {
      dropDownLabelFunc: this.eventService.getLabelForEvent,
    } as ICustom;
  }

  public eventFormOnCancel() {
    this.setShowEventForm(false);
  }

  public eventFormOnSubmit(eventE4s: IEventE4S) {
    this.$store.dispatch(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_ACTIONS_EVENT_CREATE,
      { eventE4s }
    );
  }

  public eventTeamOptionsChanged(eventTeam: IEventTeamCe) {
    console.log("eventTeamOptionsChanged", eventTeam);
    if (R.equals(eventTeam, this.compEvent.options.eventTeam)) {
      console.log("eventTeamOptionsChanged - same value, returning", {
        eventTeam,
        compEvent: this.compEvent.options.eventTeam,
      });
      return;
    }
    console.log("eventTeamOptionsChanged - different value, setting", {
      eventTeam,
      compEvent: this.compEvent.options.eventTeam,
    });
    this.compEvent.options.eventTeam = R.clone(eventTeam);
  }

  public get getShowEventForm() {
    return this.showEventForm;
  }

  public submitCompEvent() {
    this.compEventFormOnSubmit(this.compEvent);
  }

  public compEventFormOnSubmit(compEvent: ICompEvent) {
    // this.validationState = this.compEventService.validateNew(this.compEvent);

    this.validate();
    if (!this.validationController.isValid) {
      this.showValidationMessages = true;
      return;
    }

    if (this.builderCompEvent.ageGroups.length === 1) {
      this.compEvent.ageGroup = this.builderCompEvent.ageGroups[0];
    }

    const builderCompEvent: IBuilderCompEvent = R.clone(
      this.compEvent
    ) as IBuilderCompEvent;
    builderCompEvent.ageGroups = []; //  backend ignores this.
    builderCompEvent.ageCeidLink = this.builderCompEvent.ageCeidLink;
    // builderCompEvent.ageCeidLinkGender = this.genderAgeGroups;
    builderCompEvent.events = this.checkedEventGenders;
    builderCompEvent.ageCeidGenders = this.ageCeidGenders;
    builderCompEvent.createOptions = {
      addGender: this.eventGroupCreateOptions.addGender,
      addAge: this.eventGroupCreateOptions.addAge,
    };

    // builderCompEvent.startDateTime  take the offset off
    builderCompEvent.startDateTime = builderCompEvent.startDateTime.slice(
      0,
      19
    );

    this.isCompEventLoading = true;
    this.compEventArrData
      .create(builderCompEvent)
      .then((response: IServerResponse<IBuilderCompEventMessage[]>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        const messages: string[] = response.data.map((mess) => {
          return mess.ageGroup + " " + mess.eventname + " " + mess.status;
        });

        //  TODO server response not yet correct....so don't use it for anything.
        messageDispatchHelper(
          messages.join("<br>"),
          USER_MESSAGE_LEVEL.INFO.toString()
        );
        // if (this.builderCompEventBeingEdited.id > 0) {
        //     //  user come from "schedule"
        //     this.resetForm();
        // }

        //  @See builder-form.cancelShowEventSection()
        // this.$store.dispatch(
        //   BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        //     "/" +
        //     BUILDER_STORE_CONST.BUILDER_ACTIONS_COMPEVENTS_LOAD
        // );

        this.isCompEventLoading = false;
        this.showSubmit = false;

        this.resetForm();
        this.cancelForm();
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        this.isCompEventLoading = false;
      });
  }

  public multiEventOptionsChanged() {
    // because a "remove" will have changed schedule but user does not need to "save" it.
    this.$store.dispatch(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_ACTIONS_COMPEVENTS_LOAD
    );
  }

  public get getEventTimeValid() {
    return (
      this.eventTime.length > 0 &&
      this.commonService.validateTime(this.eventTime)
    );
  }

  public get getEventGroupUniqueNamesThatMatch(): string[] {
    if (this.compEvent.eventGroup.length === 0) {
      return [];
    }
    return this.builderService
      .getEventGroupUniqueNames(this.compEventsSchedule)
      .filter((base: IBase) => {
        if (base.name) {
          //  these 2 properties are not always strings, "e.g." "400" is coming up as a number: 400
          const eventGroup =
            typeof this.compEvent.eventGroup === "number"
              ? (this.compEvent.eventGroup as number).toString()
              : this.compEvent.eventGroup;
          const baseName =
            typeof base.name === "number"
              ? (base.name as number).toString()
              : base.name;
          return R.startsWith(eventGroup.toUpperCase(), baseName.toUpperCase());
        }
        return false;
      })
      .map((base: IBase) => {
        return base.name ? base.name : "";
      });
  }

  // public get getCompEventsScheduleForUnique() {
  //   //  TODO    event-teams-filter out by "ageGroups"
  //   const compEventsSchedule = this.compEventsSchedule.filter(
  //     (compEvent: ICompEvent) => {
  //       return compEvent.event.gender === this.compEvent.event.gender;
  //     }
  //   );
  //
  //   compEventsSchedule.unshift(this.compEventService.factoryGetCompEvent());
  //   return compEventsSchedule;
  // }

  public get getEventGroupsForUniqueDropDown() {
    const compEventsSchedule = this.compEventsSchedule.filter(
      (compEvent: ICompEvent) => {
        return (
          compEvent.eventGroupSummary.id !== this.compEvent.eventGroupSummary.id
        );
      }
    );

    compEventsSchedule.unshift(this.compEventService.factoryGetCompEvent());
    return compEventsSchedule;
  }

  public uniqueCompEventSelected(compEvent: ICompEvent) {
    console.log("CompEventForm.uniqueEventSelected()", compEvent);
    const eventE4sDisplay: IUniqueEventDisplay = R.clone(
      compEvent
    ) as any as IUniqueEventDisplay;
    eventE4sDisplay.ce = compEvent.id;
    if (compEvent && compEvent.id > 0) {
      this.compEvent.options.unique.push(R.clone(eventE4sDisplay));
    }
  }

  public get getUniqueEventGroupsForDisplay(): IEventGroupSelect[] {
    return this.compEvent.options.uniqueEventGroups.filter((eventGroup) => {
      return eventGroup.id !== (this.compEvent.maxGroup as any as number);
    });
  }

  public uniqueCompEventGroupSelected(
    eventGroupSelect: IEventGroupSelect
  ): void {
    if (!this.compEvent.options.uniqueEventGroups) {
      this.compEvent.options.uniqueEventGroups = [];
    }
    const uniqueEventGroups = R.clone(this.compEvent.options.uniqueEventGroups);
    uniqueEventGroups.push(eventGroupSelect);
    this.compEvent.options.uniqueEventGroups = R.clone(uniqueEventGroups);
    // this.uniqueEventGroups = uniqueEventGroups;
  }

  public removeUniqueEventGroup(id: number) {
    const uniqueEventGroups = this.compEvent.options.uniqueEventGroups.filter(
      (eventGroup) => {
        return eventGroup.id !== id;
      }
    );
    this.compEvent.options.uniqueEventGroups = R.clone(uniqueEventGroups);
  }

  public removeUnique(id: number) {
    id = Number(id);
    this.compEvent.options.unique = this.compEvent.options.unique.filter(
      (evt: IUnique) => {
        return evt.ce !== id;
      }
    );
  }

  public get getAgeGroupsForUpScale() {
    if (this.configController.isAdmin.value) {
      //  N.B. THis will show ALL age groups for admin...and would allow you
      //  to upscale the same age group...which is not allowed.  But putting here
      //  as we have some scenario where this has happened and we can fix.
      return this.ageGroupController.state.ageGroups;
    }

    let ageGroupIds: number[] = [];
    ageGroupIds = this.checkedAgeGroups.map((ageGroup) => {
      return ageGroup.id;
    });

    return this.ageGroupController.state.ageGroups.filter(
      (ageGroup: IAgeGroup) => {
        const isInCurrentAgeGroups = ageGroupIds.indexOf(ageGroup.id) > -1;
        return !isInCurrentAgeGroups;
        // return ageGroup.id !== this.compEvent.ageGroup.id;
      }
    );
  }

  public onAgeGroupsUpScaleSelected(ageGroups: IAgeGroup[]) {
    // const ageGroups: IAgeGroupBase[] = R.clone(this.compEvent.options.ageGroups);
    // ageGroups.push(R.clone(ageGroup));
    this.compEvent.options.ageGroups = R.clone(ageGroups);
  }

  public getGenderLabel(gender: GENDER) {
    return this.commonService.getGenderLabel(gender);
  }

  public onSecurityChanged(security: ISecurity) {
    this.compEvent.options.security = R.clone(security);
  }

  public onAthleteSecurityChanged(security: ISecurity) {
    this.compEvent.options.athleteSecurity = R.clone(security);
  }

  public eventGroupSelected(response: IBase | string) {
    let eventGroup: string = "";
    if (typeof response === "string") {
      eventGroup = response;
    } else {
      eventGroup = response.name ? response.name : "";
    }
    this.compEvent.eventGroup = eventGroup;
    this.validate();
  }

  public get getEventGroupNameLength() {
    return this.compEvent.eventGroup.length;
  }

  public get getEventSeedingToLabel() {
    if (this.compEvent.options.seed.qualifyToEg) {
      if (this.compEvent.options.seed.qualifyToEg.id === 0) {
        return "Feed Into Event";
      }

      return (
        "" +
        this.compEvent.options.seed.qualifyToEg.id +
        " " +
        this.compEvent.options.seed.qualifyToEg.name
      );
    }
    return "Feed Into Event";
  }

  public resetEditFromSchedule() {
    // this.$store.commit(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME + "/" +
    //     BUILDER_STORE_CONST.BUILDER_MUTATIONS_COMPEVENT_EDIT_SET, this.compEventService.factoryGetCompEvent() );
    this.$store.commit(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_MUTATIONS_BUILDERCOMPEVENT_EDIT_SET,
      this.compEventService.factoryGetCompEvent()
    );
  }

  public confirmSubmit() {
    this.showSubmit = true;
  }

  public onCancel() {
    this.showSubmit = false;
  }

  public get showOpenToAll() {
    return (
      this.builderCompetition.options.allowAdd.registered &&
      this.builderCompetition.options.allowAdd.unregistered
    );
  }

  public cancelForm() {
    this.resetForm();
    this.$emit("cancel");
  }

  public get getCurrentAgeGroups(): string {
    // if (!this.compEvent.ageGroups) {
    //   return "";
    // }
    if (this.checkedAgeGroups.length === 0) {
      return "";
    }

    // const ageGroupNames: string[] = this.compEvent.ageGroups.map((ageGroup) => {
    //   return ageGroup.name;
    // });
    const ageGroupNames: string[] = this.checkedAgeGroups.map((ageGroup) => {
      return ageGroup.name;
    });

    return this.commonService.unique(ageGroupNames).join(", ");
  }

  public get getAgeGroupsForComp(): IAgeGroup[] {
    const ageGroupsFromSchedule =
      this.builderService.getAgeGroupsFromCompEvents(this.compEventsSchedule);

    const ageGroupsToUse =
      this.ageGroupsForComp.length === 0
        ? this.ageGroupsDefault
        : this.ageGroupsForComp;

    const ageGroups = this.commonService.uniqueArrayById(
      ageGroupsFromSchedule.concat(ageGroupsToUse)
    );
    return this.ageGroupService.sortAgeGroups(ageGroups);
  }

  public get getSecondaryRefObjs(): ISecondaryRefObj[] {
    const refObjs: ISecondaryRefObj[] = [];

    refObjs.push({
      id: 0,
      objId: Number(this.compEvent.maxGroup),
      objType: "GROUP",
      compId: this.builderCompetition.id,

      objName: this.compEvent.eventGroup,
    });

    if (this.checkedAgeGroups.length === 1) {
      refObjs.push({
        id: 0,
        objId: this.compEvent.id,
        objType: "CE",
        compId: this.builderCompetition.id,

        objName: this.compEvent.event.name,
      });
    }

    return refObjs;
  }

  public get getCompDatesDisplay() {
    return {
      date: getE4sStandardHumanDateOutPut(this.builderCompetition.date),
      opens: getE4sStandardHumanDateTimeOutPut(
        this.builderCompetition.entriesOpenDateTime
      ),
      closes: getE4sStandardHumanDateTimeOutPut(
        this.builderCompetition.entriesCloseDateTime
      ),
    };
  }

  public get getGenderTypesSelected(): GenderType[] {
    //  We need all of the "events" in the Event group.  e.g. The Quad has been
    //  set up for some age groups for both genders, but the compevent being
    //  edited now is just for a subset of age groups and maybe one gender.

    return unique(
      this.compEventsSchedule
        .filter((compEvent) => {
          return (
            compEvent.eventGroupSummary.id ===
            this.compEvent.eventGroupSummary.id
          );
        })
        .map((compEvent) => {
          return compEvent.event.gender;
        })
    );
  }

  public get getIsMultiEventChild() {
    return this.compEvent.options.entriesFrom.id > 0;
  }

  public get getMultiEventOptions() {
    return this.compEvent.options.multiEventOptions
      ? this.compEvent.options.multiEventOptions
      : this.athleteCompSchedService.factoryMultiEventOptions();
  }

  public get getIsEventGroupTypeAheadEditable() {
    return !(
      this.getIsMultiEventChild &&
      this.compEvent.options.entriesFrom.isMultiEvent
    );
  }

  public entriesFromChanged(eventGroupSelect: IEventGroupSelect) {
    console.log("CompEventForm.entriesFromChanged()", eventGroupSelect);
    //  If the "from" is a multi event, e.g. quad, dec, hep, then can't change event group name
    //  so calculate a name based on the event group name.
    if (
      eventGroupSelect.id > 0 &&
      this.compEvent.options.entriesFrom.isMultiEvent
    ) {
      const eventName =
        this.compEvent.event.id > 0
          ? this.compEvent.event.name
          : this.eventGender.eventName;
      const fromNameInsert = "(use: " + eventGroupSelect.name + ")";
      this.compEvent.eventGroup = eventName + " " + fromNameInsert;
    }

    this.validate();
  }

  public includeEntriesFromChanged(eventGroupSelect: IEventGroupSelect) {
    console.log("CompEventForm.includeEntriesFromChanged()", eventGroupSelect);

    //  THis is done dynakically when laying out schedule.
    // const eventName =
    //   this.compEvent.event.id > 0
    //     ? this.compEvent.event.name
    //     : this.eventGender.eventName;
    // const fromNameInsert = "(inc: " + eventGroupSelect.name + ")";
    // this.compEvent.eventGroup = eventName + " " + fromNameInsert;
    //
    // this.validate();
  }

  public get getIsNewEvent() {
    return this.compEvent.id === 0;
  }

  /**
   * Age groups coming up  from server attached to compEvents do not have the to/from dates on them.
   */
  public get getAgeGroupCompCoverageModels(): IAgeGroupCompCoverageModel[] {
    const ageGroupIdsSelected = this.checkedAgeGroups.map((ageGroup) => {
      return ageGroup.id;
    });

    // this.ageGroupsForComp
    return (
      this.ageGroupController.state.ageGroups as IAgeGroupCompCoverageModel[]
    ).filter((ageGroup) => {
      const isAgeGroupSelected = ageGroupIdsSelected.indexOf(ageGroup.id) > -1;
      console.log(
        "ageGroup.id: " +
          ageGroup.id +
          " isAgeGroupSelected: " +
          isAgeGroupSelected
      );
      return ageGroupIdsSelected.indexOf(ageGroup.id) > -1;
    });
    // ageGroupsForComp
  }

  public ageGroupCoverageHasOverlap(hasOverlap: boolean) {
    this.compEvent.options.ageGroupOverlap = hasOverlap;
    if (hasOverlap) {
      this.isAgeGroupCoverageExpanded = true;
    }

    this.validate();
  }

  public scrollToSecurity() {
    // make sure advanced options are shown
    this.showAdvancedOptions = true;

    // and show security section
    this.sectionsExpanded.security = true;

    // set a time out of 250 and let the page render before scrolling
    setTimeout(() => {
      this.myScrollTo.scrollTo("security-section");
    }, 250);
  }

  public get isPriceValid() {
    return this.compEventService.isPriceOk(this.compEvent);
  }

  public get isDisciplineValid() {
    const isEventSelected = this.checkedEventGenders.length > 0;
    const isStartTimeOk = this.compEventService.isStartDateTimeOk(
      this.compEvent
    );
    const isEventGroupOk = this.compEvent.eventGroup.length > 0;

    return isEventSelected && isStartTimeOk && isEventGroupOk;
  }

  public setShowAdvancedOptions(show: boolean) {
    this.showAdvancedOptions = show;

    if (show) {
      setTimeout(() => {
        this.myScrollTo.scrollTo("help-text-section");
      }, 250);
    }
  }

  public get isTrackEVent() {
    if (this.eventsForGender.length > 0) {
      return this.eventsForGender[0].tf === "T";
    }

    return this.compEvent.eventGroupSummary.type === "T";
  }

  // public get getHasAgeGroupOverlap() {
  //   return this.compEvent.options.ageGroupOverlap;
  // }

  public get getCanShowAdvancedOptions() {
    return this.compEvent.event.id > 0 || this.checkedEventGenders.length > 0;
  }

  public checkIfAgeGroupsNeedLoading() {
    if (this.ageGroupsForComp.length === 0) {
      this.$store.dispatch(
        BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
          "/" +
          BUILDER_STORE_CONST.BUILDER_ACTIONS_GET_AGE_GROUPS_DEFAULT,
        {
          aocode: this.configApp.defaultao.code,
          compId: this.builderCompetition.id,
        }
      );
    }
  }

  public showContactSupport() {
    this.$emit("showContactSupport");
  }
}
</script>

<style scoped>
.comp-event-adder--grid {
  height: 65px;
}
.comp-event-adder--ad-hoc-checkbox-text {
  font-size: 14.5px;
}
</style>
