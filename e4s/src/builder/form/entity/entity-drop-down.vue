<template>
    <select class="generic-select"
            v-model="entityDropDown"
            v-on:change="onSelectedDropDown">
        <option v-for="entity in getUserEntities"
                :disabled="entity.isDisabled"
                :value="entity">
            <span v-text="getEntityDropDownLabel(entity)"></span>
        </option>
    </select>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Component from "vue-class-component";
    import Vue from "vue";
    import {mapGetters} from "vuex";
    import {CONFIG_STORE_CONST} from "../../../config/config-store";
    import {IEntity} from "../../../config/config-app-models";
    import {ConfigService} from "../../../config/config-service";
    import { Watch } from "vue-property-decorator";

    @Component({
        name: "entity-drop-down",
        computed: {
            ...mapGetters(
                {
                    userEntities: CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME + "/" + CONFIG_STORE_CONST.CONFIG_GETTERS_USER_ENTITIES
                }
            )
        }
    })
    export default class EntityDropDown extends Vue {
        public readonly userEntities: IEntity[];

        public configService: ConfigService = new ConfigService();
        public entityDropDown: IEntity = this.configService.factoryEntity();

        public created() {
            this.entityDropDown = this.configService.factoryEntity();
            this.setDefault();
        }

        @Watch("userEntities")
        public onUserEntitiesChanged(newValue: IEntity[]) {
            this.setDefault();
        }

        public get getUserEntities() {
            const def: IEntity = {
                ...this.configService.factoryEntity(),
                name: "PLEASE SELECT"
            };
            return [def, ...this.userEntities];
        }

        public setDefault() {
            if (this.userEntities.length === 1) {
                this.entityDropDown = this.userEntities[0];
                this.onSelectedDropDown();
            }
        }

        public getEntityDropDownLabel(entity: IEntity) {
            return (R.isEmpty(entity.entityName) ? "" :
                (entity.entityName === "Club" && entity.clubType === "S" ?
                    "School" :
                    entity.entityName) + " - ") + entity.name;
        }

        public onSelectedDropDown() {
            this.$emit("onSelected", R.clone(this.entityDropDown));
        }
    }

</script>
