<template>
  <div>
    <div v-if="showCreateForm">
      <div class="row">
        <div class="col s12 m12 l12">
          <self-service-form
            :self-service="selfServiceEditing"
            v-on:onCancel="onEditCancel"
            v-on:onSubmit="onEditSubmit"
          >
          </self-service-form>
        </div>
      </div>
    </div>

    <div v-if="!showCreateForm">
      <div class="e4s-vertical-spacer--large"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <ButtonGenericV2 text="Add" v-on:click="setShowCreateForm(true)" />
        </div>
      </div>

      <div class="e4s-vertical-spacer--large"></div>

      <div class="row" v-for="selfServ in selfServiceInternal">
        <div class="col s2 m2 l2">
          <!--          <a href="#" v-on:click.prevent="removeSelfServiceEntity(selfServ)"-->
          <!--            >Remove</a-->
          <!--          >-->
          <ButtonGenericV2
            text="X"
            button-type="destructive"
            style="width: 35px"
            v-on:click="removeSelfServiceEntity(selfServ)"
          />
        </div>
        <div class="col s2 m2 l2">
          <span v-text="selfServ.entityLevel"></span>
        </div>
        <div class="col s2 m2 l2">
          <span v-text="getEntityDisplayName(selfServ)"></span>
        </div>
        <div class="col s4 m4 l4">
          <span v-text="getApproversDisplay(selfServ)"></span>
        </div>
      </div>

      <!--            <div class="row">-->
      <!--                <div class="col s12 m12 l12">-->
      <!--                    selfServiceInternal{{selfServiceInternal}}-->
      <!--                </div>-->
      <!--            </div>-->
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ISelfService } from "../../builder-models";
import { BuilderService } from "../../builder-service";
import SelfServiceForm from "./self-service-form.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";

const builderService: BuilderService = new BuilderService();

@Component({
  name: "builder-entity-self-service",
  components: {
    ButtonGenericV2,
    SelfServiceForm,
  },
})
export default class BuilderEntitySelfService extends Vue {
  @Prop({
    default: () => {
      return [];
    },
  })
  public selfService: ISelfService[];

  public showCreateForm: boolean = false;
  public selfServiceInternal: ISelfService[] = [];

  public selfServiceEditing: ISelfService = builderService.factorySelfService();

  public mounted() {
    this.selfServiceInternal = R.clone(this.selfService);
  }

  @Watch("selfService")
  public onBuilderCompetitionPropChanged(newValue: ISelfService[]) {
    this.selfServiceInternal = R.clone(newValue);
  }

  public setShowCreateForm(showIt: boolean) {
    this.showCreateForm = showIt;
  }

  public onEditCancel() {
    this.showCreateForm = false;
  }

  public getApproversDisplay(selfService: ISelfService) {
    return selfService.approvalUsers
      .map((user) => {
        return user.email + " - " + user.displayName;
      })
      .join(", ");
  }

  public removeSelfServiceEntity(selfServ: ISelfService) {
    this.selfServiceInternal = this.selfServiceInternal.filter((ent) => {
      return !(
        ent.entityLevel === selfServ.entityLevel &&
        ent.clubType === selfServ.clubType
      );
    });
    this.$emit("onSubmit", R.clone(this.selfServiceInternal));
  }

  public getEntityDisplayName(selfServ: ISelfService) {
    if (selfServ.entityLevel === 1 && selfServ.clubType === "S") {
      return "School";
    }
    return selfServ.entityName;
  }

  public onEditSubmit(selfService: ISelfService) {
    if (selfService.entityLevel > 0) {
      const selfServiceFilter = this.selfServiceInternal.filter((selfServ) => {
        return !(
          selfServ.entityLevel === selfService.entityLevel &&
          selfServ.clubType === selfService.clubType
        );
      });
      selfServiceFilter.push(R.clone(selfService));
      this.selfServiceInternal = selfServiceFilter;

      this.$emit("onSubmit", R.clone(selfServiceFilter));
    }
    this.showCreateForm = false;
  }
}
</script>
