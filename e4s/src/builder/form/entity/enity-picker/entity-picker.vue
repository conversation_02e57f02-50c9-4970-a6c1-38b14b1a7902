<template>
    <div>
        <slot name="title">
            <div class="row" v-if="titleText.length > 0">
                <div class="col s12 m12 l12">
                    <span v-text="titleText"></span>
                </div>
            </div>
        </slot>

        <div class="row">
            <div class="col s12 m12 l12">
                <div>
                    <slot name="link-label"></slot>
                    <a href="#"
                       v-for="entity in getEntityLevels"
                       v-if="hasSelfServiceEntityAccess(entity)"
                       class="self-service-links"
                       :class="getPickerClass(entity)"
                       v-on:click.prevent="setShowEntityPicker(entity)">
                        <span v-text="entity.entityName"></span>
                    </a>
                    <a href="#"
                       class="self-service-links"
                       v-if="selectedPickerEntity.id > 0"
                       :class="selectedPickerEntity.id === 0 ? 'inactive' : ''"
                       v-on:click.prevent="cancelEntityPicker()">
                        Cancel
                    </a>
                </div>
            </div>

        </div>

        <div class="row" v-if="onlySelfService" v-show="getEmailMessage.length > 0">
            <div class="col s12 m12 l12">
<!--                selectedPickerEntity{{selectedPickerEntity}}-->
                <span v-text="getEmailMessage"></span>
            </div>
        </div>

        <div class="row" v-if="getShowAreaPicker">
            <div class="col s12 m12 l12">
                <AreaLookup :field-label="''" v-on:onSelected="onAreaSelected"></AreaLookup>
            </div>
        </div>
        <div class="row" v-if="getShowClubSearch">
            <div class="col s12 m12 l12">
<!--                :field-label="isSchool ? 'School' : 'Club'"-->
                <ClubSearch v-on:onSelected="onClubSelected"
                            :field-label="''"
                            :auto-all-return="false"
                            :is-school="isSchool">
                </ClubSearch>
            </div>
        </div>

    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Component from "vue-class-component";
    import Vue from "vue";
    import {IArea} from "../../../../area/area-models";
    import AreaLookup from "../../../../area/area-lookup.vue";
    import { ConfigService } from "../../../../config/config-service";
    import {ENTITY_TABLE, IEntity, IEntityLevel} from "../../../../config/config-app-models";
    import { IClubLookup } from "../../../../club/club-models";
    import ClubSearch from "../../../../club/club-search.vue";
    import {mapGetters} from "vuex";
    import {CONFIG_STORE_CONST} from "../../../../config/config-store";
    import {Prop} from "vue-property-decorator";
    import {ICompetitionInfo} from "../../../../competition/competition-models";
    import {CompetitionService} from "../../../../competition/competiton-service";
    import { BuilderService } from "../../../builder-service";
    import {ISelfService} from "../../../builder-models";
    import { IEntityPickerOutput } from "./enity-picker-models";

    const configService: ConfigService = new ConfigService();
    const competitionService: CompetitionService = new CompetitionService();
    const builderService: BuilderService = new BuilderService();

    @Component({
        name: "entity-picker",
        components: {
            AreaLookup,
            ClubSearch
        },
        computed: {
            ...mapGetters(
                {
                    isAdmin: CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME + "/" + CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN
                }
            )
        }
    })
    export default class EntityPicker extends Vue {
        @Prop({default: ""}) public readonly titleText: string;
        @Prop({default: true}) public readonly onlySelfService: boolean;
        @Prop({
            default: () => {
                return competitionService.factoryCompetitionInfo();
            }
        }) public readonly competition: ICompetitionInfo;

        public isAdmin: boolean;

        public entity: IEntity = configService.factoryEntity();

        public showWhichPicker: string = "";
        public selectedPickerEntity = configService.factoryEntity();

        public setShowEntityPicker(entity: IEntity) {
            this.selectedPickerEntity = R.clone(entity);
        }

        public get isSchool() {
            return competitionService.isSchool(this.competition);
        }

        public get getCurrentEntityMessage() {
            if (this.entity.id === 0 ) {
                return "None Selected";
            }
            return "(" +  this.entity.entityLevel + ") " + this.entity.entityName  + " - " + " (" + this.entity.id + ") " + this.entity.name;
        }

        public get getEntityLevels() {
            let entities: IEntityLevel[];
            if (this.onlySelfService) {
                entities = R.clone(this.competition.options.selfService);
            } else {
                entities = R.clone(ENTITY_TABLE);
            }
            entities.map((ent: IEntityLevel) => {
                ent.entityName = ent.entityName === "Club" && this.isSchool ? "School" : ent.entityName;
                return ent;
            });
            return entities;
        }

        public get getShowClubSearch() {
            return this.selectedPickerEntity.entityLevel === 1;
        }

        public get getShowAreaPicker() {
            return this.selectedPickerEntity.entityLevel > 1;
        }

        public onAreaSelected(area: IArea) {
            const entity = {
                ...configService.factoryEntityArea(),
                id: area.id,
                name: area.name
            };
            this.entitySelected(entity);
        }

        public onClubSelected(clubLookup: IClubLookup) {
            const entity = {
                ...configService.factoryEntityClub(this.isSchool),
                id: clubLookup.id,
                name: clubLookup.clubName
            };
            this.entitySelected(entity);
        }

        public entitySelected(entity: IEntity) {
            this.entity = R.clone(entity);
            this.showWhichPicker = "";
            const entityPickerOutput: IEntityPickerOutput = {
                entity,
                selfService: R.clone(this.selectedPickerEntity)
            };
            this.selectedPickerEntity = configService.factoryEntity();
            this.$emit("onSelected", entityPickerOutput);
        }

        public hasSelfServiceEntityAccess(entity: IEntity) {
            return (this.onlySelfService ? false : this.isAdmin) || builderService.hasSelfServiceEntity(this.competition.options, entity);
        }

        public getPickerClass(entity: IEntity) {
            return this.selectedPickerEntity.entityLevel === entity.entityLevel ? "inactive" : "";
        }

        public cancelEntityPicker() {
            this.selectedPickerEntity = configService.factoryEntity();
        }

        public get getEmailMessage() {
            const selfService: ISelfService = this.selectedPickerEntity as unknown as ISelfService;
            const emails: string = selfService.approvalUsers ? selfService.approvalUsers.map((user) => user.email).join(", ") : "";
            if (emails.length > 0) {
                return "An approval email is required to be sent to " + emails + " who will then add to your profile.";
            }
            return "";
        }

    }
</script>


<style scoped>
    .self-service-links {
        margin: 0px 5px 0px 5px;
    }
    .inactive {
        pointer-events: none;
        cursor: default;
        text-decoration: none;
        color: black;
    }
</style>
