<template>
  <div></div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";

export default defineComponent({
  name: "PricesBuilderWrapper",
  components: {},
  props: {},
  setup(props: any, context: SetupContext) {
    /*
    watch(
      () => props.athlete,
      (newValue: I<PERSON>thlete) => {
        athleteForm.init(newValue);
      },
      {
        immediate: true,
      }
    );
     */

    return {};
  },
});
</script>
