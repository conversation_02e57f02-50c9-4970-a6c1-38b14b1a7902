<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div class="e4s-flex-row e4s-gap--large">
      <div class="e4s-header--200">Schedule Time Options</div>
      <FieldHelp help-key="schedule-time-options" :get-from-server="true" />
    </div>

    <!--    {{ compEventFromToRecord }}-->

    <p
      style="
        margin-top: var(--e4s-gap--standard);
        margin-bottom: var(--e4s-gap--standard);
      "
    >
      This sets the available from/to time for ALL events on a selected and
      edited date. If specific from/to times are required for specific events on
      a given day, each event will need to be set individually in each specific
      event.
    </p>

    <div v-for="(date, index) in dates" :key="index">
      <SectionThickLine />
      <div class="e4s-flex-row e4s-gap--large e4s-flex-wrap e4s-flex-center">
        <InputCheckboxV2
          v-model="timeOptionsState[index].isEnabled"
          :value="true"
        />

        <FieldTextV2
          style="width: 120px"
          :value="getShortHumanDateOutput(timeOptionsState[index].eventDate)"
          :is-disabled="true"
        />

        {{ formatCompEventFromToRecord[date] }}
      </div>

      <div v-if="timeOptionsState[index].isEnabled" class="e4s-flex-column">
        <FormGenericFieldGridV2>
          <template slot="content">
            <FormGenericInputTemplateV2
              form-label="Entry Available From"
              :error-message="
                validationErrors[index] && validationErrors[index].availableFrom
              "
            >
              <DateTimeEntryV2
                slot="field"
                v-model="timeOptionsState[index].availableFrom"
                @onChanged="validateDates"
              />
            </FormGenericInputTemplateV2>

            <FormGenericInputTemplateV2
              form-label="Entry Available Until"
              :error-message="
                validationErrors[index] && validationErrors[index].availableTo
              "
            >
              <DateTimeEntryV2
                slot="field"
                v-model="timeOptionsState[index].availableTo"
                @onChanged="validateDates"
              />
            </FormGenericInputTemplateV2>
          </template>
        </FormGenericFieldGridV2>

        <FormGenericFieldGridV2>
          <template slot="content">
            <FormGenericInputTemplateV2
              form-label="Until Available, Display Is"
            >
              <div slot="field" class="e4s-flex-column">
                <FieldRadioV2
                  option-value="-1"
                  v-model.number="timeOptionsState[index].availableFromStatus"
                  label="Hidden"
                />
                <FieldRadioV2
                  option-value="0"
                  v-model.number="timeOptionsState[index].availableFromStatus"
                  label="Closed"
                />
              </div>
            </FormGenericInputTemplateV2>

            <FormGenericInputTemplateV2
              form-label="When Not Available, Display Is"
            >
              <div slot="field" class="e4s-flex-column">
                <FieldRadioV2
                  option-value="-1"
                  v-model.number="timeOptionsState[index].availableToStatus"
                  label="Hidden"
                />
                <FieldRadioV2
                  option-value="0"
                  v-model.number="timeOptionsState[index].availableToStatus"
                  label="Closed"
                />
              </div>
            </FormGenericInputTemplateV2>
          </template>
        </FormGenericFieldGridV2>
      </div>
    </div>

    <SectionThickLine />
    <div class="e4s-flex-row" style="margin-top: var(--e4s-gap--standard)">
      <ButtonGenericV2
        text="Cancel"
        button-type="tertiary"
        v-on:click="handleCancel"
      />
      <ButtonGenericV2
        text="Save"
        class="e4s-flex-row--end"
        button-type="primary"
        v-on:click="handleSave"
      />
    </div>

    <!--    {{ timeOptionsState }}-->
    <!--    ==========-->
    <!--    {{ validationErrors }}-->

    <LoadingSpinnerV2 v-if="isLoading" />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  SetupContext,
  watch,
  ref,
} from "@vue/composition-api";
import FormGenericFieldGridV2 from "../../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FieldRadioV2 from "../../../../common/ui/layoutV2/fields/field-radio-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import {
  getShortHumanDateOutput,
  simpleClone,
} from "../../../../common/common-service-utils";
import DateTimeEntryV2 from "../../../../common/ui/datetime/date-time-entry-v2.vue";
import FieldHelp from "../../../../common/ui/field/field-help/field-help.vue";
import FieldTextV2 from "../../../../common/ui/layoutV2/fields/field-text-v2.vue";
import { ITimeOption } from "../models/time-options-api-models";
import { timeOptionsUpdateAvailable } from "../api/time-options-api";
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue";
import { mapTimeOptionsToRequest } from "../mappers/time-options-mapper";
import InputCheckboxV2 from "../../../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import SectionThickLine from "../../../../common/ui/layoutV2/form/SectionThickLine.vue";
import { ICompEvent } from "../../../../compevent/compevent-models";
import * as CompeventscheduleServiceV2 from "../../../../compevent/compeventschedule/compeventschedule-service-v2";
import { IsoDateTime } from "../../../../common/common-models";
import {
  ICompEventFromTo,
  ICompEventFromToRecord,
} from "../../../../compevent/compeventschedule/compeventschedule-service-v2";

interface IValidationError {
  availableFrom?: string;
  availableTo?: string;
}

interface ITimeOptionScheduleState extends ITimeOption {
  isEnabled: boolean;
}

export default defineComponent({
  name: "ScheduleTimeOptions",
  methods: { getShortHumanDateOutput },
  components: {
    SectionThickLine,
    LoadingSpinnerV2,
    FieldTextV2,
    FieldRadioV2,
    FieldHelp,
    DateTimeEntryV2,
    FormGenericFieldGridV2,
    FormGenericInputTemplateV2,
    ButtonGenericV2,
    InputCheckboxV2,
  },
  props: {
    compId: {
      type: Number,
      required: true,
    },
    dates: {
      type: Array as PropType<string[]>,
      required: true,
    },
    compEvents: {
      type: Array as PropType<ICompEvent[]>,
      required: true,
    },
  },
  setup(
    props: { compId: number; dates: string[]; compEvents: ICompEvent[] },
    context: SetupContext
  ) {
    const compEventsAvailableFromAndTo = ref<ICompEventFromToRecord>(
      CompeventscheduleServiceV2.getAllCompEventsAvailableFromAndTo(
        props.compEvents
      )
    );

    // const getAllCompEventsAvailableFromAndTo = computed<ICompEventFromToRecord>(
    //   () => {
    //     return CompeventscheduleServiceV2.getAllCompEventsAvailableFromAndTo(
    //       props.compEvents
    //     );
    //   }
    // );

    function getSingleDate(
      date: string,
      toAndFrom: ICompEventFromToRecord
    ): ICompEventFromTo {
      if (!toAndFrom || !toAndFrom[date]) {
        return {
          availableFrom: "",
          availableTo: "",
        };
      }

      return toAndFrom[date].length === 1
        ? toAndFrom[date][0]
        : {
            availableFrom: "",
            availableTo: "",
          };
    }

    const timeOptionsState = reactive<ITimeOptionScheduleState[]>(
      props.dates.map((date) => ({
        eventDate: date,
        availableFrom: getSingleDate(date, compEventsAvailableFromAndTo.value)
          .availableFrom,
        availableTo: getSingleDate(date, compEventsAvailableFromAndTo.value)
          .availableTo,
        availableFromStatus: -1,
        availableToStatus: -1,
        isEnabled: false, // default to disabled
      }))
    );

    const formatCompEventFromToRecord = computed<Record<IsoDateTime, string>>(
      () => {
        return CompeventscheduleServiceV2.formatCompEventFromToRecord(
          compEventsAvailableFromAndTo.value
        );
      }
    );

    const isLoading = ref<boolean>(false);
    const validationErrors = reactive<IValidationError[]>([]);

    const isValid = computed(() => {
      return validationErrors.every(
        (error) => !error?.availableFrom && !error?.availableTo
      );
    });

    // watch compEvents
    watch(
      () => props.compEvents,
      (newCompEvents) => {
        compEventsAvailableFromAndTo.value =
          CompeventscheduleServiceV2.getAllCompEventsAvailableFromAndTo(
            props.compEvents
          );
      }
    );

    watch(
      () => props.dates,
      (newDates) => {
        // Update state when dates prop changes
        const newState = newDates.map((date) => ({
          eventDate: date,
          availableFrom: getSingleDate(date, compEventsAvailableFromAndTo.value)
            .availableFrom,
          availableTo: getSingleDate(date, compEventsAvailableFromAndTo.value)
            .availableTo,
          availableFromStatus: -1,
          availableToStatus: -1,
          isEnabled: false, // default to disabled
        }));
        Object.assign(timeOptionsState, newState);
        validateDates();
      }
    );

    // function createTimeOptions(dates: string[], compEvents: ICompEvent[]) {
    //
    //   const compEventsAvailableFromAndTo = ref<ICompEventFromToRecord>(
    //     CompeventscheduleServiceV2.getAllCompEventsAvailableFromAndTo(
    //       props.compEvents
    //     )
    //   );
    //
    //   return dates.map((date) => ({
    //     eventDate: date,
    //     availableFrom: "",
    //     availableTo: "",
    //     availableFromStatus: -1,
    //     availableToStatus: -1,
    //     isEnabled: false, // default to disabled
    //   }));
    // }

    function validateDates() {
      validationErrors.length = 0;

      timeOptionsState.forEach((timeOption, index) => {
        const errors: IValidationError = {};

        if (timeOption.availableFrom) {
          const availableFromDate = new Date(timeOption.availableFrom);
          const eventDate = new Date(timeOption.eventDate);

          if (availableFromDate >= eventDate) {
            errors.availableFrom =
              "Available From date must be before Event Date";
          }
        }

        if (timeOption.availableTo) {
          const availableToDate = new Date(timeOption.availableTo);
          const eventDate = new Date(timeOption.eventDate);
          const availableFromDate = timeOption.availableFrom
            ? new Date(timeOption.availableFrom)
            : null;

          if (availableToDate >= eventDate) {
            errors.availableTo =
              "Available Until date must be before Event Date";
          }
          if (availableFromDate && availableToDate <= availableFromDate) {
            errors.availableTo =
              "Available Until date must be after Available From date";
          }
        }
        //validationErrors[index] = errors;
        validationErrors.push(errors);
      });
    }

    function handleSave() {
      validateDates();
      if (isValid.value) {
        isLoading.value = true;

        // Only send enabled dates to server.
        const timeOptionsStateEnabled = timeOptionsState.filter(
          (timeOption) => timeOption.isEnabled
        );

        const payload = mapTimeOptionsToRequest(
          props.compId,
          timeOptionsStateEnabled
        );
        console.log("payload", payload);
        timeOptionsUpdateAvailable(payload)
          .then((resp) => {
            console.log("resp", resp);
          })
          .finally(() => {
            isLoading.value = false;
          });

        context.emit("save", simpleClone(timeOptionsState));
      }
    }

    function handleCancel() {
      context.emit("cancel");
    }

    return {
      timeOptionsState,
      validationErrors,
      isValid,
      isLoading,
      getAllCompEventsAvailableFromAndTo: compEventsAvailableFromAndTo,
      formatCompEventFromToRecord,

      validateDates,
      handleSave,
      handleCancel,
    };
  },
});
</script>
