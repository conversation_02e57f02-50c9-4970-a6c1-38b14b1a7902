import { ITimeOption } from "./time-options-api-models";
import { ICompEvent } from "../../../../compevent/compevent-models";
import { IsoDate } from "../../../../common/common-models";

export function groupCompEventsByDate(
  compEvents: ICompEvent[]
): Record<IsoDate, ITimeOption[]> {
  return compEvents.reduce<Record<IsoDate, ITimeOption[]>>((acc, compEvent) => {
    // Extract date part from startDateTime (YYYY-MM-DDTHH:mm:ss -> YYYY-MM-DD)
    const eventDate = compEvent.startDateTime.split("T")[0];

    if (!acc[eventDate]) {
      acc[eventDate] = [];
    }

    return acc;
  }, {});
}
