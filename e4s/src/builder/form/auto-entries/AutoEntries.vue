<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-form-sub-header">
          <div
            class="
              e4s-flex-row
              e4s-gap--standard
              e4s-force-img-vert-align-middle
            "
          >
            Is this a Source or Target competition
            <FieldHelp
              help-key="builder--auto-entries-section"
              :get-from-server="true"
            />
          </div>
        </div>

        <div class="e4s-section-padding-separator"></div>

        <!--        <p>Source, E.g. Qualifier competition,</p>-->
        <!--        <p>Target, E.g. Championship competition</p>-->
        <p>
          E.g. Entries from the Source (Qualifier) feed into the Target
          (Championship)
        </p>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>
    <SectionThickLine />
    <div class="e4s-vertical-spacer--large"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <span class="e4s-form-sub-header">Target Competition</span>

        <div class="e4s-section-padding-separator"></div>

        <div class="e4s-flex-column">
          <InputCheckboxV2
            class="e4s-align-self-flex-start"
            v-model="valueInternal.targetable.enabled"
            value-label="Is this a competition that can receive auto entries from other
          competitions?"
          />
        </div>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>
    <SectionThickLine />
    <div class="e4s-vertical-spacer--large"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <span class="e4s-form-sub-header">Source Competition</span>

        <div class="e4s-section-padding-separator"></div>

        <p>
          Can the entries from this competition feed another? If yes, select the
          target competition.
        </p>
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m6 l6">
        <span
          class="e4s-form-sub-header"
          :class="errors.has('Competition Name') ? 'e4s-label-invalid' : ''"
        >
          Competition Set to:
          <span v-text="getSourceCompTitle"></span>
        </span>
      </div>

      <!--      <div class="input-field col s12 m12 l12">-->
      <!--        <label class="active" for="home-info"> TimeTronics Results Url </label>-->
      <!--        <input-->
      <!--          id="home-info"-->
      <!--          name="disabledReason"-->
      <!--          v-model="valueInternal.selectedTargetComp.timeTronicsUrl"-->
      <!--          placeholder=""-->
      <!--        />-->
      <!--      </div>-->

      <div class="col s12 m12 l12">
        <div class="e4s-flex-row e4s-gap--standard">
          <ButtonGenericV2
            class="e4s-button--auto"
            v-if="!showCompPicker"
            text="Select Competition"
            @click="showCompPicker = true"
          />
          <CompetitionPicker
            v-if="showCompPicker"
            :filter="filterComps"
            v-on:input="competitionSelected"
          />
          <button-generic-v2
            v-if="showCompPicker"
            text="Reset"
            @click="resetTargetComp"
          />
        </div>
      </div>
    </div>
    <!--    <FieldHelp />-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import {
  computed,
  defineComponent,
  PropType,
  Ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { ref } from "@vue/composition-api";
import { IAutoEntries } from "../../builder-models";
import { useSimpleObjectInputController } from "../../../common/ui/form/form-controller/useSimpleObjectChangeController";
import { BuilderService } from "../../builder-service";
import FieldHelp from "../../../common/ui/field/field-help/field-help.vue";
import CompetitionPicker from "../../../competition/ui/competition-picker.vue";
import { ICompetitionSummaryPublic } from "../../../competition/competition-models";
import { simpleClone } from "../../../common/common-service-utils";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import SectionThickLine from "../../../common/ui/layoutV2/form/SectionThickLine.vue";
import InputCheckboxV2 from "../../../common/ui/layoutV2/fields/input-checkbox-v2.vue";

const builderService = new BuilderService();

export default defineComponent({
  name: "auto-entries",
  components: {
    InputCheckboxV2,
    SectionThickLine,
    ButtonGenericV2,
    CompetitionPicker,
    FieldHelp,
  },
  props: {
    value: {
      type: Object as PropType<IAutoEntries>,
      default: () => {
        return builderService.factoryAutoEntries();
      },
    },
    currentCompetition: {
      type: Object as PropType<ICompetitionSummaryPublic | null>,
    },
  },
  setup(
    props: {
      value: IAutoEntries;
      currentCompetition: ICompetitionSummaryPublic | null;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(
      verifyAutoEntry(R.clone(props.value))
    ) as Ref<IAutoEntries>;
    const PREFIX = Math.random().toString(36).substring(2);

    const simpleFormController = useSimpleObjectInputController<IAutoEntries>(
      valueInternal.value,
      context
    );

    const showCompPicker = ref(false);

    watch(
      () => props.value,
      (currentValue, oldValue) => {
        const areEqual = R.equals(currentValue, valueInternal.value);
        if (!areEqual) {
          console.log("auto-entries  PROP CHANGED!!!");
          const autoEntriesCurrent: IAutoEntries = verifyAutoEntry(
            simpleClone(currentValue)
          );
          ``;
          valueInternal.value = autoEntriesCurrent;
        }
      }
    );

    function verifyAutoEntry(autoEntries: IAutoEntries): IAutoEntries {
      if (!autoEntries.selectedTargetComp.timeTronicsUrl) {
        autoEntries.selectedTargetComp.timeTronicsUrl = "";
      }
      return autoEntries;
    }

    function competitionSelected(competition: ICompetitionSummaryPublic) {
      valueInternal.value.selectedTargetComp = {
        id: competition.compId,
        name: competition.compName,
        timeTronicsUrl: props.currentCompetition
          ? props.currentCompetition.options.autoEntries.selectedTargetComp
              .timeTronicsUrl
          : "",
      };
      showCompPicker.value = false;
    }

    function resetTargetComp() {
      valueInternal.value.selectedTargetComp = {
        id: 0,
        name: "",
        timeTronicsUrl: "",
      };
    }

    function filterComps(
      competitions: ICompetitionSummaryPublic[]
    ): ICompetitionSummaryPublic[] {
      return competitions.filter((comp) => {
        return (
          comp.options.autoEntries &&
          comp.options.autoEntries.targetable &&
          comp.options.autoEntries.targetable.enabled
        );
      });
    }

    const getSourceCompTitle = computed<string>(() => {
      if (valueInternal.value.selectedTargetComp.id > 0) {
        return (
          "[" +
          valueInternal.value.selectedTargetComp.id +
          "] " +
          valueInternal.value.selectedTargetComp.name
        );
      }
      return "";
    });

    return {
      valueInternal,
      PREFIX,
      simpleFormController,
      competitionSelected,
      getSourceCompTitle,
      showCompPicker,
      filterComps,
      resetTargetComp,
    };
  },
});
</script>
