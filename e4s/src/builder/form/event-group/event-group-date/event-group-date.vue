<template>
    <div>

        <div class="col s12 m12 l6">
            <label class="active left"
                   :for="PREFIX + 'event-group'">
                Schedule Event Name (30 chars Max)
                <FieldValidationLabel :validation-controller="validationController" :prop-path="'eventGroup'"/>
            </label>
            <event-group-type-ahead :comp-events="compEvents"
                                    :default-value="eventGroupDateModel.eventGroup"
                                    v-on:onUserInputChanged="eventGroupSelected"
                                    v-on:onSelected="eventGroupSelected">
            </event-group-type-ahead>
        </div>

        <div class="col col s12 m12 l6">
            <DateTimeEntry :iso-date-time="eventDateTime" v-on:onSelected="onDateTimeSelected">
                <label slot="label-date"
                       class="active"
                       :for="PREFIX + 'event-date'">
                    Date
                    <FieldValidationLabel :validation-controller="validationController" :prop-path="'eventDateTime'"/>
                </label>
                <label slot="label-time"
                       class="active"
                       :for="PREFIX + 'event-date'">
                    Time
                    <FieldValidationLabel :validation-controller="validationController" :prop-path="'eventDateTime'"/>
                </label>
            </DateTimeEntry>
        </div>
    </div>
</template>

<script lang="ts">
    import Vue from "vue";
    import {ValidationController} from "../../../../validation/validation-controller";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {ICompEvent} from "../../../../compevent/compevent-models";
    import {IBase, IObjectKeyType} from "../../../../common/common-models";
    import {IEventGroupDateModel} from "./event-group-date-models";
    import {IValidationProp} from "../../../../validation/validation-models";
    import {ValidationService} from "../../../../validation/validation-service";
    import EventGroupTypeAhead from "../../event-group-type-ahead.vue";
    import FieldValidationLabel from "../../../../validation/validation-field-lable.vue";
    import DateEntryMat from "../../../../common/ui/datetime/date-entry-mat.vue";
    import TimeEntry from "../../../../common/ui/datetime/time-entry.vue";
    import DateTimeEntry from "../../../../common/ui/datetime/date-time-entry.vue";

    const validationService: ValidationService = new ValidationService();


    @Component({
        name: "event-group-date",
        components: {
            DateTimeEntry,
            EventGroupTypeAhead,
            FieldValidationLabel,
            DateEntryMat,
            TimeEntry
        }
    })
    export default class EventGroupDate extends Vue {

        @Prop({
            default: () => {
                return [];
            }
        }) public readonly compEvents: ICompEvent[];
        @Prop({default: ""}) public readonly eventGroup: string;
        @Prop({default: ""}) public readonly eventDateTime: string;
        @Prop({default: true}) public readonly shouldDoValidation: boolean;

        public eventGroupDateModel: IEventGroupDateModel = {
            eventGroup: "",
            eventDateTime: ""
        };
        public validationController: ValidationController = new ValidationController();
        public PREFIX = Math.random().toString(36).substring(2);

        public created() {
            this.eventGroupDateModel.eventGroup = this.eventGroup;
            this.eventGroupDateModel.eventDateTime = this.eventDateTime;
            this.validate();
        }

        @Watch("eventGroup")
        public onEventGroupChanged(newValue: string) {
            this.eventGroupDateModel.eventGroup = newValue;
        }

        public eventGroupSelected(response: IBase | string) {
            let eventGroup: string = "";
            if (typeof response === "string") {
                eventGroup = response;
            } else {
                eventGroup = response.name ? response.name : "";
            }
            this.eventGroupDateModel.eventGroup = eventGroup;
            this.validate();
            this.$emit("eventGroupSelected", this.eventGroupDateModel.eventGroup);
        }


        public onDateTimeSelected(isoDateTime: string) {
            this.eventGroupDateModel.eventDateTime = isoDateTime;
            this.$emit("eventDateTimeSelected", this.eventGroupDateModel.eventDateTime);
            this.validate();
        }

        public validate() {

            if (!this.shouldDoValidation) {
                return;
            }

            let errors: IObjectKeyType<IValidationProp> = {};
            if (this.eventGroupDateModel.eventGroup.length === 0) {
                errors = validationService.addMessage("eventGroup", "Required", errors);
            }

            if (this.eventGroupDateModel.eventDateTime.length === 0) {
                errors = validationService.addMessage("eventDateTime", "Required", errors);
            }

            this.validationController.setErrors(errors);
        }
    }
</script>
