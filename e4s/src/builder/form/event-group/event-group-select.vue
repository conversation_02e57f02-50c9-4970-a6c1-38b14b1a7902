<template>
  <select
    v-model="eventGroupSelected"
    v-on:change="selected"
    class="browser-default e4s-input-field e4s-input-field--primary"
  >
    <option
      v-for="eventGroup in eventGroupMap"
      :key="eventGroup.id + '-' + eventGroup.eventNo"
      :value="eventGroup"
      v-text="getEventName(eventGroup)"
    ></option>
  </select>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ICompEvent } from "../../../compevent/compevent-models";
import { IEventGroupSelect } from "../../../athleteCompSched/athletecompsched-models";
import { CommonService } from "../../../common/common-service";
import { mapGetters } from "vuex";
import { CONFIG_STORE_CONST } from "../../../config/config-store";
import { EventGroupType } from "./event-group-models";
import { EventGroupIdNumber } from "../../../common/common-models";

@Component({
  name: "event-group-select",
  components: {},
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class EventGroupSelect extends Vue {
  public readonly isAdmin!: boolean;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly compEventsSchedule: ICompEvent[];

  @Prop({
    type: Number,
    default: 0,
  })
  public readonly compEventGroupId: EventGroupIdNumber;

  @Prop({
    default: 0,
  })
  public readonly value: IEventGroupSelect;

  @Prop({
    default: "NOT_FINALS",
  })
  public readonly eventGroupType: EventGroupType;

  public commonService = new CommonService();

  public created() {
    // this.eventGroupSelected = R.clone(this.value);
    this.setDefaultEventGroup();
  }

  public eventGroupSelected: IEventGroupSelect = {
    id: 0,
    name: "",
    eventNo: 0,
    isMultiEvent: false,
  };

  public eventGroupMap: IEventGroupSelect[] = [];

  @Watch("value")
  public onValueChanged(
    newValue: IEventGroupSelect,
    oldValue: IEventGroupSelect
  ) {
    // this.eventGroupSelected = R.clone(newValue);
    this.setDefaultEventGroup();
  }

  @Watch("compEventsSchedule", { immediate: true })
  public onCompEventsScheduleChanged(newValue: ICompEvent[]) {
    this.convertCompEventToEventGroupSelectMap();
  }

  public convertCompEventToEventGroupSelectMap(): void {
    const eventGroupMap: Record<string, IEventGroupSelect> =
      this.compEventsSchedule.reduce<Record<string, IEventGroupSelect>>(
        (accum, compEvent: ICompEvent) => {
          const isFinal = compEvent.maxAthletes === -1;
          if (this.eventGroupType === "NOT_FINALS" && isFinal) {
            return accum;
          }

          if (this.eventGroupType === "FINALS" && !isFinal) {
            return accum;
          }

          const key: string = compEvent.eventGroupSummary.id.toString();
          if (accum[key]) {
            return accum;
          }

          //  If value passed in, filter it out.  e.g. "Copy Events From"...itself!!!
          if (
            this.compEventGroupId > 0 &&
            this.compEventGroupId === compEvent.eventGroupSummary.id
          ) {
            return accum;
          }

          const hasChildEvents: boolean =
            compEvent.options.multiEventOptions.childEvents.length > 0;

          accum[key] = {
            id: compEvent.eventGroupSummary.id,
            name: compEvent.eventGroup,
            eventNo: compEvent.eventNo,
            isMultiEvent: hasChildEvents,
          };
          return accum;
        },
        {}
      );

    if (!eventGroupMap["0"]) {
      eventGroupMap["0"] = {
        id: 0,
        name: "",
        eventNo: 0,
        isMultiEvent: false,
      };
    }

    // { id: 0, name: "", eventNo: 0 },
    this.eventGroupMap = this.commonService.sortArray("eventNo", [
      ...Object.values(eventGroupMap),
    ]);
    this.setDefaultEventGroup();
  }

  public getEventName(eventGroupSelect: IEventGroupSelect): string {
    if (eventGroupSelect.eventNo === 0) {
      return "Select Event";
    }

    return (
      eventGroupSelect.eventNo +
      ": " +
      eventGroupSelect.name +
      (this.isAdmin ? " (" + eventGroupSelect.id + ")" : "")
    );
  }

  public setDefaultEventGroup(): void {
    this.eventGroupMap.forEach((eventGroup: IEventGroupSelect) => {
      if (eventGroup.id === this.value.id) {
        this.eventGroupSelected = R.clone(eventGroup);
      }
    });
  }

  public selected() {
    this.$emit("input", R.clone(this.eventGroupSelected));
    this.$emit("change", R.clone(this.eventGroupSelected));
  }
}
</script>
