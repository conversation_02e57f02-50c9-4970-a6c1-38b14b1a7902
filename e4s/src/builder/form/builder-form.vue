<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <CardGenericV2
          v-if="haveOrgsBeenSearched && !getHasUserAnyOrgs"
          style="margin-top: 8px"
        >
          <template #all>
            <h2>Create Organisation</h2>

            <div>
              Please enter some basic information about your organisation.
            </div>

            <BuilderOrg
              @orgSelected="orgSelected"
              show-section-default="create-new-org"
            />
          </template>
        </CardGenericV2>
      </div>
    </div>

    <div class="builder" v-if="getIsReady && getHasUserAnyOrgs">
      <!--Buttons Top-->
      <div v-if="canShowTopButtons">
        <div class="row">
          <div class="col s12 m12 l12">
            <div class="e4s-flex-row">
              <div class="e4s-flex-row e4s-gap--standard">
                <ButtonGenericV2
                  text="Clone"
                  v-on:click="getCloneConfirmation"
                />

                <ButtonGenericV2
                  text="Validate"
                  button-type="secondary"
                  v-on:click="validateThisComp"
                />

                <ButtonGotoCompOrganiserV2
                  :competition-summary-public="{
                    compOrgId: builderCompetition.compOrg.id,
                    compId: builderCompetition.id,
                  }"
                  :competition-options="builderCompetition.options"
                  button-text="Org Enter"
                  button-type="secondary"
                />

                <ButtonGenericV2
                  text="Delete"
                  button-type="destructive"
                  v-on:click="getDeleteConfirmation"
                />
              </div>

              <CompetitionGoTo
                :comp-id="builderCompetition.id"
                class="e4s-flex-row--end"
              />
            </div>

            <div class="right"></div>
          </div>
        </div>

        <div class="row">
          <div class="col s12 m12 l12">
            <div class="e4s-section-padding-separator"></div>
          </div>
        </div>
      </div>
      <!--/Buttons Top-->

      <div v-show="showSection === sections.BUILDER" id="qazwsx">
        <div
          class="row"
          id="yhntgb"
          style="margin-left: 12px; margin-right: 12px"
        >
          <div class="col s12 m12 l12">
            <div class="row">
              <SectionLinksWrapper
                style="
                  margin-top: var(--e4s-gap--standard);
                  margin-bottom: var(--e4s-gap--small);
                "
              >
                <template slot="default">
                  <SectionLinkSimple
                    class="e4s-tab-link--builder"
                    :is-active="
                      currentStepNumber === steps.STEP_GENERAL.stepNumber
                    "
                    @selected="gotoStep(1)"
                  >
                    <div
                      class="e4s-tab-link--builder-button-text"
                      v-text="steps.STEP_GENERAL.stepName"
                    ></div>
                  </SectionLinkSimple>
                  <SectionLinkSimple
                    class="e4s-tab-link--builder"
                    :is-active="
                      currentStepNumber === steps.STEP_SCHEDULE.stepNumber
                    "
                    @selected="gotoStep(4)"
                  >
                    <div
                      class="e4s-tab-link--builder-button-text"
                      v-text="steps.STEP_SCHEDULE.stepName"
                    ></div>
                  </SectionLinkSimple>
                  <SectionLinkSimple
                    class="e4s-tab-link--builder"
                    :is-active="
                      currentStepNumber === steps.STEP_RULES.stepNumber
                    "
                    @selected="gotoStep(5)"
                  >
                    <div
                      class="e4s-tab-link--builder-button-text"
                      v-text="steps.STEP_RULES.stepName"
                    ></div>
                  </SectionLinkSimple>
                </template>
              </SectionLinksWrapper>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col s12 m12 l12">
            <div v-if="builderCompetitionLoading">Loading...</div>

            <BuilderStep1
              v-show="currentStepNumber === steps.STEP_GENERAL.stepNumber"
              :step="steps.STEP_GENERAL"
              v-on:onSubmitted="completedStep1"
              v-on:showLocationForm="showLocationForm"
              @validateThisComp="validateThisComp"
              @gotoSchedule="scrollToSchedule"
              :builderCompetitionProp="builderCompetition"
            />

            <!--step 3 Schedule-->
            <div v-show="currentStepNumber === steps.STEP_SCHEDULE.stepNumber">
              <div
                v-if="!getCanShowScheduleTab"
                class="e4s-flex-column e4s-gap--standard"
              >
                <div v-if="builderCompetition.id === 0">
                  <InfoSectionV2
                    info-type="error"
                    class="e4s-vertical-spacer--large"
                  >
                    <div>
                      To complete this section, please save this competition.
                    </div>
                  </InfoSectionV2>
                </div>
                <div v-if="builderCompetition.id > 0 && prices.length === 0">
                  <InfoSectionV2
                    info-type="error"
                    class="e4s-vertical-spacer--large"
                  >
                    <div
                      class="
                        e4s-flex-row e4s-justify-flex-row-vert-center
                        e4s-gap--standard
                      "
                    >
                      Create price(s) before adding events to the schedule.
                      <PrimaryLink
                        link-text="Go to prices"
                        @onClick="gotoPrices"
                      />
                    </div>
                  </InfoSectionV2>
                </div>
              </div>

              <div class="row" v-show="getCanShowScheduleTab">
                <div class="col s12 m12 l12">
                  <CompEventSched
                    style="margin-top: 8px"
                    :is-loading="compEventLoading"
                    :comp-events-prop="compEventsSchedule"
                    v-on:onBulkEdit="onBulkCompEventEdit"
                    v-on:onDelete="onCompEventDelete"
                    v-on:onDeleteMultiple="onCompEventDeleteMultiple"
                    v-on:addEvent="showAddEventSection"
                  />
                </div>
              </div>
            </div>
            <!--/step 3 Schedule-->

            <!--step 4 Rules-->
            <div v-show="currentStepNumber === steps.STEP_RULES.stepNumber">
              <div v-if="builderCompetition.id === 0">
                <InfoSectionV2
                  info-type="error"
                  class="e4s-vertical-spacer--large"
                >
                  <div>
                    To complete this section, please save this competition.
                  </div>
                </InfoSectionV2>
              </div>

              <div v-if="builderCompetition.id > 0">
                <collapse-section
                  icon-name="call_split"
                  header-message="Rules"
                  :is-expanded="true"
                >
                  <div slot="section-content">
                    <InfoSectionV2
                      v-if="!hasCompAnyEvents"
                      info-type="error"
                      class="e4s-vertical-spacer--large"
                    >
                      <div>Create at least one event first.</div>
                    </InfoSectionV2>

                    <comp-rules
                      :age-groups="getUniqueCompAgeGroups"
                      v-if="hasCompAnyEvents"
                    />
                  </div>
                </collapse-section>

                <collapse-section
                  icon-name="attach_money"
                  header-message="Discounts"
                  :is-expanded="true"
                >
                  <div slot="section-content">
                    <discounts
                      :comp-id="builderCompetition.id"
                      :age-groups="getUniqueCompAgeGroups"
                    />
                  </div>
                </collapse-section>
              </div>
            </div>
            <!--/step 4 Rules-->
          </div>
        </div>

        <div class="row">
          <div class="input-field col s12 m12 l12">
            <div
              v-for="valid in validationResults"
              :key="valid.name"
              v-text="valid.message"
            ></div>
          </div>
        </div>
      </div>

      <div v-if="currentStepNumber === steps.STEP_ADD_EVENT.stepNumber">
        <BuilderStep2
          :step="steps.STEP_ADD_EVENT"
          :builderCompetitionProp="builderCompetition"
          v-on:cancel="cancelShowEventSection"
          @showContactSupport="showContactSupport"
        />
        <div v-if="builderCompetition.id === 0">
          A saved competition is required to see this section
        </div>
      </div>

      <location-form
        v-show="showSection === sections.LOCATION_FORM"
        :location-prop="location"
        :builder-competition="builderCompetition"
        v-on:onCancel="setShowSection(sections.BUILDER)"
        v-on:onSubmit="onLocationSubmit"
      >
      </location-form>

      <e4s-modal
        v-if="builderCompetitionLoading"
        :header-message="'Builder Loading'"
      >
        <div slot="body">Loading...<loading-spinner></loading-spinner></div>
        <div slot="footer"></div>
      </e4s-modal>

      <e4s-modal
        v-if="compEventShowConfirmDelete"
        header-message="Comp Event Delete"
        body-message="Are you sure you want to delete comp event"
        button-primary-text="Delete"
        :isLoading="compEventLoading"
        v-on:closeSecondary="compEventShowConfirmDelete = false"
        v-on:closePrimary="deleteCompEvent"
      >
      </e4s-modal>

      <e4s-modal
        v-if="compEventShowConfirmDeleteMultiple"
        :header-message="'Comp Events Delete'"
        :body-message="getDeleteCompEventMultipleMessage"
        :button-primary-text="'Delete'"
        :isLoading="compEventLoading"
        v-on:closeSecondary="compEventShowConfirmDeleteMultiple = false"
        v-on:closePrimary="deleteCompEventMultiple"
      >
      </e4s-modal>

      <e4s-modal
        v-if="showPriceConfirmDelete"
        :header-message="'Price Delete'"
        :body-message="'Are you sure you want to delete this Price'"
        :button-primary-text="'Delete'"
        :isLoading="isLoadingPrice"
        v-on:closeSecondary="onPriceDeleteCancel"
        v-on:closePrimary="priceDeleteRecord"
      >
      </e4s-modal>

      <e4s-modal
        v-if="defaultModal.show"
        :header-message="defaultModal.headerMessage"
        :body-message="defaultModal.bodyMessage"
        :button-primary-text="defaultModal.primaryButtonMessage"
        :isLoading="defaultModal.isLoading"
        v-on:closeSecondary="defaultModal.secondaryButtonFunction"
        v-on:closePrimary="defaultModal.primaryButtonFunction"
      >
      </e4s-modal>

      <e4s-modal
        v-if="getDisplayPriceCreate"
        css-class="e4s-modal-container--full-size"
      >
        <div slot="header"></div>
        <div slot="body">
          <standard-form :title="'Price'">
            <div slot="form-content">
              <PriceFormV2
                style="margin: var(--e4s-gap--standard)"
                :price="price"
                :config-options-price="configApp.options"
                :is-national="getIsNational"
                v-on:onCancel="onPriceCancel"
                v-on:onSubmit="onPriceSubmit"
              />
            </div>
          </standard-form>
        </div>
        <div slot="footer"></div>
      </e4s-modal>

      <e4s-modal
        v-if="!getDisplayAgeCreateBtn"
        css-class="e4s-modal-container--full-size"
      >
        <div slot="header"></div>
        <div slot="body">
          <standard-form :title="'Age Group'">
            <div slot="form-content">
              <age-group-form
                :comp-id="builderCompetition.id"
                class="e4s-form-wrapper"
                v-on:onCancel="setDisplayAgeCreateBtn(true)"
                v-on:onSubmit="onAgeGroupCreated"
              >
              </age-group-form>
            </div>
          </standard-form>
        </div>
        <div slot="footer"></div>
      </e4s-modal>

      <e4s-modal
        v-if="getDisplaySecondaryCreate"
        css-class="e4s-modal-container--full-size"
      >
        <div slot="header"></div>
        <div slot="body">
          <standard-form :title="'Secondary Items'">
            <div slot="form-content">
              <SecondaryForm></SecondaryForm>
            </div>
          </standard-form>
        </div>
        <div slot="footer"></div>
      </e4s-modal>

      <ModalV2
        :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
        v-if="showBuilderValidationMessages"
        :always-show-header-blank="true"
      >
        <div
          slot="body"
          style="margin: 8px; min-width: 300px; min-height: 300px"
        >
          <BuilderValidationMessages
            :builder-validation-messages="builderValidationMessages"
            @close="showBuilderValidationMessages = false"
          />
        </div>
      </ModalV2>

      <ModalV2
        :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
        v-if="cloneComp.showConfirmation"
        :always-show-header-blank="true"
      >
        <div
          slot="body"
          style="margin: 8px; min-width: 300px; min-height: 100px"
        >
          <div class="e4s-flex-column e4s-gap--standard">
            <FormGenericInputTemplateV2 form-label="Competition Date">
              <DateInputDropDownV2
                slot="field"
                :value="cloneComp.compDate"
                :default-year="cloneComp.defaultYear"
                :forceToDefaultYear="true"
                :restrict-to-years="cloneComp.restrictToYears"
                v-on:input="cloneComp.compDate = $event"
              />
            </FormGenericInputTemplateV2>

            <div class="e4s-flex-row">
              <div v-if="getIsCloneCompDateBeforeToday">
                N.B. Clone target comp date is before today!
              </div>
            </div>

            <div class="e4s-flex-row e4s-flex-end">
              <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
                <ButtonGenericV2
                  text="Cancel"
                  @click="cloneComp.showConfirmation = false"
                  button-type="secondary"
                />
                <ButtonGenericV2
                  text="Save"
                  @click="cloneCompetition"
                  :disabled="getIsCloneCompDateBeforeToday"
                />
              </div>
            </div>
          </div>
        </div>
      </ModalV2>

      <ModalV2
        :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
        v-if="showAskOrganiser"
        :always-show-header-blank="true"
      >
        <div
          slot="body"
          style="margin: 8px; min-width: 300px; min-height: 100px"
        >
          <!--For now, only using this for "adding" new events, make foaE4s/formTitle/etc. dynamic-->
          <AskOrganiserFormV2
            form-title="Ask System Admin"
            :competition-summary-public="builderCompetition"
            :section-overview="'Please enter your message below and click send. The system admin will be notified and will respond to your request.'"
            :foa-e4s="true"
            @cancel="showAskOrganiser = false"
          />
        </div>
      </ModalV2>

      <LoadingSpinnerV2 v-if="getDoesScreenNeedMasking" />
    </div>
  </div>
</template>

<script lang="ts">
import { format, parse } from "date-fns";
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Watch } from "vue-property-decorator";
import { mapState, mapGetters } from "vuex";
import { ISO_FORMAT, IValidationResult } from "../../common/common-models";
import { BUILDER_STEPS, IBuilderCompetition, IStep } from "../builder-models";
import { BuilderService } from "../builder-service";
import { IBuilderStoreState } from "../builder-store";
import { BUILDER_STORE_CONST } from "../builder-store-constants";
import BuilderStep1 from "./builder-step1.vue";
import BuilderStep2 from "./builder-step2.vue";
import LoadingSpinner from "../../common/ui/loading-spinner.vue";
import { ICompEvent } from "../../compevent/compevent-models";
import CompEventSched from "../../compevent/compeventschedule/compevent-sched.vue";
import CompRules from "./rules/comp-rules.vue";
import Discounts from "./rules/discounts.vue";
import E4sModal from "../../common/ui/e4s-modal.vue";
import { IAgeGroup } from "../../agegroup/agegroup-models";
import AgeGroupAllSelect from "../../agegroup/agegroup-builder/agegroup-all-select.vue";
import AgeGroupForm from "../../agegroup/agegroup-form.vue";
import { IBuilderCompEvent } from "../buildercompevent/builder-comp-event-models";
import AgeGroupDefaultSelectContainer from "../../agegroup/agegroup-builder/agegroup-default-select-container.vue";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import {
  BUILDER_PERM_LEVEL_NAMES,
  IConfigApp,
  IConfigPermLevel,
} from "../../config/config-app-models";
import PriceSelector from "../../price/price-selector.vue";
import PriceForm from "../../price/price-form.vue";
import { PriceService } from "../../price/price-service";
import { IPrice, IPriceSelectorUiDisplay } from "../../price/price-models";
import { ILocation } from "../../location/location-models";
import LocationForm from "../../location/location-form.vue";
import { LocationService } from "../../location/location-service";
import { BuilderData } from "../builder-data";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import StandardForm from "../../common/ui/standard-form/standard-form.vue";
import CollapseSection from "../../common/ui/collapse/collapse-section.vue";
import { PriceData } from "../../price/price-data";
import { ConfigService } from "../../config/config-service";
import SecondaryForm from "../../secondary/config/secondary-form.vue";
import PriceFormV2 from "../../price/v2/PriceFormV2.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import ButtonGotoCompOrganiserV2 from "../../common/ui/layoutV2/buttons/button-goto-comp-organiser-v2.vue";
import * as CompetitionDataV2 from "../../competition/v2/competition-data-v2";
import ModalV2 from "../../common/ui/layoutV2/modal/modal-v2.vue";
import EditPbV3 from "../../athleteCompSched/pb/v3/EditPbV3.vue";
import { VUE_MQ_SIZES } from "../../index";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";
import { useConfigController } from "../../config/useConfigStore";
import { IValidateCompResponseMessage } from "../../competition/v2/competition-data-v2";
import FieldRadioV2 from "../../common/ui/layoutV2/fields/field-radio-v2.vue";
import CompetitionGoTo from "../../competition/ui/competition-go-to.vue";
import BuilderValidationMessages from "./BuilderValidationMessages.vue";
import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import DateInputDropDownV2 from "../../common/ui/layoutV2/fields/date-input-drop-down-v2.vue";
import {
  getIsoDatePattern,
  simpleClone,
} from "../../common/common-service-utils";
import { IOrg } from "../../org/org-models";
import { OrgData } from "../../org/org-data";
import CardGenericV2 from "../../common/ui/layoutV2/card-generic-v2.vue";
import BuilderOrg from "./org/BuilderOrg.vue";
import CompeventAgeGroupCoverage from "../../compevent/compeventschedule/compevent-age-group-coverage/compevent-age-group-coverage.vue";
import InfoSectionV2 from "../../common/ui/layoutV2/info-section-v2.vue";
import PrimaryLink from "../../common/ui/layoutV2/href/PrimaryLink.vue";
import { useScrollTo } from "../../common/useScrollTo";
import SectionLink from "../../common/ui/layoutV2/tabs/section-link.vue";
import SectionLinksWrapper from "../../common/ui/layoutV2/tabs/section-links-wrapper.vue";
import SectionLinkSimple from "../../common/ui/layoutV2/tabs/section-link-simple.vue";
import { useAgeGroupController } from "../../agegroup/useAgeGroupController";
import AskOrganiserFormV2 from "../../competition/askorganiser/ask-organiser-form-v2.vue";

@Component({
  name: "builder-form",
  computed: {
    ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
      builderCompetition: (state: IBuilderStoreState) =>
        state.builderCompetition,
      builderCompetitionLoading: (state: IBuilderStoreState) =>
        state.builderCompetitionLoading,
      compEventsSchedule: (state: IBuilderStoreState) =>
        state.compEventsSchedule,
      ageGroupsForComp: (state: IBuilderStoreState) => state.ageGroupsForComp,
      prices: (state: IBuilderStoreState) => state.prices,
      pricesLoading: (state: IBuilderStoreState) => state.pricesLoading,
      compEventLoading: (state: IBuilderStoreState) => state.compEventLoading,
      compAgeGroups: (state: IBuilderStoreState) =>
        state.builderCompetition.meta.ageGroups,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapGetters({
      builderPermissions:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_BUILDER_PERMS,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
  components: {
    AskOrganiserFormV2,
    SectionLinkSimple,
    SectionLinksWrapper,
    SectionLink,
    PrimaryLink,
    InfoSectionV2,
    CompeventAgeGroupCoverage,
    BuilderOrg,
    CardGenericV2,
    DateInputDropDownV2,
    FormGenericInputTemplateV2,
    BuilderValidationMessages,
    CompetitionGoTo,
    FieldRadioV2,
    LoadingSpinnerV2,
    EditPbV3,
    ModalV2,
    ButtonGotoCompOrganiserV2,
    ButtonGenericV2,
    PriceFormV2,
    SecondaryForm,
    BuilderStep1,
    BuilderStep2,
    "loading-spinner": LoadingSpinner,
    CompEventSched,
    "comp-rules": CompRules,
    discounts: Discounts,
    "e4s-modal": E4sModal,
    "age-group-form": AgeGroupForm,
    "age-group-all-select": AgeGroupAllSelect,
    AgeGroupDefaultSelectContainer,
    "price-form": PriceForm,
    PriceSelector,
    "location-form": LocationForm,
    StandardForm,
    CollapseSection,
  },
})
export default class BuilderForm extends Vue {
  public readonly configApp: IConfigApp;
  public readonly builderCompetition: IBuilderCompetition;
  public readonly builderCompetitionLoading: boolean;
  public readonly prices: IPrice[];
  public readonly pricesLoading: boolean;
  public readonly compEventsSchedule: ICompEvent[];
  public readonly compEventLoading: boolean;
  public readonly builderPermissions: Record<
    BUILDER_PERM_LEVEL_NAMES,
    IConfigPermLevel
  >;
  public readonly isAdmin: boolean;

  public builderService: BuilderService = new BuilderService();

  public steps = R.clone(BUILDER_STEPS);
  public currentStepNumber: number = this.steps.STEP_GENERAL.stepNumber;

  // TODO set to false
  public readyToSubmit: boolean = true;
  public validationResults: IValidationResult[] = [] as IValidationResult[];

  public compEventDelete: ICompEvent = { id: 0 } as ICompEvent;
  public compEventShowConfirmDelete: boolean = false;

  public compEventShowConfirmDeleteMultiple: boolean = false;
  public compEventsDelete: ICompEvent[] = [] as ICompEvent[];

  public priceService: PriceService = new PriceService();
  public showPriceConfirmDelete: boolean = false;
  public isLoadingPrice: boolean = false;
  public displayAgeCreateBtn: boolean = true;
  public displayPriceSelector: boolean = false;
  public displayPriceCreate: boolean = false;
  public price: IPrice = this.priceService.factoryGetPrice();

  public locationService: LocationService = new LocationService();
  public location: ILocation = this.locationService.factoryGetLocation();

  public getDisplaySecondaryCreate = false;

  public showBuilderValidationMessages = false;
  public builderValidationMessages: IValidateCompResponseMessage[] = [];

  public $mq: any;
  public VUE_MQ_SIZES = VUE_MQ_SIZES;
  public doesScreenNeedMasking = false;

  public configController = useConfigController();
  public ageGroupController = useAgeGroupController();

  public showAskOrganiser = false;

  public priceSelectorUiDisplay: IPriceSelectorUiDisplay = {
    showButtons: {
      delete: true,
      edit: true,
      select: false,
    },
  };

  public cloneComp = {
    showConfirmation: false,
    defaultYear: new Date().getFullYear(),
    compDate: "",
    restrictToYears: [new Date().getFullYear(), new Date().getFullYear() + 1],
  };

  public sections = {
    BUILDER: "BUILDER",
    LOCATION_FORM: "LOCATION_FORM",
    NONE: "NONE",
  };
  public showSection: string = this.sections.BUILDER;

  public defaultModal = {
    show: false,
    headerMessage: "",
    bodyMessage: "",
    primaryButtonMessage: "",
    secondaryButtonMessage: "",
    isLoading: false,
    primaryButtonFunction: () => {
      return;
    },
    secondaryButtonFunction: () => {
      this.defaultModal.show = false;
      this.defaultModal.primaryButtonFunction = () => {
        return;
      };
    },
  };

  public myScrollTo = useScrollTo();

  public created() {
    // console.log("BuilderForm.created()....this.$route", this.$route);
    const id: number = isNaN(Number(this.$route.params.id))
      ? 0
      : parseInt(this.$route.params.id, 0);
    this.$store
      .dispatch(
        BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
          "/" +
          BUILDER_STORE_CONST.BUILDER_ACTIONS_GET_BUILDER_BY_ID,
        { id }
      )
      .then(() => {
        this.setMyOrgs();

        if (this.builderCompetition.id > 0) {
          window.setTimeout(() => {
            console.log(
              "BuilderForm.created()....this.prices.length: ",
              this.prices.length
            );
            if (this.prices.length === 0) {
              this.gotoPrices();
            }
          }, 250);
        }
      });

    this.ageGroupController.getAgeGroupsForComp(id);

    // this.getSecondaryData(id);
  }

  public mounted() {
    this.gotoStep(this.steps.STEP_GENERAL.stepNumber);
  }

  public myUserOrgs: IOrg[] = [];
  public isLoadingMyOrgs: boolean = false; //  splash screen
  public haveOrgsBeenSearched: boolean = false;

  public get getHasUserAnyOrgs() {
    if (this.builderCompetition.id > 0) {
      //  Existing comp.
      return true;
    }
    return this.myUserOrgs.length > 0;
  }

  public setMyOrgs(): Promise<void> {
    this.isLoadingMyOrgs = true;
    const orgData = new OrgData();
    return orgData
      .getMyOrgs(this.configApp.userInfo.user.id)
      .then((response) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
        }
        this.haveOrgsBeenSearched = true;
        this.myUserOrgs = Object.values(response.data);
        this.$store.commit(
          BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
            "/" +
            BUILDER_STORE_CONST.BUILDER_MUTATIONS__SET_MY_ORGS,
          response.data
        );
        if (this.myUserOrgs.length === 1) {
          this.orgSelected(this.myUserOrgs[0]);
        }
      })
      .finally(() => {
        this.isLoadingMyOrgs = false;
      });
  }

  public orgSelected(org: IOrg) {
    console.log("BuilderStep1 orgSelected", org);
    // this.builderCompetition.compOrg = org;
    if (org.id > 0) {
      console.log(
        "* * * Builder orgSelected A",
        simpleClone(this.builderCompetition.compOrg)
      );

      this.$store.commit(
        BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
          "/" +
          BUILDER_STORE_CONST.BUILDER_MUTATIONS_SET_COMP_ORG_WITH_LOCATIONS,
        org
      );

      console.log(
        "* * * Builder orgSelected B",
        simpleClone(this.builderCompetition.compOrg)
      );

      //  Makes sure it's in list
      this.myUserOrgs = [...this.myUserOrgs, org];
    }
  }

  @Watch("$route")
  public onBuilderFormRoutechanged(newValue: any) {
    console.log("BuilderForm.onBuilderFormRoutechanged", newValue);
  }

  @Watch("$route.params.id")
  public onBuilderFormRouteChangedParam(newValue: any) {
    console.log(
      "BuilderForm.onBuilderFormRoutechanged $route.params.id",
      newValue
    );
  }

  public beforeRouteUpdate(to: any, from: any, next: any) {
    //  E.g. dynamically switching from builder/23 --> builder/24, reload.
    console.log("BuilderForm.beforeRouteUpdate", { to, from });
    next();
  }

  public beforeRouteLeave(to: any, from: any, next: any) {
    console.log("BuilderForm.beforeRouteLeave", { to, from });
    next();
  }

  public gotoStep(stepNumber: number) {
    this.currentStepNumber = stepNumber;
  }

  public gotoStepName(step: IStep) {
    this.currentStepNumber = step.stepNumber;
  }

  // public getSecondaryData(id: number) {
  //     const payload: ISecondariesGetPayload = {
  //         objType: "COMP",
  //         objId: id,
  //         compId: id
  //     }
  //     this.$store.dispatch(SECONDARY_STORE_CONST.SECONDARY_CONST_MODULE_NAME + "/" +
  //         SECONDARY_STORE_CONST.SECONDARY_ACTIONS_SECONDARIES_GET, payload);
  // }

  public get getHasBuilderAccess(): boolean {
    return new ConfigService().hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.builderCompetition.compOrg.id,
      this.builderCompetition.id
    );
  }

  public validateToStep(stepNumber: number) {
    const isNow = format(parse(new Date()), ISO_FORMAT);
    let validationResults: IValidationResult[] = [];
    if (stepNumber > 1) {
      validationResults = validationResults.concat(
        this.builderService.validateBuilderStep1(
          this.builderCompetition,
          isNow,
          {
            theme: this.configApp.theme,
            stripe: {
              useStripeConnect: this.configApp.options.useStripeConnect,
            },
          },
          this.compEventsSchedule
        )
      );
    }
    if (stepNumber > 2) {
      validationResults = validationResults.concat(
        this.builderService.validateBuilderStep2(this.builderCompetition)
      );
    }
    this.validationResults = validationResults;
  }

  public onAgeGroupCreated(ageGroup: IAgeGroup) {
    this.setDisplayAgeCreateBtn(true);
    this.onAgeGroupAllSelected(ageGroup);
  }

  public onAgeGroupAllSelected(ageGroup: IAgeGroup) {
    console.log("BuilderForm.onAgeGroupAllSelected", ageGroup);
    this.$store.dispatch(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_ACTIONS_GET_AGE_GROUPS_DEFAULT,
      {
        aocode: this.configApp.defaultao.code,
        compId: this.builderCompetition.id,
      }
    );
  }

  public createAge() {
    this.displayAgeCreateBtn = false;
  }

  public get getDisplayAgeCreateBtn() {
    return this.displayAgeCreateBtn;
  }

  public setDisplayAgeCreateBtn(display: boolean) {
    this.displayAgeCreateBtn = display;
  }

  public completedStep1(payload: {
    comp: IBuilderCompetition;
    isNew: boolean;
  }) {
    console.log("BuilderForm.completedStep1", payload.comp);
    this.steps.STEP_GENERAL.isValid = true;
    // this.$store.commit(
    //   BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
    //     "/" +
    //     BUILDER_STORE_CONST.BUILDER_MUTATIONS_SET_BUILDER,
    //   builderCompetition
    // );
    this.$store.commit(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_MUTATIONS_SET_BUILDER_FROM_ID,
      payload.comp
    );

    if (
      payload.comp.meta.validation &&
      payload.comp.meta.validation.length > 0
    ) {
      this.builderValidationMessages = payload.comp.meta.validation;
      this.showBuilderValidationMessages = true;
      return;
    }

    if (payload.isNew) {
      this.goToBuilderComp(payload.comp.id);
    }
    // this.goToBuilderComp(builderCompetition.id);
  }

  public goToBuilderComp(id: number) {
    //  TODO
    //  (headbang)  Gahhahaha. WTF!  Can't get this to f-ing route!!!!!
    // this.$store.dispatch(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME + "/" +
    //     BUILDER_STORE_CONST.BUILDER_ACTIONS_GET_BUILDER_BY_ID, {id: builderCompetition.id});
    // this.$router.push({ name: "/admin/builder", params: { id: builderCompetition.id + "" } });
    // this.$router.push({ path: "/admin/builder/250" });
    // const pathBuilder = "/admin/builder/" + builderCompetition.id;
    // this.$router.push(
    //     {
    //         path: pathBuilder
    //     }
    // );
    // this.$router.push(
    //     {
    //         name: "builder",
    //         params: {
    //             id: builderCompetition.id + ""
    //         }
    //     }
    // );
    // alert("xxxxx");
    window.location.href =
      window.location.href.split("#")[0] + "#/builder/" + id;
  }

  // public onCompEventEdit(compEvent: ICompEvent) {
  //     this.$store.commit(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME + "/" +
  //         BUILDER_STORE_CONST.BUILDER_MUTATIONS_COMPEVENT_EDIT_SET, compEvent );
  //
  //     this.gotoStep(this.steps.STEP_THREE.stepNumber);
  // }

  public onBulkCompEventEdit(builderCompEvent: IBuilderCompEvent) {
    this.$store.commit(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_MUTATIONS_BUILDERCOMPEVENT_EDIT_SET,
      builderCompEvent
    );

    // this.gotoStep(this.steps.STEP_ADD_EVENT.stepNumber);
    this.showAddEventSection();
  }

  public onCompEventDelete(compEvent: ICompEvent) {
    console.log("onCompEventDelete");
    this.compEventDelete = R.clone(compEvent);
    this.compEventShowConfirmDelete = true;
  }

  public deleteCompEvent() {
    this.$store
      .dispatch(
        BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
          "/" +
          BUILDER_STORE_CONST.BUILDER_ACTIONS_COMPEVENT_DELETE,
        { compEvent: this.compEventDelete }
      )
      .then(() => {
        this.compEventShowConfirmDelete = false;
      });
  }

  public get getDeleteCompEventMultipleMessage() {
    return (
      "Are you sure you want to delete " +
      this.compEventsDelete.length +
      " events?"
    );
  }

  public onCompEventDeleteMultiple(compEvents: ICompEvent[]) {
    console.log("onCompEventDeleteMultiple");
    this.compEventsDelete = R.clone(compEvents);
    this.compEventShowConfirmDeleteMultiple = true;
  }

  public deleteCompEventMultiple() {
    this.$store
      .dispatch(
        BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
          "/" +
          BUILDER_STORE_CONST.BUILDER_ACTIONS_COMPEVENT_DELETE_MULTIPLE,
        { compEvents: this.compEventsDelete }
      )
      .then(() => {
        this.compEventShowConfirmDeleteMultiple = false;
      });
  }

  public onPriceSelectedCancel() {
    this.displayPriceSelector = false;
  }

  public setDisplayPriceCreate(display: boolean) {
    this.displayPriceCreate = display;
  }

  public get getDisplayPriceCreate() {
    return this.displayPriceCreate;
  }

  public onPriceDeletedConfirm(price: IPrice) {
    console.log("CompEventAdder.onPriceDeletedConfirm", price);
    this.showPriceConfirmDelete = true;
  }

  public onPriceDeleted(price: IPrice) {
    console.log("CompEventAdder.onPriceDeleted", price);

    this.price = R.clone(price);
    this.price.compId = this.builderCompetition.id;

    this.showPriceConfirmDelete = true;
  }

  public priceDeleteRecord() {
    this.showPriceConfirmDelete = false;
    new PriceData()
      .delete(this.price.id)
      .then((response) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        messageDispatchHelper(
          "Price " + this.price.name + " deleted.",
          USER_MESSAGE_LEVEL.INFO.toString()
        );
        this.$store.dispatch(
          BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
            "/" +
            BUILDER_STORE_CONST.BUILDER_ACTIONS_PRICES_GET_FOR_COMP,
          { compId: this.builderCompetition.id }
        );
        return;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return;
      });
  }

  public get getIsNational() {
    return this.builderCompetition.compOrg.name.toUpperCase() === "NATIONAL";
  }

  public onCreatePrice() {
    this.price = this.priceService.factoryGetPrice();
    this.price.compId = this.builderCompetition.id;
    this.setDisplayPriceCreate(true);
  }

  public onPriceCancel() {
    this.setDisplayPriceCreate(false);
  }

  public onPriceSubmit(price: IPrice) {
    this.setDisplayPriceCreate(false);
    this.$store.dispatch(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_ACTIONS_PRICES_GET_FOR_COMP,
      { compId: this.builderCompetition.id }
    );
  }

  public onPriceDeleteCancel() {
    this.showPriceConfirmDelete = false;
  }

  public onPriceEdited(price: IPrice) {
    console.log("CompEventAdder.onEditSelected", price);
    this.price = R.clone(price);
    this.price.compId = this.builderCompetition.id;

    this.displayPriceSelector = false;
    this.displayPriceCreate = true;
  }

  public onLocationSubmit(location: ILocation) {
    console.log("BuilderForm.locationSubmit", location);

    this.$store.commit(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_MUTATIONS_UPDATE_LOCATION,
      location
    );
    this.setShowSection(this.sections.BUILDER);
  }

  public onLocationCancel() {
    console.log("BuilderForm.locationCancel");
  }

  public setShowSection(section: string) {
    this.showSection = section;
  }

  public showLocationForm(payload: {
    builderCompetition: IBuilderCompetition;
    create: boolean;
  }) {
    this.$store.commit(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_MUTATIONS_SET_BUILDER,
      R.clone(payload.builderCompetition)
    );

    if (payload.create) {
      this.location = this.locationService.factoryGetLocation();
    } else {
      this.location = R.clone(this.builderCompetition.location);
    }
    this.setShowSection(this.sections.LOCATION_FORM);
  }

  public getCloneConfirmation() {
    // this.defaultModal.show = true;
    // this.defaultModal.headerMessage = "Clone Competition";
    // this.defaultModal.bodyMessage = "Do you want to clone the competition?";
    // this.defaultModal.primaryButtonMessage = "OK";
    // this.defaultModal.secondaryButtonMessage = "Cancel";
    // this.defaultModal.primaryButtonFunction = () => {
    //   return this.cloneCompetition();
    // };

    this.cloneComp.showConfirmation = true;
    this.cloneComp.compDate = "";
  }

  public getDeleteConfirmation() {
    this.defaultModal.show = true;
    this.defaultModal.headerMessage = "Delete Competition";
    this.defaultModal.bodyMessage = "Do you want to DELETE the competition?";
    this.defaultModal.primaryButtonMessage = "OK";
    this.defaultModal.secondaryButtonMessage = "Cancel";
    this.defaultModal.primaryButtonFunction = () => {
      return this.deleteCompetition();
    };
  }

  public get getUniqueCompAgeGroups(): IAgeGroup[] {
    return this.builderService.getUniqueAgeGroups(this.compEventsSchedule);
  }

  public cloneCompetition() {
    // this.defaultModal.isLoading = true;
    this.cloneComp.showConfirmation = false;

    const builderData: BuilderData = new BuilderData();
    builderData
      .cloneCompetition(
        this.configApp.defaultao.code,
        this.builderCompetition.id,
        this.cloneComp.compDate
      )
      .then((response) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
        } else {
          messageDispatchHelper(
            "Competition has been cloned, id: " + response.data.id,
            USER_MESSAGE_LEVEL.INFO.toString()
          );
          this.goToBuilderComp(response.data.id);
        }
      })
      .finally(() => {
        this.defaultModal.isLoading = false;
        this.defaultModal.show = false;
      });
  }

  public deleteCompetition() {
    this.defaultModal.isLoading = true;

    const builderData: BuilderData = new BuilderData();
    builderData
      .delete(this.builderCompetition.id)
      .then((response) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
        } else {
          messageDispatchHelper(
            "Competition has been deleted, id: " + response.data.id,
            USER_MESSAGE_LEVEL.INFO.toString()
          );
        }
      })
      .finally(() => {
        this.defaultModal.isLoading = false;
        this.defaultModal.show = false;
      });
  }

  public showAddEventSection() {
    this.showSection = this.sections.NONE;
    this.gotoStepName(this.steps.STEP_ADD_EVENT);
  }

  public cancelShowEventSection() {
    this.showSection = this.sections.BUILDER;

    this.$store.dispatch(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_ACTIONS_COMPEVENTS_LOAD
    );

    this.gotoStepName(this.steps.STEP_SCHEDULE);
  }

  public get getIsReady(): boolean {
    return !!this.configApp;
  }

  public validateThisComp() {
    this.doesScreenNeedMasking = true;
    CompetitionDataV2.validateComp(this.builderCompetition.id)
      .then((resp) => {
        console.log("validateThisComp", resp);
        if (resp.errNo === 0) {
          this.showBuilderValidationMessages = true;
          this.builderValidationMessages = resp.data;
        }
      })
      .finally(() => {
        this.doesScreenNeedMasking = false;
      });
  }

  public get getDoesScreenNeedMasking() {
    return (
      this.doesScreenNeedMasking ||
      this.compEventLoading ||
      this.isLoadingMyOrgs
    );
  }

  public get getTabV1CssClass(): string[] {
    const cssEnv = this.configController.getCssEnvIdentifier.value;
    //  For now, leave empty for irl, but could use the e4s-tabs-v1--ia.
    const cssClasses: string[] = ["e4s-tabs-v1"];
    if (cssEnv.length > 0) {
      cssClasses.push("e4s-tabs-v1--" + cssEnv);
    }

    // return cssEnv === "" ? "e4s-tabs-v1" : "";
    return cssClasses;
  }

  // public get getAgeGroupHelperText() {
  // select the age groups that will be available when adding events.
  // Select from below if others are required when adding events.
  // }

  public get getCanShowScheduleTab() {
    return this.builderCompetition.id > 0 && this.prices.length > 0;
  }

  public get getIsCloneCompDateBeforeToday() {
    if (this.cloneComp.compDate.length === 0) {
      return false;
    }
    return this.cloneComp.compDate < format(new Date(), getIsoDatePattern());
  }

  public gotoPrices() {
    this.currentStepNumber = this.steps.STEP_GENERAL.stepNumber;

    setTimeout(() => {
      this.myScrollTo.scrollTo("prices-section", { toMiddleOfScreen: true });
    }, 250);
  }

  public scrollToSchedule() {
    this.currentStepNumber = this.steps.STEP_SCHEDULE.stepNumber;

    window.scrollTo({ top: 0, behavior: "smooth" });
  }

  public get hasCompAnyEvents() {
    return this.builderCompetition.meta.compEvents.length > 0;
  }

  public get canShowTopButtons() {
    return (
      (this.isAdmin ||
        this.builderPermissions.ADMIN ||
        this.getHasBuilderAccess) &&
      this.builderCompetition.id > 0 &&
      this.showSection === this.sections.BUILDER
    );
  }

  public showContactSupport() {
    this.showAskOrganiser = true;
    // this.$router.push({ name: "contact-support" });
  }
}
</script>
