<template>
  <div class="row-rule">
    <div class="cell-rule">
      <span
        v-text="organiser.user.displayName"
        v-if="!configController.isAdmin.value"
      ></span>
      <PrimaryLink
        v-if="configController.isAdmin.value"
        :link="'#' + LAUNCH_ROUTES_PATHS.USER + '/' + organiser.user.id"
        :link-text="organiser.user.displayName"
        target=""
      />
    </div>
    <div class="cell-rule">
      <PrimaryLink
        :link="'mailto:' + organiser.user.userEmail"
        :link-text="organiser.user.userEmail"
      />
    </div>
    <div class="cell-rule">
      <div class="e4s-flex-row">
        <span v-text="organiser.role.name"></span>

        <ButtonGenericV2
          button-type="destructive"
          text="X"
          class="e4s-button--destructive-x e4s-button--slim e4s-flex-row--end"
          @click="doDelete"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { IBuilderOrgUser } from "../../builder-models";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import { LAUNCH_ROUTES_PATHS } from "../../../launch/launch-routes";
import { useConfigController } from "../../../config/useConfigStore";

export default defineComponent({
  name: "BuilderContactOrganiserRow",
  computed: {
    LAUNCH_ROUTES_PATHS() {
      return LAUNCH_ROUTES_PATHS;
    },
  },
  components: { LoadingSpinnerV2, ButtonGenericV2, PrimaryLink },
  props: {
    organiser: {
      type: Object as PropType<IBuilderOrgUser>,
      required: true,
    },
  },
  setup(props: { organiser: IBuilderOrgUser }, context: SetupContext) {
    const configController = useConfigController();

    function doDelete() {
      console.log("doDelete", props.organiser);
      context.emit("onDelete", props.organiser);
    }

    return { doDelete, configController };
  },
});
</script>

<style scoped>
/* Table row */
.row-rule {
  display: flex; /* Makes each row a flex container */
  border-bottom: 1px solid #ccc;
}

.row-rule:last-child {
  border-bottom: none; /* Remove bottom border for the last row */
}

/* Table column */
.cell-rule {
  flex: 1; /* Equal width columns */
  padding: var(--e4s-gap--tiny) var(--e4s-gap--standard);
  border-right: 1px solid #ccc;
  text-align: start;
}

.cell-rule:first-child {
  flex: 0 0 250px; /* Fixed width of 100px */
}

.cell-rule:last-child {
  flex: 0 0 250px; /* Fixed width of 100px */
  border-right: none; /* Remove right border for the last cell */
  text-align: start;
}
</style>
