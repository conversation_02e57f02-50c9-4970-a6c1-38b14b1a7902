<template>
  <div class="e4s-flex-column">
    <div class="e4s-flex-row">
      <div class="e4s-subheader--general" for="contact-org-admins">
        Organiser Admins for: <span v-text="org.name"></span>
      </div>

      <slot name="button-right"> </slot>
    </div>

    <div class="e4s-vertical-spacer--large"></div>

    <div class="table-rule">
      <!-- Header row -->
      <div class="row-rule header-rule">
        <div class="cell-rule">Name</div>
        <div class="cell-rule">Email</div>
        <div class="cell-rule">Level</div>
      </div>
      <!-- Data rows -->
      <BuilderContactOrganiserRow
        v-for="organiser in organisers"
        :key="organiser.permId"
        :organiser="organiser"
        @onDelete="askConfirmDelete"
      />
    </div>

    <!--    <div-->
    <!--      class="e4s-flex-column"-->
    <!--      v-for="organiser in organisers"-->
    <!--      :key="organiser.permId"-->
    <!--      id="contact-org-admins"-->
    <!--    >-->
    <!--      <div class="e4s-flex-row e4s-full-width e4s-justify-flex-space-between">-->
    <!--        <div v-text="organiser.user.displayName"></div>-->
    <!--        <div>-->
    <!--          <PrimaryLink-->
    <!--            :link="'mailto:' + organiser.user.userEmail"-->
    <!--            :link-text="organiser.user.userEmail"-->
    <!--          />-->
    <!--        </div>-->
    <!--        <div v-text="organiser.role.name"></div>-->
    <!--      </div>-->
    <!--    </div>-->
    <LoadingSpinnerV2 v-if="isLoading" />

    <ModalV2
      :always-show-header-blank="true"
      :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
      v-if="showUserConfirm"
    >
      <div
        slot="body"
        class="e4s-flex-column e4s-gap--large"
        style="margin: var(--e4s-gap--standard)"
      >
        <div class="e4s-flex-row e4s-header--400">
          Delete {{ builderOrgUserToDelete.user.displayName }}?
        </div>
        <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
          <ButtonGenericV2
            button-type="tertiary"
            text="Cancel"
            @click="showUserConfirm = false"
            :disabled="isLoading"
          />
          <ButtonGenericV2
            button-type="destructive"
            text="Confirm"
            @click="doDelete"
            :disabled="isLoading"
          />
        </div>
      </div>
    </ModalV2>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
} from "@vue/composition-api";
import { IBuilderOrgUser } from "../../builder-models";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import { OrgService } from "../../../org/org-service";
import { IOrg } from "../../../org/org-models";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import { PermissionData } from "../../../admin/permissions/permission-data";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import BuilderContactOrganiserRow from "./builder-contact-organiser-row.vue";
import ModalV2 from "../../../common/ui/layoutV2/modal/modal-v2.vue";
import { simpleClone } from "../../../common/common-service-utils";
import { VUE_MQ_SIZES } from "../../../index";

export default defineComponent({
  name: "BuilderContactOrganisers",
  computed: {
    VUE_MQ_SIZES() {
      return VUE_MQ_SIZES;
    },
  },
  components: {
    ModalV2,
    BuilderContactOrganiserRow,
    LoadingSpinnerV2,
    ButtonGenericV2,
    PrimaryLink,
  },
  props: {
    org: {
      type: Object as PropType<IOrg>,
      default: () => {
        const org: IOrg = new OrgService().factory();
        return org;
      },
    },
    organisers: {
      type: Array as PropType<IBuilderOrgUser[]>,
      default: () => [],
    },
  },
  setup(
    props: { org: IOrg; organisers: IBuilderOrgUser[] },
    context: SetupContext
  ) {
    const showUserConfirm = ref(false);
    const isLoading = ref(false);

    const builderOrgUserToDelete = ref<IBuilderOrgUser>({
      compId: 0,
      orgId: 0,
      permId: 0,
      role: {
        id: 0,
        name: "",
      },
      user: {
        id: 0,
        displayName: "",
        userEmail: "",
      },
    });

    /*
    watch(
      () => props.athlete,
      (newValue: IAthlete) => {
        athleteForm.init(newValue);
      },
      {
        immediate: true,
      }
    );
     */

    function askConfirmDelete(organiser: IBuilderOrgUser) {
      console.log("askConfirmDelete", organiser);
      builderOrgUserToDelete.value = simpleClone(organiser);
      showUserConfirm.value = true;
    }

    function doDelete() {
      const organiser = builderOrgUserToDelete.value;
      console.log("doDelete", organiser);

      const permissionData = new PermissionData();
      isLoading.value = true;
      permissionData
        .deleteUserPermission(organiser.permId)
        .then((response) => {
          if (response.errNo > 0) {
            messageDispatchHelper(response.error);
          } else {
            context.emit("onDelete", organiser);
          }
          showUserConfirm.value = false;
        })
        .finally(() => {
          isLoading.value = false;
        });
    }

    return {
      askConfirmDelete,
      showUserConfirm,
      isLoading,
      doDelete,
      builderOrgUserToDelete,
    };
  },
});
</script>

<style scoped>
.table-rule {
  display: flex;
  flex-direction: column; /* Each row stacks vertically */
  border: 1px solid #ccc;
  border-collapse: collapse;
}

/* Table row */
.row-rule {
  display: flex; /* Makes each row a flex container */
  border-bottom: 1px solid #ccc;
}

.row-rule:last-child {
  border-bottom: none; /* Remove bottom border for the last row */
}

/* Table column */
.cell-rule {
  flex: 1; /* Equal width columns */
  padding: var(--e4s-gap--tiny) var(--e4s-gap--standard);
  border-right: 1px solid #ccc;
  text-align: start;
}

.cell-rule:first-child {
  flex: 0 0 250px; /* Fixed width of 100px */
}

.cell-rule:last-child {
  flex: 0 0 250px; /* Fixed width of 100px */
  border-right: none; /* Remove right border for the last cell */
  text-align: start;
}

/* Header row styling */
.header-rule {
  background-color: #f0f0f0;
  font-weight: bold;
}
</style>
