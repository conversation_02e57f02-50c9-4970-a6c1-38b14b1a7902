<template>
  <CommonTypeAhead
    slot="default"
    :default-object="valueInternal"
    :data-function="eventSearchTermChanged"
    :get-object-description="getLabelOnceSelected"
    :show-cancel-button="false"
    :is-modal="isModal"
    :is-disabled="isDisabled"
    v-on:selected="onChanged"
    v-on:reset="reset"
  >
    <div slot-scope="{ result }">
      <div v-text="getOptionDisplayValue(result)"></div>
    </div>
  </CommonTypeAhead>
</template>

<script lang="ts">
import { defineComponent, ref, SetupContext } from "@vue/composition-api";
import CommonTypeAhead from "../../../../common/ui/type-ahead/common-type-ahead.vue";
import { IEventE4S } from "../../../../event/event-models";
import { IEventGender, IEventGenderV2 } from "../event-selector-builder-models";
import { IServerResponseList } from "../../../../common/common-models";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import { IListParams } from "../../../../common/resource/resource-service";
import { EventData } from "../../../../event/event-data";
import { EventSelectorBuilderService } from "../event-selector-builder-service";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import {
  convertObjectToArray,
  simpleClone,
} from "../../../../common/common-service-utils";

export default defineComponent({
  name: "EventSelectorV2",
  components: { CommonTypeAhead },
  props: {
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    isModal: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  setup(
    props: { isDisabled: boolean; isModal: boolean },
    context: SetupContext
  ) {
    const valueInternal = ref<IEventGenderV2>({
      id: 0,
      eventName: "",
      eventNameDisplay: "",
      events: {},
    });

    // const events = ref<IEventE4S[]>([]);
    // const eventGenders = ref<IEventGenderV2[]>([]);
    // const serverResponseList = reactive<IServerResponseList<IEventGenderV2[]>>({
    //   errNo: 0,
    //   error: "",
    //   data: [],
    // });

    // const isLoading = ref<boolean>(false);
    const eventData: EventData = new EventData();
    const eventSelectorBuilderService: EventSelectorBuilderService =
      new EventSelectorBuilderService();

    const eventGenderDefaultValue: IEventGenderV2 = {
      id: 0,
      eventName: "",
      eventNameDisplay: "",
      events: {},
    };

    // const debounceSearch = debounce((searchTerm: string, loadingX: any) => {
    //   return eventSearchTermChanged(searchTerm);
    // }, 250);

    function eventSearchTermChanged(
      searchKey: string
    ): Promise<IServerResponseList<IEventGenderV2>> {
      const serverResponseList: IServerResponseList<IEventGenderV2> = {
        errNo: 0,
        error: "",
        data: [],
      };

      if (searchKey.length === 0) {
        context.emit("onSelected", {
          eventName: "",
          eventNameDisplay: "",
          events: {},
        } as IEventGenderV2);
        return Promise.resolve(serverResponseList);
      }
      const listParams = {
        startswith: searchKey,
        pagenumber: 1,
        pagesize: 100,
        sortkey: "name",
      } as IListParams;

      const prom = eventData.list(listParams);
      handleResponseMessages(prom);
      return prom.then((response) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return serverResponseList;
        }

        const eventGenders = mergeGenderEvents(response.data);
        serverResponseList.data = eventGenders;
        return serverResponseList;
      });
    }

    function mergeGenderEvents(events: IEventE4S[]): IEventGenderV2[] {
      const eventGenders: IEventGender[] =
        eventSelectorBuilderService.getEventsForDropDown(events);
      return eventGenders.map((eventGender, index) => {
        const eventGenderV2: IEventGenderV2 = {
          ...eventGender,
          id: index,
        };
        return eventGenderV2;
      });
    }

    function onChanged(eventGender: IEventGenderV2) {
      valueInternal.value = simpleClone(eventGender);
      context.emit("input", eventGender);
    }

    function reset() {
      valueInternal.value = simpleClone(eventGenderDefaultValue);
      context.emit("input", simpleClone(eventGenderDefaultValue));
    }

    function getOptionDisplayValue(eventGender: IEventGenderV2) {
      const eventsForAllGenders = convertObjectToArray(eventGender.events);
      const genderList = eventsForAllGenders.map((event) => event.gender);
      return eventGender.eventName + " (" + genderList.join(", ") + ")";
    }

    function getLabelOnceSelected(eventGender: IEventGenderV2) {
      return eventGender.eventName;
    }
    /*
    watch(
      () => props.athlete,
      (newValue: IAthlete) => {
        athleteForm.init(newValue);
      },
      {
        immediate: true,
      }
    );
     */

    return {
      valueInternal,
      eventSearchTermChanged,
      getOptionDisplayValue,
      getLabelOnceSelected,
      onChanged,
      reset,
    };
  },
});
</script>
