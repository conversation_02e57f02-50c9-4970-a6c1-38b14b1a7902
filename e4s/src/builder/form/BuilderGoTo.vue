<template>
  <!--  <PrimaryLink :link="getPath" :link-text="linkText" />-->
  <PrimaryHref :link="getPath" :link-text="linkText" />
</template>

<script lang="ts">
import { LAUNCH_ROUTES_PATHS } from "../../launch/launch-routes";
import { computed, defineComponent } from "@vue/composition-api";
import PrimaryHref from "../../common/ui/layoutV2/href/PrimaryHref.vue";

export default defineComponent({
  name: "BuilderGoTo",
  components: { PrimaryHref },
  props: {
    compId: {
      type: Number,
      default: 0,
    },
    linkText: {
      type: String,
      default: "Go To Builder",
    },
  },
  setup(props) {
    const getPath = computed(() => {
      return "#/" + LAUNCH_ROUTES_PATHS.BUILDER + "/" + props.compId;
    });

    return {
      getPath,
    };
  },
});
</script>
