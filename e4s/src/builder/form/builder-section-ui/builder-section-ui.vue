<template>
  <div class="row">
    <div class="col s12 m12 l12">
      <CollapseSection
        icon-name="launch"
        header-message="Entry Ui"
        :is-expanded="false"
        @isSectionBodyVisible="setIsSectionBodyVisible"
      >
        <div slot="section-content" v-if="isSectionBodyVisible">
          <FormGenericFieldGridV2>
            <template slot="content">
              <FormGenericInputTextV2
                form-label="Entry Button Text"
                v-model="builderSectionUiInternal.enterButtonText"
                v-on:change="onChangeUi"
              />

              <FormGenericInputTemplateV2 form-label="Default entry panel">
                <FieldHelp
                  slot="after-label"
                  title="Default entry panel"
                  message="Select the default panel a user will see when they enter the comp.  Default is 'Schedule'."
                ></FieldHelp>
                <template slot="field">
                  <select
                    class="browser-default"
                    v-model="builderSectionUiInternal.entryDefaultPanel"
                    id="options-ui-entryDefaultPanel"
                    v-on:change="onChangeUi"
                  >
                    <option
                      v-for="(
                        key, entryDefaultPanelType
                      ) in entryDefaultPanelOptions"
                      :value="entryDefaultPanelType"
                      v-text="key"
                      :key="entryDefaultPanelType"
                    ></option>
                  </select>
                </template>
              </FormGenericInputTemplateV2>
            </template>
          </FormGenericFieldGridV2>

          <FormGenericFieldGridV2>
            <template slot="content">
              <FormGenericInputTemplateV2 form-label="Ticket Competition">
                <template slot="field">
                  <CompetitionDropDownV2
                    :value="builderSectionUiInternal.ticketCompBase"
                    :comp-filter-params="getTicketCompSearchParams"
                    v-on:input="ticketCompChanged"
                  />
                </template>
              </FormGenericInputTemplateV2>

              <FormGenericInputTextV2
                form-label="Tickets Button Text"
                v-model="builderSectionUiInternal.ticketCompButtonText"
                :is-disabled="builderSectionUiInternal.ticketCompBase.id === 0"
                v-on:change="onChangeUi"
              />
            </template>
          </FormGenericFieldGridV2>
        </div>
      </CollapseSection>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import CollapseSection from "../../../common/ui/collapse/collapse-section.vue";
import {
  BuilderUiSectionsToHide,
  EntryDefaultPanel,
  IBuilderSectionUi,
} from "../../builder-models";
import { BuilderService } from "../../builder-service";
import FieldHelp from "../../../common/ui/field/field-help/field-help.vue";
import {
  convertDateToIsoWithOffset,
  isEqual,
  simpleClone,
} from "../../../common/common-service-utils";
import CompetitionDropDownV2 from "../../../competition/v2/CompetitionDropDownV2.vue";
import { IBaseConcrete } from "../../../common/common-models";
import FormGenericFieldGridV2 from "../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputTextV2 from "../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import { ICompFilterParams } from "../../../competition/v2/competition-data-v2";

@Component({
  name: "builder-section-ui",
  components: {
    FormGenericInputTemplateV2,
    FormGenericInputTextV2,
    FormGenericFieldGridV2,
    CompetitionDropDownV2,
    FieldHelp,
    CollapseSection,
  },
})
export default class BuilderSectionUi extends Vue {
  @Prop({ required: true })
  public readonly orgId: number;

  @Prop({ required: true })
  public readonly value: IBuilderSectionUi;

  public builderService: BuilderService = new BuilderService();
  public builderSectionUiInternal: IBuilderSectionUi =
    this.builderService.factoryBuilderSectionUi();

  public entryDefaultPanelOptions: Record<EntryDefaultPanel, string> = {
    SCHEDULE: "Schedule",
    SHOP: "Shop",
    SHOP_ONLY: "Shop Only",
  };

  public isSectionBodyVisible: boolean = false;

  public created() {
    const builderSectionUiInternal = simpleClone(this.value);
    if (!builderSectionUiInternal.ticketCompBase) {
      builderSectionUiInternal.ticketCompBase = {
        id: 0,
        name: "",
      };
    }
    this.builderSectionUiInternal = builderSectionUiInternal;
  }

  @Watch("value")
  public onBuilderCompetitionChanged(
    newValue: IBuilderSectionUi,
    oldValue: IBuilderSectionUi
  ) {
    // if (!isEqual(newValue, this.value.sectionsToHide)) {
    //
    // }
    const builderSectionUiInternal = simpleClone(newValue);
    //  Handle legacy
    if (!builderSectionUiInternal.sectionsToHide) {
      builderSectionUiInternal.sectionsToHide = {
        ATHLETES: false,
        TEAMS: false,
        SCHEDULE: false,
        SHOP: false,
      };
    }
    this.builderSectionUiInternal = builderSectionUiInternal;
  }

  @Watch("builderSectionUiInternal.sectionsToHide", { deep: true })
  public onSsectionsToHideChanged(
    newValue: BuilderUiSectionsToHide,
    oldValue: BuilderUiSectionsToHide
  ) {
    if (!isEqual(newValue, this.value.sectionsToHide)) {
      this.onChangeUi();
    }
  }

  public onChangeUi() {
    this.$emit("input", simpleClone(this.builderSectionUiInternal));
    this.$emit("onChange", simpleClone(this.builderSectionUiInternal));
  }

  public get getTicketCompSearchParams(): Partial<ICompFilterParams> {
    return {
      fromDate: convertDateToIsoWithOffset(new Date()),
      organiser: {
        id: this.orgId,
        name: "",
      },
      compTypes: ["TICKET"],
    };
  }

  public ticketCompChanged(comp: IBaseConcrete) {
    this.builderSectionUiInternal.ticketComp = comp.id;
    this.builderSectionUiInternal.ticketCompBase = {
      id: comp.id,
      name: comp.name,
    };
    this.onChangeUi();
  }

  public setIsSectionBodyVisible(isVisible: boolean) {
    this.isSectionBodyVisible = isVisible;
  }
}
</script>
