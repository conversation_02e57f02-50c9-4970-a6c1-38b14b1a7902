<template>
    <div>
        <div class="row">
            <div class="col s1 m1 l1">
                #
            </div>
            <div class="col s2 m2 l2">
                Created
            </div>
            <div class="col s2 m2 l2">
                User
            </div>
            <div class="col s2 m2 l2">
                Reason
            </div>
        </div>
        <div v-for="audit in audits">
            <Audit :audit="audit"></Audit>
        </div>
    </div>
</template>

<script lang="ts">

    import Audit from "./audit.vue";
    import Vue from "vue";
    import { Prop } from "vue-property-decorator";
    import Component from "vue-class-component";
    import {IAudit} from "../../builder-models";

    @Component({
        name: "audit-container",
        components: {
            Audit
        }
    })
    export default class BulkUpdateForm extends Vue {
        @Prop() public readonly audits: IAudit[];
    }


</script>
