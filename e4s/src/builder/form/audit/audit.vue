<template functional>
    <div class="row">
        <div class="col s1 m1 l1">
            <span v-text="props.audit.id"/>
        </div>
        <div class="col s2 m2 l2">
            <span v-text="$options.methods.getCreatedTime(props.audit.created)"/>
        </div>
        <div class="col s2 m2 l2">
            <span v-text="props.audit.userName"/>
        </div>
        <div class="col s2 m2 l2">
            <span v-text="props.audit.reason"/>
        </div>
    </div>
</template>

<script lang="ts">
    import { parse, format } from "date-fns";

    export default {
        props: ["audit"],
        methods: {
            getCreatedTime: (created: string): string => {
                return format(parse(created), "DD/MM/YYYY HH:mm:ss");
            }
        }
    };

</script>
