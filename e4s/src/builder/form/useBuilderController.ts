import { IBuilderCompetition } from "../builder-models";
import { BuilderData } from "../builder-data";
import { IServerResponse, IsoDate } from "../../common/common-models";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import { reactive } from "@vue/composition-api";
import { useStore } from "../../app.store";
import { BUILDER_STORE_CONST } from "../builder-store-constants";
import { simpleClone } from "../../common/common-service-utils";
import { AO_CODE } from "../../common/ui/athletic-org/athletic-org-models";
import { BuilderService } from "../builder-service";
import * as CompetitionDataV2 from "../../competition/v2/competition-data-v2";
import { IValidateCompResponseMessage } from "../../competition/v2/competition-data-v2";

export interface IBuilderControllerState {
  isLoading: boolean;
  builderCompetition: IBuilderCompetition;
  validation: {
    builderValidationMessages: IValidateCompResponseMessage[];
    showBuilderValidationMessages: boolean;
  };
  ui: {
    org: {
      showNewOrg: boolean;
    };
    showFlyerUpload: boolean;
  };
}

export function useBuilderController(
  builderCompetition: IBuilderCompetition | undefined
) {
  const builderService: BuilderService = new BuilderService();

  const state = reactive<IBuilderControllerState>({
    isLoading: false,
    builderCompetition: simpleClone(
      builderCompetition
        ? builderCompetition
        : builderService.factoryGetBuilder({})
    ),
    validation: {
      builderValidationMessages: [],
      showBuilderValidationMessages: false,
    },
    ui: {
      org: {
        showNewOrg: false,
      },
      showFlyerUpload: false,
    },
  });

  const store = useStore();

  function setBuilderCompetition(builderCompetition: IBuilderCompetition) {
    state.builderCompetition = simpleClone(builderCompetition);
  }

  function validateThisComp(): Promise<void> {
    state.isLoading = true;
    return CompetitionDataV2.validateComp(state.builderCompetition.id)
      .then((resp) => {
        console.log("validateThisComp", resp);
        if (resp.errNo === 0) {
          state.validation.builderValidationMessages = resp.data;
          if (resp.data.length > 0) {
            state.validation.showBuilderValidationMessages = true;
          }
        }
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  function canActiveChangedContinue(
    currentActiveState: boolean
  ): Promise<boolean> {
    if (currentActiveState) {
      //  Switching from active to inactive, no need to validate
      state.builderCompetition.active = false;
      return Promise.resolve(true);
    }
    return validateThisComp().then(() => {
      if (state.validation.builderValidationMessages.length > 0) {
        state.builderCompetition.active = false;
        return false;
      }
      state.builderCompetition.active = true;
      return true;
    });
  }

  function submit() {
    state.isLoading = true;

    const builderData: BuilderData = new BuilderData();
    const prom =
      state.builderCompetition.id === 0
        ? builderData.create(state.builderCompetition)
        : builderData.update(state.builderCompetition);

    prom
      .then((response: IServerResponse<IBuilderCompetition>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        } else {
          messageDispatchHelper(
            "Competition Saved.",
            USER_MESSAGE_LEVEL.INFO.toString()
          );

          store.commit(
            BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
              "/" +
              BUILDER_STORE_CONST.BUILDER_MUTATIONS_SET_BUILDER,
            builderCompetition
          );
        }
        return;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return;
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  function cloneCompetition(
    compIdToClone: number,
    aoCode: AO_CODE,
    date: IsoDate
  ) {
    state.isLoading = true;

    const builderData: BuilderData = new BuilderData();
    builderData
      .cloneCompetition(aoCode, compIdToClone, date)
      .then((response) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
        } else {
          messageDispatchHelper(
            "Competition has been cloned, id: " + response.data.id,
            USER_MESSAGE_LEVEL.INFO.toString()
          );
          goToBuilderComp(response.data.id);
        }
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  function goToBuilderComp(id: number) {
    //  TODO
    //  (headbang)  Gahhahaha. WTF!  Can't get this to f-ing route!!!!!
    // this.$store.dispatch(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME + "/" +
    //     BUILDER_STORE_CONST.BUILDER_ACTIONS_GET_BUILDER_BY_ID, {id: builderCompetition.id});
    // this.$router.push({ name: "/admin/builder", params: { id: builderCompetition.id + "" } });
    // this.$router.push({ path: "/admin/builder/250" });
    // const pathBuilder = "/admin/builder/" + builderCompetition.id;
    // this.$router.push(
    //     {
    //         path: pathBuilder
    //     }
    // );
    // this.$router.push(
    //     {
    //         name: "builder",
    //         params: {
    //             id: builderCompetition.id + ""
    //         }
    //     }
    // );
    window.location.href =
      window.location.href.split("#")[0] + "#/builder/" + id;
  }

  return {
    state,
    setBuilderCompetition,
    submit,
    cloneCompetition,
    validateThisComp,
    canActiveChangedContinue,
  };
}
