import {IBuilderCompetition, IBuilderSubscription} from "../../builder-models";
import { IValidationProp } from "../../../validation/validation-models";
import {isAfter, isBefore, parse} from "date-fns";
import { ValidationService } from "../../../validation/validation-service";

export function validateSubscription(
  builderSubscription: IBuilderSubscription,
  builderCompetition: IBuilderCompetition
): Record<string, IValidationProp> {
  let validationState: Record<string, IValidationProp> = {};
  const validationService: ValidationService = new ValidationService();

  const compDate = parse(builderCompetition.date);

  if (builderSubscription.enabled) {
    if (builderSubscription.processRefundTime.length === 0) {
      validationState = validationService.addMessage(
        "processRefundTime",
        "Required.",
        validationState
      );
    } else {
      const processRefundTime = parse(builderSubscription.processRefundTime)
      if (isBefore(processRefundTime, compDate)) {
        validationState = validationService.addMessage(
          "processRefundTime",
          "Must be same same as comp date or after.",
          validationState
        );
      }
    }

    if (builderSubscription.timeCloses.length > 0) {
      const entriesOpenDateTime = parse(builderCompetition.entriesOpenDateTime);
      const entriesCloseDateTime = parse(builderCompetition.entriesCloseDateTime);
      const timeCloses = parse(builderSubscription.timeCloses);
      if (isBefore(timeCloses, entriesOpenDateTime)) {
        validationState = validationService.addMessage(
          "timeCloses",
          "Before entries open.",
          validationState
        );
      }
      if (isAfter(timeCloses, entriesCloseDateTime)) {
        validationState = validationService.addMessage(
          "timeCloses",
          "After entries close.",
          validationState
        );
      }
    }

  }

  return validationState;
}
