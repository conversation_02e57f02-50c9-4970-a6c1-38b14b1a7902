import { ResourceData } from "../common/resource/resource-service";
import { IBaseRaw, IsoIshDateTime } from "../common/common-models";
import { CLUB_TYPE } from "../config/config-app-models";

export interface IClubDataV2Rep extends IBaseRaw {
  userLogin: string;
  userEmail: string;
}

export interface IClubDataApiV2 extends IBaseRaw {
  externalid: string;
  shortcode: null;
  areaid: number;
  clubtype: CLUB_TYPE;
  active: 1 | 0;
  options: null;
  modified: IsoIshDateTime;
  clubname: string;
  region: string;
  country: string;
  areaname: string;
  reps: IClubDataV2Rep[];
}

export class ClubDataV2 extends ResourceData<IClubDataApiV2> {
  constructor() {
    super("/v5/clubCrud");
  }
}

export function factoryIClubDataApiV2(): IClubDataApiV2 {
  return {
    id: 0,
    externalid: "",
    shortcode: null,
    clubname: "",
    clubtype: "C",
    areaid: 0,
    areaname: "",
    region: "",
    country: "",
    active: 0,
    modified: "",
    reps: [],
    options: null,
  };
}
