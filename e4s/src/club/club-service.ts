import {IClub, IClubCrud, IClubLookup} from "./club-models";

export class ClubService {

    public factoryClubCrud(): IClubCrud {
        return {
            id: 0,
            clubname: "",
            clubtype: "C",
            areaid: 0,
            region: "",
            country: "",
            active: true,
            areaname: ""
        }
    }

    public factoryIClub(): IClub {
        return {
            id: 0,
            clubid: 0,
            club: ""
        };
    }

    public factoryIClubLookup(): IClubLookup {
        return {
            id: 0,
            areaId: 0,
            clubName: "",
            country: "",
            county: "",
            region:  "",
        };
    }

    public convertClubToClubLookup(club: IClub): IClubLookup {
        return {
            ...this.factoryIClubLookup(),
            id: club.id,
            clubName: club.club,
            areaId: 0,
            county: "",
            country: "",
            region: ""
        };
    }
}
