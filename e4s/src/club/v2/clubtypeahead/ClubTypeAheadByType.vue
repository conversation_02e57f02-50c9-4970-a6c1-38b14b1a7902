<template>
  <CommonTypeAhead
    slot="default"
    :default-object="valueInternal"
    :data-function="search"
    :get-object-description="getLabel"
    :show-cancel-button="false"
    :is-modal="isModal"
    :is-disabled="isDisabled"
    v-on:selected="onChanged"
    v-on:reset="reset"
  >
    <div slot-scope="{ result }">
      <div v-text="getOptionDisplayValue(result)"></div>
    </div>
  </CommonTypeAhead>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  reactive,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { IClubCrud } from "../../club-models";
import { ClubDataCrud, ClubType } from "../../crud/club-data-crud";
import { useConfigController } from "../../../config/useConfigStore";
import CommonTypeAhead from "../../../common/ui/type-ahead/common-type-ahead.vue";

export default defineComponent({
  name: "ClubTypeAheadByType",
  components: { CommonTypeAhead },
  inheritAttrs: false,
  props: {
    value: {
      type: Object as PropType<IClubCrud>,
      default: () => {
        return {
          id: 0,
          clubname: "",
          clubtype: "C",
          areaid: 0,
          areaname: "",
          region: "",
          country: "",
          active: true,
        };
      },
    },
    clubType: {
      type: String as PropType<ClubType>,
      default: "C",
    },
    option: {
      type: String,
      default: "",
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    isModal: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  setup(
    props: {
      value: IClubCrud;
      clubType: ClubType;
      option: string;
      isDisabled: boolean;
      isModal: boolean;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(props.value);
    const clubDataCrud: ClubDataCrud = new ClubDataCrud();
    const configController = useConfigController();

    const state = reactive({
      searchClubResults: [] as IClubCrud[],
    });

    watch(
      () => props.value,
      (newValue: IClubCrud) => {
        if (newValue !== valueInternal.value) {
          console.log("field-drop-down-v2 value changed", {
            new: newValue,
            internal: valueInternal.value,
          });
          valueInternal.value = newValue;
        }
      },
      {
        immediate: true,
      }
    );

    function search(searchTerm: string) {
      return clubDataCrud.searchClubsByType(
        0,
        searchTerm,
        props.clubType,
        props.option,
        20
      );
    }

    function getLabel(clubCrud: IClubCrud) {
      return clubCrud.clubname;
    }

    function onChanged(selectedValue: IClubCrud) {
      valueInternal.value = selectedValue;
      context.emit("input", valueInternal.value);
      context.emit("onChanged", valueInternal.value);
    }

    function getOptionDisplayValue(clubCrud: IClubCrud) {
      return (
        clubCrud.clubname +
        (configController.isAdmin.value ? " (" + clubCrud.id + ")" : "") +
        ", Type: " +
        clubCrud.clubtype
      );
    }

    function reset() {
      const clubDefault: IClubCrud = {
        id: 0,
        clubname: "",
        clubtype: "C",
        areaid: 0,
        areaname: "",
        region: "",
        country: "",
        active: true,
      };

      if (props.isModal) {
        //  If modal, the expectation is it just changes data in modal, not underlying
        //  component...
        valueInternal.value = clubDefault;
        return;
      }

      //  But if club type ahead is on form, reset the model.
      context.emit("input", clubDefault);
      context.emit("onChanged", clubDefault);
    }

    return {
      state,
      valueInternal,
      onChanged,
      search,
      configController,
      getLabel,
      reset,
      getOptionDisplayValue,
    };
  },
});
</script>

<style></style>
