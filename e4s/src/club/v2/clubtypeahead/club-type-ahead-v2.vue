<template>
  <CommonTypeAhead
    slot="default"
    :default-object="valueInternal"
    :data-function="search"
    :get-object-description="getLabel"
    :show-cancel-button="false"
    :is-modal="isModal"
    :is-disabled="isDisabled"
    :useClickOutsideReset="useClickOutsideReset"
    :reset-input-on-selected="resetInputOnSelected"
    @searchTermChanged="searchTermChanged"
    @selected="onChanged"
    @reset="reset"
  >
    <div slot-scope="{ result }">
      <div v-text="getOptionDisplayValue(result)"></div>
    </div>
  </CommonTypeAhead>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  reactive,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { IClubCrud } from "../../club-models";
import { ClubDataCrud } from "../../crud/club-data-crud";
import { useConfigController } from "../../../config/useConfigStore";
import CommonTypeAhead from "../../../common/ui/type-ahead/common-type-ahead.vue";

export default defineComponent({
  name: "club-type-ahead-v2",
  components: { CommonTypeAhead },
  inheritAttrs: false,
  props: {
    value: {
      type: Object as PropType<IClubCrud>,
      default: () => {
        return {
          id: 0,
          clubname: "",
          clubtype: "C",
          areaid: 0,
          areaname: "",
          region: "",
          country: "",
          active: true,
        };
      },
    },
    isSchool: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    isInternational: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    isModal: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    useClickOutsideReset: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    resetInputOnSelected: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  setup(
    props: {
      value: IClubCrud;
      isSchool: boolean;
      isInternational: boolean;
      isDisabled: boolean;
      isModal: boolean;
      useClickOutsideReset: boolean;
      resetInputOnSelected: boolean;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(props.value);
    const clubDataCrud: ClubDataCrud = new ClubDataCrud();
    const configController = useConfigController();

    const state = reactive({
      searchClubResults: [] as IClubCrud[],
    });

    watch(
      () => props.value,
      (newValue: IClubCrud) => {
        if (newValue !== valueInternal.value) {
          console.log("field-drop-down-v2 value changed", {
            new: newValue,
            internal: valueInternal.value,
          });
          valueInternal.value = newValue;
        }
      },
      {
        immediate: true,
      }
    );

    // const debounceSearch = debounce((searchTerm: string, loading: any) => {
    //   searchForClub(searchTerm, loading);
    // }, 250);

    function search(searchTerm: string) {
      return clubDataCrud.searchClubs(
        0,
        searchTerm,
        props.isSchool,
        20,
        props.isInternational
      );
    }

    // function searchForClub(searchTerm: string, loading: any) {
    //   state.searchClubResults = [];
    //   if (searchTerm.length === 0) {
    //     return;
    //   }
    //   if (loading) {
    //     loading(true);
    //   }
    //   console.log(
    //     "searchForClub props.isInternational: ",
    //     props.isInternational
    //   );
    //   const prom = clubDataCrud.searchClubs(
    //     0,
    //     searchTerm,
    //     props.isSchool,
    //     20,
    //     props.isInternational
    //   );
    //   handleResponseMessages(prom);
    //   prom
    //     .then((resp) => {
    //       if (resp.errNo === 0) {
    //         state.searchClubResults = resp.data;
    //       }
    //     })
    //     .finally(() => {
    //       // this.isLoading = false;
    //       // if (loading){loading(false);}
    //     });
    // }

    function getLabel(clubCrud: IClubCrud) {
      return clubCrud.clubname;
    }

    function onChanged(selectedValue: IClubCrud) {
      valueInternal.value = selectedValue;
      context.emit("input", valueInternal.value);
    }

    function getOptionDisplayValue(clubCrud: IClubCrud) {
      return (
        clubCrud.clubname +
        (configController.isAdmin.value
          ? " (" + clubCrud.id + ", Type: " + clubCrud.clubtype + ")"
          : "")
      );
    }

    function reset() {
      const clubDefault: IClubCrud = {
        id: 0,
        clubname: "",
        clubtype: "C",
        areaid: 0,
        areaname: "",
        region: "",
        country: "",
        active: true,
      };

      if (props.isModal) {
        //  If modal, the expectation is it just changes data in modal, not underlying
        //  component...
        valueInternal.value = clubDefault;
        return;
      }

      //  But if club type ahead is on form, reset the model.
      context.emit("input", clubDefault);
    }

    function searchTermChanged(searchTerm: string) {
      console.log("searchTermChanged", searchTerm);
      context.emit("searchTermChanged", searchTerm);
    }

    return {
      state,
      valueInternal,
      onChanged,
      search,
      configController,
      getLabel,
      reset,
      getOptionDisplayValue,
      searchTermChanged,
    };
  },
});
</script>

<style></style>
