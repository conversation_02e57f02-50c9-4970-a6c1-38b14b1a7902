import {ISecondaryAttributeValue, ISecondaryDef, ISecondaryProduct, ISecondaryProductBase, ISecondaryRefObj, ISecondaryVariant} from "../secondary-models"
import {IBaseConcrete} from "../../common/common-models"

export interface ISecondaryPurchaseParentEmit {
    secondaryDef: ISecondaryDef
    prod: ISecondaryProduct | ISecondaryVariant;
    qtyRequired: number;
    attributeValues: ISecondaryAttributeValue[];        // [{name: "size", value: "L"}, {name: "colour", value: "Red"}]
}

export interface ISecondaryPurchaseVariantEmit {
    variant: ISecondaryVariant;
    qtyRequired: number;
    attributeValues: ISecondaryAttributeValue[];        // [{name: "size", value: "L"}, {name: "colour", value: "Red"}]
}

export interface ISecondaryCustDef {
    id: number;
    prod: ISecondaryProduct;
    maxAllowed: number;                                 //  How many can they buy 0 = no max.
    refObj: ISecondaryRefObj;
    perAcc: "A" | "U" | "";                             //   If "A" implied that athlete has entered.
}

export interface ISecondaryPurchaseSubmit {
    user: IBaseConcrete;
    athlete: IBaseConcrete;
    prod: ISecondaryProduct | ISecondaryVariant;
    qtyRequired: number;
    attributeValues: ISecondaryAttributeValue[];
}

export interface ISecondaryPurchaseRead extends ISecondaryProductBase {
    user: IBaseConcrete;
    orderId: number;
    prodId: number;
    variantId: number;
    attributeValues: ISecondaryAttributeValue[];
    qtyPurchased: number;
    orderLine?: {
        _athlete?: string;
    }
}



