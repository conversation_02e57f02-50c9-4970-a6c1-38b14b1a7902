<template>

    <div class="row">
        <div class="col s12 m12 l12">

            <div class="col s2 m2 l2">
                <AttachmentThumbnail :image-link="secondaryVariant.image"></AttachmentThumbnail>
            </div>

            <div class="col s10 m10 l10">

                <div class="row">

                    <div class="col s12 m12 l12">
                        <i v-if="secondaryDef.perAcc === 'A'" class="material-icons normal">directions_walk</i>
                        <span v-text="getDescription"></span>
<!--                        <span v-text="getVariationDescription"></span>-->

                        <div class="right e4s-bold">
                            <span v-text="getPrice"></span>
                        </div>
                    </div>

                    <div class="col s12 m12 l12">

                        <div class="e4s-force-inline-block" v-if="getHasAttributesThatNeedUserSelection">
                            <SecondaryCustAttrPicker :attributes="attributesThatNeedUserSelection"
                                                     v-on:onSelect="onAttributeValueSelection"
                            ></SecondaryCustAttrPicker>
                        </div>

<!--                        <span v-text="getPrice"></span>-->

                        <div class="right" v-if="!hasMaxAllowedBeenReached">

                            <select class="e4s-select e4s-select-number"
                                    :id="PREFIX + 'qty-required'"
                                    v-model.number="qtyRequired">
                                <option v-for="qty in getQtyOptions" :key="qty" :value="qty">{{qty}}</option>
                            </select>

                            <slot name="select-button">
                                <button class="btn waves-effect waves green"
                                        :disabled="isLoading"
                                        v-on:click.stop="onSelectedVariant">
                                    <span>Add</span>
                                </button>
                            </slot>

                            <div>
                                <ValidationFieldLable :validation-controller="validationController" prop-path="qtyRequired"/>
                                <ValidationFieldLable :validation-controller="validationController" prop-path="attributes"/>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {ISecondaryDef, ISecondaryVariant, ISecondaryAttributes} from "../secondary-models"
import {SecondaryVariationService} from "../config/variation/secondary-variation-service"
import * as R from "ramda"
import {ValidationService} from "../../validation/validation-service"
import {IValidationProp} from "../../validation/validation-models"
import {ValidationController} from "../../validation/validation-controller"
import {ISecondaryPurchaseVariantEmit} from "./secondary-cust-models"
import ValidationFieldLable from "../../validation/validation-field-lable.vue"
import SecondaryCustAttrPicker from "./secondary-cust-attr-picker.vue"
import {CommonService} from "../../common/common-service"
import { SecondaryService } from "../secondary-service";
import { SecondaryCustService } from "./secondary-cust-service";
import AttachmentThumbnail from "../../common/ui/attachments/attachment-thumbnail.vue"
import {IConfigApp} from "../../config/config-app-models"
import {mapState} from "vuex"
import {CONFIG_STORE_CONST, IConfigStoreState} from "../../config/config-store"

@Component({
    name: "secondary-cust-variant-row",
    components: {AttachmentThumbnail, SecondaryCustAttrPicker, ValidationFieldLable},
    computed: {
        ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
            configApp: (state: IConfigStoreState) => state.configApp
        })
    }
})
export default class SecondaryCustVariantRow extends Vue {
    public configApp: IConfigApp;

    @Prop({
        required: true
    })
    public readonly secondaryVariant: ISecondaryVariant ;

    @Prop({
        required: true
    })
    public readonly secondaryDef: ISecondaryDef;

    @Prop({
        default: false
    })
    public readonly isLoading: boolean;

    @Prop({
        default: 0
    })
    public readonly maxAllowed: number;

    @Prop({
        default: 0
    })
    public readonly alreadyPurchased: number;

    @Prop({
        default: false
    })
    public readonly hasMaxAllowedBeenReached: boolean;

    public secondaryService: SecondaryService = new SecondaryService();
    public secondaryCustService: SecondaryCustService = new SecondaryCustService();

    public secondaryVariantInternal: ISecondaryVariant = this.secondaryService.factorySecondaryVariant();
    public PREFIX = Math.random().toString(36).substring(2);
    public secondaryVariationService: SecondaryVariationService = new SecondaryVariationService();
    public validationController: ValidationController = new ValidationController();
    public qtyRequired = 0;
    public qtyRequiredOptions: string[] = [];
    public commonService: CommonService = new CommonService();

    public attributesThatNeedUserSelection: ISecondaryAttributes[] = []

    public created() {

        this.secondaryVariantInternal = R.clone(this.secondaryVariant);
        this.attributesThatNeedUserSelection = this.getAttributesThatNeedUserSelection();
        this.setQtyOptions();
    }

    @Watch("maxAllowed")
    public onMaxAllowedChanged(newValue: number, oldValue: number) {

    }

    public setQtyOptions() {
        const qtyRequiredOptions: string[] = [];

        const maxQty = this.maxAllowed > 0 ? this.maxAllowed : 100;

        for ( let i = 0; i <= maxQty; i++ ) {
            qtyRequiredOptions.push( i + "" );
        }
        this.qtyRequiredOptions = qtyRequiredOptions;
    }

    public get getQtyOptions(): string[] {
        const qtyRequiredOptions: string[] = [];

        const maxQty = this.maxAllowed > 0 ? this.maxAllowed : 100;

        for ( let i = 0; i <= maxQty; i++ ) {
            qtyRequiredOptions.push( i + "" );
        }
        return qtyRequiredOptions;
    }

    public get getDescription() {
        // return this.secondaryDef.refObj.objName + " - " +
        //     this.secondaryDef.prod.name + " (" + this.secondaryDef.prod.id + "/" + this.secondaryVariant.id  + ")";
        // return this.secondaryDef.refObj.objName + " - " + this.secondaryDef.prod.name;
        return this.secondaryVariantInternal.name && this.secondaryVariantInternal.name.length > 0 ?
            this.secondaryVariantInternal.name :
            this.secondaryDef.refObj.objName + " - " + this.secondaryDef.prod.name;
    }

    public get getVariationDescription() {
        return this.secondaryVariationService.getRowDescription(this.secondaryVariantInternal);
    }

    public get getQty() {
        return this.secondaryVariantInternal.stockQty;
    }

    public get getPrice() {
        return this.configApp.currency + this.secondaryVariantInternal.price.price;
    }

    public getAttributesThatNeedUserSelection(): ISecondaryAttributes[] {

        const attributeValuesKey = this.commonService.convertArrayToObject("name", this.secondaryVariantInternal.attributeValues);

        const attrToIgnore = this.secondaryService.getAttributeNameToIgnore();

        return this.secondaryDef.prod.attributes.reduce( (accum, attr)=> {
            const attrValueKey = attributeValuesKey[attr.name];

            if ((attr.name !== attrToIgnore) && attrValueKey && (attrValueKey.value.length === 0) ) {
                accum.push(attr);
            }
            return accum;
        }, [] as ISecondaryAttributes[]);
    }

    public get getHasAttributesThatNeedUserSelection(): boolean {
        return this.attributesThatNeedUserSelection.length > 0;
    }

    public validate() {
        this.validationController.reset();

        const validationService: ValidationService = new ValidationService();
        let validationState: Record<string, IValidationProp>  = {};

        validationState = this.secondaryCustService.validateVariation(this.secondaryVariantInternal, this.secondaryDef);

        if (this.qtyRequired <= 0) {
            validationState = validationService.addMessage("qtyRequired", "Value more than zero.", validationState);
        }

        this.validationController.setErrors(validationState);
    }

    public onAttributeValueSelection(secondaryAttributeValuesSelected: Record<string, string> ) {

        const currentValues = R.clone(this.secondaryVariantInternal.attributeValues);
        const newValues = currentValues.map( (attrValue) => {
            // const currentValue = secondaryAttributeValuesSelected[attrValue.name];
            if (secondaryAttributeValuesSelected.hasOwnProperty(attrValue.name)) {
                const attrVal = secondaryAttributeValuesSelected[attrValue.name];
                attrValue.value = attrVal;
                // attrValue.value = secondaryAttributeValuesSelected[attrValue.name];
            }
            return attrValue;
        });
        this.secondaryVariantInternal.attributeValues = newValues;
    }

    public onSelectedVariant() {
        const secondaryPurchaseVariant: ISecondaryPurchaseVariantEmit = {
            variant: this.secondaryVariantInternal,
            qtyRequired: this.qtyRequired,
            attributeValues: this.secondaryVariantInternal.attributeValues
        };

        this.validate();
        if (!this.validationController.isValid) {
            return;
        }
        this.$emit("onSelectedVariant", R.clone(secondaryPurchaseVariant));
    }
}
</script>
