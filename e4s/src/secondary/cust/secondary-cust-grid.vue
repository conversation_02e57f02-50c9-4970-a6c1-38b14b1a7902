<template>
  <div>
    <SchedInfo :sched-info="getSchedInfo">
      <div slot="extra">
        <button
          v-if="getAreAnyAthleteItems"
          :disabled="getIsLoading"
          class="btn waves-effect waves green"
          v-on:click="selectAthlete"
        >
          <!--                    <i class="material-icons normal">format_list_numbered</i>-->
          Select Athlete
        </button>
        <LoadingSpinner v-if="getIsLoading"></LoadingSpinner>
        <!--                v-if="selectedCompetition.options.stadium && selectedCompetition.options.stadium.length > 0"-->
        <!--                <button-->
        <!--                    class="btn waves-effect waves green"-->
        <!--                    v-on:click="showStadium = true"-->
        <!--                >-->
        <!--                    &lt;!&ndash;                    <i class="material-icons normal">format_list_numbered</i>&ndash;&gt;-->
        <!--                    Show Seating Diagram-->
        <!--                </button>-->

        <!--                <div-->
        <!--                    v-if="selectedCompetition.options.stadium && selectedCompetition.options.stadium.length > 0"-->
        <!--                    style="padding: 1rem 0;font-weight: 500"-->
        <!--                >-->
        <!--                    <a :href="getStadiumUrl" target="_seating">View Stadium Seating</a>-->
        <!--                </div>-->

        <div v-text="getHelpText"></div>

        <button
          v-if="
            selectedCompetition.options &&
            selectedCompetition.options.stadium &&
            selectedCompetition.options.stadium.length > 0
          "
          class="btn waves-effect waves green"
          v-on:click="openStadiumSeating"
        >
          View Seating Layout
        </button>

        <!--                isReloading: {{isReloading}} isLoadingInternal: {{isLoadingInternal}}-->
      </div>
    </SchedInfo>

    <div class="e4s-section--padding"></div>

    <div
      class="secondary-cust-grid--card"
      :class="index % 2 === 0 ? '' : 'e4s-card-std__row-odd'"
      v-for="(secondary, index) in getSecondaryCustDefsInternal"
      :key="secondary.id"
    >
      <SecondaryCustDefRow
        :secondary-def="secondary"
        :show-ref-obj-name="showRefObjName"
        :secondary-purchase-read="
          getSecondaryPurchaseReadKey[secondary.prod.id]
        "
        :secondary-purchase-read-user="
          getSecondaryPurchaseReadUserKey[secondary.prod.id]
        "
        :secondary-purchase-read-athlete="
          getSecondaryPurchaseReadAthleteKey[secondary.prod.id]
        "
        :secondary-cart-for-this-item="getSecondaryCartKey[secondary.prod.id]"
        :has-athlete-entered-any-events="hasAthleteEnteredAnyEvents"
        :athlete-entered-events="getAthleteEnteredEvents"
        :is-loading="getIsLoading"
        v-on:onSelectedParent="onSelectedParent"
        v-on:onSelectedVariant="onSelectedParent"
      >
      </SecondaryCustDefRow>
    </div>

    <E4sModalSimple
      v-if="showSelectAthleteDialog"
      css-class="e4s-modal-container--full-size"
    >
      <StandardForm title="Select Athlete" slot="body">
        <div slot="form-content" style="padding: 1rem">
          <SecondaryAthleteSelect
            :comp-id="selectedCompetition.id"
            v-on:linked="athleteLinked"
            v-on:cancel="showSelectAthleteDialog = false"
          ></SecondaryAthleteSelect>
        </div>
      </StandardForm>
    </E4sModalSimple>

    <E4sModalSimple
      v-if="showStadium"
      css-class="e4s-modal-container--full-size"
    >
      <StandardForm title="Stadium" slot="body">
        <div slot="form-content" style="padding: 1rem">
          <StadiumSeating
            :url="getStadiumUrl"
            v-on:cancel="showStadium = false"
          ></StadiumSeating>
        </div>
      </StandardForm>
    </E4sModalSimple>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  ISecondaryCustDef,
  ISecondaryPurchaseSubmit,
  ISecondaryPurchaseParentEmit,
  ISecondaryPurchaseRead,
} from "./secondary-cust-models";
import { SecondaryCustService } from "./secondary-cust-service";
import SecondaryCustDefRow from "./secondary-cust-def-row.vue";
import { mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { IConfigApp } from "../../config/config-app-models";
import { SecondaryCustData } from "./secondary-cust-data";
import { IAthlete } from "../../athlete/athlete-models";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import {
  ATH_COMP_SCHED_STORE_CONST,
  IAthCompSchedStoreState,
} from "../../athletecompsched/store/athleteCompSched-store";
import {
  IAthleteCompSched,
  IAthleteCompSchedRuleEvent,
  ISchedInfo,
} from "../../athletecompsched/athletecompsched-models";
import { AthleteCompSchedService } from "../../athletecompsched/athletecompsched-service";
import { CommonService } from "../../common/common-service";
import { ENTRY_STORE_CONST, IEntryStoreState } from "../../entry/entry-store";
import {
  ICartWooCommerce,
  ISecondaryAttributeValue,
  ISecondaryVariant,
  IWooCommerceLineItem,
} from "../secondary-models";
import { IServerResponse } from "../../common/common-models";
import { SecondaryService } from "../secondary-service";
import SchedInfo from "../../athletecompsched/sched-info/sched-info.vue";
import { SchedInfoService } from "../../athletecompsched/sched-info/sched-info-service";
import E4sModalSimple from "../../common/ui/modal/e4s-modal-simple.vue";
import StandardForm from "../../common/ui/standard-form/standard-form.vue";
import {
  ICompetitionInfo,
  ICompetitionSummaryPublic,
} from "../../competition/competition-models";
import { ICompetitionAthleteSummary } from "../../competition/athletes/competition-athlete-models";
import { AthleteData } from "../../athlete/athlete-data";
import StadiumSeating from "../stadium/stadium-seating.vue";
import { STADIUM_URL_MANCHESTER } from "../../common/config";
// import {CONFIG} from "../../common/config";
// import SecondaryAthleteSelect from "../athlete-select/secondary-athlete-select.vue";

const secondaryCustData: SecondaryCustData = new SecondaryCustData();
const secondaryCustService: SecondaryCustService = new SecondaryCustService();

@Component({
  name: "secondary-cust-grid",
  components: {
    StadiumSeating,
    SecondaryAthleteSelect: () => {
      return import(
        /* webpackPrefetch: true */
        /* webpackChunkName: "secondary-cust-grid--secondary-athlete-select" */
        "../athlete-select/secondary-athlete-select.vue"
      );
    },
    StandardForm,
    E4sModalSimple,
    SchedInfo,
    SecondaryCustDefRow,
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapState(ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME, {
      eventsSelected: (state: IAthCompSchedStoreState) => state.eventsSelected,
      eventsAvailableToAthlete: (state: IAthCompSchedStoreState) =>
        state.eventsProcessed,
      cart: (state: IAthCompSchedStoreState) => state.cart,
      cartIsLoading: (state: IAthCompSchedStoreState) => state.cartLoading,
    }),
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      selectedCompetition: (state: IEntryStoreState) =>
        state.entryForm.selectedCompetition,
      competitionSummaryPublic: (state: IEntryStoreState) =>
        state.entryForm.competitionSummaryPublic,
      selectedAthlete: (state: IEntryStoreState) =>
        state.entryForm.selectedAthlete,
      searchedAthletes: (state: IEntryStoreState) =>
        state.entryForm.searchedAthletes,
    }),
  },
})
export default class SecondaryCustGrid extends Vue {
  public readonly configApp: IConfigApp;
  public readonly selectedAthlete: IAthlete;
  public readonly searchedAthletes!: IAthlete[];
  public readonly eventsSelected: IAthleteCompSchedRuleEvent[];
  public readonly eventsAvailableToAthlete: IAthleteCompSchedRuleEvent[];
  public readonly cart: IServerResponse<
    IAthleteCompSched[],
    { wcCart: ICartWooCommerce }
  >;
  public readonly cartIsLoading: boolean;
  public readonly selectedCompetition!: ICompetitionInfo;
  public readonly competitionSummaryPublic!: ICompetitionSummaryPublic;

  @Prop({
    required: true,
    default: () => {
      return [];
    },
  })
  public readonly secondaryCustDefs: ISecondaryCustDef[];

  @Prop({
    default: true,
  })
  public readonly showRefObjName: boolean;

  @Prop({
    required: true,
    default: () => {
      return [];
    },
  })
  public readonly userSecondaryPurchaseRead!: ISecondaryPurchaseRead[];

  @Prop({
    required: true,
    default: () => {
      return [];
    },
  })
  public readonly athleteSecondaryPurchaseRead!: ISecondaryPurchaseRead[];

  @Prop({
    default: false,
  })
  public readonly isReloading: boolean;

  public isLoadingInternal = false;

  public secondaryCustData = secondaryCustData;
  public secondaryCustService = secondaryCustService;
  public secondaryService: SecondaryService = new SecondaryService();
  public secondaryCustDefsInternal: ISecondaryCustDef[] = [];
  public secondaryCustDefSelected: ISecondaryCustDef =
    this.secondaryCustService.factorySecondaryCustDef();
  public athleteCompSchedService: AthleteCompSchedService =
    new AthleteCompSchedService();
  public commonService: CommonService = new CommonService();
  public schedInfoService: SchedInfoService = new SchedInfoService();
  // public schedInfo: ISchedInfo = this.schedInfoService.factorySchedInfo();

  public showSelectAthleteDialog = false;
  public showStadium = false;

  public created() {
    this.secondaryCustDefsInternal = R.clone(this.secondaryCustDefs);
  }

  @Watch("secondaryCustDefs")
  public onSecondaryCustDefsChanged(newValue: ISecondaryCustDef[]) {
    this.secondaryCustDefsInternal = R.clone(newValue);
  }

  public get getSchedInfo(): ISchedInfo {
    const schedInfo: ISchedInfo = this.schedInfoService.factorySchedInfo();
    schedInfo.title = "Shop";

    if (this.getSecondaryCustDefsInternal.length === 0) {
      schedInfo.schedInfoDetails.push({
        title: "Currently no items available.",
        body: "",
      });
    }

    if (this.getAreAnyAthleteItems) {
      schedInfo.schedInfoDetails.push({
        title: "",
        body:
          '<span class="e4s-bold">Some items are only available when an eligible athlete' +
          ' (<i class="material-icons normal">directions_run</i>) is selected.</span>',
      });
    }
    if (
      this.selectedAthlete &&
      this.selectedAthlete.id &&
      this.selectedAthlete.id > 0
    ) {
      schedInfo.schedInfoDetails.push({
        title: "",
        body:
          '<span class="e4s-bold">Currently Selected Athlete (<i class="material-icons normal">directions_run</i>)' +
          this.selectedAthlete.firstName +
          " " +
          this.selectedAthlete.surName +
          "</span>",
      });
    }
    return schedInfo;
  }

  public get getHelpText(): string {
    if (
      this.competitionSummaryPublic &&
      this.competitionSummaryPublic.compId > 0 &&
      this.competitionSummaryPublic.options.helpText.cart &&
      this.competitionSummaryPublic.options.helpText.cart.length > 0
    ) {
      return this.competitionSummaryPublic.options.helpText.cart;
    }
    return "";
  }

  public get getSecondaryCustDefsInternal(): ISecondaryCustDef[] {
    if (this.getIsAthleteSelected && this.hasAthleteEnteredAnyEvents) {
      return this.secondaryCustDefsInternal;
    }
    return this.secondaryCustDefsInternal.filter((secondaryCustDef) => {
      return secondaryCustDef.perAcc !== "A";
    });
  }

  public get getSecondaryPurchaseReadUserKey(): Record<
    number,
    ISecondaryPurchaseRead[]
  > {
    return this.commonService.convertArrayToObjectArray(
      "prodId",
      this.userSecondaryPurchaseRead
    );
  }

  public get getSecondaryPurchaseReadAthleteKey(): Record<
    number,
    ISecondaryPurchaseRead[]
  > {
    return this.commonService.convertArrayToObjectArray(
      "prodId",
      this.athleteSecondaryPurchaseRead
    );
  }

  public get getSecondaryPurchaseReadKey(): Record<
    number,
    ISecondaryPurchaseRead[]
  > {
    return this.commonService.convertArrayToObjectArray("prodId", [
      ...this.userSecondaryPurchaseRead,
      ...this.athleteSecondaryPurchaseRead,
    ]);
  }

  public get getSecondaryCartKey(): Record<number, IWooCommerceLineItem[]> {
    console.warn("SecondaryCustGrid...getSecondaryCartKey() ...a");
    if (
      this.cart.meta &&
      this.cart.meta.wcCart &&
      this.cart.meta.wcCart.items &&
      this.cart.meta.wcCart.items.length > 0
    ) {
      console.warn("SecondaryCustGrid...getSecondaryCartKey() ...b");
      const propName: keyof IWooCommerceLineItem = "productId";
      return this.commonService.convertArrayToObjectArray(
        propName,
        this.cart.meta.wcCart.items
      );
    }
    console.warn("SecondaryCustGrid...getSecondaryCartKey() ...z");
    return {};
  }

  // public get getSecondaryCartProdIdKey(): Record<string, IWooCommerceLineItem[]> {
  //   if (this.cart.meta && this.cart.meta.wcCart && this.cart.meta.wcCart.items && this.cart.meta.wcCart.items.length > 0) {
  //     const secondaryCustDefsForAthleteByProdIdKey: Record<number, ISecondaryCustDef> = this.secondaryCustDefsInternal.filter((secondaryCustDef: ISecondaryCustDef) => {
  //       return secondaryCustDef.perAcc = "A";
  //     })
  //     .reduce((accum, secondaryCustDef: ISecondaryCustDef) => {
  //       accum[secondaryCustDef.prod.id] = secondaryCustDef;
  //       return accum;
  //     }, {} as Record<number, ISecondaryCustDef>);
  //
  //     const wooByProdAndAthleteId: Record<string, IWooCommerceLineItem[]> = this.cart.meta.wcCart.items.filter((wooCommerceLineItem: IWooCommerceLineItem) => {
  //       return !!secondaryCustDefsForAthleteByProdIdKey[wooCommerceLineItem.productId];
  //     })
  //     .reduce((accum, wooCommerceLineItem: IWooCommerceLineItem) => {
  //       const key = wooCommerceLineItem.productId;
  //       if (!accum[key]) {
  //         accum[key] = [];
  //       }
  //       accum[key].push(wooCommerceLineItem);
  //       return accum;
  //     }, {} as Record<string, IWooCommerceLineItem[]>);
  //
  //     return wooByProdAndAthleteId;
  //   }
  //   return {};
  // }

  public get getShowAthleteItemsAvailable() {
    return this.getAreAnyAthleteItems && !this.hasAthleteEnteredAnyEvents;
  }

  public get getAreAnyAthleteItems(): boolean {
    return this.secondaryCustService.anyAthleteItems(this.secondaryCustDefs);
  }

  public isSelected(secondaryCustDef: ISecondaryCustDef) {
    return this.secondaryCustDefSelected.id === secondaryCustDef.id;
  }

  public onCancel() {
    this.$emit("onCancel");
  }

  public get getIsAthleteSelected() {
    return (
      this.selectedAthlete &&
      this.selectedAthlete.id &&
      this.selectedAthlete.id > 0
    );
  }

  public get hasAthleteEnteredAnyEvents(): boolean {
    console.warn("SecondaryCustGrid...hasAthleteEnteredAnyEvents() ...a");
    const resp = this.getAthleteEnteredEvents.length > 0;
    console.warn("SecondaryCustGrid...hasAthleteEnteredAnyEvents() ...z");
    return resp;
  }

  public get getAthleteEnteredEvents(): IAthleteCompSchedRuleEvent[] {
    if (!this.getIsAthleteSelected) {
      return [];
    }
    //  The cart + athlete available events.
    const events = [...this.eventsSelected, ...this.eventsAvailableToAthlete];
    let athleteEntered = this.athleteCompSchedService
      .getEventsForAthlete(this.selectedAthlete.id, events)
      .filter((compEvent) => {
        return compEvent.entered || compEvent.order.productId > 0;
      });
    athleteEntered = this.commonService.uniqueArrayById(athleteEntered, "ceid");
    return athleteEntered;
  }

  public get getIsLoading() {
    return this.isLoadingInternal || this.cartIsLoading || this.isReloading;
  }

  public selectAthlete() {
    this.showSelectAthleteDialog = true;
  }

  public onSelectedParent(
    secondaryPurchaseParent: ISecondaryPurchaseParentEmit
  ) {
    const user = this.configApp.userInfo.user;
    const athlete = {
      id: 0,
      name: "",
    };

    const secondaryPurchase: ISecondaryPurchaseSubmit = {
      user: {
        id: user.id,
        name: user.name ? user.name : "",
      },
      athlete: athlete,
      prod: secondaryPurchaseParent.prod,
      qtyRequired: secondaryPurchaseParent.qtyRequired,
      attributeValues: secondaryPurchaseParent.attributeValues,
    };

    if (secondaryPurchaseParent.secondaryDef.perAcc === "A") {
      if (!this.getIsAthleteSelected) {
        messageDispatchHelper(
          "This item requires an athlete is selected",
          USER_MESSAGE_LEVEL.ERROR.toString()
        );
        return;
      }

      if (!this.hasAthleteEnteredAnyEvents) {
        messageDispatchHelper(
          "This item requires an athlete is entered into at least one event.",
          USER_MESSAGE_LEVEL.ERROR.toString()
        );
        return;
      }

      athlete.id = this.selectedAthlete.id;
      athlete.name =
        this.selectedAthlete.firstName + " " + this.selectedAthlete.surName;

      const attributeValues: ISecondaryAttributeValue[] =
        this.secondaryService.setAthleteAttributeName(
          this.selectedAthlete,
          secondaryPurchase.attributeValues
        );
      secondaryPurchase.attributeValues = attributeValues;
      (secondaryPurchase.prod as ISecondaryVariant).attributeValues =
        attributeValues;
    }

    this.isLoadingInternal = true;
    const prom =
      this.secondaryCustData.submitSecondaryPurchase(secondaryPurchase);
    handleResponseMessages(prom);
    prom
      .then((response) => {
        if (response.errNo > 0) {
          return;
        }
        messageDispatchHelper("Added.", USER_MESSAGE_LEVEL.ERROR.toString());

        this.$emit("reload");

        // this.$store.dispatch(
        //     ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
        //     ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_GET_CART
        // );
        //
        // this.$store.dispatch(
        //     SECONDARY_STORE_CONST.SECONDARY_CONST_MODULE_NAME + "/" +
        //     SECONDARY_STORE_CONST.SECONDARY_ACTIONS_USER_PURCHASES_GET,
        //
        // )
      })
      .finally(() => {
        this.isLoadingInternal = false;
      });
  }

  public get getStadiumUrl() {
    return STADIUM_URL_MANCHESTER;
    // return "https://uat.entry4sports.co.uk/resources/EnglishSchoolsStadiumMap.png";
    // return CONFIG.E4S_HOST + "/resources/EnglishSchoolsStadiumMap.png";
  }

  public openStadiumSeating() {
    window.open(this.getStadiumUrl, "_seating");
  }

  public athleteLinked(competitionAthleteSummary: ICompetitionAthleteSummary) {
    //  TODO    This bit does not work because the object in athlete "grid" is not a "full"
    //  TODO    athlete object required by entry screen.
    // const athleteAlreadyLinked: IAthlete | null = this.commonService.findFirst( (athlete)=> {
    //     return athlete.id === competitionAthleteSummary.athleteId;
    // }, this.searchedAthletes);
    //
    // if (athleteAlreadyLinked) {
    //     this.$store.commit(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME + "/" +
    //       ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_SELECTED_ATHLETE, athleteAlreadyLinked);
    //     this.showSelectAthleteDialog = false;
    //     return;
    // }

    this.showSelectAthleteDialog = false;
    this.isLoadingInternal = true;
    const prom = new AthleteData().getAthlete(
      competitionAthleteSummary.athleteId
    );
    // handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp) {
          this.$store.commit(
            ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
              "/" +
              ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_SELECTED_ATHLETE,
            resp
          );
        }
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
      })
      .finally(() => {
        // this.showSelectAthleteDialog = false;
        this.isLoadingInternal = false;
      });
  }
}
</script>

<style>
/*.is-selected {*/
/*    font-weight: 700;*/
/*}*/
.secondary-cust-grid--card {
  padding-top: 5px;
  border-bottom: 1px solid #c3c3c3;
}
</style>
