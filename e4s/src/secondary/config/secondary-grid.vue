<template>
    <div>

        <div class="row">
            <div class="input-field col s1 m1 l1">
                <span>ID</span>
            </div>

            <div class="input-field col s1 m1 l1">
                Type
            </div>

            <div class="input-field col s5 m5 l5">
                <span>Name</span>
            </div>

            <div class="input-field col s1 m1 l1">
                Max
            </div>

            <div class="input-field col s1 m1 l1">
                Price
            </div>

            <div class="input-field col s1 m1 l1">
                Stock/Sold
            </div>

            <div class="input-field col s4 m2 l2">

            </div>

        </div>

        <div class="e4s-card-std"
             :class="index % 2 === 0 ? '' : 'e4s-card-std__row-odd'"
             v-for="(secondary, index) in secondariesInternal"
             :key="secondary.id">

            <SecondaryDefRow :secondary-def="secondary"
                             :show-ref-obj-name="showRefObjName"
                             :is-loading="isLoading"
                             v-on:onCancel="onCancel"
                             v-on:onDeleted="onDeleted"
                             v-on:onEdited="onEdited">
                <div slot="select-button"></div>
            </SecondaryDefRow>

        </div>
    </div>
</template>


<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";

    import {Prop, Watch} from "vue-property-decorator"
    import {ISecondaryDef} from "../secondary-models"
    import {SecondaryService} from "../secondary-service"
    import SecondaryVariantRow from "./variation/secondary-variant-row.vue"
    import SecondaryDefRow from "./secondary-def-row.vue"

    const secondaryService: SecondaryService = new SecondaryService();

    @Component({
        name: "secondary-grid",
        components: {SecondaryDefRow, SecondaryVariantRow},
    })
    export default class SecondaryGrid extends Vue {
        @Prop({
            default: () => {
                return [];
            }
        }) public readonly secondaries: ISecondaryDef[];

        @Prop({
            default: true
        })
        public readonly showRefObjName: boolean;

        @Prop({
            default: false
        })
        public readonly isLoading: boolean;

        public secondaryService = secondaryService;
        public secondariesInternal: ISecondaryDef[] = [];
        public secondarySelected: ISecondaryDef = this.secondaryService.factorySecondaryDef();

        public created() {
            this.secondariesInternal = R.clone(this.secondaries);
        }

        @Watch("secondaries")
        public onSecondariesChanged(newValue: ISecondaryDef[]) {
            this.secondariesInternal = R.clone(newValue);
        }

        public isSelected(secondary: ISecondaryDef) {
            return this.secondarySelected.id === secondary.id;
        }

        public onCancel() {
            this.$emit("onCancel");
        }

        public onDeleted(secondary: ISecondaryDef) {
            this.$emit("onDeleted", secondary);
        }

        public onEdited(secondary: ISecondaryDef) {
            this.$emit("onEdited", R.clone(secondary));
        }

        public onSelected(secondary: ISecondaryDef) {
            this.secondarySelected = R.clone(secondary);
            this.$emit("onSelected", R.clone(secondary));
        }
    }
</script>


<style scoped>
    .is-selected {
        font-weight: 700;
    }
</style>
