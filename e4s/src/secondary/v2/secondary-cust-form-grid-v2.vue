<template>
  <!--  <SecondaryCustGrid-->
  <!--    :secondary-cust-defs="secondaryCustDefs"-->
  <!--    :user-secondary-purchase-read="-->
  <!--      secondaryStoreState.userSecondaryPurchaseRead-->
  <!--    "-->
  <!--    :athlete-secondary-purchase-read="-->
  <!--      secondaryStoreState.athleteSecondaryPurchaseRead-->
  <!--    "-->
  <!--    :is-reloading="getIsLoading"-->
  <!--    v-on:reload="reload"-->
  <!--  ></SecondaryCustGrid>-->
  <SecondaryCustGridV2
    :secondary-cust-defs="secondaryCustDefs"
    :user-secondary-purchase-read="
      secondaryStoreState.userSecondaryPurchaseRead
    "
    :athlete-secondary-purchase-read="
      secondaryStoreState.athleteSecondaryPurchaseRead
    "
    :is-reloading="getIsLoading"
    v-on:reload="reload"
  />
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ISecondaryRefObj } from "../secondary-models";
// import { SecondaryCustService } from "./secondary-cust-service";
import { SecondaryCustService } from "../cust/secondary-cust-service";
// import {
//   ISecondaryCustDef,
//   ISecondaryPurchaseRead,
// } from "./secondary-cust-models";
import {
  ISecondaryCustDef,
  ISecondaryPurchaseRead,
} from "../cust/secondary-cust-models";
import { SecondaryCustData } from "../cust/secondary-cust-data";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import * as R from "ramda";
import { mapState } from "vuex";
import { ENTRY_STORE_CONST, IEntryStoreState } from "../../entry/entry-store";
import { IAthlete } from "../../athlete/athlete-models";
import SecondaryCustGridV2 from "./secondary-cust-grid-v2.vue";

import { SecondaryService } from "../secondary-service";
import {
  SECONDARY_STORE_CONST,
  ISecondaryStoreState,
} from "../secondary-store";
import { ATH_COMP_SCHED_STORE_CONST } from "../../athleteCompSched/store/athleteCompSched-store";
import { SECONDARY_CUST_STORE_CONST } from "../cust/secondary-cust-store";

const secondaryService: SecondaryService = new SecondaryService();
@Component({
  name: "secondary-cust-form-grid-v2",
  components: { SecondaryCustGridV2 },
  computed: {
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      selectedAthlete: (state: IEntryStoreState) =>
        state.entryForm.selectedAthlete,
    }),
    ...mapState(SECONDARY_STORE_CONST.SECONDARY_CONST_MODULE_NAME, {
      secondaryStoreState: (state: ISecondaryStoreState) => state,
    }),
  },
})
export default class SecondaryCustFormGridV2 extends Vue {
  public readonly selectedAthlete: IAthlete;
  public readonly secondaryStoreState: ISecondaryStoreState;

  @Prop({
    default: () => {
      return secondaryService.factorySecondaryRefObj();
    },
  })
  public readonly secondaryRefObj: ISecondaryRefObj;

  public secondaryCustService: SecondaryCustService =
    new SecondaryCustService();
  public secondaryCustData: SecondaryCustData = new SecondaryCustData();

  public secondaryCustDefs: ISecondaryCustDef[] = [];
  public userSecondaryPurchaseRead: ISecondaryPurchaseRead[] = [];
  public athleteSecondaryPurchaseRead: ISecondaryPurchaseRead[] = [];

  public isLoading: boolean = false;
  public isReloading = false;
  // public isLoadingAthletePurchases = false;
  // public sections = {
  //     FORM: "FORM",
  //     GRID: "GRID"
  // };
  // public showSection: string = this.sections.GRID;

  public mounted() {
    this.getSecondaryCustData();
    this.getSecondaryCustPurchases();
  }

  @Watch("secondaryRefObj")
  public onSecondaryRefObjChanged(
    newValue: ISecondaryRefObj,
    oldValue: ISecondaryRefObj
  ) {
    if (!R.equals(newValue, oldValue)) {
      // this.getSecondaryCustData();
      // this.getSecondaryCustPurchases();
      this.reload();
    }
  }

  @Watch("selectedAthlete")
  public onSelectedAthleteChanged(newValue: IAthlete, oldValue: IAthlete) {
    if (!R.equals(newValue, oldValue)) {
      this.getSecondaryCustPurchasesAthlete();
    }
  }

  public reload() {
    this.$store.dispatch(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_GET_CART
    );

    this.isReloading = true;
    Promise.all([
      this.getSecondaryCustData(),
      this.getSecondaryCustPurchases(),
      this.getSecondaryCustPurchasesAthlete(),
    ]).finally(() => {
      this.isReloading = false;
    });
  }

  public getSecondaryCustData() {
    if (this.secondaryRefObj.objId === 0) {
      return;
    }

    // const payload: ISecondariesGetPayload = {
    //     objType: this.secondaryRefObj.objType,
    //     objId: this.secondaryRefObj.objId,
    //     compId: this.secondaryRefObj.compId
    // }
    // this.$store.dispatch(SECONDARY_STORE_CONST.SECONDARY_CONST_MODULE_NAME + "/" +
    //     SECONDARY_STORE_CONST.SECONDARY_ACTIONS_SECONDARIES_GET, payload);

    const prom = this.secondaryCustData.getSecondaries(
      this.secondaryRefObj.objType,
      this.secondaryRefObj.objId,
      this.secondaryRefObj.compId
    );
    handleResponseMessages(prom);
    return prom
      .then((response) => {
        if (response.errNo > 0) {
          return;
        }
        this.secondaryCustDefs = response.data;
        this.$store.commit(
          SECONDARY_CUST_STORE_CONST.SECONDARY_CUST_CONST_MODULE_NAME +
            "/" +
            SECONDARY_CUST_STORE_CONST.SECONDARY_CUST_MUTATIONS_SECONDARIES_SET,
          response.data
        );
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public getSecondaryCustPurchases() {
    const compId = this.secondaryRefObj.compId;
    if (compId === 0) {
      return;
    }
    if (this.selectedAthlete.id === 0) {
      return;
    }

    console.log("getSecondaryCustPurchases() compId: " + compId);

    // this.$store.dispatch(SECONDARY_STORE_CONST.SECONDARY_CONST_MODULE_NAME + "/" + SECONDARY_STORE_CONST.SECONDARY_ACTIONS_USER_PURCHASES_GET, compId);

    const userPurchasesProm = this.secondaryCustData.getPurchases(compId);
    handleResponseMessages(userPurchasesProm);
    return userPurchasesProm
      .then((response) => {
        if (response.errNo > 0) {
          return;
        }
        this.userSecondaryPurchaseRead = response.data;
        this.$store.commit(
          SECONDARY_STORE_CONST.SECONDARY_CONST_MODULE_NAME +
            "/" +
            SECONDARY_STORE_CONST.SECONDARY_MUTATIONS_SECONDARIES_SET,
          response.data
        );
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public getSecondaryCustPurchasesAthlete() {
    const compId = this.secondaryRefObj.compId;
    if (compId === 0) {
      return;
    }

    if (
      this.selectedAthlete &&
      this.selectedAthlete.id &&
      this.selectedAthlete.id > 0
    ) {
      console.log(
        "getSecondaryCustPurchasesAthlete() selectedAthlete.id: " +
          this.selectedAthlete.id +
          ", compId: " +
          compId
      );
      return this.$store.dispatch(
        SECONDARY_STORE_CONST.SECONDARY_CONST_MODULE_NAME +
          "/" +
          SECONDARY_STORE_CONST.SECONDARY_ACTIONS_ATHLETE_PURCHASES_GET,
        {
          compId,
          athleteId: this.selectedAthlete.id,
        }
      );
    }

    return;

    //
    // const userPurchasesProm = this.secondaryCustData.getPurchasesAthlete(compId, this.selectedAthlete.id);
    // handleResponseMessages(userPurchasesProm);
    // userPurchasesProm
    //     .then((response) => {
    //         if (response.errNo > 0) {
    //             return;
    //         }
    //         this.athleteSecondaryPurchaseRead = response.data;
    //     })
    //     .finally(() => {
    //         this.isLoading = false;
    //     })
  }

  public get getIsLoading() {
    return (
      this.isReloading ||
      this.isLoading ||
      this.secondaryStoreState.secondariesLoading ||
      this.secondaryStoreState.athleteSecondaryPurchasesLoading
    );
  }
}
</script>
