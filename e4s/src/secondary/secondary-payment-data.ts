// import {ResourceData} from "../common/resource/resource-service"
// import {ISecondaryPayment} from "./secondary-models"
// import https from "../common/https"
// import {IServerPagingResponseList} from "../common/common-models"

// export class SecondaryPaymentData extends ResourceData<ISecondaryPayment> {
//     constructor() {
//         super("/v5/secondarypayment");
//     }
//
//     public getPaymentsList(compId: number = 0): Promise<IServerPagingResponseList<ISecondaryPayment>> {
//         return https.get(this.getEndPoint() + "/list?compid=" + compId) as any as Promise<IServerPagingResponseList<ISecondaryPayment>> ;
//     }
//
// }
