<template>
    <div>

        <div class="e4s-section-padding-separator"></div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div v-text="getTicketHeader" class="ticket--section-header"></div>
            </div>
        </div>

        <div class="row">
            <div class="input-field col s6 m6 l6" v-if="ticketFormInternal.athlete.id > 0">
                <label class="active" :for="PREFIX + 'athlete'">
                    Athlete
                </label>
                <div :id="PREFIX + 'athlete'">
                    <span v-text="ticketFormInternal.athlete.id"></span>:&nbsp;
                    <span v-text="ticketFormInternal.athlete.name"></span>:&nbsp;
                    <span v-text="ticketFormInternal.athlete.club"></span>
                    <div v-text="getEntriesDescription"></div>
                </div>
            </div>

            <div class="input-field col s6 m6 l6" v-if="ticketFormInternal.athlete.id === 0">
                <label class="active" :for="PREFIX + 'user'">
                    Ticket (order ID)
                </label>
                <div :id="PREFIX + 'user'">
                    <span v-text="ticketFormInternal.product.name"></span>
                    (<span v-text="ticketFormInternal.orderId"></span>)
                </div>
            </div>

            <div class="input-field col s6 m6 l6" v-if="getHasSeat">
                <label class="active" :for="PREFIX + 'seat'">
                    Seat
                </label>
                <div :id="PREFIX + 'seat'">
                    <span v-text="getSeatDescription"></span>
                </div>
            </div>

            <div class="input-field col s6 m6 l6">
                <label class="active" :for="PREFIX + 'comp'">
                    Competition
                </label>
                <div :id="PREFIX + 'comp'">
                    <span v-text="ticketFormInternal.competition.id"></span>: <span v-text="ticketFormInternal.competition.name"></span>
                </div>
            </div>

            <div class="input-field col s6 m6 l6">
                <label class="active" :for="PREFIX + 'user'">
                    User
                </label>
                <div :id="PREFIX + 'user'">
                    <span v-text="ticketFormInternal.user.email"></span>
                </div>
            </div>

        </div>


<!--        <div class="e4s-section-padding-separator"></div>-->

<!--        <div class="row">-->
<!--            <div class="col s12 m12 l12">-->
<!--                <div v-if="getIsDataRequired" class="ticket-form-print&#45;&#45;data-required">Contact Data: Required.</div>-->
<!--                <div v-if="!getIsDataRequired" class="ticket-form-print&#45;&#45;data-optional">Contact Data: Optional.</div>-->
<!--            </div>-->
<!--        </div>-->

        <div class="e4s-section-padding-separator"></div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div class="ticket--section-header">Contact Data</div>
            </div>
        </div>

        <div class="row">
            <div class="input-field col s6 m6 l6">
                <label class="active" :for="PREFIX + 'name'">
                    Name
                    <FieldValidationLabel :validation-controller="validationController" prop-path="data.name"/>
                </label>
                <input
                    :id="PREFIX + 'name'"
                    :name="PREFIX + 'name'"
                    type="text"
                    v-model="ticketFormInternal.data.name"
                    placeholder=""/>
            </div>

            <div class="input-field col s6 m6 l6">
                <label class="active" :for="PREFIX + 'tel'">
                    Tel
                    <FieldValidationLabel :validation-controller="validationController" prop-path="data.telNo"/>
                </label>
                <input
                    :id="PREFIX + 'tel'"
                    :name="PREFIX + 'tel'"
                    type="text"
                    v-model="ticketFormInternal.data.telNo"
                    placeholder=""/>
            </div>

            <div class="input-field col s12 m12 l12">
                <label class="active" :for="PREFIX + 'address'">
                    Address
                    <FieldValidationLabel :validation-controller="validationController" prop-path="data.address"/>
                </label>
                <input
                    :id="PREFIX + 'address'"
                    :name="PREFIX + 'address'"
                    type="text"
                    v-model="ticketFormInternal.data.address"
                    placeholder=""/>
            </div>

            <div class="input-field col s6 m6 l6">
                <label class="active" :for="PREFIX + 'email'">
                    Email
                    <FieldValidationLabel :validation-controller="validationController" prop-path="data.email"/>
                </label>
                <input
                    :id="PREFIX + 'email'"
                    :name="PREFIX + 'email'"
                    type="text"
                    v-model="ticketFormInternal.data.email"
                    placeholder=""/>
            </div>
        </div>

        <div v-html="qrHtml" class="ticket-form-print--qr-code-image"></div>

        <div v-if="ticketFormInternal.text.ticketText.length > 0">

            <div class="e4s-section-padding-separator"></div>

            <div class="row">
                <div class="input-field col s12 m12 l12">
                    <label class="active" :for="PREFIX + 'info'">
                        Information
                    </label>
                    <div :id="PREFIX + 'info'" v-html="ticketFormInternal.text.ticketText"></div>
                </div>
            </div>
        </div>

        <div v-if="ticketFormInternal.text.terms.length > 0">

            <div class="e4s-section-padding-separator"></div>

            <div class="row">
                <div class="input-field col s12 m12 l12">
                    <label class="active" :for="PREFIX + 'info'">
                        Terms and Conditions
                    </label>
                    <div :id="PREFIX + 'info'" v-html="ticketFormInternal.text.terms"></div>
                </div>
            </div>
        </div>

    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {ITicketForm} from "./ticket-models"
import {TicketService} from "./ticket-service"
import {ValidationController} from "../../validation/validation-controller"
import FieldValidationLabel from "../../validation/validation-field-lable.vue";

const ticketService: TicketService = new TicketService();

@Component({
    name: "ticket-form-print",
    components: {FieldValidationLabel}
})
export default class TicketFormPrint extends Vue {
    @Prop({
        default: () => {
            return ticketService.factoryTicketForm();
        }
    })
    public readonly ticketForm: ITicketForm;

    @Prop({default: false})
    public readonly isLoading: boolean;

    @Prop({default: ""})
    public readonly qrHtml: string;

    public PREFIX = Math.random().toString(36).substring(2);
    public ticketService = ticketService;
    public ticketFormInternal: ITicketForm = this.ticketService.factoryTicketForm();
    public validationController: ValidationController = new ValidationController();


    public created() {
        this.init(this.ticketForm);
    }

    public get getTicketHeader() {
        return this.ticketService.getTicketHeader(this.ticketFormInternal);
    }

    public get getIsDataRequired() {
        return this.ticketFormInternal.ticket.dataReq;
    }

    public get getEntriesDescription(): string {
        return this.ticketService.getEntriesDescription(this.ticketFormInternal);
    }

    @Watch("ticketForm")
    public onTicketFormChanged(newValue: ITicketForm, oldValue: ITicketForm) {
        this.init(newValue);
    }

    public init(ticketForm: ITicketForm) {
        this.ticketFormInternal = R.clone(ticketForm);
        if (this.getIsDataRequired) {
            this.validateFormData()
        }
    }

    public validateFormData() {
        this.validationController.reset();
        this.validationController.setErrors(this.ticketService.validate(this.ticketFormInternal));
    }

    public get getHasSeat(): boolean {
        return this.ticketService.hasSeat(this.ticketFormInternal);
    }

    public get getSeatDescription() {
        return this.ticketService.getSeatDescription(this.ticketFormInternal);
    }

}
</script>

