<template>
    <div>
        <div class="no-print" v-if="true">
            <div class="row" v-if="isLoading">
                <div class="input-field col s6 m6 l6">
                    Loading...<LoadingSpinner></LoadingSpinner>
                </div>
            </div>

            <div v-for="(ticketForm, index) in ticketForms" :key="ticketForm.guid">

                <div class="row" v-if="!ticketForm.competition">
                    <div class="input-field col s6 m6 l6">
                        No competition specified.
                    </div>
                </div>

                <TicketForm :ticket-form="ticketForm"
                            :index-message="getIndexMessage(index)"
                            :is-loading="isLoading"
                            :qr-html="getQrCode(ticketForm)"
                            v-on:submitData="submitData"
                            v-on:submitOnSite="submitOnSite"
                ></TicketForm>

            </div>


        </div>


        <div class="only-print ticket-form-print--only-print-this" v-if="!isLoading">

            <div v-for="ticketForm in ticketForms" class="page-break" :key="ticketForm.guid">

                <div class="ticket-form-print--page-layout">

                    <div class="ticket-form-print--body">
                        <TicketFormPrint slot="form-content"
                                         :ticket-form="ticketForm"
                                         :qr-html="getQrCode(ticketForm)"
                        ></TicketFormPrint>
                    </div>


                    <div class="ticket-form-print-footer">
                        <div class="ticket-form-print--footer-e4s">
                            <span class="ticket-form-print--e4s-text">Powered by Entry4Sports</span>
                        </div>

                    </div>
                </div>

            </div>

        </div>

    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {TicketData} from "../../ticket-data"
import {handleResponseMessages} from "../../../../common/handle-http-reponse"
import {ITicketForm, ITicketFormDataPayload, ITicketFormBaseMeta} from "../../ticket-models"
import qrcode from "qrcode-generator";
import {CONFIG} from "../../../../common/config"
import { LAUNCH_ROUTES_PATHS } from "../../../../launch/launch-routes";
import TicketFormPrint from "../../ticket-form-print.vue"
import TicketForm from "../../ticket-form.vue"
import {TicketService} from "../../ticket-service"

@Component({
    name: "ticket-order-container",
    components: {TicketForm, TicketFormPrint}
})
export default class TicketOrderContainer extends Vue {

    public isLoading = false;
    public compId: number = 0;
    public routeKey: string = "";

    public ticketData = new TicketData();
    public ticketService: TicketService = new TicketService();
    public ticketForms: ITicketForm[] = []
    public ticketFormBaseMeta: ITicketFormBaseMeta = this.ticketService.factoryTicketFormBaseMeta();
    public LAUNCH_ROUTES_PATHS = LAUNCH_ROUTES_PATHS;

    public created() {
        console.log("TicketOrderContainer.created()....this.$route", this.$route);

        const id: string = this.$route.params.id ? this.$route.params.id : "";
        this.routeKey = id;

        if ( id.length > 0) {
            this.loadData();
        }
    }

    public loadData() {
        this.isLoading = true;
        const prom = this.ticketData.getTicketsForOrder(this.routeKey)
        handleResponseMessages(prom);
        prom.then( (response) => {
            if (response.errNo === 0) {
                const ticketFormBaseMeta = response.meta ? response.meta : this.ticketService.factoryTicketFormBaseMeta();
                this.ticketFormBaseMeta = ticketFormBaseMeta
                const ticketForms = response.data.map( (ticketFormBase) => {
                    const ticketForm: ITicketForm = {
                        ...ticketFormBase,
                        competition: ticketFormBaseMeta.competition,
                        text: {
                            ticketText: "",
                            terms: ""
                        }
                    };
                    return ticketForm;
                });
                this.ticketForms = ticketForms;
            }
        })
            .finally(()=> {
                this.isLoading = false;
            })
    }

    public getIndexMessage(index: number): string {
        return "(" + (index + 1) + " of " + this.ticketForms.length + ")";
    }

    public getQrCode(ticketForm: ITicketForm): string {

        if (!ticketForm.competition) {
            return "";
        }

        const url = (process.env.NODE_ENV === "development" ?
            "https://dev.entry4sports.com" :
            CONFIG.E4S_HOST) + "/#/" + LAUNCH_ROUTES_PATHS.TICKET_FORM + "/" + ticketForm.guid;

        console.log("TicketOrderContainer.buildQrCode() url: " + url);

        const qr = qrcode(0, 'M');
        qr.addData(unescape(encodeURI( url )));
        qr.make();
        return qr.createImgTag(4, 4);
    }

    public submitData(ticketForm: ITicketForm) {
        this.isLoading = true;
        const ticketFormDataPayload: ITicketFormDataPayload = {
            id: ticketForm.id,
            data: ticketForm.data
        };
        const prom = this.ticketData.submitFormData(this.routeKey, ticketFormDataPayload);
        handleResponseMessages(prom);
        prom.then( (response) => {
            if (response.errNo === 0 && response.data.data) {
                // this.ticketForm.data = response.data.data;
                this.loadData();
            }
        })
            .finally(()=> {
                this.isLoading = false;
            })
    }

    public submitOnSite(ticketForm: ITicketForm) {
        this.isLoading = true;
        const ticketFormDataPayload: ITicketFormDataPayload = {
            id: ticketForm.data.id,
            onSite: ticketForm.ticket.onSite
        };
        const prom = this.ticketData.submitFormData(this.routeKey, ticketFormDataPayload);
        handleResponseMessages(prom);
        prom
            .then( (response) => {
                if (response.errNo === 0 && response.data.data) {
                    // this.ticketForm.data = response.data.data;
                    this.loadData();
                }
            })
            .finally(()=> {
                this.isLoading = false;
            })
    }
}
</script>

<style scoped>

.ticket-form-print--only-print-this {
    width: 210mm;
    height: 290mm;
    /*background-color: green;*/
}

.ticket-form-print--page-layout {
    border-radius: 2vw;
    border: solid 2px black;
    padding: 2vw;
    margin: 1vw;
    /*height: 100%;*/
}

.ticket-form-print--header {
    margin-bottom: 2vw;
}

.ticket-form-print--header-message {
    font-size: 3vw;
}

.ticket-form-print--header-location {
    font-size: 2vw;
}

.ticket-form-print--body {
    /*font-size: 2vw;*/
}

.ticket-form-print--footer {

}

.ticket-form-print--footer-e4s {
    text-align: right;
}
.ticket-form-print--e4s-logo {
    height: 100px;
}

.ticket-form-print--e4s-text {
    /*font-size: 2vw;*/
    /*float: right;*/
}
</style>
