<template functional>
  <div class="ticket-search-row">
    <div class="row">
      <div class="col s12">
        <i class="material-icons mat-icon-img">receipt</i>
        <span class="e4s-bold" v-text="props.ticketForm.product.name"></span>
        <span
          class="e4s-bold right"
          v-text="'Order:' + props.ticketForm.orderId"
        ></span>
        <i class="material-icons mat-icon-img red-text" v-if="props.isMatch"
          >camera_alt</i
        >
      </div>
    </div>

    <div class="row">
      <div class="col s6">
        <span v-text="props.ticketForm.user.email"></span>
      </div>
      <div class="col s6">
        <span v-text="props.ticketForm.user.name" class="right"></span>
      </div>
    </div>

    <div class="row">
      <div class="col s12">
        <span
          v-text="
            $options.methods.getTicketVariationName(
              props.ticketForm,
              props.secondaryService
            )
          "
        ></span>
      </div>
    </div>

    <div
      class="row"
      v-if="
        $options.methods.getSeatDescription(
          props.ticketForm,
          props.secondaryService
        ).length > 0
      "
    >
      <div class="col s12">
        <span
          v-text="
            $options.methods.getSeatDescription(
              props.ticketForm,
              props.secondaryService
            )
          "
        ></span>
      </div>
    </div>

    <div
      class="row"
      v-if="
        $options.methods.hasContactData(
          props.ticketForm,
          props.secondaryService
        )
      "
    >
      <div class="col s12">
        <div class="ticket-search-row--contact-row-divider-line"></div>
        <i class="material-icons mat-icon-img">contact_phone</i>
        <span
          v-text="
            $options.methods.getContactData(
              props.ticketForm,
              props.secondaryService
            )
          "
        ></span>
        <div class="ticket-search-row--contact-row-divider-line"></div>
      </div>
    </div>

    <div class="row">
      <div class="col s12">
        <!--                <div class="e4s-bold left">-->

        <!--                </div>-->
        <div
          class="e4s-bold left"
          :class="
            props.ticketForm.ticket.onSite
              ? 'ticket-search-row--on-site'
              : 'ticket-search-row--off-site'
          "
          v-text="props.ticketForm.ticket.onSite ? 'On site' : 'Off Site'"
        ></div>

        <div class="right">
          <button
            class="e4s-button e4s-button--green e4s-button--medium"
            :disabled="props.isLoading"
            v-if="props.showSetSite"
            v-on:click="listeners.onSetSite(props.index)"
          >
            <span
              v-text="$options.methods.getOnSiteSetDesc(props.ticketForm)"
            ></span>
          </button>

          <button
            class="e4s-button e4s-button--green e4s-button--medium"
            :disabled="props.isLoading"
            v-if="props.showSelect"
            v-on:click="listeners.onSelect(props.index)"
          >
            Edit
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ITicketFormBase } from "../../ticket-models";
import { SecondaryService } from "../../../secondary-service";

export default {
  props: [
    "ticketForm",
    "isLoading",
    "isMatch",
    "onSelect",
    "index",
    "secondaryService",
    "showSelect",
    "showSetSite",
    "onSetSite",
  ],
  methods: {
    getTicketVariationName: (
      ticketFormBase: ITicketFormBase,
      secondaryService: SecondaryService
    ): string => {
      const carReg = secondaryService.getCarReg(ticketFormBase);
      if (carReg.length > 0) {
        return carReg;
      }
      return secondaryService.getAthleteVariationName(ticketFormBase);
    },
    getSeatDescription: (
      ticketFormBase: ITicketFormBase,
      secondaryService: SecondaryService
    ): string => {
      return secondaryService.getSeatDescription(ticketFormBase);
    },
    getOnSiteSetDesc: (ticketFormBase: ITicketFormBase) => {
      return ticketFormBase.ticket.onSite ? "Set Off" : "Set On";
    },
    hasContactData: (
      ticketFormBase: ITicketFormBase,
      secondaryService: SecondaryService
    ) => {
      return secondaryService.hasFormData(ticketFormBase);
    },
    getContactData: (
      ticketFormBase: ITicketFormBase,
      secondaryService: SecondaryService
    ) => {
      if (!ticketFormBase.data) {
        return "";
      }
      const data: string[] = [];
      const formData = ticketFormBase.data;
      if (formData.name.length > 0) {
        data.push(formData.name);
      }
      if (formData.email.length > 0) {
        data.push(formData.email);
      }
      if (formData.telNo.length > 0) {
        data.push(formData.telNo);
      }
      if (formData.address.length > 0) {
        data.push(formData.address);
      }
      return data.join(", ");
    },
  },
};
</script>
