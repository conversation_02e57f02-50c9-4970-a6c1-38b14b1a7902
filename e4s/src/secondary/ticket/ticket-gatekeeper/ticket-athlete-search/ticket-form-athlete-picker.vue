<template>
    <div>
<!--        <div class="row">-->
<!--            <div class="col s12 m12 l12">-->
<!--                <span class="e4s-bold">Athlete Search</span>-->
<!--            </div>-->
<!--        </div>-->

        <CheckinAthleteSearch :comp-id="compId" :is-loading="getIsLoading">
            <div slot="qr-link"></div>
            <div slot="button-checkin-clear-down"></div>
        </CheckinAthleteSearch>

        <div class="e4s-section-padding-separator"></div>

        <div v-for="(checkinAthlete, index) in athleteCheckinInternal"
             class="checkin--athlete"
             :key="checkinAthlete.id">

                <TicketAthleteSearchRow
                    :checkin-athlete="checkinAthlete"
                    :is-loading="getIsLoading"
                    :index="index"
                    v-on:onSelect="select"
                ></TicketAthleteSearchRow>

        </div>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import CheckinAthleteSearch
    from "../../../../competition/checkin/checkin-organiser/checkin-athlete-search/checkin-athlete-search.vue"
import {Prop, Watch} from "vue-property-decorator"
import {mapState} from "vuex"
import {CHECKIN_STORE_CONST} from "../../../../competition/checkin/checkin-store"
import {ICheckinAthlete, ICheckinCompSummary, ICheckInStoreState} from "../../../../competition/checkin/checkin-models"
import * as R from "ramda"
import {TicketData} from "../../ticket-data"
import {handleResponseMessages} from "../../../../common/handle-http-reponse"
import {format, parse} from "date-fns"
import TicketAthleteSearchRow from "./ticket-athlete-search-row.vue";

@Component({
    name: "ticket-form-athlete-picker",
    components: {CheckinAthleteSearch,TicketAthleteSearchRow},
    computed: {
        ...mapState(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME, {
            checkInStoreState: (state: ICheckInStoreState) => state,
            checkinSummary: (state: ICheckInStoreState) => state.checkinSummary,
            athleteCheckin: (state: ICheckInStoreState) => state.athleteCheckin,
            // isLoading: (state: ICheckInStoreState) => state.isLoading,
            isLoadingSummary: (state: ICheckInStoreState) => state.isLoadingSummary
            // message: (state: ICheckInStoreState) => state.message
        })
    }
})
export default class TicketFormAthletePicker extends Vue {
    public readonly checkInStoreState: ICheckInStoreState
    public readonly checkinSummary: ICheckinCompSummary;
    public readonly athleteCheckin: ICheckinAthlete[];
    // public readonly isLoading: boolean;
    public readonly isLoadingSummary: boolean;
    // public readonly message: string;

    @Prop({default: 0})
    public readonly compId: number;

    public ticketData: TicketData = new TicketData();

    public athleteCheckinInternal: ICheckinAthlete[] = [];
    public isLoadingInternal: boolean = false;

    public created() {
        this.$store.commit(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" +
            CHECKIN_STORE_CONST.CHECKIN_MUTATIONS_RESET_CHECKIN_ATHLETES);
    }

    @Watch("athleteCheckin")
    public onAthleteCheckinChanged(newValue: ICheckinAthlete[]) {
        this.athleteCheckinInternal = R.clone(newValue);
    }

    public cancel() {
        this.$emit("cancel");
    }

    public select(index: number) {
        this.selectedCheckInAthlete(this.athleteCheckinInternal[index]);
    }

    public get getIsLoading() {
        return this.checkInStoreState.isLoading || this.isLoadingInternal || this.isLoadingSummary;
    }

    public getDateOfBirth(dob: string) {
        const dobFormatted = format(parse(dob), "Do MMM YYYY" );
        return dobFormatted;
    }

    public selectedCheckInAthlete(checkinAthlete: ICheckinAthlete) {

        this.isLoadingInternal = true;
        const prom = this.ticketData.getTicketForAthlete(this.compId, checkinAthlete.athleteId);
        handleResponseMessages(prom);

        prom.then( (response) => {
            if (response.errNo > 0) {
                return;
            }
            this.$emit("selectedTicketForm", R.clone(response.data));
        })
        .finally(() => {
            this.isLoadingInternal = false;
        });
    }

}
</script>
