<template functional>
    <div class="ticket-search-row">
        <div class="row">
            <div class="col s12">
                <i class="material-icons mat-icon-img">directions_run</i>
                <span class="e4s-bold" v-text="$options.methods.getAthleteName(props.ticketForm)"></span>
                <span class="e4s-bold right" v-text="$options.methods.getOrderId(props.ticketForm)"></span>
            </div>
        </div>

        <div class="row">
            <div class="col s12">
                <span v-text="$options.methods.getEventsDescription(props.ticketForm, props.ticketService)"></span>
            </div>
        </div>

        <div class="row">
            <div class="col s6">
                <span v-text="props.ticketForm.user.email"></span>
            </div>
            <div class="col s6">
                <span v-text="props.ticketForm.user.name" class="right"></span>
            </div>
        </div>

        <div class="row">

            <div class="col s12">
                <div class="e4s-bold left" :class="props.ticketForm.ticket.onSite ?  'ticket-search-row--on-site' : 'ticket-search-row--off-site' "
                     v-text="props.ticketForm.ticket.onSite ? 'On site' : 'Off Site'"
                ></div>

                <div class="right">
                    <button class="e4s-button e4s-button--green e4s-button--medium"
                            :disabled="props.isLoading"
                            v-if="props.showSetSite"
                            v-on:click="listeners.onSetSite(props.index)"
                    ><span v-text="$options.methods.getOnSiteSetDesc(props.ticketForm)"></span></button>

                    <button class="e4s-button e4s-button--green e4s-button--medium"
                            :disabled="props.isLoading"
                            v-if="props.showSelect"
                            v-on:click="listeners.onSelect(props.index)"
                    >Select</button>
                </div>

            </div>
        </div>
    </div>
</template>

<script lang="ts">
import {ITicketFormBase} from "../../ticket-models"
import {TicketService} from "../../ticket-service"

export default {
    props: ["ticketForm", "isLoading", "onSelect", "index", "ticketService", "showSelect", "showSetSite", "onSetSite"],
    methods: {
        getAthleteName: (ticketFormBase: ITicketFormBase) => {
            return ticketFormBase.athlete.id + ": " + ticketFormBase.athlete.name + ": " + ticketFormBase.athlete.club;
        },
        getEventsDescription: (ticketFormBase: ITicketFormBase, ticketService: TicketService) => {
            return ticketService.getEntriesDescription(ticketFormBase);
        },
        getOnSiteSetDesc: (ticketFormBase: ITicketFormBase) => {
            return ticketFormBase.ticket.onSite ? "Set Off" : "Set On";
        },
        getOrderId: (ticketFormBase: ITicketFormBase): string => {
            return ticketFormBase.orderId > 0 ? "#" + ticketFormBase.orderId : "";
        }
    }
}
</script>
