<template>
    <div>
        <div class="row">
            <div class="input-field col s12 m12 l12">
                Only first few characters required when searching.
            </div>
        </div>

        <div class="e4s-section-padding-separator"></div>

        <div class="row">
            <div class="input-field col s6 m6 l6">
                <input
                    :id="PREFIX + 'first-name'"
                    :name="PREFIX + 'first-name'"
                    class="e4s-input"
                    type="text"
                    v-model="state.firstName"
                    v-on:keyup.enter="search"
                    placeholder=""/>
                <label class="active" :for="PREFIX + 'first-name'">
                    First Name
                    <ValidationFieldLable :validation-controller="validationController" :prop-path="'state.firstName'"/>
                </label>
            </div>
            <div class="input-field col s6 m6 l6">
                <input
                    :id="PREFIX + 'sur-name'"
                    :name="PREFIX + 'sur-name'"
                    class="e4s-input"
                    type="text"
                    v-model="state.surName"
                    v-on:keyup.enter="search"
                    placeholder=""/>
                <label class="active" :for="PREFIX + 'sur-name'">
                    Last Name
                    <ValidationFieldLable :validation-controller="validationController" :prop-path="'state.surName'"/>
                </label>
            </div>
        </div>

        <div class="row">
            <div class="input-field col s6 m6 l6">
                <input
                    :id="PREFIX + 'urn'"
                    :name="PREFIX + 'urn'"
                    class="e4s-input"
                    type="text"
                    v-model="state.urn"
                    v-on:keyup.enter="search"
                    placeholder=""/>
                <label class="active" :for="PREFIX + 'urn'">
                    URN
                </label>
            </div>
            <div class="input-field col s6 m6 l6">
                <input
                    :id="PREFIX + 'club'"
                    :name="PREFIX + 'club'"
                    class="e4s-input"
                    type="text"
                    v-model="state.club"
                    v-on:keyup.enter="search"
                    placeholder=""/>
                <label class="active" :for="PREFIX + 'club'">
                    Club
                </label>
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div class="right">
                    <LoadingSpinner v-if="getIsLoading"></LoadingSpinner>
                    <span v-text="ticketSearches.length"></span> Records found.
                    <button class="e4s-button e4s-button--green e4s-button--medium"
                            :disabled="isLoadingInternal"
                            v-on:click="search"
                    >Search</button>
                </div>
            </div>
        </div>

        <div class="e4s-section-padding-separator"></div>

        <table class="ticket-search-table">

            <template v-for="(ticketSearch, index) in ticketSearches">

                <tr :key="ticketSearch.guid" :class="ticketSearch.ticket.onSite ? 'e4s-indicator--row-green' : 'e4s-indicator--row-red'">
                    <td class="e4s-indicator--td"
                        :class="ticketSearch.ticket.onSite ? 'e4s-indicator--green' : 'e4s-indicator--red'">
                    </td>
                    <td>
                        <TicketAthleteRow
                            :index="index"
                            :is-loading="getIsLoading"
                            :show-select="true"
                            :ticket-form="ticketSearch"
                            :ticket-service="ticketService"
                            v-on:onSelect="select(index)"
                        ></TicketAthleteRow>
                    </td>
                    <td class="e4s-indicator--td"
                        :class="ticketSearch.ticket.onSite ? 'e4s-indicator--green' : 'e4s-indicator--red'">
                    </td>
                </tr>

            </template>

        </table>

    </div>
</template>


<script lang="ts">
import Component from "vue-class-component";
import Vue from "vue";
import {Prop} from "vue-property-decorator"
import {ValidationController} from "../../../../validation/validation-controller"
import ValidationFieldLable from "../../../../validation/validation-field-lable.vue"
import {TicketData, ITicketSearchPayload} from "../../ticket-data"
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import {TicketService} from "../../ticket-service"
import {ITicketForm, ITicketFormBase, ITicketFormBaseMeta} from "../../ticket-models"
import TicketAthleteRow from "./ticket-athlete-row.vue";
import * as R from "ramda"
import {SecondaryService} from "../../../secondary-service"

@Component({
    name: "ticket-athlete-search",
    components: {ValidationFieldLable, TicketAthleteRow}
})
export default class TicketAthleteSearch extends Vue {
    @Prop({default: 0})
    public readonly compId: number;

    @Prop({default: false})
    public readonly isLoading: boolean;

    public isLoadingInternal: boolean = false;
    public PREFIX = Math.random().toString(36).substring(2);
    public validationController: ValidationController = new ValidationController();

    public secondaryService: SecondaryService = new SecondaryService();
    public ticketData: TicketData = new TicketData();
    public ticketService: TicketService = new TicketService();
    public ticketSearches: ITicketFormBase[] = [];
    public ticketFormBaseMeta: ITicketFormBaseMeta = this.ticketService.factoryTicketFormBaseMeta();

    public state = {
        firstName: "",
        surName: "",
        club: "",
        urn: "",
    };

    public get getIsLoading() {
        return this.isLoading || this.isLoadingInternal;
    }

    public search() {
        const ticketSearchPayload: ITicketSearchPayload = {
            firstname: this.state.firstName,
            surname: this.state.surName,
            club: this.state.club,
            urn: this.state.urn
        };
        this.isLoadingInternal = true;
        const prom = this.ticketData.getTicketsByAthleteSearch( this.compId, ticketSearchPayload);
        handleResponseMessages(prom);
        prom
            .then( (response) => {
                if (response.errNo > 0) {
                    return;
                }
                this.ticketFormBaseMeta = response.meta ? response.meta : this.ticketFormBaseMeta;
                this.ticketSearches = response.data;
            })
            .finally(()=> {
                this.isLoadingInternal = false;
            })
    }

    public select(index: number) {

        const ticketFormBase: ITicketFormBase = this.ticketSearches[index];
        const ticketForm: ITicketForm = {
            ...ticketFormBase,
            competition: this.ticketFormBaseMeta.competition,
            text: {
                terms: "",
                ticketText: ""
            }
        }
        this.$emit("selectedTicketForm", R.clone(ticketForm));
    }
}
</script>
