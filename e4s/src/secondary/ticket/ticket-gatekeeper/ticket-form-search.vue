<template>
    <div>
        <div class="row" v-show="false">
            <div class="col s12 m12 l12">
                Search By:

                <div class="e4s-force-inline-block">
                    <label>
                        <input type="radio"
                               id="indoor"
                               class="browser-default"
                               :value="sections.ATHLETE"
                               v-model="showSection">
                        <span>Athlete</span>
                    </label>
                </div>

                <div class="e4s-force-inline-block">
                    <label>
                        <input type="radio"
                               id="outdoor"
                               class="browser-default"
                               :value="sections.TICKET"
                               v-model="showSection">
                        <span>Ticket</span>
                    </label>
                </div>

<!--                <button class="e4s&#45;&#45;button e4s&#45;&#45;button-red e4s&#45;&#45;button-fat e4s&#45;&#45;button-medium"-->
<!--                        :disabled="showSection === sections.ATHLETE"-->
<!--                        v-on:click.stop="showSection = sections.ATHLETE">-->
<!--                    <span>Athlete</span>-->
<!--                </button>-->
<!--                <button class="e4s&#45;&#45;button e4s&#45;&#45;button-red e4s&#45;&#45;button-fat e4s&#45;&#45;button-medium"-->
<!--                        :disabled="showSection === sections.TICKET"-->
<!--                        v-on:click.stop="showSection = sections.TICKET">-->
<!--                    <span>Ticket</span>-->
<!--                </button>-->
                <button class="e4s-button e4s-button--red e4s-button--medium right"
                        v-on:click.stop="cancel">
                    <span>Cancel</span>
                </button>
            </div>
        </div>


        <div class="row">
            <div class="input-field col s12 m12 l12">
                <input
                    :id="PREFIX + 'search-term'"
                    :name="PREFIX + 'search-term'"
                    class="e4s-input"
                    type="text"
                    v-model="searchTerm"
                    v-on:keyup.enter="search"
                    placeholder="First, last name, order no..."/>
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">

            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                <span v-text="tickets.length"></span> Records found.
                <div class="right">
                    <LoadingSpinner v-if="isLoading"></LoadingSpinner>

                    <button class="e4s-button e4s-button--green e4s-button--medium"
                            v-on:click="scan">
                        <span>Scan</span>
                    </button>

                    <button class="e4s-button e4s-button--green e4s-button--medium"
                            :disabled="isLoading"
                            v-on:click="search"
                    >Search</button>

                    <button class="e4s-button e4s-button--red e4s-button--medium"
                            v-on:click.stop="cancel">
                        <span>Cancel</span>
                    </button>

                </div>
            </div>
        </div>

<!--        <div class="e4s-section-padding-separator"></div>-->

<!--        <TicketFormAthletePicker :comp-id="compId"-->
<!--                                 v-if="showSection === sections.ATHLETE"-->
<!--                                 v-on:selectedTicketForm="selectedCheckInTicket"-->
<!--        ></TicketFormAthletePicker>-->

<!--        <TicketAthleteSearch-->
<!--            :comp-id="compId"-->
<!--            v-if="showSection === sections.ATHLETE"-->
<!--            v-on:selectedTicketForm="selectedCheckInTicket"-->
<!--        ></TicketAthleteSearch>-->

<!--        <TicketSearch :comp-id="compId"-->
<!--                      v-if="showSection === sections.TICKET"-->
<!--                      v-on:selectedTicketForm="selectedCheckInTicket">-->
<!--        </TicketSearch>-->

       <TicketTable
           :tickets="tickets"
           :ticket-form-base-trigger-search="ticketFormBaseTriggerSearch"
           v-on:onSelect="selectedCheckInTicket"
           v-on:onSetSite="onSetSite"
       ></TicketTable>

        <LoadingSpinnerModal :show-it="getIsLoading"></LoadingSpinnerModal>

    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {Prop, Watch} from "vue-property-decorator";
import TicketFormAthletePicker from "./ticket-athlete-search/ticket-form-athlete-picker.vue"
import {ITicketForm, ITicketFormBase, ITicketFormDataPayload} from "../ticket-models"
import TicketSearch from "./ticket-search/ticket-search.vue"
import TicketAthleteSearch from "./ticket-athlete-search/ticket-athlete-search.vue"
import TicketTable from "./ticket-table.vue"
import {TicketData} from "../ticket-data"
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import LoadingSpinnerModal from "../../../common/ui/modal/loading-spinner-modal.vue";
import {TicketService} from "../ticket-service";

const ticketService = new TicketService();

@Component({
    name: "ticket-form-search",
    components: {LoadingSpinnerModal, TicketTable, TicketAthleteSearch, TicketSearch, TicketFormAthletePicker}
})
export default class TicketFormSearch extends Vue {
    @Prop({default: 0})
    public readonly compId: number;

    // @Prop({default: ""})
    // public readonly searchTermTrigger: string;

    @Prop({
        default: () => {
            return ticketService.factoryTicketFormBase();
        }
    })
    public readonly ticketFormBaseTriggerSearch!: ITicketFormBase;

    public sections = {
        SEARCH: "SEARCH",
        ATHLETE: "ATHLETE",
        TICKET: "TICKET"
    };
    public showSection: string = this.sections.ATHLETE;

    public tickets: ITicketFormBase[] = [];
    public ticketData: TicketData = new TicketData();
    public searchTerm: string = "";
    public isLoading = false;
    public PREFIX = Math.random().toString(36).substring(2);
    public isLoadingInternal: boolean = false;

    // @Watch("searchTermTrigger")
    // public onSearchTermTriggerChanged(newValue: string, oldValue: string) {
        // this.searchTerm = newValue;
        // if (newValue.length > 0) {
        //     this.search();
        // }
    // }

    @Watch("ticketFormBaseTriggerSearch")
    public onTicketFormBaseMetaChange(newValue: ITicketFormBase, oldValue: ITicketFormBase) {
        if (newValue.orderId > 0) {
            this.searchTerm = "Order:" + newValue.orderId;
            this.search();
        }
    }

    public search() {
        this.isLoading = true;
        const prom = this.ticketData.getTicketsSearch(this.compId, this.searchTerm);
        handleResponseMessages(prom);
        prom.then((response) => {
            if (response.errNo === 0) {
                this.tickets = response.data;
            }
        })
        .finally( () => {
            this.isLoading = false;
        })
    }

    public submitData(ticketForm: ITicketFormBase) {
        // if (this.getIsDataRequired) {
        //     this.validateFormData();
        //     if (!this.validationController.isValid) {
        //         return;
        //     }
        // }
        this.isLoadingInternal = true;

        //  N.B.  this toggles the status
        const ticketFormDataPayload: ITicketFormDataPayload = {
            id: ticketForm.id,
            data: ticketForm.data,
            onSite: !ticketForm.ticket.onSite
        };
        const prom = this.ticketData.submitFormData(ticketForm.guid, ticketFormDataPayload);
        handleResponseMessages(prom);
        prom
            .then( (response) => {
                if (response.errNo === 0 && response.data.data) {
                    const tickets = this.tickets.map( (ticket) => {
                        const foundMatch = ticket.guid === ticketForm.guid;
                        console.log(ticket.guid + "=" + ticketForm.guid + " ?" + foundMatch)
                        if (foundMatch) {
                            ticket.ticket.onSite = ticketFormDataPayload.onSite!;
                            // return ticketForm;
                        }
                        return  ticket;
                    })
                    this.tickets = R.clone(tickets);
                }
            })
            .finally(()=> {
                this.isLoadingInternal = false;
            })
    }

    public cancel() {
        this.$emit("cancel");
    }

    public scan() {
        this.$emit("scan");
    }

    public selectedCheckInTicket(ticketFormAthlete: ITicketForm) {
        this.$emit("selectedCheckInTicket", R.clone(ticketFormAthlete));
    }

    public get getIsLoading() {
        return this.isLoading || this.isLoadingInternal;
    }

    public onSetSite(ticketForm: ITicketFormBase) {
        this.submitData(ticketForm)
        // this.$emit("onSetSite", R.clone(ticketFormAthlete));
    }
}
</script>
