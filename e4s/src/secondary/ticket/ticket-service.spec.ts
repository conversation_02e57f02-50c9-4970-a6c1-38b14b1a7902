import { TicketService } from "./ticket-service";
import { ITicketForm } from "./ticket-models";

const ticketService = new TicketService();

describe("TicketService", () => {
  test("getCarReg", () => {
    const ticketForm: ITicketForm = {
      guid: "121-749d07862e75c18cf301e1eb22606f9c",
      id: 121,
      orderId: 137836,
      user: { id: 1, name: "E4S Admin", email: "<EMAIL>" },
      product: { productId: 0, name: "Car Park", variationId: 137823 },
      athlete: { id: 0, bibno: 0, name: "" },
      entries: [],
      variations: { e4s_car_reg: "D4 YMR,A1" },
      ticket: {
        element: 2,
        scannedCount: 0,
        searchCount: 20,
        onSite: false,
        totalCount: 2,
        dataReq: true,
        seat: {
          id: 0,
          stadium: "",
          stand: "",
          block: "",
          row: "",
          date: "",
          seatNo: 0,
        },
      },
      data: { id: 2887, name: "", address: "", telNo: "", email: "" },
      text: { terms: "", ticketText: "", ticketForm: "" },
      audit: [
        {
          id: 17530,
          action: "Read",
          auditDate: "2023-06-06T17:57:13+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17529,
          action: "Read",
          auditDate: "2023-06-06T07:41:09+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17526,
          action: "Read",
          auditDate: "2023-06-06T06:20:32+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17525,
          action: "Read",
          auditDate: "2023-06-06T03:58:00+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17524,
          action: "Read",
          auditDate: "2023-06-06T03:57:02+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17523,
          action: "Read",
          auditDate: "2023-06-06T03:52:24+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17522,
          action: "Read",
          auditDate: "2023-06-06T03:39:57+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17521,
          action: "Read",
          auditDate: "2023-06-06T03:32:43+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17520,
          action: "Read",
          auditDate: "2023-06-06T03:31:47+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17519,
          action: "Read",
          auditDate: "2023-06-06T03:29:53+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17518,
          action: "Read",
          auditDate: "2023-06-06T03:29:32+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17517,
          action: "Read",
          auditDate: "2023-06-06T03:25:27+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17516,
          action: "Read",
          auditDate: "2023-06-05T20:05:16+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17515,
          action: "Read",
          auditDate: "2023-06-05T19:59:11+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17514,
          action: "Read",
          auditDate: "2023-06-05T19:58:58+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17513,
          action: "Read",
          auditDate: "2023-06-05T19:53:43+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17512,
          action: "Read",
          auditDate: "2023-06-05T19:53:01+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17510,
          action: "Read",
          auditDate: "2023-06-05T16:09:39+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17509,
          action: "Read",
          auditDate: "2023-06-05T16:09:26+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17507,
          action: "Read",
          auditDate: "2023-06-05T16:01:17+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 17506,
          action: "Read",
          auditDate: "2023-06-05T16:01:12+01:00",
          user: { id: 1, name: "E4S Admin" },
        },
        {
          id: 60,
          action: "Read",
          auditDate: "2021-03-31T06:06:15+01:00",
          user: { id: 18329, name: "Kirsten Fraser" },
        },
        {
          id: 59,
          action: "Read",
          auditDate: "2021-03-31T06:06:07+01:00",
          user: { id: 18329, name: "Kirsten Fraser" },
        },
      ],
      competition: {
        id: 370,
        date: "2023-07-01",
        name: "Car Park Testing",
        location: { id: 20, name: "Pingles S'tadium 2" },
        organiser: { id: 54, name: "Nuneaton Opens" },
        dates: ["2023-07-01"],
      },
    } as any as ITicketForm;

    const res = ticketService.getCarReg(ticketForm);

    expect(
      res
    ).toBe("A1");

  });
});
